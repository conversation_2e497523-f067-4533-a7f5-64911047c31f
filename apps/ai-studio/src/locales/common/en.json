{"action": {"add": "Add", "cancel": "Cancel", "remove": "Remove"}, "agent": {"processing": "I'm processing your request, just a moment!"}, "builderLayout": {"apply": "Apply", "cancel": "Cancel", "publish": "Publish", "published": "Published successfully", "run": "Run"}, "button": {"add": "Add", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "delete": "Delete", "duplicate": "Duplicate", "edit": "Edit", "export": "Export", "reconnect": "Reconnect", "remove": "Remove", "save": "Save", "saveAsTemplate": "Save as template", "test": "Test"}, "error": {"pattern": "The value must match the pattern: {pattern}.", "required": "This field is required."}, "historyRuns": {"copied": "<PERSON>pied", "copy": "Copy", "error": "Error", "failed": "Failed", "input": "Input", "logs": "Logs", "noError": "No error occurred", "noInput": "No input was provided.", "noLogs": "No logs are available at this time.", "noOutput": "No output was generated.", "output": "Output", "running": "Running", "success": "Success", "title": "Recent Runs"}, "instructions": {"generate": "Generate", "placeholder": "Write your instruction of agent here", "title": "Instructions"}, "key": "Key", "messageBuilder": {"copy": "Copy", "replay": "Replay"}, "pageHeader": {"buttonMenuActions": {"fromScratch": "From Scratch", "fromTemplate": "From Template", "importFromFile": "Import from file"}}, "settings": {"model": {"info": {"frequencyPenalty": "Frequency Penalty:", "presencePenalty": "Presence Penalty:", "temperature": "Temperature:", "textFormat": "Response Format:", "textFormatValue": "text", "tokens": "Tokens:", "toolChoice": "tool_choice:", "toolChoiceValue": "web_search_preview", "topP": "Top P:"}, "label": "Model", "multiAgents": {"collaboration": {"addCollaborator": "Add a new Collaborator", "agent": "Collaborator agent", "agentPlaceholder": "Please select an agent", "collaborator": {"description": "An agent in multi-agent collaboration that gathers information from other agents and delivers the final response.", "title": "Collaborators"}, "description": "Multi-agent collaboration allows this agent assign tasks to other agents. As a supervisor agent, it gathers and organizes responses from its collaborator agents.", "editCollaborator": "Edit Collaborator", "handoffInstruction": "Collaborator handoff instruction", "handoffInstructionDescription": "Describe the agent’s role in multi-agent collaboration. We recommend using clear and specific examples. You can also define the preferred style and tone. When referring to the agent, use its collaborator name.", "mode": {"description": "Select how this agent should manage responses:", "options": {"routing": {"description": "The agent routes information to the most suitable collaborator agent, which then provides the final response.", "title": "Routing"}, "supervisor": {"description": "The agent gathers inputs from collaborator agents and delivers a final response.", "title": "Supervisor"}}, "title": "Collaboration Mode"}, "name": "Collaborator name", "nameDescription": "A secondary name used only in collaboration instructions.", "noAgent": "There is no collaborator agent yet, please create one first.", "title": "Multi-agent collaboration"}, "mode": {"description": "Select how this agent should manage responses:", "title": "Collaboration Mode"}, "title": "Agents"}, "placeholder": "Select a model", "settings": {"frequencyPenalty": "Frequency Penalty", "maxTokens": "Tokens", "presencePenalty": "Presence Penalty", "responseFormat": "Response Format", "stopSequences": "Stop sequences", "stopSequencesPlaceholder": "Enter sequences and press tab", "temperature": "Temperature", "topP": "Top P"}, "tools": {"all": "All", "builtIn": "Build-in tools", "city": "City", "configureWebsearch": "Configure Web Search tool", "country": "Country", "function": "Functions", "large": "Large", "medium": "Medium", "noBuiltInTools": "No tools available", "region": "Region", "searchContextSize": "Search Context Size", "searchPlaceholder": "Search tools and functions...", "selectCountry": "Select a country...", "selectTimezone": "Select a timezone...", "small": "Small", "timezone": "Timezone", "title": "Tools", "tool": "Tools", "websearch": "Websearch"}}, "title": "Settings"}, "systemMessageSettings": {"message": {"ai": "AI message", "assistant": "Assistant message", "placeholder": "Write your system message here", "user": "User message"}, "title": "Messages"}, "template": {"saved": "Template saved successfully.", "saveFailed": "Couldn't save the template. Please try again."}, "unsavedChangesModal": {"cancel": "Cancel", "confirm": "Continue without saving", "description": "Are you sure you want to continue? Your Unsaved changes will be lost. Make sure you publish your changes", "title": "Unsaved Changes"}, "value": "Value", "versionControl": {"cancel": "Cancel", "createVersion": "Add to version history", "descriptionPlaceholder": "Describe what changed", "editVersion": "Edit version information", "name": "Version Name", "rename": "<PERSON><PERSON>", "restore": "Rest<PERSON>", "restoreSuccess": "Restored “{name}” successfully. ", "restoreThisVersion": "Restore this version", "restoreVersionDescription": "Are you sure you want to restore this version? Your current changes will be replaced. \nYou can also save the current changes before restoring.", "saveAndRestore": "Save and restore", "saveAndRestoreSuccess": "Current prompt saved as a new version. Restored “{name}” successfully. ", "searchVersion": "Search version", "title": "Version", "titlePlaceholder": "Title"}, "flow": {"duplicated": "Flow duplicated successfully..", "duplicateFailed": "Couldn’t duplicate the Flow. Please try again."}}