import type React from 'react';
import { createContext, useContext, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useSWR from 'swr';

import { useHandleApiError } from '@/hooks/useHandleApiError';
import type {
  IPrompt,
  IPromptCreatePayload,
  IPromptTemplateCategory,
  ITemplate,
  ITemplateCreatePayload,
} from '@/models';
import type { IStudioSuccessListResponse } from '@/models/response';
import { PromptAPI } from '@/services/api';
import { TemplateAPI } from '@/services/api/template';
import { useAppContext } from '../AppContext';
import { DEFAULT_MODEL_OPTIONS } from '@/constants/agent';
import { useListPromptModel } from '@/hooks/useListPromptModel';

const usePrompt = () => {
  const { workspaceId } = useParams();
  const navigate = useNavigate();
  const [limit, setLimit] = useState(10);
  const [cursor, setCursor] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<IPromptTemplateCategory | null>();
  const [selectedTemplate, setSelectedTemplate] = useState<ITemplate | null>(null);
  const { openConfirmModal, closeConfirmModal, orgId } = useAppContext();
  const { handleApiError } = useHandleApiError();

  const { data: models } = useListPromptModel();

  const { data: prompts, mutate } = useSWR<IStudioSuccessListResponse<IPrompt>>(
    workspaceId ? ['prompts', workspaceId, limit, cursor, searchValue] : null,
    () => PromptAPI.getList(workspaceId ?? '', limit, cursor, searchValue),
    {
      revalidateOnFocus: false,
    }
  );

  const createPrompt = async (prompt: Omit<IPromptCreatePayload, 'workspaceId'>) => {
    const model = models?.[0]?.name ?? DEFAULT_MODEL_OPTIONS.model;
    try {
      const newPrompt = await PromptAPI.create({
        ...prompt,
        orgId,
        workspaceId: workspaceId ?? '',
        settings: prompt?.settings || {
          model,
          modelOptions: {
            ...DEFAULT_MODEL_OPTIONS,
            model,
          },
        },
      });
      if (newPrompt.id) {
        navigate(`/studio/${workspaceId}/prompts/${newPrompt.id}`);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const duplicatePrompt = async (prompt: IPrompt, isNavigate?: boolean) => {
    try {
      const newPrompt = await PromptAPI.create({
        ...prompt,
        workspaceId: workspaceId ?? '',
        name: `${prompt.name} (Copy)`,
      });
      mutate();
      if (isNavigate) {
        navigate(`/studio/${workspaceId}/prompts/${newPrompt.id}`);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const updatePrompt = async (prompt: IPrompt) => {
    try {
      await PromptAPI.update(prompt);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const deletePrompt = async (id: string) => {
    try {
      await PromptAPI.delete(id);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const exportPrompt = (id: string) => {
    console.log('export prompt', id);
  };

  const getSearchPrompts = (value: string) => {
    setSearchValue(value);
  };

  const createTemplate = async (template: ITemplateCreatePayload) => {
    try {
      await TemplateAPI.create(workspaceId ?? '', template);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleDeletePrompt = async (prompt: IPrompt, callback?: () => void) => {
    openConfirmModal({
      name: prompt.name,
      onConfirm: async () => {
        await deletePrompt(prompt.id);
        closeConfirmModal();
        callback?.();
      },
      onCancel: () => {
        closeConfirmModal();
      },
    });
  };

  const handleSaveAsTemplate = async (prompt: IPrompt) => {
    createTemplate({
      name: prompt?.name ?? '',
      description: prompt?.description ?? '',
      type: 'prompt',
      resource: 'prompt',
      settings: prompt?.settings,
    });
  };

  return {
    prompts,
    promptModels: models ?? [],
    createPrompt,
    duplicatePrompt,
    updatePrompt,
    deletePrompt,
    exportPrompt,
    getSearchPrompts,
    limit,
    setLimit,
    cursor,
    setCursor,
    searchValue,
    setSearchValue,
    selectedCategory,
    setSelectedCategory,
    selectedTemplate,
    setSelectedTemplate,
    createTemplate,
    handleDeletePrompt,
    handleSaveAsTemplate,
  };
};

export type PromptContextType = ReturnType<typeof usePrompt>;

const context = createContext<PromptContextType | null>(null);

export const PromptContextProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value?: PromptContextType;
}) => {
  const defaultValue = usePrompt();
  const contextValue = value || defaultValue;

  return <context.Provider value={contextValue}>{children}</context.Provider>;
};

export const usePromptContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('usePromptContext must be used inside PromptContextProvider');
  }

  return value;
};
