import type { ReactNode } from 'react';
import { describe, it, expect, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { CredentialContextProvider, useCredentialContext } from './index';
import { type ITool, type ICredentialPayload, CredentialType, ProviderType } from '@/models';
import { notifications } from '@mantine/notifications';
import { ICredential } from '@resola-ai/ui';

let mockWorkspaceId: string | undefined = '123';

// Mock router
vi.mock('react-router-dom', () => ({
  useParams: () => ({ workspaceId: mockWorkspaceId }),
}));

// Mock API
vi.mock('@/services/api/credential', () => ({
  CredentialAPI: {
    get: vi.fn(),
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    test: vi.fn(),
    reconnect: vi.fn(),
  },
}));

describe('CredentialContext', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <CredentialContextProvider>{children}</CredentialContextProvider>
  );

  const mockCredential: ICredential = {
    id: '1',
    name: 'Test Credential',
    description: 'Test Description',
    authenticationScheme: CredentialType.API_KEY,
    provider: ProviderType.CUSTOM,
    settings: { apiKey: 'test-key' },
    metadata: {},
    workspaceId: '123',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockTool: ITool = {
    id: '1',
    name: ProviderType.CUSTOM,
    displayName: 'Test Tool',
    description: 'Test Description',
    categories: ['test'],
    settings: {
      credential: {
        credentialTypes: ['api_key'],
      },
    },
    credentials: [
      {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: [
          {
            name: 'apiKey',
            displayName: 'API Key',
            description: 'The API key value',
            type: 'password',
            required: true,
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    mockWorkspaceId = '123';
  });

  it('provides credential context with default values when workspaceId exists', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(result.current.selectedTool).toBeUndefined();
    expect(result.current.loading).toBe(false);
    expect(result.current.limit).toBe(10);
    expect(result.current.cursor).toBe('');
    expect(result.current.searchValue).toBe('');
  });

  it('useCredentialContext without provider', () => {
    expect(() => {
      renderHook(() => useCredentialContext());
    }).toThrow('useCredentialContext must be used within a CredentialContextProvider');
  });

  it('provides credential context with default values when workspaceId is undefined', () => {
    mockWorkspaceId = undefined;
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(result.current.selectedTool).toBeUndefined();
    expect(result.current.loading).toBe(false);
    expect(result.current.limit).toBe(10);
    expect(result.current.cursor).toBe('');
    expect(result.current.searchValue).toBe('');
  });

  it('Handle actions when workspaceId is undefined', async () => {
    mockWorkspaceId = undefined;
    const { result } = renderHook(() => useCredentialContext(), { wrapper });
    await act(async () => {
      // fetchCredentials removed, nothing to call here
    });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.loading).toBe(false);
    // createCredential
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    await act(async () => {
      await result.current.createCredential(mockCreatePayload);
    });

    expect(result.current.loading).toBe(false);

    // updateCredential
    await act(async () => {
      await result.current.updateCredential(mockCredential);
    });

    expect(result.current.loading).toBe(false);

    // deleteCredential
    await act(async () => {
      await result.current.deleteCredential('1');
    });

    expect(result.current.loading).toBe(false);

    // testCredential
    await act(async () => {
      await result.current.testCredential(mockCredential);
    });

    expect(result.current.loading).toBe(false);

    // reconnectCredential
    await act(async () => {
      await result.current.reconnectCredential('1');
    });

    expect(result.current.loading).toBe(false);

    // fetchCredential
    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('updates credentials when setCredentials is called', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSelectedCredential(mockCredential);
    });

    expect(result.current.selectedCredential).toEqual(mockCredential);
  });

  it('updates selectedTool when setSelectedTool is called', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSelectedTool(mockTool);
    });

    expect(result.current.selectedTool).toEqual(mockTool);
  });

  it('handles search value updates', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSearchValue('test');
    });

    expect(result.current.searchValue).toBe('test');
  });

  it('handles pagination updates', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setLimit(20);
      result.current.setCursor('2');
    });

    expect(result.current.limit).toBe(20);
    expect(result.current.cursor).toBe('2');
  });

  it('fetches credentials successfully', async () => {
    const mockData = {
      data: [mockCredential],
    };
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.getList).mockResolvedValueOnce(mockData);
  });

  it('creates a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    vi.mocked(CredentialAPI.create).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.createCredential(mockCreatePayload);
    });

    expect(CredentialAPI.create).toHaveBeenCalledWith('123', mockCreatePayload);
    expect(result.current.loading).toBe(false);
  });

  it('handles createCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(new Error('Failed to create credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCreatePayload);
      });
    }).rejects.toThrow('Failed to create credential');

    expect(CredentialAPI.create).toHaveBeenCalledWith('123', mockCreatePayload);
    expect(result.current.loading).toBe(false);
  });

  it('updates a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.update).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.updateCredential(mockCredential);
    });

    expect(CredentialAPI.update).toHaveBeenCalledWith('123', mockCredential);
    expect(result.current.loading).toBe(false);
  });

  it('handles updateCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.update).mockRejectedValueOnce(new Error('Failed to update credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.updateCredential(mockCredential);
      });
    }).rejects.toThrow('Failed to update credential');

    expect(CredentialAPI.update).toHaveBeenCalledWith('123', mockCredential);
    expect(result.current.loading).toBe(false);
  });

  it('deletes a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.delete).mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.deleteCredential('1');
    });

    expect(CredentialAPI.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles deleteCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.delete).mockRejectedValueOnce(new Error('Failed to delete credential'));
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.deleteCredential('1', true);
    });

    expect(CredentialAPI.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(notificationsSpy).toHaveBeenCalledWith({
      message: 'Failed to delete credential',
      color: 'red',
    });

    notificationsSpy.mockRestore();
  });

  it('tests a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.test).mockResolvedValueOnce({ error: 'testFailed' });

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredential, true);
    });

    expect(CredentialAPI.test).toHaveBeenCalledWith('123', mockCredential);
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('handles testCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.test).mockRejectedValueOnce(new Error('Failed to test credential'));
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredential, true);
    });

    expect(CredentialAPI.test).toHaveBeenCalledWith('123', mockCredential);
    expect(result.current.loading).toBe(false);
    expect(notificationsSpy).toHaveBeenCalledWith({
      message: 'Failed to test credential',
      color: 'red',
    });

    notificationsSpy.mockRestore();
  });

  it('reconnects a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.reconnect).mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.reconnectCredential('1');
    });

    expect(CredentialAPI.reconnect).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('gets a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.get).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(CredentialAPI.get).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toEqual(mockCredential);
  });

  it('handles fetchCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.get).mockRejectedValueOnce(new Error('Failed to fetch credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(CredentialAPI.get).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('handles reconnectCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.reconnect).mockRejectedValueOnce(
      new Error('Failed to reconnect credential')
    );
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });
    await act(async () => {
      await result.current.reconnectCredential('1', true);
    });

    expect(CredentialAPI.reconnect).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(notificationsSpy).toHaveBeenCalledWith({
      message: 'Failed to reconnect credential',
      color: 'red',
    });

    notificationsSpy.mockRestore();
  });
});
