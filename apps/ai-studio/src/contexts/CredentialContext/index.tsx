import type React from 'react';
import { createContext, useContext, useState } from 'react';
import { type ICredentialPayload, type ICredential, credentialNodes } from '@resola-ai/ui';
import { useParams } from 'react-router-dom';
import { CredentialAPI } from '@/services/api/credential';
import { useHandleApiError } from '@/hooks/useHandleApiError';
import useSWR from 'swr';
import type { IStudioSuccessListResponse } from '@/models';

export interface CredentialContextType {
  credentials: IStudioSuccessListResponse<ICredential> | undefined;
  tools: any[];
  limit: number;
  cursor: string;
  setLimit: (limit: number) => void;
  setCursor: (cursor: string) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
  loading: boolean;
  createCredential: (credential: ICredentialPayload) => Promise<void>;
  updateCredential: (credential: ICredentialPayload) => Promise<void>;
  deleteCredential: (id: string, isHandleError?: boolean) => Promise<void>;
  testCredential: (credential: ICredentialPayload, isHandleError?: boolean) => Promise<void>;
  reconnectCredential: (id: string, isHandleError?: boolean) => Promise<void>;
  selectedCredential?: ICredential;
  setSelectedCredential: (credential: ICredential | undefined) => void;
  selectedTool?: any;
  setSelectedTool: (tool: any | undefined) => void;
  loadingCredential: boolean;
  fetchCredential: (id: string) => Promise<void>;
}

const getErrorDetails = (error: any, options?: { defaultMessage?: string }) => {
  const errorCode = error?.response?.data?.error?.errorCode;
  const errorMessage =
    error?.response?.data?.error?.details ||
    error?.response?.data?.error?.message ||
    error?.message ||
    options?.defaultMessage;
  return { errorCode, errorMessage };
};

export const useCredential = () => {
  const { workspaceId } = useParams();
  const [selectedCredential, setSelectedCredential] = useState<ICredential | undefined>();
  const [selectedTool, setSelectedTool] = useState<any>();
  const [tools, _] = useState<any[]>(credentialNodes);
  const [limit, setLimit] = useState(10);
  const [cursor, setCursor] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingCredential, setLoadingCredential] = useState(false);
  const { handleApiError } = useHandleApiError();

  const { data: credentials, mutate } = useSWR<IStudioSuccessListResponse<ICredential>>(
    workspaceId ? ['credentials', workspaceId, limit, cursor, searchValue] : null,
    () => CredentialAPI.getList(workspaceId || '', limit, cursor, searchValue),
    {
      revalidateOnFocus: false,
    }
  );

  const fetchCredential = async (id: string) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoadingCredential(true);
      const data = await CredentialAPI.get(workspaceId, id);
      setSelectedCredential(data);
    } catch (err) {
      handleApiError(err);
    } finally {
      setLoadingCredential(false);
    }
  };

  const createCredential = async (credential: ICredentialPayload) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.create(workspaceId, credential);
      mutate();
    } catch (err) {
      const { errorMessage } = getErrorDetails(err);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateCredential = async (credential: ICredentialPayload) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.update(workspaceId, credential);
      mutate();
    } catch (err) {
      const { errorMessage } = getErrorDetails(err);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const deleteCredential = async (id: string, isHandleError?: boolean) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.delete(workspaceId, id);
      mutate();
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const testCredential = async (
    credential: ICredentialPayload,
    isHandleError?: boolean
  ): Promise<void> => {
    try {
      if (!workspaceId) {
        return;
      }

      setLoading(true);
      const data = await CredentialAPI.test(workspaceId, credential);
      setSelectedCredential(undefined);
      if (data?.error) {
        throw new Error('data.error');
      }
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const reconnectCredential = async (id: string, isHandleError?: boolean) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.reconnect(workspaceId, id);
      setSelectedCredential(undefined);
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    credentials,
    tools,
    limit,
    cursor,
    searchValue,
    setCursor,
    setLimit,
    setSearchValue,
    loading,
    loadingCredential,
    fetchCredential,
    createCredential,
    updateCredential,
    deleteCredential,
    testCredential,
    reconnectCredential,
    selectedCredential,
    setSelectedCredential,
    selectedTool,
    setSelectedTool,
  };
};

const CredentialContext = createContext<CredentialContextType | null>(null);

export const CredentialContextProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value?: CredentialContextType;
}) => {
  const defaultValue = useCredential();
  const contextValue = value ?? defaultValue;

  return <CredentialContext.Provider value={contextValue}>{children}</CredentialContext.Provider>;
};

export const useCredentialContext = () => {
  const context = useContext(CredentialContext);
  if (!context) {
    throw new Error('useCredentialContext must be used within a CredentialContextProvider');
  }
  return context;
};
