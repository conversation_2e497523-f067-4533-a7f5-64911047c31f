import { act, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import FlowBuilder from './index';
import userEvent from '@testing-library/user-event';
import { FlowBuilderProvider, useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { renderHook } from '@testing-library/react';
import type { ResourceType } from '@/types';
import { useState, useEffect } from 'react';
mockLibraries();

// Mock navigate function
const mockNavigate = vi.fn();

// Mock React Router
vi.mock('react-router-dom', () => ({
  ...vi.importActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({ workspaceId: 'test-workspace', flowId: 'test-flow' }),
  useLocation: () => ({ pathname: '/studio/test-workspace/flows/test-flow' }),
}));

// Mock useConnection hook
vi.mock('@xyflow/react', async () => {
  const actual = await vi.importActual('@xyflow/react');
  return {
    ...actual,
    useConnection: () => ({
      inProgress: false,
      fromNode: { id: 'other-node' },
    }),
    Handle: ({ position, type, id }) => <div data-testid={`handle-${position}-${type}-${id}`} />,
  };
});

// Mock BuilderLayout component
vi.mock('@/components/BuilderLayout', () => ({
  default: function MockBuilderLayout({
    children,
    title,
    onBack,
    onTitleChange,
    onPublish,
    versionControl,
    historyRuns,
    seeMoreActions,
  }) {
    const [isEditing, setIsEditing] = useState(false);
    const [localTitle, setLocalTitle] = useState(title);

    useEffect(() => {
      setLocalTitle(title);
    }, [title]);

    const handleEditClick = () => {
      setIsEditing(true);
    };

    const handleSave = () => {
      if (localTitle) {
        setIsEditing(false);
        onTitleChange(localTitle);
      }
    };

    const handleCancel = () => {
      setLocalTitle(title);
      setIsEditing(false);
    };

    return (
      <div data-testid='builder-layout'>
        <div data-testid='builder-header'>
          <button type='button' onClick={onBack} data-testid='back-button'>
            Back
          </button>
          {isEditing ? (
            <>
              <input
                data-testid='title-name-input'
                value={localTitle}
                onChange={(e) => setLocalTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSave();
                  if (e.key === 'Escape') handleCancel();
                }}
              />
              <button type='button' onClick={handleSave} data-testid='save-title-button'>
                Save
              </button>
              <button type='button' onClick={handleCancel} data-testid='cancel-title-button'>
                Cancel
              </button>
            </>
          ) : (
            <h1 data-testid='flow-title'>{localTitle}</h1>
          )}
          <button type='button' onClick={handleEditClick} data-testid='edit-title-button'>
            Edit
          </button>
          <button type='button' onClick={onPublish} data-testid='publish-button'>
            Publish
          </button>
          <button
            type='button'
            onClick={() => console.log('Opening versions')}
            data-testid='version-control'
          >
            {versionControl}
          </button>
          <div data-testid='history-runs'>
            {historyRuns && (
              <div data-testid='builder-action-popover'>
                <button type='button' onClick={() => console.log('Opening history runs')}>
                  History Runs
                </button>
                <div data-testid='history-runs-list' style={{ display: 'none' }}>
                  <div data-testid='run-item'>Mock Run Item</div>
                </div>
              </div>
            )}
          </div>
          <div data-testid='see-more-actions'>
            {seeMoreActions && (
              <button
                type='button'
                onClick={() => seeMoreActions.onEdit?.()}
                data-testid='edit-action'
              >
                Edit
              </button>
            )}
          </div>
        </div>
        <div data-testid='builder-content'>{children}</div>
      </div>
    );
  },
}));

// Mock BuilderActionIcon component
vi.mock('@/components/BuilderLayout/BuilderActionIcon', () => ({
  default: ({ children, onClick }) => (
    <button type='button' onClick={onClick} data-testid='builder-action-icon'>
      {children}
    </button>
  ),
}));

describe('FlowBuilder', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // it('renders with BuilderLayout and flow content', async () => {
  //   renderWithMantine(<FlowBuilder />);

  //   // Wait for the component to load
  //   await waitFor(() => {
  //     // Check for the BuilderLayout elements
  //     expect(screen.getByTestId('builder-layout')).toBeInTheDocument();
  //     expect(screen.getByTestId('flow-title')).toHaveTextContent('Untitled Flow');

  //     // Check for the new BuilderLayout elements
  //     expect(screen.getByTestId('version-control')).toBeInTheDocument();
  //     expect(screen.getByTestId('history-runs')).toBeInTheDocument();
  //     expect(screen.getByTestId('see-more-actions')).toBeInTheDocument();

  //     // Check for the ReactFlow elements inside the builder content
  //     expect(screen.getByText('Select a trigger')).toBeInTheDocument();
  //     expect(screen.getByText('Select a trigger that starts your flow')).toBeInTheDocument();
  //   });
  // });

  it('navigates back when back button is clicked', async () => {
    renderWithMantine(<FlowBuilder />);
    const user = userEvent.setup();

    await waitFor(() => {
      expect(screen.getByTestId('back-button')).toBeInTheDocument();
    });

    await user.click(screen.getByTestId('back-button'));

    expect(mockNavigate).toHaveBeenCalledWith('/studio/test-workspace/flows');
  });

  it('updates flow title when title is changed', async () => {
    renderWithMantine(<FlowBuilder />);
    const user = userEvent.setup();

    await waitFor(() => {
      expect(screen.getByTestId('edit-title-button')).toBeInTheDocument();
    });

    // Click edit button to enter edit mode
    await user.click(screen.getByTestId('edit-title-button'));

    // Verify that the input field appears
    await waitFor(() => {
      expect(screen.getByTestId('title-name-input')).toBeInTheDocument();
    });

    // Change the title
    const titleInput = screen.getByTestId('title-name-input');
    await user.clear(titleInput);
    await user.type(titleInput, 'New Flow Title');

    // Save the changes
    await user.click(screen.getByTestId('save-title-button'));

    // Verify that the title was updated
    await waitFor(() => {
      expect(screen.getByTestId('flow-title')).toHaveTextContent('New Flow Title');
    });
  });

  it('handles version control button click', async () => {
    // Create a spy to track console.log calls
    const consoleSpy = vi.spyOn(console, 'log');

    renderWithMantine(<FlowBuilder />);
    const user = userEvent.setup();

    // Find the version control element and click it
    const versionControl = await screen.findByTestId('version-control');
    await user.click(versionControl);

    // Verify that the version control action was called
    expect(consoleSpy).toHaveBeenCalledWith('Opening versions');

    // Clean up
    consoleSpy.mockRestore();
  });

  it('handles history runs button click', async () => {
    renderWithMantine(<FlowBuilder />);
    const user = userEvent.setup();

    // Find and click the history runs button to open the popover
    const historyRunsButton = await screen.findByTestId('history-runs');
    const popoverButton = historyRunsButton.querySelector(
      '[data-testid="builder-action-popover"] button'
    );
    if (!popoverButton) throw new Error('Popover button not found');
    await user.click(popoverButton);

    // Verify that the history runs popover is shown
    await waitFor(() => {
      expect(screen.getByTestId('history-runs-list')).toBeInTheDocument();
    });
  });

  it('adds a new version', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <FlowBuilderProvider>{children}</FlowBuilderProvider>
    );
    const { result } = renderHook(() => useFlowBuilderContext(), { wrapper });

    const newVersion = {
      name: 'New Version',
      description: 'Test version',
      createdAt: new Date().toISOString(),
      version: '1',
      workspaceId: 'test-workspace',
      resourceType: 'flow' as ResourceType,
    };

    act(() => {
      result.current.handleAddVersion(newVersion);
    });

    // Check that the new version was added to the beginning of the array
    expect(result.current.versions[0]).toEqual(
      expect.objectContaining({
        ...newVersion,
        id: expect.any(String), // nanoid generates a string ID
      })
    );
  });

  it('edits an existing version', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <FlowBuilderProvider>{children}</FlowBuilderProvider>
    );
    const { result } = renderHook(() => useFlowBuilderContext(), { wrapper });

    // First add a version to edit
    const originalVersion = {
      name: 'Original Version',
      description: 'Original description',
      createdAt: new Date().toISOString(),
      version: '1',
      workspaceId: 'test-workspace',
      resourceType: 'flow' as ResourceType,
    };

    act(() => {
      result.current.handleAddVersion(originalVersion);
    });

    const versionId = result.current.versions[0].id;
    const updatedVersion = {
      id: versionId,
      name: 'Updated Version',
      description: 'Updated description',
      createdAt: originalVersion.createdAt,
      version: '1',
      workspaceId: 'test-workspace',
      resourceType: 'flow' as ResourceType,
    };

    act(() => {
      result.current.handleEditVersion(updatedVersion);
    });

    // Check that the version was updated
    expect(result.current.versions[0]).toEqual(updatedVersion);
  });

  it('deletes a version', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <FlowBuilderProvider>{children}</FlowBuilderProvider>
    );
    const { result } = renderHook(() => useFlowBuilderContext(), { wrapper });

    // First add a version to delete
    const versionToDelete = {
      name: 'Version to Delete',
      description: 'Will be deleted',
      createdAt: new Date().toISOString(),
      version: '1',
      workspaceId: 'test-workspace',
      resourceType: 'flow' as ResourceType,
    };

    act(() => {
      result.current.handleAddVersion(versionToDelete);
    });

    const versionId = result.current.versions[0].id;
    const version = result.current.versions[0];

    act(() => {
      result.current.handleDeleteVersion(version);
    });

    // Check that the version was removed
    expect(result.current.versions).not.toContainEqual(version);
    expect(result.current.versions.find((v) => v.id === versionId)).toBeUndefined();
  });
});
