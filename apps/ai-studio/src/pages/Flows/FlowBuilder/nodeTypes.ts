import { SelectActionNode } from '@/components/FlowBuilder';
import AddNode from '@/components/FlowBuilder/Nodes/AddNode';
import LoopingFrameNode from '@/components/FlowBuilder/Nodes/LoopingFrameNode';
import LoopingNode from '@/components/FlowBuilder/Nodes/LoopingNode';
import MainPathNode from '@/components/FlowBuilder/Nodes/MainPathNode';
import SubPathNode from '@/components/FlowBuilder/Nodes/SubPathNode';
import { FlowNodeType } from '@/models/flow';

const nodeTypes: Record<FlowNodeType, any> = {
  // [FlowNodeType.WebhookTrigger]: SelectTriggerNode,
  // [FlowNodeType.ScheduleTrigger]: SelectTriggerNode,
  // [FlowNodeType.Schedule]: SelectTriggerNode,
  // [FlowNodeType.EventTrigger]: SelectTriggerNode,
  // [FlowNodeType.NewTrigger]: SelectTriggerNode,
  [FlowNodeType.WebhookTrigger]: SelectActionNode,
  [FlowNodeType.ScheduleTrigger]: SelectActionNode,
  [FlowNodeType.Schedule]: SelectActionNode,
  [FlowNodeType.EventTrigger]: SelectActionNode,
  [FlowNodeType.NewTrigger]: SelectActionNode,
  [FlowNodeType.AddNode]: AddNode,
  [FlowNodeType.AddPathNode]: AddNode,
  [FlowNodeType.Branch]: MainPathNode,
  [FlowNodeType.Path]: MainPathNode,
  [FlowNodeType.SubPathNode]: SubPathNode,
  [FlowNodeType.Code]: SelectActionNode,
  [FlowNodeType.EmptyNode]: SelectActionNode,
  [FlowNodeType.Looping]: LoopingNode,
  [FlowNodeType.LoopingFrame]: LoopingFrameNode,
  [FlowNodeType.Gmail]: SelectActionNode,
  [FlowNodeType.Slack]: SelectActionNode,
  [FlowNodeType.Hubspot]: SelectActionNode,
  [FlowNodeType.GoogleDocs]: SelectActionNode,
  [FlowNodeType.GoogleSheets]: SelectActionNode,
  [FlowNodeType.GoogleSlides]: SelectActionNode,
  [FlowNodeType.GoogleCalendar]: SelectActionNode,
  [FlowNodeType.GoogleDrive]: SelectActionNode,
  [FlowNodeType.ZoomMeetings]: SelectActionNode,
  [FlowNodeType.Filter]: SelectActionNode,
  [FlowNodeType.Wait]: SelectActionNode,
  [FlowNodeType.Http]: SelectActionNode,
  [FlowNodeType.Chatbot]: SelectActionNode,
  [FlowNodeType.Pages]: SelectActionNode,
  [FlowNodeType.DecaTables]: SelectActionNode,
  [FlowNodeType.DecaCrm]: SelectActionNode,
  [FlowNodeType.DecaLivechat]: SelectActionNode,
  [FlowNodeType.OpenAI]: SelectActionNode,
  [FlowNodeType.Function]: SelectActionNode,
  [FlowNodeType.Formatter]: SelectActionNode,
  [FlowNodeType.DecaKb]: SelectActionNode,
  [FlowNodeType.DecaAiWidgets]: SelectActionNode,
  [FlowNodeType.Webhook]: SelectActionNode,
};

export default nodeTypes;
