import { describe, it, expect } from 'vitest';
import nodeTypes from './nodeTypes';
import { FlowNodeType } from '@/models/flow';

describe('nodeTypes', () => {
  it('should have all required node types defined', () => {
    // Check that the nodeTypes object is defined
    expect(nodeTypes).toBeDefined();
    expect(typeof nodeTypes).toBe('object');
  });

  it('should map WebhookTrigger to SelectTriggerNode', () => {
    expect(nodeTypes[FlowNodeType.WebhookTrigger]).toBeDefined();
  });

  it('should map Webhook to SelectActionNode', () => {
    expect(nodeTypes[FlowNodeType.Webhook]).toBeDefined();
  });

  it('should map EventTrigger to SelectTriggerNode', () => {
    expect(nodeTypes[FlowNodeType.EventTrigger]).toBeDefined();
  });

  it('should map NewTrigger to SelectTriggerNode', () => {
    expect(nodeTypes[FlowNodeType.NewTrigger]).toBeDefined();
  });

  // Tests for schedule node functionality (TK-8980)
  describe('Schedule Node Functionality', () => {
    it('should map Schedule to SelectTriggerNode', () => {
      expect(nodeTypes[FlowNodeType.Schedule]).toBeDefined();
      expect(nodeTypes[FlowNodeType.Schedule]).toBe(nodeTypes[FlowNodeType.WebhookTrigger]);
    });

    it('should map ScheduleTrigger to SelectTriggerNode', () => {
      expect(nodeTypes[FlowNodeType.ScheduleTrigger]).toBeDefined();
      expect(nodeTypes[FlowNodeType.ScheduleTrigger]).toBe(nodeTypes[FlowNodeType.WebhookTrigger]);
    });

    it('should have consistent trigger node mappings', () => {
      // All trigger types should map to the same component
      const triggerComponent = nodeTypes[FlowNodeType.WebhookTrigger];

      expect(nodeTypes[FlowNodeType.Schedule]).toBe(triggerComponent);
      expect(nodeTypes[FlowNodeType.ScheduleTrigger]).toBe(triggerComponent);
      expect(nodeTypes[FlowNodeType.EventTrigger]).toBe(triggerComponent);
      expect(nodeTypes[FlowNodeType.NewTrigger]).toBe(triggerComponent);
    });

    it('should include schedule nodes in the nodeTypes mapping', () => {
      const scheduleNodeTypes = [FlowNodeType.Schedule, FlowNodeType.ScheduleTrigger];

      scheduleNodeTypes.forEach(nodeType => {
        expect(nodeTypes).toHaveProperty(nodeType);
        expect(nodeTypes[nodeType]).toBeDefined();
      });
    });

    it('should map schedule nodes to trigger components', () => {
      // Schedule and ScheduleTrigger should both map to SelectTriggerNode
      expect(nodeTypes[FlowNodeType.Schedule]).toBeDefined();
      expect(nodeTypes[FlowNodeType.ScheduleTrigger]).toBeDefined();

      // They should be the same component as other triggers
      expect(nodeTypes[FlowNodeType.Schedule]).toBe(nodeTypes[FlowNodeType.WebhookTrigger]);
      expect(nodeTypes[FlowNodeType.ScheduleTrigger]).toBe(nodeTypes[FlowNodeType.WebhookTrigger]);
    });
  });

  it('should map action nodes correctly', () => {
    const actionNodeTypes = [
      FlowNodeType.Code,
      FlowNodeType.EmptyNode,
      FlowNodeType.Gmail,
      FlowNodeType.Slack,
      FlowNodeType.Hubspot,
      FlowNodeType.GoogleDocs,
      FlowNodeType.GoogleSheets,
      FlowNodeType.GoogleSlides,
      FlowNodeType.GoogleCalendar,
      FlowNodeType.GoogleDrive,
      FlowNodeType.ZoomMeetings,
      FlowNodeType.Filter,
      FlowNodeType.Wait,
      FlowNodeType.Http,
      FlowNodeType.Chatbot,
      FlowNodeType.Pages,
      FlowNodeType.DecaTables,
      FlowNodeType.DecaCrm,
      FlowNodeType.DecaLivechat,
      FlowNodeType.OpenAI,
      FlowNodeType.Function,
      FlowNodeType.Formatter,
      FlowNodeType.DecaKb,
      FlowNodeType.DecaAiWidgets,
    ];

    actionNodeTypes.forEach(nodeType => {
      expect(nodeTypes).toHaveProperty(nodeType);
      expect(nodeTypes[nodeType]).toBeDefined();
    });
  });

  it('should map structural nodes correctly', () => {
    expect(nodeTypes[FlowNodeType.AddNode]).toBeDefined();
    expect(nodeTypes[FlowNodeType.AddPathNode]).toBeDefined();
    expect(nodeTypes[FlowNodeType.Branch]).toBeDefined();
    expect(nodeTypes[FlowNodeType.Path]).toBeDefined();
    expect(nodeTypes[FlowNodeType.SubPathNode]).toBeDefined();
    expect(nodeTypes[FlowNodeType.Looping]).toBeDefined();
    expect(nodeTypes[FlowNodeType.LoopingFrame]).toBeDefined();
  });

  it('should not have undefined mappings', () => {
    Object.values(nodeTypes).forEach(component => {
      expect(component).toBeDefined();
      expect(component).not.toBeNull();
    });
  });

  it('should have consistent component references', () => {
    // Components that should be the same
    const triggerComponents = [
      nodeTypes[FlowNodeType.WebhookTrigger],
      nodeTypes[FlowNodeType.ScheduleTrigger],
      nodeTypes[FlowNodeType.Schedule],
      nodeTypes[FlowNodeType.EventTrigger],
      nodeTypes[FlowNodeType.NewTrigger],
    ];

    // All trigger components should be the same reference
    const firstTriggerComponent = triggerComponents[0];
    triggerComponents.forEach(component => {
      expect(component).toBe(firstTriggerComponent);
    });
  });
});
