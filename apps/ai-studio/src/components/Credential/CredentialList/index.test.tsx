import { screen, fireEvent, waitFor } from '@testing-library/react';
import { CredentialList } from './index';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import { AppContextProvider } from '@/contexts/AppContext';
import { mockCredentials } from '@/mockdata/credential';

mockLibraries();

vi.mock('@resola-ai/ui', async () => {
  const actual = await vi.importActual('@resola-ai/ui');
  return {
    ...actual,
    getIcon: (name: string) => {
      return <span data-testid={`icon-${name}`}>{name}</span>;
    },
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams({ lang: 'en' })],
  };
});

const mockOnEdit = vi.fn();
const mockOnDelete = vi.fn();
const mockOnTest = vi.fn();
const mockOnReconnect = vi.fn();

const renderWithRouter = (ui: React.ReactElement) => {
  return renderWithMantine(
    <MemoryRouter>
      <AppContextProvider>{ui}</AppContextProvider>
    </MemoryRouter>
  );
};

describe('CredentialList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the list of credentials', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    expect(screen.getByText(mockCredentials[0].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[1].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[2].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[0].description ?? '')).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[1].description ?? '')).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[2].description ?? '')).toBeInTheDocument();
  });

  it('displays correct icons for different providers', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials.slice(0, 3)}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Check if icons are rendered
    expect(screen.getByTestId('icon-openai')).toBeInTheDocument();
    expect(screen.getByTestId('icon-google')).toBeInTheDocument();
    expect(screen.getByTestId('icon-slack')).toBeInTheDocument();
  });

  it('handles edit action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    expect(editAction).toBeInTheDocument();

    await user.click(editAction);
    // Check if edit modal is opened
    const modal = await screen.findByText('modalEdit.title');
    expect(modal).toBeInTheDocument();

    // Fill in the edit form
    const nameInput = await screen.findByTestId('title-input');
    const descriptionInput = await screen.findByTestId('description-input');

    fireEvent.change(nameInput, { target: { value: 'Updated Name' } });
    fireEvent.change(descriptionInput, { target: { value: 'Updated Description' } });

    // Submit the form
    const confirmButton = screen.getByTestId('confirm-edit-modal');
    expect(confirmButton).toBeInTheDocument();

    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith({
        ...mockCredentials[0],
        name: 'Updated Name',
        description: 'Updated Description',
      });
      expect(screen.queryByText('modalEdit.title')).not.toBeVisible();
    });
  });

  it('Close edit modal when cancel button is clicked', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    const cancelButton = await screen.findByText('common.button.cancel');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('modalEdit.title')).toBeNull();
    });
  });

  it('show delete modal when delete button is clicked', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    await waitFor(() => {
      expect(mockOnDelete).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('handles test action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    fireEvent.click(menuButton);

    const user = userEvent.setup();
    // Click the test button in the menu
    const testButton = await screen.findByText('action.test');
    await user.click(testButton);

    await waitFor(() => {
      expect(mockOnTest).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('handles reconnect action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(menuButton);

    // Click the reconnect button in the menu
    const reconnectButton = await screen.findByText('action.reconnect');
    expect(reconnectButton).toBeInTheDocument();

    await user.click(reconnectButton);

    await waitFor(() => {
      expect(mockOnReconnect).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('renders with full width layout when isFullWidth is true', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        isFullWidth={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const gridLayout = screen.getByTestId('grid-layout');
    const children = gridLayout.children;
    Array.from(children).forEach((child) => {
      expect(child.getAttribute('data-testid')).toBe('aic-card-full-width');
    });
  });
});
