import { useTranslate } from '@tolgee/react';
import { getIcon, type ICredential } from '@resola-ai/ui';
import GridLayout from '@/components/GridLayout';
import AICard from '@/components/AICard';
import { EditModal } from '@/components/Modals';
import { useDisclosure } from '@mantine/hooks';
import { useState } from 'react';
import type { Callback, CardStatus } from '@/types';
interface CredentialListProps {
  credentials: ICredential[];
  isFullWidth?: boolean;
  onEdit?: (credential: ICredential) => Promise<void>;
  onDelete?: (credential: ICredential, setStatus?: (status: CardStatus) => void) => Promise<void>;
  onTest?: (credential: ICredential, setStatus?: (status: CardStatus) => void) => Promise<void>;
  onReconnect?: (
    credential: ICredential,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onSelect?: (credential: ICredential) => void;
}

export const CredentialList = ({
  credentials,
  isFullWidth = false,
  onEdit,
  onDelete,
  onTest,
  onReconnect,
  onSelect,
}: CredentialListProps) => {
  const { t } = useTranslate('credential');
  const [openedEditModal, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [editCredential, setEditCredential] = useState<ICredential | null>(null);

  const handleEdit = (credential: ICredential) => {
    setEditCredential(credential);
    openEditModal();
  };

  const handleTest = (credential: ICredential, setStatus?: (status: CardStatus) => void) => {
    onTest?.(credential, setStatus);
  };

  const handleClick = (credential: ICredential) => {
    onSelect?.(credential);
  };

  const handleDelete = (credential: ICredential, setStatus?: (status: CardStatus) => void) => {
    onDelete?.(credential, setStatus);
  };

  const handleReconnect = (credential: ICredential, setStatus?: (status: CardStatus) => void) => {
    onReconnect?.(credential, setStatus);
  };

  return (
    <GridLayout isFullWidth={isFullWidth} data-testid='grid-layout'>
      {credentials.map((credential) => (
        <AICard
          isFullWidth={isFullWidth}
          key={credential.id}
          title={credential.name}
          description={credential.description}
          icon={getIcon(credential.provider)}
          updatedAt={credential.updatedAt}
          actions={{
            onEdit: () => {
              handleEdit(credential);
            },
            onDelete: (callback?: Callback<CardStatus>) => {
              handleDelete(credential, callback);
            },
            onTest: (callback?: Callback<CardStatus>) => {
              handleTest(credential, callback);
            },
            onReconnect: (callback?: Callback<CardStatus>) => {
              handleReconnect(credential, callback);
            },
          }}
          data-testid={`credential-card-${credential.id}`}
          onClick={() => {
            handleClick(credential);
          }}
        />
      ))}
      <EditModal
        opened={openedEditModal}
        onCancel={closeEditModal}
        onConfirm={async (data) => {
          if (editCredential?.id) {
            await onEdit?.({
              ...editCredential,
              name: data.title,
              description: data.description,
            });
          }

          closeEditModal();
        }}
        title={t('modalEdit.title')}
        options={{
          titleLabel: t('modalEdit.nameLabel'),
          descriptionLabel: t('modalEdit.descriptionLabel'),
        }}
        initialValues={{
          title: editCredential?.name ?? '',
          description: editCredential?.description,
        }}
        data-testid='edit-modal'
      />
    </GridLayout>
  );
};
