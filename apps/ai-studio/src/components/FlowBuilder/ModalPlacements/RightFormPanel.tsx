import { ActionIcon, Box, Group, Paper, Transition, rem, useMantineTheme } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import {
  SchemaEngineWithTolgee,
  type SchemaFormInput,
} from '@resola-ai/ui/components/SchemaEngine';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import useFlowSchema from '@/hooks/useFlowSchema';
import { CredentialAPI } from '@/services/api/credential';
import { useParams } from 'react-router-dom';
import CatalogModal, {
  type CatalogModalRef,
  TriggerNodes,
  LoopingNodes,
  ActionNodes,
  type NodeNameTypes,
} from './CatalogModal';
import { FlowNodeType } from '@/models/flow';
import set from 'lodash/set';
import get from 'lodash/get';
import { useListCredential } from '@/hooks/useListCredential';
import { FlowDiagram } from '@/helpers/flowDiagram';
import NodeName from './NodeName';
import { resolveRefs } from '@resola-ai/ui/utils/schema';
import { NodeNameMapNode } from '@resola-ai/ui/components/Catalog';
import debounce from 'lodash/debounce';
import { ACTION_FIELD, TRIGGER_FIELD } from '@resola-ai/ui/components/SchemaEngine/SchemaEngine';
import type { ComboboxNode, ICredentialPayload, NodeApiCallCallback } from '@resola-ai/ui';
import { CatalogNodeIcon } from '@resola-ai/ui';

// Define the Panel Width
const PANEL_WIDTH = rem(400); // Adjust as needed
const PANEL_Z_INDEX = 150;

// --- Side Panel Component ---
interface SidePanelProps {
  opened: boolean;
  onClose: () => void;
}

const getUpdatePath = (fieldName: string) => {
  let updatedPath = '';

  switch (fieldName) {
    case 'action': {
      updatedPath = 'action';
      break;
    }
    case 'trigger': {
      updatedPath = 'trigger';
      break;
    }
    case 'credential': {
      updatedPath = 'settings.credential.id';
      break;
    }
    case 'completedFormStep': {
      updatedPath = 'settings.ui.step';
      break;
    }
    default: {
      updatedPath = `settings.${fieldName}`;
    }
  }
  return updatedPath;
};

const collectOptions = (
  schemaSection: any,
  key: string | undefined,
  icon: any,
  group: string
): ComboboxNode['options'] => {
  const options: ComboboxNode['options'] = [];
  if (key && schemaSection?.[key]?.data?.properties) {
    const properties = schemaSection[key].data.properties;
    Object.keys(properties).forEach(propKey => {
      const item = properties[propKey];
      if (item) {
        options.push({
          value: propKey,
          label: item.displayName,
          description: item.description,
          icon,
          group,
        });
      }
    });
  }
  return options;
};

function RightFormPanel({ opened, onClose }: SidePanelProps) {
  const theme = useMantineTheme();
  const { workspaceId } = useParams();
  const [activeStep, setActiveStep] = useState<number>(0);
  const catalogModalRef = useRef<CatalogModalRef>(null);

  const {
    flow,
    closeCatalog,
    flowActionHandlers,
    currentSelectNodeId,
    nodeIdToOrderNumber,
    currentSelectNodeData,
    handleUpdateDisplayName,
    handleUpdateVirtualNodeByPath,
  } = useFlowBuilderContext();

  const { schema } = useFlowSchema({ flowNodeType: currentSelectNodeData?.type });

  const {
    data,
    isLoading: isCredentialsLoading,
    mutate: mutateCredentials,
  } = useListCredential({
    workspaceId,
  });
  const credentials = useMemo(() => {
    if (!data || !data.data) return [];
    return data.data.filter((credential) => credential.provider === currentSelectNodeData?.type);
  }, [data, currentSelectNodeData?.type]);

  const nodeInfo = useMemo(() => {
    if (!(currentSelectNodeId && flow)) return undefined;
    return {
      ...flowActionHandlers.flowActions.getNodeInfo(flow, currentSelectNodeId),
      isVirtualNode: currentSelectNodeData?.type === FlowNodeType.SubPathNode,
    };
  }, [flow, currentSelectNodeId, flowActionHandlers.flowActions, currentSelectNodeData?.type]);

  const currentNodeData = useMemo(() => {
    if (!currentSelectNodeData) return {};
    const itemObject = nodeInfo?.isTriggerNode ? flow?.triggers : flow?.nodes;

    if (nodeInfo?.isVirtualNode) {
      const parentNodeId = currentSelectNodeData?.parentNodeId ?? '';
      const parentNode = get(itemObject, parentNodeId);
      const paths = get(parentNode, 'settings.paths', []);
      const pathData = paths.find(path => path.next === currentSelectNodeData?.nodeId);
      return pathData;
    } else {
      const node = get(itemObject, currentSelectNodeData?.nodeId ?? '');
      return node;
    }
  }, [currentSelectNodeData, flow, nodeInfo]);

  const displayName = useMemo(() => {
    if (currentNodeData?.displayName) {
      return currentNodeData?.displayName;
    }
    return schema?.displayName;
  }, [currentNodeData?.displayName, schema?.displayName]);

  const formValues = useMemo(() => {
    if (!currentNodeData) return {};

    const formData = {};

    const action = get(currentNodeData, 'action');
    const trigger = get(currentNodeData, 'trigger');
    const settings = get(currentNodeData, 'settings', {});
    const credentialId = get(settings, 'credential.id');

    set(formData, 'settings', settings);
    if (credentialId) {
      set(formData, 'settings.credential', credentialId);
    }
    if (action) {
      set(formData, 'action', action);
    }
    if (trigger) {
      set(formData, 'trigger', trigger);
    }
    return formData;
  }, [currentNodeData]) as SchemaFormInput;

  const completedFormStep = useMemo(() => {
    if (!currentNodeData) return -1;
    return get(currentNodeData, 'settings.ui.step', -1);
  }, [currentNodeData]);

  const previousNodes = useMemo(() => {
    if (!(currentSelectNodeId && flow)) {
      return [];
    }

    return flowActionHandlers?.flowActions
      .getPreviousNodes(
        flow,
        currentSelectNodeData?.type === FlowNodeType.SubPathNode
          ? (currentSelectNodeData?.parentNodeId ?? '')
          : (currentSelectNodeData?.nodeId ?? '')
      )
      .sort((a, b) => {
        return nodeIdToOrderNumber[a.id] - nodeIdToOrderNumber[b.id];
      })
      .reduce<ComboboxNode[]>((acc, node) => {
        let name = node.name;
        if (name === FlowNodeType.WebhookTrigger) {
          name = FlowNodeType.NewTrigger;
        }

        const nodeSchema = NodeNameMapNode?.[name || ''];
        if (!nodeSchema) {
          return acc;
        }

        const schema = resolveRefs(nodeSchema, nodeSchema);
        const nodeName = node?.displayName || node?.name || '';
        const orderNumber = nodeIdToOrderNumber?.[node?.id]
          ? `${nodeIdToOrderNumber?.[node?.id]}. `
          : '';

        const trigger = get(node, 'trigger') as string;
        const action = get(node, 'action') as string;
        let options: ComboboxNode['options'] = [];
        options = [
          ...collectOptions(schema.triggers, trigger, schema.icon, 'Triggers'),
          ...collectOptions(schema.actions, action, schema.icon, 'Actions'),
        ];

        return [
          ...acc,
          {
            label: `${orderNumber}${nodeName}`,
            icon: schema.icon,
            options,
          },
        ];
      }, []);
  }, [
    flow,
    nodeIdToOrderNumber,
    currentSelectNodeId,
    currentSelectNodeData,
    flowActionHandlers?.flowActions,
  ]);

  const createCredential = useCallback(
    async (credential: ICredentialPayload) => {
      if (!workspaceId) {
        return null;
      }

      try {
        const response = await CredentialAPI.create(workspaceId, credential);
        mutateCredentials();
        return response;
      } catch (error) {
        console.error(error);
        return null;
      }
    },
    [workspaceId, mutateCredentials]
  );

  const handleOpenAppCatalog = useCallback(() => {
    if (!nodeInfo) return;
    if (nodeInfo?.isTriggerNode) {
      catalogModalRef.current?.openWithEnabledOptions(TriggerNodes as NodeNameTypes[]);
    } else {
      catalogModalRef.current?.openWithEnabledOptions([
        ...LoopingNodes,
        ...ActionNodes,
      ] as NodeNameTypes[]);
    }
  }, [nodeInfo]);

  const handleOnSelectCatalog = useCallback(
    (item: { id: string; name: string; icon: string; displayName: string }) => {
      if (!currentSelectNodeId) return;

      setActiveStep(0);
      flowActionHandlers.handleUpdateNode(currentSelectNodeId, {
        name: item.name as FlowNodeType,
        displayName: item.displayName,
        icon: item.icon,
      });
    },
    [currentSelectNodeId, flowActionHandlers.handleUpdateNode]
  );

  const handleFormChange = useCallback(
    debounce((formValues: SchemaFormInput, fieldName: string) => {
      if (!currentSelectNodeId) return;

      const value = formValues[fieldName];
      const updatedPath = getUpdatePath(fieldName);

      if (nodeInfo?.isVirtualNode) {
        handleUpdateVirtualNodeByPath(currentSelectNodeId, updatedPath, value);
        return;
      }

      const schemaField = `${fieldName}s`;
      const paths = [{ path: updatedPath, value }];
      if (FlowDiagram.shouldUpdateNodeName(currentNodeData, schema)) {
        const displayName = schema[schemaField]?.[value]?.displayName;
        ['action', 'trigger'].includes(fieldName) &&
          paths.push({ path: 'displayName', value: displayName });
      }

      if (nodeInfo?.isTriggerNode) {
        flowActionHandlers.handleUpdateTriggerByPaths(currentSelectNodeId, paths);
      } else {
        flowActionHandlers.handleUpdateNodeByPaths(currentSelectNodeId, paths);
      }
    }, 200),
    [
      schema,
      nodeInfo,
      currentSelectNodeId,
      handleUpdateVirtualNodeByPath,
      flowActionHandlers.handleUpdateNodeByPaths,
      flowActionHandlers.handleUpdateTriggerByPaths,
    ]
  );

  const handleSaveDisplayName = useCallback(
    (displayName: string) => {
      if (!currentSelectNodeId) return;
      handleUpdateDisplayName(currentSelectNodeId, displayName);
    },
    [currentSelectNodeId, handleUpdateDisplayName]
  );

  // TODO: Implement the actual API call
  const handleNodeApiCall: NodeApiCallCallback = useCallback(async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (schema.name === 'webhook') {
      return {
        data: {
          url: 'https://hooks.myservice.com/webhooks/incoming/82cf97ae-3b9e-4e77-9405-1d12e3d21fb6',
          apiKey:
            '********************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        },
      };
    }
    return {
      data: {},
    };
  }, [schema.name]);

  useLayoutEffect(() => {
    setActiveStep(0);
  }, [currentSelectNodeId]);

  return (
    <>
      <Transition mounted={opened} transition='slide-left' duration={250} timingFunction='ease'>
        {styles => (
          <Paper
            shadow='lg' // Add shadow for separation
            radius={'md'} // No radius for edge-to-edge panel
            style={{
              ...styles, // Apply transition styles
              top: `calc(var(--app-shell-header-height) + ${rem(56)})`,
              right: rem(10),
              bottom: rem(10),
              zIndex: PANEL_Z_INDEX,
              display: 'flex',
              position: 'fixed',
              width: PANEL_WIDTH,
              flexDirection: 'column',
              borderLeft: `1px solid ${theme.colors.gray[3]}`,
            }}>
            {/* Panel Header */}
            <Group
              justify='space-between'
              p='md'
              style={{
                borderBottom: `1px solid ${theme.colors.gray[3]}`,
              }}>
              <Group gap='xs' flex={1}>
                <ActionIcon size='md' variant='transparent'>
                  <CatalogNodeIcon name={schema?.name} size={20} />
                </ActionIcon>
                <NodeName
                  orderedNumber={currentSelectNodeData?.orderNumber ?? 0}
                  displayName={displayName ?? ''}
                  onSave={handleSaveDisplayName}
                />
              </Group>
              <ActionIcon variant='subtle' color='gray' onClick={onClose} aria-label='Close panel'>
                <IconX size={20} />
              </ActionIcon>
            </Group>

            <Box p='md' sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <SchemaEngineWithTolgee
                schema={schema}
                form={formValues}
                onClose={onClose}
                activeStep={activeStep}
                key={currentSelectNodeId}
                credentials={credentials}
                onStepChange={setActiveStep}
                previousNodes={previousNodes}
                onFormChange={handleFormChange}
                completedStep={completedFormStep}
                createCredential={createCredential}
                onOpenAppCatalog={handleOpenAppCatalog}
                isCredentialsLoading={isCredentialsLoading}
                nodeContext={nodeInfo?.isTriggerNode ? TRIGGER_FIELD : ACTION_FIELD}
                onNodeApiCall={handleNodeApiCall}
              />
            </Box>
          </Paper>
        )}
      </Transition>

      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default RightFormPanel;
