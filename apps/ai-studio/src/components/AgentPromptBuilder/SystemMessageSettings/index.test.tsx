import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

import { MESSAGE_ROLES } from '@/constants/agent';
import { renderWithMantine } from '@/utils/test';
import SystemMessageSettings, { SystemMessageItem } from './index';

// Mock useTranslate
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'systemMessageSettings.title': 'System Message Settings',
        'systemMessageSettings.description': 'Configure system messages',
        'systemMessageSettings.message.placeholder': 'Enter message content',
        'systemMessageSettings.message.user': 'User',
        'systemMessageSettings.message.assistant': 'Assistant',
        'systemMessageSettings.message.system': 'System',
      };
      return translations[key] || key;
    },
  }),
}));

describe('SystemMessageSettings', () => {
  const mockMessages = [
    { role: MESSAGE_ROLES.USER, content: 'Test message 1' },
    { role: MESSAGE_ROLES.ASSISTANT, content: 'Test message 2' },
  ];

  const onMessagesChangeMock = vi.fn();
  const user = userEvent.setup();

  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with provided messages', () => {
    renderWithMantine(
      <SystemMessageSettings messages={mockMessages} onMessagesChange={onMessagesChangeMock} />
    );

    expect(screen.getByText('System Message Settings')).toBeInTheDocument();
    expect(screen.getAllByTestId(/system-message-item-\d+/)).toHaveLength(2);

    // Check content of messages
    const textareas = screen.getAllByTestId(/system-message-input-\d+/);
    expect(textareas[0]).toHaveValue('Test message 1');
    expect(textareas[1]).toHaveValue('Test message 2');
  });

  it('renders correctly with empty messages array', () => {
    renderWithMantine(
      <SystemMessageSettings messages={[]} onMessagesChange={onMessagesChangeMock} />
    );

    expect(screen.getByText('System Message Settings')).toBeInTheDocument();
    expect(screen.queryAllByTestId(/system-message-item-\d+/)).toHaveLength(0);
  });

  it('adds a new message when plus button is clicked', async () => {
    renderWithMantine(
      <SystemMessageSettings messages={mockMessages} onMessagesChange={onMessagesChangeMock} />
    );

    const addButton = screen.getByTestId('system-message-add-button');
    await user.click(addButton);

    // Check that a new message item was added
    expect(screen.getAllByTestId(/system-message-item-\d+/)).toHaveLength(3);
  });

  it('removes a message when trash icon is clicked', async () => {
    renderWithMantine(
      <SystemMessageSettings messages={mockMessages} onMessagesChange={onMessagesChangeMock} />
    );

    // Find the trash icon buttons (using role since we don't have a specific testid)
    const trashButtons = screen.getAllByRole('button', { name: '' });
    // Skip the first button which is the add button
    await user.click(trashButtons[1]);

    // Check that a message was removed
    expect(screen.getAllByTestId(/system-message-item-\d+/)).toHaveLength(1);
  });

  it('allows editing message content', async () => {
    renderWithMantine(
      <SystemMessageSettings messages={mockMessages} onMessagesChange={onMessagesChangeMock} />
    );

    const textareas = screen.getAllByTestId(/system-message-input-\d+/);
    await user.clear(textareas[0]);
    await user.type(textareas[0], 'Updated message');

    expect(textareas[0]).toHaveValue('Updated message');
  });

  it('allows changing message role', async () => {
    renderWithMantine(
      <SystemMessageSettings messages={mockMessages} onMessagesChange={onMessagesChangeMock} />
    );

    const roleSelect = screen.getByTestId('system-message-role-select-0');
    await user.click(roleSelect);

    // Find and click the assistant option
    const assistantOption = screen.getAllByText('Assistant')[0];
    await user.click(assistantOption);

    // Verify that onMessagesChange was called with the updated message
    expect(onMessagesChangeMock).toHaveBeenCalledWith([
      { role: MESSAGE_ROLES.ASSISTANT, content: 'Test message 1' },
      { role: MESSAGE_ROLES.ASSISTANT, content: 'Test message 2' },
    ]);
  });
});

describe('SystemMessageItem', () => {
  const mockMessage = { role: MESSAGE_ROLES.USER, content: 'Test message' };
  const onRemoveMock = vi.fn();
  const onMessageChangeMock = vi.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with provided message data', () => {
    renderWithMantine(
      <SystemMessageItem
        index={0}
        message={mockMessage}
        onRemove={onRemoveMock}
        onMessageChange={onMessageChangeMock}
      />
    );

    expect(screen.getByTestId('system-message-item-0')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test message')).toBeInTheDocument();
    const roleSelect = screen.getByTestId('system-message-role-select-0');
    expect(roleSelect).toBeInTheDocument();
  });

  it('calls onRemove with correct index when trash icon is clicked', async () => {
    renderWithMantine(
      <SystemMessageItem
        index={2}
        message={mockMessage}
        onRemove={onRemoveMock}
        onMessageChange={onMessageChangeMock}
      />
    );

    const removeButton = screen.getByTestId('system-message-remove-button-2');
    await user.click(removeButton);

    expect(onRemoveMock).toHaveBeenCalledWith(2);
  });

  it('updates local state when editing message text', async () => {
    renderWithMantine(
      <SystemMessageItem
        index={0}
        message={mockMessage}
        onRemove={onRemoveMock}
        onMessageChange={onMessageChangeMock}
      />
    );

    const textarea = screen.getByPlaceholderText('Enter message content');
    await user.clear(textarea);
    await user.type(textarea, 'New message content');
    expect(textarea).toHaveValue('New message content');
  });

  it('updates local state when changing role', async () => {
    renderWithMantine(
      <SystemMessageItem
        index={0}
        message={mockMessage}
        onRemove={onRemoveMock}
        onMessageChange={onMessageChangeMock}
      />
    );

    const roleSelect = screen.getByTestId('system-message-role-select-0');
    await user.click(roleSelect);

    // Find and click the assistant option
    const assistantOption = screen.getByText('Assistant');
    await user.click(assistantOption);

    // Verify that onMessageChange was called with the updated message
    expect(onMessageChangeMock).toHaveBeenCalledWith(0, {
      role: MESSAGE_ROLES.ASSISTANT,
      content: 'Test message',
    });
  });

  it('calls onMessageChange with correct parameters when content changes', async () => {
    renderWithMantine(
      <SystemMessageItem
        index={0}
        message={mockMessage}
        onRemove={onRemoveMock}
        onMessageChange={onMessageChangeMock}
      />
    );

    const textarea = screen.getByTestId('system-message-input-0');
    await user.clear(textarea);
    await user.type(textarea, 'Updated content');

    // Verify that onMessageChange was called with correct index and updated message
    expect(onMessageChangeMock).toHaveBeenCalledWith(0, {
      role: MESSAGE_ROLES.USER,
      content: 'Updated content',
    });
  });

  it('covers fallback branch in handleRoleChange with null', () => {
    const localMessage = { role: 'assistant', content: 'Test' };

    const value = null;
    const updated = {
      ...localMessage,
      role: value ?? MESSAGE_ROLES.USER,
    };

    expect(updated).toEqual({
      role: MESSAGE_ROLES.USER,
      content: 'Test',
    });
  });
});
