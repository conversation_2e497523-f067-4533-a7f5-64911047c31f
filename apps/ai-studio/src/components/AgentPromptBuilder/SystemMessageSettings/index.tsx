import { MESSAGE_ROLES, MESSAGE_ROLES_OPTIONS } from '@/constants/agent';
import type { SystemMessage } from '@/types/model';
import { ActionIcon, Box, Flex, rem, Select, Stack, Text, Textarea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface SystemMessageSettingsProps {
  messages: SystemMessage[];
  onMessagesChange: (messages: SystemMessage[]) => void;
}

interface SystemMessageItemProps {
  index: number;
  message: SystemMessage;
  onRemove: (index: number) => void;
  onMessageChange: (index: number, message: SystemMessage) => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    padding: theme.spacing.md,
    position: 'relative',
    height: '100%',
  },
  item: {
    padding: theme.spacing.md,
    border: `1px solid ${theme.colors.decaLight[3]}`,
    borderRadius: rem(8),
  },
  input: {
    padding: 0,
    minHeight: rem(22),
  },
  stickyHeader: {
    position: 'sticky',
    top: 0,
    backgroundColor: theme.white,
    zIndex: 10,
    paddingBottom: rem(12),
  },
}));

export const SystemMessageItem = ({
  index,
  message,
  onRemove,
  onMessageChange,
}: SystemMessageItemProps) => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();
  const [localMessage, setLocalMessage] = useState(message);

  useEffect(() => {
    setLocalMessage(message);
  }, [message]);

  const handleRoleChange = (value: string | null) => {
    const updated = { ...localMessage, role: value ?? MESSAGE_ROLES.USER };
    setLocalMessage(updated);
    onMessageChange(index, updated);
  };

  return (
    <Stack gap={rem(10)} className={classes.item} data-testid={`system-message-item-${index}`}>
      <Flex justify='space-between' align='center'>
        <Select
          data-testid={`system-message-role-select-${index}`}
          miw={rem(180)}
          data={MESSAGE_ROLES_OPTIONS.map((option) => ({
            ...option,
            label: t(`${option.label}`),
          }))}
          value={localMessage.role}
          defaultValue={MESSAGE_ROLES.USER}
          comboboxProps={{ position: 'right-start' }}
          onChange={handleRoleChange}
        />
        <ActionIcon
          variant='transparent'
          size={17}
          c='decaGrey.6'
          onClick={() => onRemove(index)}
          data-testid={`system-message-remove-button-${index}`}
        >
          <IconTrash />
        </ActionIcon>
      </Flex>
      <Textarea
        data-testid={`system-message-input-${index}`}
        classNames={{
          input: classes.input,
        }}
        autosize
        size='md'
        variant='unstyled'
        value={localMessage.content}
        placeholder={t('systemMessageSettings.message.placeholder')}
        onChange={(e) => {
          setLocalMessage({ ...localMessage, content: e.target.value });
          onMessageChange(index, { ...localMessage, content: e.target.value });
        }}
      />
    </Stack>
  );
};

const SystemMessageSettings = ({
  messages: initialMessages,
  onMessagesChange,
}: SystemMessageSettingsProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate('common');
  const [messages, setMessages] = useState<SystemMessage[]>(initialMessages);
  const lastMessageRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    setMessages(initialMessages);
  }, [initialMessages]);

  useEffect(() => {
    if (lastMessageRef?.current) {
      lastMessageRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length]);

  const handleMessagesChange = useCallback(
    (messages: SystemMessage[]) => {
      setMessages(messages);
      onMessagesChange(messages);
    },
    [onMessagesChange]
  );

  const handleAddMessage = useCallback(() => {
    const newMessage = { role: MESSAGE_ROLES.USER, content: '' };
    handleMessagesChange([...messages, newMessage]);
  }, [messages, handleMessagesChange]);

  const handleRemoveMessage = useCallback(
    (index: number) => {
      const newMessages = messages.filter((_, i) => i !== index);
      handleMessagesChange(newMessages);
    },
    [messages, handleMessagesChange]
  );

  const handleMessageChange = useCallback(
    (index: number, message: SystemMessage) => {
      const newMessages = messages.map((m, i) => (i === index ? message : m));
      handleMessagesChange(newMessages);
    },
    [messages, handleMessagesChange]
  );

  return (
    <Box className={classes.root}>
      <Stack gap={rem(12)}>
        <Box className={classes.stickyHeader}>
          <Flex justify='space-between' align='center'>
            <Text size='xl' fw={500} c='decaGrey.9'>
              {t('systemMessageSettings.title')}
            </Text>
            <ActionIcon
              variant='transparent'
              size={22}
              c='decaGrey.6'
              onClick={handleAddMessage}
              data-testid='system-message-add-button'
            >
              <IconPlus />
            </ActionIcon>
          </Flex>
        </Box>
        <Stack>
          {messages.map((message, index) => (
            <Box
              key={`${message.role}-${index}`}
              ref={index === messages.length - 1 ? lastMessageRef : null}
            >
              <SystemMessageItem
                index={index}
                message={message}
                onRemove={handleRemoveMessage}
                onMessageChange={handleMessageChange}
              />
            </Box>
          ))}
        </Stack>
      </Stack>
    </Box>
  );
};

export default SystemMessageSettings;
