import AICard from '@/components/AICard';
import GridLayout from '@/components/GridLayout';
import { EditModal } from '@/components/Modals';
import { useAppContext } from '@/contexts/AppContext';
import { useFlowListContext } from '@/contexts/FlowListContext';
import { timeAgo } from '@/helpers/timeHelper';
import type { Flow } from '@/models/flow';
import { createTheme, MantineProvider, Switch, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { ModelIcons } from './ModelIcons';

interface FlowListProps {
  isFullWidth?: boolean;
}

const FlowListComponent = ({ isFullWidth = false }: FlowListProps) => {
  const { t } = useTranslate('flow');
  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const [searchParams] = useSearchParams();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const {
    flowList,
    handleEditFLow,
    openedEditModal,
    handleDeleteFlow,
    setOpenedEditModal,
    handleDuplicateFlow,
  } = useFlowListContext();
  const [editFlow, setEditFlow] = useState<Flow>();
  const lang = searchParams.get('lang') ?? 'ja';

  const onEdit = useCallback(
    (flow: Flow) => {
      setEditFlow(flow);
      setOpenedEditModal(true);
    },
    [setOpenedEditModal]
  );

  const handleCloseEditModal = useCallback(() => {
    setEditFlow(undefined);
    setOpenedEditModal(false);
  }, [setOpenedEditModal]);

  const onExport = useCallback(() => {
    console.log('onExport');
  }, []);

  const onDuplicate = useCallback(
    (flow: Flow) => {
      handleDuplicateFlow?.(flow);
    },
    [handleDuplicateFlow]
  );

  const onDelete = useCallback(
    (flow: Flow) => {
      openConfirmModal({
        title: t('deleteModalTitle'),
        name: flow?.name,
        onConfirm: async () => {
          closeConfirmModal();
          await handleDeleteFlow?.(flow);
        },
        onCancel: () => {
          closeConfirmModal();
        },
      });
    },
    [openConfirmModal, closeConfirmModal, handleDeleteFlow, t]
  );

  const handleChangeStatus = useCallback(
    (flow: Flow) => {
      if (flow?.status === 'enabled') {
        openConfirmModal({
          title: t('disableFlow'),
          content: (
            <Text
              fz={16}
              fw={400}
              dangerouslySetInnerHTML={{
                __html: t('disableFlowDescription'),
              }}
            />
          ),
          confirmText: t('disableFlowConfirm'),
          enableCheckbox: false,
          isRemoving: false,
          confirmVariant: 'primary',
          onConfirm: async () => {
            const newFlow = {
              ...flow,
              status: 'disabled',
            };
            handleEditFLow?.(newFlow as Flow);
            closeConfirmModal();
          },
          onCancel: () => {
            closeConfirmModal();
          },
        });
      } else {
        const newFlow = {
          ...flow,
          status: 'enabled',
        };
        handleEditFLow?.(newFlow as Flow);
      }
    },
    [handleEditFLow, openConfirmModal, closeConfirmModal, t]
  );

  const theme = createTheme({
    cursorType: 'pointer',
  });

  return (
    <>
      <GridLayout isFullWidth={isFullWidth}>
        {flowList.map((flow) => {
          return (
            <AICard
              {...flow}
              key={flow?.id ?? ''}
              title={flow?.name}
              updatedAt={undefined}
              isFullWidth={isFullWidth}
              actions={{
                onEdit: () => onEdit(flow),
                onDelete: () => onDelete(flow),
                onExport: () => onExport(),
                onDuplicate: () => onDuplicate(flow),
              }}
              switchButton={
                <MantineProvider theme={theme}>
                  <Switch
                    checked={flow?.status === 'enabled'}
                    color='lime'
                    onChange={() => handleChangeStatus(flow)}
                  />
                </MantineProvider>
              }
              modelIcons={<ModelIcons flowModels={flow?.metadata?.nodeNames} />}
              updateTime={
                <Text c='gray.6' fz={14} fw={400}>
                  {t('updateAtLabel', { time: timeAgo(flow?.updatedAt, lang) })}
                </Text>
              }
              onClick={() => {
                navigate(`/studio/${workspaceId}/flows/${flow?.id}`);
              }}
            />
          );
        })}
      </GridLayout>
      <EditModal
        opened={openedEditModal}
        data-testid='edit-modal'
        title={t('editModalTitle')}
        onCancel={() => handleCloseEditModal()}
        onConfirm={async (data) => {
          handleCloseEditModal();
          if (editFlow?.id) {
            await handleEditFLow?.({
              ...editFlow,
              name: data.title,
              description: data.description!,
            });
          }
        }}
        options={{
          titleLabel: t('editModalFlowTitle'),
          descriptionLabel: t('editModalFlowDescription'),
          titleMaxLength: 40,
          descriptionMaxLength: 200,
        }}
        initialValues={{
          title: editFlow?.name ?? '',
          description: editFlow?.description ?? '',
        }}
      />
    </>
  );
};

export default FlowListComponent;
