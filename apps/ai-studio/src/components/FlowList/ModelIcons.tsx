import { Avatar } from '@mantine/core';
import { CatalogNodeIcon, iconMapper, NodeNameMapNode } from '@resola-ai/ui';

const NUMBER_OF_ICONS_DISPLAYED = 3;

// Helper function to get the emoji icon for a model name
const getModelIcon = (modelName: string): string => {
  const nodeData = NodeNameMapNode[modelName];
  return nodeData?.icon || modelName;
};

export const ModelIcons = ({ flowModels }: { flowModels?: string[] }) => {
  if (!flowModels) return;

  const modelIcons = flowModels.filter((model) => iconMapper[model]);

  if (modelIcons?.length > NUMBER_OF_ICONS_DISPLAYED)
    return [
      ...modelIcons.slice(0, NUMBER_OF_ICONS_DISPLAYED - 1).map((model) => {
        return model ? (
          <Avatar
            size='md'
            radius='xl'
            key={model}
            alt={model}
            data-testid={`aic-model-icon-${getModelIcon(model)}`}
          >
            <CatalogNodeIcon name={model} size={20} />
          </Avatar>
        ) : null;
      }),
      <Avatar
        fz={16}
        fw={500}
        c='dark'
        size='md'
        radius='xl'
        key={'addition-number'}
        data-testid='aic-model-icon-addition-number'
      >
        +{modelIcons.length - NUMBER_OF_ICONS_DISPLAYED + 1}
      </Avatar>,
    ];
  return modelIcons?.map((model) => {
    return model ? (
      <Avatar
        size='md'
        radius='xl'
        key={model}
        alt={model}
        data-testid={`aic-few-model-icon-${getModelIcon(model)}`}
      >
        <CatalogNodeIcon name={model} size={20} />
      </Avatar>
    ) : null;
  });
};
