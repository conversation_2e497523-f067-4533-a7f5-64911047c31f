import {
  Flex,
  Group,
  type ModalOverlayProps,
  Text,
  rem,
  Input,
  type ModalProps,
} from '@mantine/core';
import { DecaButton, DecaCheckbox } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import Modal from './Modal';
import { useModalStyles } from './useModalStyles';
import DOMPurify from 'dompurify';

export interface ConfirmModalOptions {
  isRemoving?: boolean;
  isShowCancel?: boolean;
  className?: string;
  modalSize?: string;
  zIndex?: number;
  overlayProps?: ModalOverlayProps;
}

export interface ReEnter {
  value: string;
  text?: string;
  placeholder?: string;
  validate?: (value: string) => string | undefined;
}

export interface ConfirmModalProps {
  opened: boolean;
  title?: string;
  content?: string | React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  onConfirm: (value: string) => Promise<void>;
  onCancel?: () => void;
  options?: ConfirmModalOptions;
  reEnter?: ReEnter;
  isRemoving?: boolean;
  name?: string;
  enableCheckbox?: boolean;
  confirmVariant?: 'primary' | 'secondary' | 'negative';
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  opened,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  options,
  reEnter,
  name = '',
  isRemoving = true,
  enableCheckbox = true,
  confirmVariant = undefined,
}) => {
  const { t } = useTranslate('home');
  const { cx, classes } = useModalStyles({ size: 'md' } as ModalProps);
  const [value, setValue] = useState('');
  const [error, setError] = useState('');
  const [checked, setChecked] = useState(false);
  const {
    isShowCancel = true,
    className,
    modalSize = 'md',
    zIndex = 1000,
    overlayProps = {
      blur: 0,
      opacity: 0.5,
    },
  } = options || {};

  const handleConfirm = async () => {
    if (reEnter) {
      let errorMessage: string | undefined;
      if (reEnter.validate) {
        errorMessage = reEnter.validate(value);
      } else if (reEnter.value !== value) {
        errorMessage = t('reEnterError');
      }

      if (errorMessage) {
        setError(errorMessage);
        return;
      }
    }
    await onConfirm(value);
    setError('');
    setValue('');
  };

  const handleCancel = () => {
    setError('');
    setValue('');
    setChecked(false);
    onCancel?.();
  };

  useEffect(() => {
    if (!opened) {
      setChecked(false);
    }
    return () => {
      setChecked?.(false);
    };
  }, [opened]);

  return (
    <Modal
      opened={opened}
      onClose={handleCancel}
      size={modalSize}
      className={cx(className, classes.confirmModal)}
      zIndex={zIndex}
      title={title}
      overlayProps={overlayProps}
    >
      <Flex direction={'column'} justify={'center'} gap={rem(5)}>
        {content ? (
          <Text className={classes.content} data-testid='content-text-confirm-modal'>
            {content}
          </Text>
        ) : (
          <Text
            className={classes.content}
            data-testid='content-text-confirm-modal'
            style={{ whiteSpace: 'break-spaces' }}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(t('action.deleteDescription', { name: name })),
            }}
          />
        )}
        {reEnter && (
          <Flex direction={'column'} gap={rem(5)}>
            <Text className={classes.preEnterText}>
              {reEnter.text ?? t('reEnter.title', { value: reEnter.value })}
            </Text>
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder={
                reEnter.placeholder ?? `${t('reEnter.description', { value: reEnter.value })}`
              }
              radius={'sm'}
              data-testid='re-enter-input-confirm-modal'
              error={error}
            />
            {error && (
              <Text
                className={classes.error}
                style={{ whiteSpace: 'break-spaces' }}
                data-testid='error-text-confirm-modal'
              >
                {error}
              </Text>
            )}
          </Flex>
        )}
        {enableCheckbox && (
          <Group gap={rem(10)} justify={'flex-start'} mt={'xs'}>
            <DecaCheckbox
              checked={checked}
              data-testid='checkbox-confirm-modal'
              onChange={() => setChecked(!checked)}
              label={t('action.confirmDelete')}
              style={{ cursor: 'pointer' }}
            />
          </Group>
        )}
      </Flex>
      <Group gap={rem(10)} justify={'flex-end'} mt={rem(16)}>
        {isShowCancel && (
          <DecaButton
            data-testid='cancel-confirm-modal'
            size='md'
            className={classes.button}
            variant='neutral'
            onClick={handleCancel}
            radius={'sm'}
          >
            {cancelText ?? t('common.button.cancel')}
          </DecaButton>
        )}
        <DecaButton
          data-testid='confirm-modal'
          size='md'
          className={classes.button}
          variant={confirmVariant || (isRemoving ? 'negative' : 'secondary')}
          onClick={handleConfirm}
          disabled={enableCheckbox && !checked}
          radius='sm'
        >
          {confirmText ?? t('common.button.delete')}
        </DecaButton>
      </Group>
    </Modal>
  );
};

export default ConfirmModal;
