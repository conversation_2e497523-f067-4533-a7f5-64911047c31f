import { vi, describe, it, expect, beforeEach } from 'vitest';
import { showSuccessNotification, showErrorNotification } from './index';
import { hideNotification, showNotification } from '@mantine/notifications';
import { ulid } from 'ulid';
import type { ReactElement } from 'react';
import { rem } from '@mantine/core';

// Mock ulid to return a consistent id for testing
vi.mock('ulid', () => ({
  ulid: vi.fn().mockReturnValue('test-id'),
}));

// Mock mantine notifications
vi.mock('@mantine/notifications', () => ({
  showNotification: vi.fn(),
  hideNotification: vi.fn(),
}));

describe('Notification Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('showSuccessNotification', () => {
    it('should call showNotification with correct parameters', () => {
      const testMessage = 'Test success message';

      showSuccessNotification(testMessage);

      // Check if showNotification was called
      expect(showNotification).toHaveBeenCalledTimes(1);

      // Get the first argument passed to showNotification
      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Check id format
      expect(notificationConfig.id).toBe(`success-notification${ulid()}`);

      // Check other configuration settings
      expect(notificationConfig.color).toBe('green');
      expect(notificationConfig.withCloseButton).toBe(false);
      expect(notificationConfig.icon).toBeDefined();
      expect(notificationConfig.autoClose).toBe(3000);
      expect(notificationConfig.styles).toBeDefined();
    });

    it('should include a close button that calls hideNotification when clicked', () => {
      const testMessage = 'Test success message';

      showSuccessNotification(testMessage);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Extract the message component and ensure it's a React element
      const messageComponent = notificationConfig.message as ReactElement;
      expect(messageComponent).toBeDefined();

      // Verify messageComponent has the expected structure
      expect(messageComponent.props).toBeDefined();
      expect(messageComponent.props.children).toBeDefined();
      expect(Array.isArray(messageComponent.props.children)).toBe(true);
      expect(messageComponent.props.children.length).toBeGreaterThanOrEqual(2);

      // Find the action icon in the message component's props
      const actionIcon = messageComponent.props.children[1];

      // Verify the action icon is defined and has an onClick handler
      expect(actionIcon).toBeDefined();
      expect(actionIcon.props).toBeDefined();
      expect(actionIcon.props.onClick).toBeDefined();
      expect(typeof actionIcon.props.onClick).toBe('function');

      // Simulate clicking the action icon
      actionIcon.props.onClick();

      // Check that hideNotification was called with the correct ID
      expect(hideNotification).toHaveBeenCalledTimes(1);
      expect(hideNotification).toHaveBeenCalledWith(`success-notification${ulid()}`);
    });

    // check styles
    it('should have the correct styles configuration', () => {
      const testMessage = 'Test success message';

      showSuccessNotification(testMessage);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Check if the styles property is defined
      expect(notificationConfig.styles).toBeDefined();

      // Check if the styles is a function
      expect(typeof notificationConfig.styles).toBe('function');

      // Create a minimal mock theme with just the properties needed
      // Using any type to avoid complex MantineTheme type issues
      const mockTheme = {
        spacing: { xs: '8px', sm: '16px' },
        colors: {
          decaGreen: [
            '#e0f2e9',
            '#c1e5d9',
            '#a2d8c9',
            '#83cbb9',
            '#64bea9',
            '#45b199',
            '#26a489',
            '#078979',
            '#007969',
            '#006859',
          ],
        },
        rem: (size: number) => `${size}px`,
      } as any;

      // Ensure styles is defined and can be called
      if (notificationConfig.styles && typeof notificationConfig.styles === 'function') {
        // Use explicit type casting to fix type issues
        const stylesFn = notificationConfig.styles as (theme: any) => any;
        const styles = stylesFn(mockTheme);

        // Ensure we got the expected object structure
        expect(styles).toHaveProperty('root');

        const { root } = styles;

        // Test specific style properties
        expect(root.borderRadius).toBe(rem(16));
        expect(root.padding).toBe('8px 16px');
        expect(root.top).toBe(rem(100));
        expect(root.float).toBe('right');
        expect(root.backgroundColor).toBe('#e0f2e9');

        // Test nested selectors
        expect(root['.mantine-Notification-icon']).toHaveProperty('backgroundColor', 'unset');
        expect(root.svg).toHaveProperty('stroke', '#006859');
      } else {
        // Fail the test if styles is not a function
        expect.fail('styles should be a function');
      }
    });

    it('should handle ReactNode message types', () => {
      const testMessage = <span>Test JSX message</span>;

      showSuccessNotification(testMessage);

      // Check if showNotification was called
      expect(showNotification).toHaveBeenCalledTimes(1);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];
      expect(notificationConfig.id).toBe(`success-notification${ulid()}`);
    });
  });

  describe('showErrorNotification', () => {
    it('should call showNotification with correct parameters', () => {
      const testMessage = 'Test error message';

      showErrorNotification(testMessage);

      // Check if showNotification was called
      expect(showNotification).toHaveBeenCalledTimes(1);

      // Get the first argument passed to showNotification
      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Check id format
      expect(notificationConfig.id).toBe(`error-notification${ulid()}`);

      // Check other configuration settings
      expect(notificationConfig.color).toBe('red');
      expect(notificationConfig.withCloseButton).toBe(false);
      expect(notificationConfig.icon).toBeDefined();
      expect(notificationConfig.autoClose).toBe(4000);
      expect(notificationConfig.styles).toBeDefined();
    });

    it('should include a close button that calls hideNotification when clicked', () => {
      const testMessage = 'Test error message';

      showErrorNotification(testMessage);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Extract the message component and ensure it's a React element
      const messageComponent = notificationConfig.message as ReactElement;
      expect(messageComponent).toBeDefined();

      // Verify messageComponent has the expected structure
      expect(messageComponent.props).toBeDefined();
      expect(messageComponent.props.children).toBeDefined();
      expect(Array.isArray(messageComponent.props.children)).toBe(true);
      expect(messageComponent.props.children.length).toBeGreaterThanOrEqual(2);

      // Find the action icon in the message component's props
      const actionIcon = messageComponent.props.children[1];

      // Verify the action icon is defined and has an onClick handler
      expect(actionIcon).toBeDefined();
      expect(actionIcon.props).toBeDefined();
      expect(actionIcon.props.onClick).toBeDefined();
      expect(typeof actionIcon.props.onClick).toBe('function');

      // Simulate clicking the action icon
      actionIcon.props.onClick();

      // Check that hideNotification was called with the correct ID
      expect(hideNotification).toHaveBeenCalledTimes(1);
      expect(hideNotification).toHaveBeenCalledWith(`error-notification${ulid()}`);
    });

    it('should have the correct styles configuration', () => {
      const testMessage = 'Test error message';

      showErrorNotification(testMessage);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];

      // Check if the styles property is defined
      expect(notificationConfig.styles).toBeDefined();

      // Check if the styles is a function
      expect(typeof notificationConfig.styles).toBe('function');

      // Create a minimal mock theme with just the properties needed
      const mockTheme = {
        spacing: { xs: '8px', sm: '16px' },
        colors: {
          red: [
            '#ffe0e0',
            '#ffc1c1',
            '#ffa2a2',
            '#ff8383',
            '#ff6464',
            '#ff4545',
            '#ff2626',
            '#ff0707',
            '#e60000',
            '#cc0000',
          ],
        },
        rem: (size: number) => `${size}px`,
      } as any;

      // Ensure styles is defined and can be called
      if (notificationConfig.styles && typeof notificationConfig.styles === 'function') {
        const stylesFn = notificationConfig.styles as (theme: any) => any;
        const styles = stylesFn(mockTheme);

        // Ensure we got the expected object structure
        expect(styles).toHaveProperty('root');

        const { root } = styles;

        // Test specific style properties
        expect(root.borderRadius).toBe(rem(16));
        expect(root.padding).toBe('8px 16px');
        expect(root.top).toBe(rem(100));
        expect(root.float).toBe('right');
        expect(root.backgroundColor).toBe('#ffe0e0');

        // Test nested selectors
        expect(root['.mantine-Notification-icon']).toHaveProperty('backgroundColor', 'unset');
        expect(root.svg).toHaveProperty('stroke', '#cc0000');
      } else {
        // Fail the test if styles is not a function
        expect.fail('styles should be a function');
      }
    });

    it('should handle ReactNode message types', () => {
      const testMessage = <span>Test JSX message</span>;

      showErrorNotification(testMessage);

      // Check if showNotification was called
      expect(showNotification).toHaveBeenCalledTimes(1);

      const notificationConfig = vi.mocked(showNotification).mock.calls[0][0];
      expect(notificationConfig.id).toBe(`error-notification${ulid()}`);
    });
  });
});
