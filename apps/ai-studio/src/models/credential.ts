import { ICredential } from '@resola-ai/ui';

export interface ICredentialSettingsBase {
  [key: string]: any;
}

export interface IFieldConfig {
  name: string;
  displayName: string;
  description: string;
  type: 'text' | 'password' | 'options' | 'boolean' | 'number' | 'keyvalue';
  required?: boolean;
  options?: Array<{ name: string; value: string }>;
  default?: any;
  visibleIf?: Record<string, string[]>;
  pattern?: string;
}

export interface ICredentialConfig {
  name: string;
  displayName: string;
  description: string;
  properties: IFieldConfig[];
}

export interface ICredentialPayload extends Partial<ICredential> {}

export enum CredentialType {
  BASIC_AUTH = 'basic_auth',
  DIGEST_AUTH = 'digest_auth',
  API_KEY = 'api_key',
  BEARER_TOKEN = 'bearer_token',
  OAUTH2 = 'oauth2',
  AWS_SIG_V4 = 'aws_sig_v4',
  NTLM_AUTH = 'ntlm_auth',
  WSSE_AUTH = 'wsse_auth',
}

export enum ProviderType {
  OPENAI = 'openai',
  SLACK = 'slack',
  GOOGLE = 'google',
  AWS = 'aws',
  GITHUB = 'github',
  AZURE = 'azure',
  ANTHROPIC = 'anthropic',
  CUSTOM = 'custom',
}

export interface ITool {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  categories: string[];
  settings: any;
  credentials: any;
}

export interface ICategory {
  id: string;
  name: string;
}
