import { MantineProvider, createTheme } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { Colors } from '@resola-ai/ui/constants';
import { defaultFontName } from '@resola-ai/utils';
import { emotionCache } from './cache';

// Ensure all color arrays have 10 elements as required by Mantine
const fixedColors = {
  ...Colors,
  decaMono: [
    '#000000',
    '#FFFFFF',
    '#000000',
    '#000000',
    '#000000',
    '#000000',
    '#000000',
    '#000000',
    '#000000',
    '#000000',
  ],
  decaGray: [
    '#CCCCCC',
    '#B8B8B8',
    '#A3A3A3',
    '#CED4DA',
    '#7A7A7A',
    '#495057',
    '#5C5C5C',
    '#525252',
    '#474747',
    '#333333',
  ],
};

const themeConfiguration = createTheme({
  fontFamily: defaultFontName,
  headings: { fontFamily: defaultFontName },
  components: {
    Textarea: {
      styles: (theme) => ({
        root: {
          '.mantine-Input-input': {
            '&:focus': {
              borderColor: theme.colors.navy[0],
            },
            '&:focus-within': {
              borderColor: theme.colors.navy[0],
            },
          },
        },
      }),
    },
    Input: {
      styles: (theme) => ({
        root: {
          '.mantine-Input-input': {
            '&:focus': {
              borderColor: theme.colors.violet[5],
            },
            '&:focus-within': {
              borderColor: theme.colors.violet[5],
            },
          },
        },
      }),
    },
    Switch: {
      styles: () => ({
        root: {
          '.mantine-Switch-track': {
            cursor: 'pointer',
          },
        },
      }),
    },
    Checkbox: {
      styles: () => ({
        root: {
          '.mantine-Checkbox-input, .mantine-Checkbox-label': {
            cursor: 'pointer',
          },
        },
      }),
    },
  },
  colors: {
    ...fixedColors,
    navy: [
      '#1D2088',
      '#3539BC', // 1 bit lighter
      '#9ea1f7', // 2 more lighter
      '#b7c4ed', // 3 more lighter
      '#0A66CC',
      '#1D2088',
      '#BBD9FC',
      '#1D2088',
      '#1D2088',
      '#101155',
    ],
    documentDisplayColors: [
      '#ced4da',
      '#d3d8de',
      '#d8dde3',
      '#dde2e8',
      '#e2e7ed',
      '#E1EFFE80',
      '#E4F0FF',
      '#E7F1FF',
      '#EAF2FF',
      '#1F84F4',
    ],
  } as any,
});

const AppMantineEmotionProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <MantineEmotionProvider cache={emotionCache}>
      <MantineProvider
        theme={themeConfiguration}
        stylesTransform={emotionTransform}
        withGlobalClasses
      >
        {children}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

export default AppMantineEmotionProvider;
