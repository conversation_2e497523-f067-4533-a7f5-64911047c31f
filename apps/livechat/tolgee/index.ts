import { Tolgee, FormatSimple } from '@tolgee/react';
import { InContextTools } from '@tolgee/web/tools';

import enCommon from '../locales/common/en.json';
import enWorkspace from '../locales/workspace/en.json';
import jaCommon from '../locales/common/ja.json';
import jaWorkspace from '../locales/workspace/ja.json';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';

// import AppConfig from '@/configs';
import AppConfig from '../configs';

// Assuming the JSON files contain key-value pairs of strings
type Translations = Record<string, string>;

const tolgee = Tolgee()
  .use(AppConfig.TOLGEE_TOOLS_ENABLED ? InContextTools() : undefined)
  .use(FormatSimple())
  // .use(BackendFetch({ prefix: 'CDN_URL' })) // TODO: if use CDN
  .init({
    language: DEFAULT_LANGUAGE,
    fallbackLanguage: FALLBACK_LANGUAGE,
    defaultNs: 'common',
    apiUrl: AppConfig.TOLGEE_URL,
    apiKey: AppConfig.TOLGEE_KEY,

    staticData: {
      'en:common': enCommon as Translations,
      'ja:common': jaCommon as Translations,
      'en:workspace': enWorkspace as Translations,
      'ja:workspace': jaWorkspace as Translations,
    },
    onFormatError: (error) => {
      console.error('translate error', error);
      return error;
    },
  });

export { tolgee };
