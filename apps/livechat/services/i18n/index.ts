import axios from 'axios';
import i18n from 'i18next';
import Backend from 'i18next-http-backend';
import AppConfig from '../../configs';
import { defaultNS, fallbackLng, i18nNamespaces } from '../../i18n/settings';

// import enLocaleCommon from "../public/locales/en/common.json";
// import enLocaleLangauge from "../public/locales/en/language.json";
// import esLocaleCommon from "../public/locales/es/common.json";
// import esLocaleLangauge from "../public/locales/es/language.json";

// const resources = {
//   en: {
//     common: enLocaleCommon,
//     language: enLocaleLangauge
//   },
//   es: {
//     common: esLocaleCommon,
//     language: esLocaleLangauge
//   }
// };

export const DEFAULT_LOCALES = [
  { name: 'English', code: 'en' },
  { name: '日本語', code: 'ja' },
];

const DEFAULT_LANGUAGE = fallbackLng;

export const AVAILABLE_LOCALES = DEFAULT_LOCALES;

const dynamicBackendBundle = (locale, ns) => {
  const publicFolder = AppConfig.IS_PRODUCTION
    ? AppConfig.CDN_URL
    : `${window.location.origin}${AppConfig.I18N_URL}`;
  return axios
    .get(`${publicFolder}/locales/${locale}/${ns}.json`, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': '*',
        'Access-Control-Allow-Headers': '*',
      },
    })
    .then((res) => res.data);
};

const dynamicFrontendBundle = (locale, ns) => {
  return new Promise((resolve) => {
    const jsonFile = require(`../../public/locales/${locale}/${ns}.json`);
    resolve(jsonFile);
  });
};

function loadLocaleBundle(locale, ns) {
  return dynamicFrontendBundle(locale, ns);
}

const backendOptions = {
  loadPath: '{{lng}}|{{ns}}',
  request: (options, url, payload, callback) => {
    try {
      const [lng, ns] = url.split('|');

      // this mocks the HTTP fetch plugin behavior so it works with the backend AJAX pattern in this XHR library

      loadLocaleBundle(lng, ns).then((data) => {
        callback(null, {
          data: JSON.stringify(data),
          status: 200, // status code is required by XHR plugin to determine success or failure
        });
      });
    } catch (e) {
      console.error(e);
      callback(null, {
        status: 500,
      });
    }
  },
};

i18n.use(Backend).init({
  // resources,
  lng: DEFAULT_LANGUAGE,
  defaultNS: defaultNS,
  ns: i18nNamespaces,
  fallbackLng: DEFAULT_LANGUAGE,
  interpolation: {
    escapeValue: false,
  },
  backend: backendOptions,
  react: {
    // wait: true,
    useSuspense: true,
  },
});

export default i18n;
