import {
  ConversationListFilterType,
  CreateTeamInfo,
  IConversation,
  IGetConversationMessageParams,
  IMessage,
  IOperatorProfile,
  IPaginationParam,
  ISuccessConversationResponse,
  ISuccessResponse,
  ITeamDetail,
  ITeamResponse,
  MetaData,
  OperatorProfileUpdateType,
  Status,
  StatusEnum,
  TeamInfoModification,
} from '@resola-ai/models';
import { ConversationSettings } from '@resola-ai/models/collection/settings';
import { EngineProps } from '@resola-ai/widget-engine';
import { AutomationData, AutomationList } from '../../models/automation';
import { ConversationListParams } from '../../models/conversationParams';
import { IResource, ResourceType } from '../../models/resource';
import { UserStatus } from '../../models/user';
import { IWorkspaceUnreadConversation, IWorkspaces } from '../../models/workspace';
import {
  AllTypeIntegration,
  ILineIntegration,
  IPayloadCreateToolIntegration,
  IResponseExternalToolIntegration,
  IWebhookIntegration,
  WebTransformConfigs,
} from '../../modules/Settings/Integrations/models';
import {
  IAutoReplyConfig,
  IBusinessHoursStatePayload,
  IHolidaysStatePayload,
  IOperationSettings,
  IOperationSettingsStatePayload,
  IOutBusinessHourStatePayload,
  IPreferences,
} from '../../modules/Settings/Operations/models';
import { IWidgetSetting } from '../../modules/Widgets/models';
import { RestApi } from '../axios';
import logger from '../logger';
import { Preferences } from './../../../../packages/models/collection/settings';

type CustomResponseType<T> =
  | {
      status: StatusEnum.Success;
      data: T;
    }
  | { status: StatusEnum.Failed; message: string };

type ResponseUpdateOperationSetting =
  | IOperationSettingsStatePayload
  | IBusinessHoursStatePayload
  | IHolidaysStatePayload
  | IOutBusinessHourStatePayload
  | IAutoReplyConfig
  | { preferences: IPreferences };

class ApiService {
  static async getTeamList({
    perPage = 100,
  }: Partial<Exclude<IPaginationParam, ['ts', 'count', 'numPages']>>): Promise<ITeamDetail[]> {
    try {
      const { data } = await RestApi.get<ITeamResponse>(`/teams?per_page=${perPage}`);
      const temp = data.data || ([] as ITeamDetail[]);
      return temp.map((item) => {
        const tmpItem = { ...item };
        return tmpItem;
      });
    } catch (error) {
      logger.error(error);
      return [];
    }
  }

  static async createTeam({
    name,
    description,
    picture,
  }: CreateTeamInfo): Promise<CustomResponseType<ITeamDetail>> {
    try {
      const { data } = await RestApi.post<ISuccessResponse<ITeamDetail>>('/teams', {
        name,
        description,
        picture,
      });
      return {
        status: data.status as StatusEnum.Success,
        data: data.data,
      };
    } catch (error: any) {
      logger.error(error);
      return {
        status: StatusEnum.Failed,
        message: error.response?.data.message,
      };
    }
  }

  static async getTeam(teamId: string): Promise<ITeamDetail | null> {
    try {
      const res = await RestApi.get<ISuccessResponse<ITeamDetail>>(`/teams/${teamId}`);
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async teamInfoModification({
    teamId,
    action,
    ...res
  }: TeamInfoModification): Promise<CustomResponseType<ITeamDetail>> {
    try {
      const url = action ? `/teams/${teamId}?action=${action}` : `/teams/${teamId}`;
      const response = await RestApi.put<ISuccessResponse<ITeamDetail>>(url, { ...res });
      return {
        status: StatusEnum.Success,
        data: response.data.data,
      };
    } catch (error: any) {
      logger.error(error);
      return {
        status: StatusEnum.Failed,
        message: error.response?.data.message,
      };
    }
  }

  static async updateUserProfile(
    userId: string,
    dataUpdate:
      | OperatorProfileUpdateType
      | { conversationSetting: ConversationSettings }
      | { status: UserStatus }
      | { preferences: Preferences }
      | { notificationSetting: { sound: { selectedSoundId: string; mode: string } } }
  ): Promise<CustomResponseType<IOperatorProfile>> {
    try {
      const res = await RestApi.put<ISuccessResponse<IOperatorProfile>>(`/users/${userId}`, {
        ...dataUpdate,
      });
      return {
        status: StatusEnum.Success,
        data: res.data.data,
      };
    } catch (e) {
      logger.error(e);
      return {
        status: StatusEnum.Failed,
        message: 'ERROR MODIFICATION USER PROFILE',
      };
    }
  }

  static async getListOperators(status?: boolean): Promise<IOperatorProfile[]> {
    try {
      const { data } = await RestApi.get<ISuccessResponse<IOperatorProfile[]>>('/users', {
        params: { status },
      });
      const temp = data.data || ([] as IOperatorProfile[]);
      return temp.map((item) => {
        const tmpItem = { ...item };
        tmpItem.teams = tmpItem?.teams || [''];
        tmpItem.name = tmpItem?.name || tmpItem?.email || '';
        tmpItem.status = tmpItem?.status || Status.Offline;
        return tmpItem;
      });
    } catch (error) {
      logger.error(error);
      return [];
    }
  }

  static async getOperatorProfile(throwError = false): Promise<IOperatorProfile | null> {
    try {
      const { data } = await RestApi.get<ISuccessResponse<IOperatorProfile>>('/users/profile');
      return data.data || null;
    } catch (e) {
      if (throwError) {
        console.log('API USER PROFILE ERROR');
        throw e;
      }
      logger.error(e);
      return null;
    }
  }

  static async getOperatorWorkspace(): Promise<IWorkspaces> {
    try {
      const { data } = await RestApi.get('/users/workspace');
      return data.data;
    } catch (error) {
      logger.error(error);
      return {
        myAssignedConversationNo: 0,
        unAssignedTeamConversationNo: 0,
        teams: [],
      };
    }
  }

  static async getUnreadConversationStatus(): Promise<IWorkspaceUnreadConversation> {
    try {
      const { data } = await RestApi.get('/users/unread_conversation');
      return data.data;
    } catch (error) {
      logger.error(error);
      return {
        unreadConversations: {
          quantity: [],
          new: [],
          inProgress: [],
          completed: [],
        },
        readLastMessageAt: {},
      };
    }
  }

  static async getConversationById(conversationId: string) {
    return RestApi.get<ISuccessResponse<IConversation>>(`/conversations/${conversationId}`)
      .then((response) => {
        return response.data;
      })
      .then((data) => {
        return data.data;
      })
      .catch(() => {
        return null;
      });
  }

  static async getConversations(params: ConversationListParams) {
    delete (params as any)?.params;
    return RestApi.get<ISuccessConversationResponse>('/conversations', { params })
      .then((response) => {
        return response.data;
      })
      .then((response) => {
        // return response.data;
        return {
          data: response.data,
          status: response.status,
          pagination: response.pagination,
        };
      });
  }
  static async getBookmarkConversations(params: ConversationListParams) {
    return RestApi.get<ISuccessConversationResponse>('/conversations/bookmarks/list', {
      params,
    })
      .then((response) => {
        return response.data;
      })
      .then((response) => {
        return {
          data: response.data,
          status: response.status,
          pagination: response.pagination,
        };
      });
  }

  static async getAutomationList(): Promise<AutomationList | null> {
    try {
      const res = await RestApi.get<ISuccessResponse<AutomationList>>('/automations');
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async getAutomationDetail(automationId: string): Promise<AutomationData | null> {
    try {
      const res = await RestApi.get<ISuccessResponse<AutomationData>>(
        `/automations/${automationId}`
      );
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async putAutomationData(
    automationId: string,
    automation: AutomationData
  ): Promise<AutomationData | null> {
    try {
      const res = await RestApi.put(`/automations/${automationId}`, {
        ...automation,
      });
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async createAutomation(automation) {
    try {
      const res = await RestApi.post('/automations', { ...automation });
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async updateAutomation(automationId: string, automation) {
    try {
      const res = await RestApi.put(`/automations/${automationId}`, { ...automation });
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async getAutomationConfigs() {
    try {
      const res = await RestApi.get('/automations/configs');
      return res.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async deleteAutomation(id: string) {
    try {
      const res = await RestApi.delete(`/automations/${id}`);
      return res.data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static assignOperatorToConversation = async ({
    conversationId,
    operatorId,
    teamId,
    assignToMe,
  }: {
    operatorId: string;
    conversationId: string;
    teamId: string | null | undefined;
    assignToMe?: boolean;
  }) => {
    const payload = {
      user: operatorId,
    };
    if (teamId) {
      payload['team'] = teamId;
    }

    let url = `/conversations/${conversationId}`;

    if (assignToMe !== undefined) {
      url += `?assignToMe=${assignToMe}`;
    }

    const response = await RestApi.put<ISuccessResponse<IConversation | { message: string }>>(
      url,
      payload
    );
    return response.data.data;
  };

  static getConversation = async (conversationId: string) => {
    try {
      const res = await RestApi.get<ISuccessResponse<[]>>(`/conversations/${conversationId}`);
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return [];
    }
  };

  static convoSetAutoComplete = async ({
    conversationId,
    enable,
    interval,
  }: {
    conversationId: string;
    enable: boolean;
    interval: Number;
  }) => {
    const response = await RestApi.put<ISuccessResponse<IConversation>>(
      `/conversations/${conversationId}`,
      {
        autoCompleted: {
          enable: enable,
          interval: interval,
        },
      }
      // teamId
      //     ? {
      //           user: operatorId,
      //           team: teamId,
      //       }
      //     : { user: operatorId },
    );
    return response.data.data;
  };

  static assignTeamToConversation = async ({
    conversationId,
    teamId,
  }: {
    conversationId: string;
    teamId: string;
  }) => {
    const response = await RestApi.put<ISuccessResponse<IConversation>>(
      `/conversations/${conversationId}`,
      {
        team: teamId,
      }
    );
    return response.data.data;
  };

  static getListMemoConversation = async (conversationId: string) => {
    try {
      const res = await RestApi.get<ISuccessResponse<[]>>(`/conversations/${conversationId}/memos`);
      return res.data.data;
    } catch (e) {
      logger.error(e);
      return [];
    }
  };

  static saveMemoConversation = async (conversationId: string, memo: {}) => {
    try {
      await RestApi.post<ISuccessResponse<any>>(`/conversations/${conversationId}/memos`, {
        ...memo,
      });
      return true;
    } catch (e) {
      return false;
    }
  };

  static updateMemoConversation = async (conversationId: string, memo: {}) => {
    try {
      await RestApi.put<ISuccessResponse<any>>(`/conversations/${conversationId}/memos`, {
        ...memo,
      });
      return true;
    } catch (e) {
      return false;
    }
  };

  static deleteMemoConversation = async (conversationId: string, memo: string) => {
    try {
      await RestApi.delete<ISuccessResponse<any>>(`/conversations/${conversationId}/memos`, {
        data: {
          id: memo,
        },
      });
      return true;
    } catch (e) {
      return false;
    }
  };

  static getConversationMessages = (
    conversationId: string,
    params?: IGetConversationMessageParams
  ) => {
    if (!conversationId) {
      return;
    }

    return RestApi.get<any>(`/conversations/${conversationId}/messages`, {
      params: {
        ...params,
        next: params?.ts,
      },
    }).then((response) => {
      return {
        data: response.data.data,
        status: response.data.status,
        pagination: {
          perPage: response.data.pagination.perPage,
          ts: response.data.pagination.next,
        },
      };
    });
  };

  static getConversationMessagesTimelineBase = async (
    converId: string,
    params: IGetConversationMessageParams
  ): Promise<IMessage[]> => {
    if (!converId) {
      return;
    }
    try {
      const res = await RestApi.get<any>(`/conversations/${converId}/messages`, {
        params: {
          ...params,
        },
      });
      return res?.data?.data || [];
    } catch (e) {
      return [];
    }
  };

  static sendConversationMessage = async (
    conversationId: string,
    text: string,
    type: string,
    metadata: MetaData
  ) => {
    return RestApi.post<ISuccessResponse<IMessage>>(`/conversations/${conversationId}/messages`, {
      text,
      type,
      metadata,
    })
      .then((response) => {
        return response.data;
      })
      .then((data) => {
        return [data.data];
      });
  };

  static sendConversationMedia = async (
    conversationId: string,
    url: string,
    assetId: string,
    messageType: string,
    metadata: MetaData
  ) => {
    return RestApi.post<ISuccessResponse<IMessage>>(`/conversations/${conversationId}/messages`, {
      type: messageType,
      text: url,
      originalContentUrl: url,
      previewImageUrl: url,
      asset: assetId,
      metadata,
    })
      .then((response) => {
        return response.data;
      })
      .then((data) => {
        return [data.data];
      });
  };

  static getWsToken = async () => {
    return RestApi.get<ISuccessResponse<{ wsAccessToken: string }>>(`/users/token/renew`)
      .then((response) => {
        return response.data;
      })
      .then((data) => {
        return data.data.wsAccessToken;
      })
      .catch((e) => {
        console.log('error get ws token', e);
        return '';
      });
  };

  static markConversationAsRead = async (conversationId: string) => {
    return RestApi.get<ISuccessResponse<{}>>(`/conversations/${conversationId}/read`)
      .then((response) => {
        return response.data;
      })
      .then((data) => {
        return data.status;
      });
  };

  static changeConversationStatus = async (
    conversationId: string,
    status: ConversationListFilterType
  ) => {
    const response = await RestApi.put<ISuccessResponse<IConversation>>(
      `/conversations/${conversationId}`,
      {
        status: status,
      }
    );
    return response.data.data;
  };

  static createNewConversationFromEndedOne = async (conversationId: string) => {
    try {
      const response = await RestApi.post<ISuccessResponse<any>>(`/conversations`, {
        conversationId,
      });
      return response.data.data;
    } catch (e) {
      return null;
    }
  };

  static selfAssignConversationAmongAll = async () => {
    try {
      const response = await RestApi.put<ISuccessResponse<IConversation>>(
        `/conversations/assign/oldest`
      );
      if (response.data.data === null) {
        return 'open';
      } else {
        return response.data.data;
      }
    } catch (e) {
      return null;
    }
  };

  static toggleBookmark = async (conversationId: string, type: 'add' | 'remove') => {
    const response = await RestApi.put<ISuccessResponse<IConversation>>(
      `/conversations/${conversationId}/bookmark`,
      {
        type,
      }
    );
    return response.data.data;
  };

  static uploadAsset = async (resourceId: string, resourceType: ResourceType, file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    // formData.append("resourceId", resourceId);
    // formData.append("resourceType", resourceType);
    const response = await RestApi.post<ISuccessResponse<IResource>>(
      `/assets/${resourceType}/${resourceId}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data.data;
  };

  static deleteAsset = async (resourceId: string, statusCreated: string) => {
    try {
      await RestApi.delete<ISuccessResponse<any>>(`/assets/${resourceId}/${statusCreated}`);
    } catch (e) {}
  };

  static async getOperationSettings(): Promise<IOperationSettings | null> {
    try {
      const { data } =
        await RestApi.get<ISuccessResponse<IOperationSettings>>('/settings/operations');
      return data.data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async updateOperationSettings(payload: ResponseUpdateOperationSetting) {
    try {
      const { data } = await RestApi.put<ISuccessResponse<IOperationSettings>>(
        '/settings/operations',
        payload
      );
      return data.data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async getToolsIntegration(
    integration_type: 'webhook' | undefined = undefined,
    per_page: number | undefined = undefined
  ): Promise<IResponseExternalToolIntegration | null> {
    try {
      const { data } = await RestApi.get<IResponseExternalToolIntegration>(
        '/settings/integrations',
        { params: { integration_type, per_page } }
      );
      return data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async getAllToolsOnPlatform(): Promise<ISuccessResponse<AllTypeIntegration[]> | null> {
    try {
      const { data } = await RestApi.get<ISuccessResponse<AllTypeIntegration[]>>(
        '/settings/integrations/platforms/supported'
      );
      return data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async createToolsIntegration(
    payload: IPayloadCreateToolIntegration
  ): Promise<ISuccessResponse<AllTypeIntegration> | null> {
    try {
      const { data } = await RestApi.post<ISuccessResponse<ILineIntegration>>(
        '/settings/integrations',
        payload
      );
      return data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async updateToolsIntegration(
    integrationId: string,
    payload: IPayloadCreateToolIntegration
  ): Promise<ISuccessResponse<AllTypeIntegration> | null> {
    try {
      const { data } = await RestApi.put<ISuccessResponse<AllTypeIntegration>>(
        `/settings/integrations/${integrationId}`,
        payload
      );
      return data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async deleteToolsIntegration(
    integrationId: string
  ): Promise<ISuccessResponse<ILineIntegration> | null> {
    try {
      const { data } = await RestApi.delete<ISuccessResponse<ILineIntegration>>(
        `/settings/integrations/${integrationId}`
      );
      return data;
    } catch (error) {
      logger.error(error);
      return null;
    }
  }

  static async getWebhookIntegration(webhookId: string): Promise<IWebhookIntegration | null> {
    try {
      const data = await RestApi.get<IWebhookIntegration>(`/settings/integrations/${webhookId}`);
      return (data.data as any).data;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async getWebhookSamples(): Promise<WebTransformConfigs | null> {
    try {
      const data = await RestApi.get<WebTransformConfigs>('/settings/integrations/webhook/payload');
      return (data.data as any).data as WebTransformConfigs;
    } catch (e) {
      logger.error(e);
      return null;
    }
  }

  static async sendWebhookTestData(integration: IPayloadCreateToolIntegration) {
    try {
      const { data } = await RestApi.post('/settings/integrations/webhook/test', integration);
      return data?.data;
    } catch (e) {}
  }

  static async getWidgets(): Promise<EngineProps[]> {
    try {
      const { data } = await RestApi.get<ISuccessResponse<any>>('/widgets');
      return data.data
        .filter((item) => item.template.component !== 'chatDetail')
        .map((item) => {
          return {
            component: item.template.component,
            widgetId: item.id,
            data: item.config,
          } as EngineProps;
        });
    } catch (error) {
      logger.error(error);
      return [];
    }
  }

  static async getWidgetSettings(): Promise<IWidgetSetting[]> {
    try {
      const { data } = await RestApi.get<ISuccessResponse<any>>('/widgets_settings');
      return data.data
        .filter((item) => item.widget.template.component !== 'chatDetail')
        .map((item) => {
          return {
            component: item.widget.template.component,
            id: item.widgetSetting.id,
            orgId: item.widgetSetting.orgId,
            status: item.widgetSetting.status,
            userId: item.widgetSetting.userId,
            widgetId: item.widget.id,
            created: item.widgetSetting.created,
            name: item.widgetSetting.name,
            description: item.widgetSetting.description,
            data: item.widgetSetting.status === 'available' ? item.widgetSetting.data : {},
            widgetSettingId: item.widgetSetting.id,
            engine: {
              component: item.widget.template.component,
              widgetId: item.widget.id,
              data: item.widgetSetting.status === 'available' ? item.widgetSetting.data : {},
              widgetSettingId: item.widgetSetting.id,
            },
          } as IWidgetSetting;
        });
    } catch (error) {
      logger.error(error);
      return [];
    }
  }

  static removeWidgetSetting = async (widgetSettingId: string) => {
    const response = await RestApi.delete<ISuccessResponse<any>>(
      `/widgets_settings/${widgetSettingId}`
    );
    return response.data;
  };

  static addWidgetSetting = async (
    widgetId: string,
    name: string = '',
    description: string = '',
    data: any
  ) => {
    try {
      const response = await RestApi.post<ISuccessResponse<any>>(`/widgets_settings`, {
        widgetId,
        name,
        description,
        data,
      });
      return response.data;
    } catch (error) {
      console.log('Error create new widget setting', error);
      return null;
    }
  };

  static updateWidgetSetting = async (widgetSettingId: string, data: any) => {
    const response = await RestApi.put<ISuccessResponse<any>>(
      `/widgets_settings/${widgetSettingId}`,
      {
        ...data,
      }
    );
    return response.data;
  };

  static getCRMConfigData = async () => {
    try {
      const res = await RestApi.get('/widget_settings/crm/objects');
      return res?.data?.data || [];
    } catch (e) {
      return [];
    }
  };

  static async getActivityConversations(
    endUserId: string,
    ocsChannel: string,
    currentConversationId?: string,
    nextParam?: string
  ) {
    let url = `/conversations/enduser/${endUserId}/channel/${ocsChannel}`;
    try {
      const response = await RestApi.get(url, {
        params: {
          currentConversationId: currentConversationId,
          next: nextParam,
        },
      });
      const {
        data,
        pagination: { next = '' },
      } = response?.data;
      return { data, next };
    } catch (e) {
      return null;
    }
  }

  static async getChatBotMessages(endUserId: string, integrationId: string, nextParam?: string) {
    const url = `/conversations/enduser/${endUserId}/channel/${integrationId}/chatbot_messages`;
    try {
      const response = await RestApi.get(url, {
        params: {
          next: nextParam,
        },
      });
      const {
        data,
        pagination: { next = '' },
      } = response?.data;
      return { data, next };
    } catch (e) {
      return null;
    }
  }

  static async getCrmEndUserInfo(endUserId: string) {
    const url = `/endusers/crm/${endUserId}`;
    try {
      const response = await RestApi.get(url);
      const { data } = response?.data;
      return data;
    } catch (e) {
      return null;
    }
  }
}

export default ApiService;
