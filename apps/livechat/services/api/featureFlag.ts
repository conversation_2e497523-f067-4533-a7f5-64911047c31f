import { ISuccessResponse } from '@resola-ai/models';
import { FeatureFlag } from '@resola-ai/models/collection/featureFlag';
import { RestApi } from '../axios';
import logger from '../logger';

export const FeatureFlagAPI = {
  getList: async () => {
    try {
      const response = await RestApi.get<ISuccessResponse<FeatureFlag>>('/settings/features');
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
};
