import { notifications } from '@mantine/notifications';

// type NotificationType = 'success' | 'info' | 'warning' | 'error';

class NotificationService {
  static defaultProp = {
    autoClose: 10000,
    withBorder: true,
    withCloseButton: true,
    sx: { top: '55px' },
    message: '',
  };

  static sendNotification() {
    // eslint-disable-next-line no-unused-vars
    // type: NotificationType = 'info',
    // // eslint-disable-next-line no-unused-vars
    // onClick: () => void = () => {
    //     this.hideNotification(message);
    // },
    // TODO: disable the notification for the MVP only, after Chiekon san provides detail specs, it will be enabled.
    // if (type === "error") {
    //   notifications.show({
    //     ...this.defaultProp,
    //     id: message,
    //     title,
    //     message,
    //     color: "red",
    //     onClick,
    //   });
    //   return;
    // }
    // if (type === "success") {
    //   notifications.show({
    //     ...this.defaultProp,
    //     id: message,
    //     title,
    //     message,
    //     color: "green",
    //     onClick,
    //   });
    //   return;
    // }
    // notifications.show({
    //   ...this.defaultProp,
    //   id: message,
    //   title,
    //   message,
    //   color: "violet",
    //   onClick,
    // });
  }

  static hideNotification(id: string) {
    notifications.hide(id);
  }
}

export default NotificationService;
