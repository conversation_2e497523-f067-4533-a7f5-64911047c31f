import { IFailResponse } from '@resola-ai/models';
import axios, { AxiosError, AxiosInstance } from 'axios';
import AppConfig from '../../configs';
// import NotificationService from '../notification';
import { reloadWholePage } from '../../utils/widgets';

class AxiosService {
  instance: AxiosInstance;
  private accessToken: string;
  private orgId: string;

  constructor() {
    this.accessToken = '';
    this.orgId = '';
    this.instance = axios.create({
      baseURL: AppConfig.BASE_URL_API,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    this.instance.interceptors.request.use(
      (config) => {
        // handle get accessToken from session
        const token = {
          accessToken: this.accessToken,
          refreshToken: 'refresh-token',
        };

        if (token?.accessToken) {
          config.headers.Authorization = `Bearer ${token.accessToken}`;
        }

        if (this.orgId.length > 0) {
          config.headers['x-org-id'] = this.orgId;
        }
        config.headers['ngrok-skip-browser-warning'] = 'true';

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    this.instance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error: AxiosError<IFailResponse>) => {
        console.log('error', error);
        const errorMessage = error?.response?.data?.message;
        if (parseInt(`${error?.response?.status}`, 10) === 401 && errorMessage === 'Unauthorized') {
          reloadWholePage();
          return Promise.reject();
        }

        if (errorMessage) {
          if (errorMessage === 'USER_NO_LIVE_CHAT_PERMISSION') {
            // redirect to
            window.location.href = AppConfig.NO_PERMISSION_REDIRECT_URL;
            return Promise.reject();
          }
          // NotificationService.sendNotification(errorMessage, '', 'error');
        }
        return Promise.reject(error);
      }
    );
  }

  setAccessToken(accessToken: string) {
    this.accessToken = accessToken;
  }

  getAccessToken() {
    return this.accessToken;
  }

  setOrgId(orgId: string) {
    this.orgId = orgId;
  }

  getOrgId() {
    return this.orgId;
  }
}

const axiosService = new AxiosService();
const RestApi = axiosService.instance;
typeof window !== 'undefined' && localStorage.removeItem('access_token'); // TODO will remove later, this is just for remove the access token for the old user.

export { RestApi };

export default axiosService;
