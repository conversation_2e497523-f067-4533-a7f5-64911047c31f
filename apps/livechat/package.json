{"name": "livechat", "version": "1.26.0", "private": true, "scripts": {"dev": "next dev", "prepare:web:pr": "node ./scripts/previewEnvVarPrepare.js", "prepare": "cd ../../ && husky install", "build": "next build && next export", "start": "next start", "lint": "biome lint .", "lint:next": "next lint", "lint:eslint:fix": "biome check --apply .", "format": "biome format --write .", "lint-staged-check": "lint-staged", "test": "jest", "test:unit": "jest --no-watch", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "coverage": "jest --coverage", "release": "standard-version -t livechat@", "release:minor": "standard-version -t livechat@ --release-as minor", "release:patch": "standard-version -t livechat@ --release-as patch", "release:major": "standard-version -t livechat@ --release-as major"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emoji-mart/data": "1.1.2", "@emoji-mart/react": "1.1.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:^", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@resola-ai/widget-engine": "workspace:*", "@tabler/icons-react": "3.17.0", "@tiptap/core": "2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-placeholder": "^2.5.8", "@tiptap/pm": "^2.5.8", "@tiptap/react": "^2.5.8", "@tiptap/starter-kit": "^2.5.8", "@tolgee/react": "^5.33.2", "@tolgee/web": "^5.33.2", "@uiw/react-json-view": "2.0.0-alpha.24", "@uiw/react-textarea-code-editor": "^3.1.0", "axios": "^1.8.2", "centrifuge": "5.0.0", "dayjs": "^1.11.12", "elkjs": "^0.8.2", "emoji-datasource-apple": "15.0.1", "emoji-datasource-google": "15.0.1", "emoji-mart": "^3.0.1", "framer-motion": "^10.18.0", "husky": "8.0.3", "i18next": "23.10.0", "i18next-http-backend": "3.0.2", "jsdom": "^22.1.0", "jsonata": "^2.0.5", "lint-staged": "^15.5.0", "next": "13.5.11", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.11.4", "react-resizable-panels": "^2.1.4", "react-zoom-pan-pinch": "3.1.0", "reactflow": "^11.10.1", "showdown": "^2.1.0", "standard-version": "^9.5.0", "styled-components": "^6.1.8", "swr": "2.2.2", "uuid": "^9.0.1", "web-worker": "^1.3.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@biomejs/biome": "1.5.3", "@resola-ai/biome-config": "workspace:^", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/emoji-mart": "3.0.9", "@types/jest": "29.5.0", "@types/node": "^17.0.45", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitest/coverage-v8": "^2.1.9", "dotenv": "16.3.1", "extend-expect": "link:@testing-library/jest-dom/extend-expect", "jest": "29.5.0", "jest-dom": "^4.0.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "29.0.5", "typescript": "5.6.3"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["biome format --write"]}}