import { useRouter } from 'next/router';
import { AutomationDetailScreen } from '../../../modules/Automation/AutomationDetailScreen/AutomationDetailScreen';
import { AutomationDetailContextProvider } from '../../../modules/Automation/AutomationDetailScreen/contexts/AutomationDetailContext';

export default function Page() {
  const { query } = useRouter();

  return (
    <AutomationDetailContextProvider automationId={query?.automationId as string}>
      <AutomationDetailScreen />
    </AutomationDetailContextProvider>
  );
}
