import { Auth0Provider } from '@auth0/auth0-react';
import { ColorSchemeScript } from '@mantine/core';
import '@mantine/core/styles.css';
import { Notifications } from '@mantine/notifications';
import { datadogService } from '@resola-ai/services-shared';
import { AnimatePresence } from 'framer-motion';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import Router from 'next/router';
import React from 'react';
import AppConfig from '../configs';
import { AppMantineEmotionProvider } from '../emotion';
import RootLayout from '../modules/RootLayout/RootLayout';
import { isExcludedBasePathDomains, isInCludedBasePathDomains } from '../utils/location';
import { getOrganizationName } from '../utils/organization';
import { getPublicUrl } from '../utils/public';
import { getRedirectUri } from '../utils/redirect';
import { getAppVersion } from '../utils/version';

// Initialize Datadog service
/* eslint-disable turbo/no-undeclared-env-vars */
datadogService.init({
  applicationId: process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID ?? '',
  clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN ?? '',
  site: process.env.NEXT_PUBLIC_DATADOG_SITE ?? '',
  service: process.env.NEXT_PUBLIC_DATADOG_SERVICE ?? '',
  env: process.env.NEXT_PUBLIC_DATADOG_ENV ?? '',
  version: getAppVersion(),
});

const App = ({ Component, pageProps, router }: AppProps) => {
  const organizationName = getOrganizationName();
  const onRedirectCallback = (appState) => {
    if (isExcludedBasePathDomains()) {
      window.location.href = window.location.origin;
      return;
    }
    if (isInCludedBasePathDomains()) {
      window.location.href = window.location.origin + AppConfig.BASE_URL;
      return;
    }
    Router.replace(appState?.returnTo || AppConfig.BASE_URL);
  };
  const redirectUri = getRedirectUri();
  getAppVersion();
  return (
    <AppMantineEmotionProvider>
      <Auth0Provider
        domain={AppConfig.AUTH0.DOMAIN}
        clientId={AppConfig.AUTH0.CLIENT_ID}
        onRedirectCallback={onRedirectCallback}
        authorizationParams={{
          redirect_uri: redirectUri,
          audience: AppConfig.AUTH0.AUDIENCE,
          scope: AppConfig.AUTH0.SCOPE,
          organization: organizationName,
        }}
      >
        <Head>
          <title>DECA Livechat</title>
          <link rel='icon' href={getPublicUrl('/favicon/favicon.ico')} sizes='any' />
          <ColorSchemeScript />
        </Head>

        <RootLayout>
          <Notifications position='top-right' zIndex={2077} limit={6} />

          <AnimatePresence mode='wait' initial={false} onExitComplete={() => window.scrollTo(0, 0)}>
            <Component {...pageProps} key={router.asPath} />
          </AnimatePresence>
        </RootLayout>
      </Auth0Provider>
    </AppMantineEmotionProvider>
  );
};

export default App;
