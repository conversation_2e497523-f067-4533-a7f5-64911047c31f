import { useRouter } from 'next/router';
import TeamDetail from '../../../../modules/Settings/TeamsAndUsers/TeamDetail';
import { TeamDetailSettingContextProvider } from '../../../../modules/Settings/TeamsAndUsers/TeamDetail/TeamDetailContext';

export default function Page() {
  const {
    query: { teamId = null },
  } = useRouter();
  return (
    <TeamDetailSettingContextProvider teamId={teamId as string}>
      <TeamDetail />
    </TeamDetailSettingContextProvider>
  );
}
