import { createContext, useContext } from 'react';
import useS<PERSON> from 'swr';
import ApiService from '../services/api';

const useTeam = () => {
  const {
    data: teamList = [],
    isLoading: isLoadingTeamList,
    error: errorLoadingTeamList,
    mutate: reloadTeamList,
  } = useSWR('/get-team-list', ApiService.getTeamList);
  return {
    teamList,
    isLoadingTeamList,
    errorLoadingTeamList,
    reloadTeamList,
  };
};

export type TeamContextType = ReturnType<typeof useTeam>;

const context = createContext<TeamContextType | null>(null);

export const TeamContextProvider: React.FC<any> = ({ children }) => {
  const value = useTeam();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useTeamContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useTeamContext must be used inside TeamContextProvider');
  }

  return value;
};
