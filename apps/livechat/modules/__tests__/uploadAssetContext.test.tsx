// Mock the utils functions FIRST
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
}));

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, act } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import createCache from '@emotion/cache';
import { UploadAssetContextProvider, useUploadAssetContext } from '../uploadAssetContext';

// Mock the conversation action context
const mockUseConversationActionContext = jest.fn();
jest.mock('../conversationActionContext', () => ({
  useConversationActionContext: () => mockUseConversationActionContext(),
}));

// Create emotion cache for testing
const emotionCache = createCache({ key: 'mantine' });

// Create a custom theme for testing
const testTheme = {
  colors: {
    decaBlue: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
    gray: [
      '#f8f9fa',
      '#f1f3f4',
      '#e8eaed',
      '#dadce0',
      '#bdc1c6',
      '#9aa0a6',
      '#80868b',
      '#5f6368',
      '#3c4043',
      '#202124',
    ] as const,
    navy: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
};

// Wrapper component for testing
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MantineEmotionProvider cache={emotionCache}>
    <MantineProvider theme={testTheme}>
      <UploadAssetContextProvider>{children}</UploadAssetContextProvider>
    </MantineProvider>
  </MantineEmotionProvider>
);

// Test component to access the context
const TestComponent: React.FC = () => {
  const context = useUploadAssetContext();
  return (
    <div>
      <div data-testid='limit-size'>{context.LIMIT_SIZE}</div>
      <div data-testid='limit-file-per-upload'>{context.LIMIT_FILE_PER_UPLOAD}</div>
      <div data-testid='allow-mime-types'>{context.ALLOW_MIME_TYPES.join(',')}</div>
      <div data-testid='reject-errors'>{JSON.stringify(context.rejectErrors)}</div>
      <button
        data-testid='set-reject-errors'
        onClick={() => context.setRejectErrors({ 'test-error': true })}
      >
        Set Error
      </button>
    </div>
  );
};

describe('uploadAssetContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseConversationActionContext.mockReturnValue({
      currentConversationId: 'test-conversation-id',
    });
  });

  describe('UploadAssetContextProvider', () => {
    it('should render children without crashing', () => {
      render(
        <TestWrapper>
          <div data-testid='test-child'>Test Child</div>
        </TestWrapper>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('should provide context values', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('limit-size')).toHaveTextContent('5242880'); // 5MB in bytes
      expect(screen.getByTestId('limit-file-per-upload')).toHaveTextContent('1');
      expect(screen.getByTestId('allow-mime-types')).toHaveTextContent(
        'image/png,image/jpg,image/jpeg,image/svg+xml,application/pdf,video/mp4,video/quicktime,video/x-msvideo,video/3gpp,video/webm,video/ogg,video/x-ms-wmv,video/x-matroska,video/avi,video/x-ms-asf'
      );
      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{}');
    });

    it('should allow setting reject errors', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const setErrorButton = screen.getByTestId('set-reject-errors');
      act(() => {
        setErrorButton.click();
      });

      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{"test-error":true}');
    });

    it('should clear reject errors when conversation ID changes', () => {
      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Set an error first
      const setErrorButton = screen.getByTestId('set-reject-errors');
      act(() => {
        setErrorButton.click();
      });
      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{"test-error":true}');

      // Change conversation ID
      mockUseConversationActionContext.mockReturnValue({
        currentConversationId: 'new-conversation-id',
      });

      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{}');
    });

    it('should handle empty conversation ID', () => {
      mockUseConversationActionContext.mockReturnValue({
        currentConversationId: '',
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{}');
    });

    it('should handle null conversation ID', () => {
      mockUseConversationActionContext.mockReturnValue({
        currentConversationId: null,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{}');
    });
  });

  describe('useUploadAssetContext hook', () => {
    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useUploadAssetContext must be used inside UploadAssetContextProvider');

      consoleSpy.mockRestore();
    });

    it('should provide correct constants', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Check LIMIT_SIZE (5MB)
      expect(screen.getByTestId('limit-size')).toHaveTextContent('5242880');

      // Check LIMIT_FILE_PER_UPLOAD
      expect(screen.getByTestId('limit-file-per-upload')).toHaveTextContent('1');

      // Check ALLOW_MIME_TYPES
      const mimeTypes = screen.getByTestId('allow-mime-types').textContent;
      expect(mimeTypes).toContain('image/png');
      expect(mimeTypes).toContain('image/jpg');
      expect(mimeTypes).toContain('image/jpeg');
      expect(mimeTypes).toContain('image/svg+xml');
      expect(mimeTypes).toContain('application/pdf');
      expect(mimeTypes).toContain('video/mp4');
    });

    it('should provide setRejectErrors function', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const setErrorButton = screen.getByTestId('set-reject-errors');
      expect(setErrorButton).toBeInTheDocument();

      act(() => {
        setErrorButton.click();
      });
      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{"test-error":true}');
    });

    it('should handle multiple reject errors', () => {
      const TestComponentMultiple: React.FC = () => {
        const context = useUploadAssetContext();
        return (
          <div>
            <div data-testid='reject-errors'>{JSON.stringify(context.rejectErrors)}</div>
            <button
              data-testid='set-multiple-errors'
              onClick={() =>
                context.setRejectErrors({
                  'file-too-large': true,
                  'file-not-support': true,
                  'test-error': true,
                })
              }
            >
              Set Multiple Errors
            </button>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestComponentMultiple />
        </TestWrapper>
      );

      const setMultipleErrorsButton = screen.getByTestId('set-multiple-errors');
      act(() => {
        setMultipleErrorsButton.click();
      });

      expect(screen.getByTestId('reject-errors')).toHaveTextContent(
        '{"file-too-large":true,"file-not-support":true,"test-error":true}'
      );
    });

    it('should clear reject errors when set to empty object', () => {
      const TestComponentClear: React.FC = () => {
        const context = useUploadAssetContext();
        return (
          <div>
            <div data-testid='reject-errors'>{JSON.stringify(context.rejectErrors)}</div>
            <button
              data-testid='set-error'
              onClick={() => context.setRejectErrors({ 'test-error': true })}
            >
              Set Error
            </button>
            <button data-testid='clear-errors' onClick={() => context.setRejectErrors({})}>
              Clear Errors
            </button>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestComponentClear />
        </TestWrapper>
      );

      // Set an error first
      act(() => {
        screen.getByTestId('set-error').click();
      });
      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{"test-error":true}');

      // Clear errors
      act(() => {
        screen.getByTestId('clear-errors').click();
      });
      expect(screen.getByTestId('reject-errors')).toHaveTextContent('{}');
    });
  });

  describe('constants validation', () => {
    it('should have correct file size limit', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const limitSize = parseInt(screen.getByTestId('limit-size').textContent || '0');
      expect(limitSize).toBe(5 * 1024 * 1024); // 5MB in bytes
    });

    it('should have correct file per upload limit', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const limitFilePerUpload = parseInt(
        screen.getByTestId('limit-file-per-upload').textContent || '0'
      );
      expect(limitFilePerUpload).toBe(1);
    });

    it('should include all required MIME types', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const mimeTypes = screen.getByTestId('allow-mime-types').textContent?.split(',') || [];

      // Check for image types
      expect(mimeTypes).toContain('image/png');
      expect(mimeTypes).toContain('image/jpg');
      expect(mimeTypes).toContain('image/jpeg');
      expect(mimeTypes).toContain('image/svg+xml');

      // Check for document types
      expect(mimeTypes).toContain('application/pdf');

      // Check for video types
      expect(mimeTypes).toContain('video/mp4');
      expect(mimeTypes).toContain('video/quicktime');
      expect(mimeTypes).toContain('video/x-msvideo');
      expect(mimeTypes).toContain('video/3gpp');
      expect(mimeTypes).toContain('video/webm');
      expect(mimeTypes).toContain('video/ogg');
      expect(mimeTypes).toContain('video/x-ms-wmv');
      expect(mimeTypes).toContain('video/x-matroska');
      expect(mimeTypes).toContain('video/avi');
      expect(mimeTypes).toContain('video/x-ms-asf');
    });
  });
});
