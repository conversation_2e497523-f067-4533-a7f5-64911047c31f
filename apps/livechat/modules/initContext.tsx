import { sendCustomEvent } from '@resola-ai/utils';
import { isEmptyObject } from '@tiptap/react';
import { useRouter } from 'next/router';
import { createContext, useContext, useEffect } from 'react';
import { CONVERSATION_LIST_DEFAULT_PAGE_SIZE } from '../constants';
import {
  DEFAULT_CONVERSATION_ASSIGNED,
  DEFAULT_CONVERSATION_STATUS,
} from '../constants/conversation';
import { useConversationContext } from './conversationContext';

const useInit = () => {
  const router = useRouter();
  const { refetchConversListByNewParams, setCurrentConversation } = useConversationContext();

  useEffect(() => {
    // set param and conversation at the start if query params is available
    if (router.query) {
      let team = router.query?.team as string;
      const currentAssigned = router.query?.assigned;
      let assigned: string | string[] | boolean = true;
      const status = router.query.status ?? DEFAULT_CONVERSATION_STATUS;

      if (!isEmptyObject(router.query)) {
        assigned = currentAssigned ?? DEFAULT_CONVERSATION_ASSIGNED;
      }

      sendCustomEvent('deca-livechat-init-context-send-event-setup-workspace', {
        status: status,
        assigned: currentAssigned === undefined && team ? currentAssigned : assigned,
        team: router.query?.team as string,
        bookmark: false,
      });

      if (team === 'undefined') {
        team = undefined;
      }

      if (typeof assigned === 'boolean') {
        assigned = assigned as boolean;
      } else if (typeof assigned === 'string') {
        assigned = assigned === 'true';
      }

      const requestParams = {
        ...router.query,
        status: status as any,
        assigned: (currentAssigned === undefined && team ? currentAssigned : assigned) as boolean,
        team: team,
        per_page: router.query.per_page
          ? parseInt(router.query.per_page as string)
          : CONVERSATION_LIST_DEFAULT_PAGE_SIZE,
      };
      refetchConversListByNewParams(requestParams);

      if (router.query.cId) {
        setCurrentConversation({
          id: router.query.cId as string,
        } as any);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {};
};

export type InitContextType = ReturnType<typeof useInit>;

const context = createContext<InitContextType | null>(null);

export const InitContextProvider: React.FC<any> = ({ children }) => {
  const value = useInit();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useInitContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useInitContext must be used inside InitContextProvider');
  }

  return value;
};
