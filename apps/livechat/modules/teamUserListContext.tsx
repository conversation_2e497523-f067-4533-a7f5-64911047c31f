import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { TeamUserTabType } from './Settings/TeamsAndUsers/types';
import { useRouter } from 'next/router';

const useTeamUserList = () => {
  const router = useRouter();
  const [teamUserListActiveTab, setTeamUserListActiveTab] = useState<TeamUserTabType>(null);
  const { asPath } = router;
  const isTeamUserListPage = useMemo(() => asPath.includes('team-user-list'), [asPath]);

  useEffect(() => {
    /* set default param for the team-user-list page */
    if (router.query && isTeamUserListPage) {
      const activeTab = router.query?.tab ?? 'team-list';
      setTeamUserListActiveTab(activeTab as TeamUserTabType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isTeamUserListPage]);

  return useMemo(
    () => ({
      teamUserListActiveTab,
      setTeamUserListActiveTab,
    }),
    [teamUserListActiveTab]
  );
};

export type TeamUserListContextType = ReturnType<typeof useTeamUserList>;

const context = createContext<TeamUserListContextType | null>(null);

export const TeamUserListContextProvider: React.FC<any> = ({ children }) => {
  const value = useTeamUserList();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useTeamUserListContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useTeamUserListContext must be used inside TeamUserListContextProvider');
  }

  return value;
};
