import { Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { createCustomEventListener } from '@resola-ai/utils';
import { IconArrowNarrowDown } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import { GLOBAL_REALTIME_EVENT_NAME, MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID } from '../../constants';
import { useConversationActionContext } from '../conversationActionContext';

const useStyle = createStyles((theme) => ({
  notificationButton: {
    backgroundColor: 'transparent',
    color: theme.colors.decaBlue[5],
    display: 'flex',
    alignItems: 'center',
    padding: '0',
    fontSize: rem(14),
    fontWeight: 500,
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: 'transparent',
      color: theme.colors.decaBlue[5],
      padding: '0',
      fontSize: rem(14),
      fontWeight: 500,
      border: 'none',
      cursor: 'pointer',
    },
  },
}));

export const NewMessageNotification = ({
  onNewMessageChange,
}: {
  onNewMessageChange?: (hasNewMessages: boolean) => void;
}) => {
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');
  const { currentConversationId } = useConversationActionContext();
  const [hasNewMessages, setHasNewMessages] = useState(false);

  const isScrolledToBottom = () => {
    // Find the message list container by looking for the element with the scroll point ID
    const messageList = document.getElementById(MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID)
      ?.parentElement as HTMLElement;
    if (!messageList) return true;

    const { scrollTop, scrollHeight, clientHeight } = messageList;
    // Consider "at bottom" if within 10px of the bottom
    return scrollHeight - scrollTop - clientHeight < 10;
  };

  const handleNewMessage = useCallback(
    (event: CustomEvent) => {
      const { data } = event.detail;
      if (data.type === 'message.new') {
        const message = data.data;

        // Only process if the message is from this conversation
        if (message.conversationId === currentConversationId) {
          const wasAtBottom = isScrolledToBottom();

          if (wasAtBottom) {
            // User was at bottom, auto-scroll to show the new message
            setTimeout(() => {
              const messageList = document.getElementById(MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID)
                ?.parentElement as HTMLElement;
              if (messageList) {
                messageList.scrollTo({
                  top: messageList.scrollHeight,
                  behavior: 'smooth',
                });
              }
            }, 150);
          } else {
            // User was not at bottom, show notification
            setHasNewMessages(true);
            onNewMessageChange?.(true);
          }
        }
      }
    },
    [currentConversationId, onNewMessageChange]
  );

  const handleScrollToBottom = () => {
    // Use a slight delay for smoother scroll when user clicks
    setTimeout(() => {
      const messageList = document.getElementById(MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID)
        ?.parentElement as HTMLElement;
      if (messageList) {
        messageList.scrollTo({
          top: messageList.scrollHeight,
          behavior: 'smooth',
        });
      }
    }, 50);
    setHasNewMessages(false);
    onNewMessageChange?.(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleScrollToBottom();
    }
  };

  useEffect(() => {
    const controller = new AbortController();

    createCustomEventListener(GLOBAL_REALTIME_EVENT_NAME, handleNewMessage, controller.signal);

    // Add scroll listener to hide notification when user scrolls to bottom
    const handleScroll = () => {
      if (hasNewMessages && isScrolledToBottom()) {
        setHasNewMessages(false);
        onNewMessageChange?.(false);
      }
    };

    // Find the scrollable container and add scroll listener
    const messageList = document.getElementById(MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID)
      ?.parentElement as HTMLElement;
    if (messageList) {
      messageList.addEventListener('scroll', handleScroll);
    }

    return () => {
      controller.abort();
      if (messageList) {
        messageList.removeEventListener('scroll', handleScroll);
      }
    };
  }, [handleNewMessage, hasNewMessages, onNewMessageChange]);

  return (
    <Transition
      keepMounted
      duration={400}
      mounted={hasNewMessages}
      timingFunction='ease'
      transition={'fade-up'}
    >
      {(transitionStyle) => (
        <div
          className={classes.notificationButton}
          onClick={handleScrollToBottom}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role='button'
          aria-label={t('new_message_received')}
          style={{
            ...transitionStyle,
          }}
        >
          <IconArrowNarrowDown size={20} style={{ marginRight: 4 }} />
          {t('new_message_received')}
        </div>
      )}
    </Transition>
  );
};
