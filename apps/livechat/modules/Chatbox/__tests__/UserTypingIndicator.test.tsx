// Mock the utils functions FIRST
jest.mock('@resola-ai/utils', () => ({
  createCustomEventListener: jest.fn(),
  sendCustomEvent: jest.fn(),
}));

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

import '@testing-library/jest-dom';
import React from 'react';

import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { act, render, screen } from '@testing-library/react';
import { UserTypingIndicator } from '../UserTypingIndicator';

// Mock the appContext
const mockUseAppContext = jest.fn();
jest.mock('../../appContext', () => ({
  useAppContext: () => mockUseAppContext(),
}));

// Mock the conversationActionContext
const mockUseConversationActionContext = jest.fn();
jest.mock('../../conversationActionContext', () => ({
  useConversationActionContext: () => mockUseConversationActionContext(),
}));

// Mock the constants
jest.mock('../../../constants', () => ({
  TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST: 'TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST',
  TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER: 'TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER',
  TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST: 'TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST',
  USER_TYPING_STATUS_HAPPENING: 'USER_TYPING_STATUS_HAPPENING',
  USER_TYPING_STATUS_STOP: 'USER_TYPING_STATUS_STOP',
}));

// Mock the grid constants
jest.mock('../../../constants/grid', () => ({
  MESSAGE_PERCENT: 80,
}));

// Mock the utils functions
jest.mock('../../../utils/scroll', () => ({
  scrollToMessageListBottom: jest.fn(),
}));

jest.mock('../../../utils/public', () => ({
  getPublicUrl: jest.fn((path) => `public${path}`),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Box: ({
    children,
    className,
    style,
    w,
  }: {
    children: React.ReactNode;
    className?: string;
    style?: any;
    w?: string;
  }) => (
    <div data-testid='box' data-classname={className} data-w={w} style={style}>
      {children}
    </div>
  ),
  Flex: ({
    children,
    w,
    gap,
    justify,
    direction,
    style,
  }: {
    children: React.ReactNode;
    w?: string;
    gap?: string;
    justify?: string;
    direction?: string;
    style?: any;
  }) => (
    <div
      data-testid='flex'
      data-w={w}
      data-gap={gap}
      data-justify={justify}
      data-direction={direction}
      style={style}
    >
      {children}
    </div>
  ),
  rem: (value: number) => `${value}rem`,
  Transition: ({
    children,
    mounted,
    duration,
    timingFunction,
    transition,
  }: {
    children: (style: any) => React.ReactNode;
    mounted: boolean;
    duration: number;
    timingFunction: string;
    transition: string;
  }) => (
    <div
      data-testid='transition'
      data-mounted={mounted}
      data-duration={duration}
      data-timing-function={timingFunction}
      data-transition={transition}
    >
      {mounted ? children({}) : null}
    </div>
  ),
}));

// Mock the emotion createStyles
jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      boxAvatar: 'box-avatar-class',
      boxContent: 'box-content-class',
      boxContentResponsive: 'box-content-responsive-class',
    },
  }),
}));

// Mock Next.js Image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({
    width,
    height,
    alt,
    src,
  }: {
    width: number;
    height: number;
    alt: string;
    src: string;
  }) => (
    <img
      data-testid='image'
      data-width={width}
      data-height={height}
      data-alt={alt}
      data-src={src}
    />
  ),
}));

// Mock the Preview component
jest.mock('../../../components/Preview', () => ({
  __esModule: true,
  default: ({
    children,
    showFileName,
    showDownloadButton,
    imageUrl,
  }: {
    children: React.ReactNode;
    showFileName: boolean;
    showDownloadButton: boolean;
    imageUrl: string;
  }) => (
    <div
      data-testid='preview'
      data-show-filename={showFileName}
      data-show-download-button={showDownloadButton}
      data-image-url={imageUrl}
    >
      {children}
    </div>
  ),
}));

describe('UserTypingIndicator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockUseAppContext.mockReturnValue({
      responsive: { responsiveScreen: false },
    });

    mockUseConversationActionContext.mockReturnValue({
      currentCustomer: { picture: 'test-avatar.jpg' },
      currentConversationId: 'test-conversation-id',
    });

    // Reset the mocked functions
    (createCustomEventListener as jest.Mock).mockReturnValue(jest.fn());
    (sendCustomEvent as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders nothing when not typing', () => {
    render(<UserTypingIndicator />);

    const transition = screen.getByTestId('transition');
    expect(transition).toHaveAttribute('data-mounted', 'false');
  });

  it('sets up event listeners on mount', () => {
    render(<UserTypingIndicator />);

    expect(createCustomEventListener).toHaveBeenCalledTimes(2);
    expect(createCustomEventListener).toHaveBeenCalledWith(
      'TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST',
      expect.any(Function),
      expect.any(Object)
    );
    expect(createCustomEventListener).toHaveBeenCalledWith(
      'TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER',
      expect.any(Function),
      expect.any(Object)
    );
  });

  it('sends initial status request after delay', () => {
    render(<UserTypingIndicator />);

    // Fast-forward timers to trigger the initial request
    act(() => {
      jest.advanceTimersByTime(200);
    });

    expect(sendCustomEvent).toHaveBeenCalledWith('TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST', {
      callerId: 'test-conversation-id',
      conversationId: 'test-conversation-id',
    });
  });

  it('shows typing indicator when typing status is happening', () => {
    render(<UserTypingIndicator />);

    // Get the event handler from the mock
    const call = (createCustomEventListener as jest.Mock).mock.calls[0];
    const eventHandler = call && call.length > 1 ? call[1] : undefined;
    if (typeof eventHandler === 'function') {
      act(() => {
        eventHandler({
          detail: {
            data: {
              data: [
                {
                  conversationId: 'test-conversation-id',
                  status: 'USER_TYPING_STATUS_HAPPENING',
                },
              ],
            },
          },
        });
      });
    }

    expect(screen.getByTestId('transition')).toBeInTheDocument();
    expect(screen.getByTestId('transition')).toHaveAttribute('data-mounted', 'true');
  });

  it('hides typing indicator when typing status stops', () => {
    render(<UserTypingIndicator />);

    // First set typing to true
    const typingEvent = new CustomEvent('test', {
      detail: {
        data: {
          data: [
            {
              conversationId: 'test-conversation-id',
              status: 'USER_TYPING_STATUS_HAPPENING',
            },
          ],
        },
      },
    });

    const call = (createCustomEventListener as jest.Mock).mock.calls[0];
    const eventHandler = call && call.length > 1 ? call[1] : undefined;
    if (typeof eventHandler === 'function') {
      act(() => {
        eventHandler(typingEvent);
      });
    }

    // Then stop typing
    const stopEvent = new CustomEvent('test', {
      detail: {
        data: {
          data: [
            {
              conversationId: 'test-conversation-id',
              status: 'USER_TYPING_STATUS_STOP',
            },
          ],
        },
      },
    });

    act(() => {
      eventHandler(stopEvent);
    });

    const transition = screen.getByTestId('transition');
    expect(transition).toHaveAttribute('data-mounted', 'false');
  });

  it('renders loading animation when typing status is happening', () => {
    render(<UserTypingIndicator />);

    // Trigger typing to show the component
    const typingEvent = new CustomEvent('test', {
      detail: {
        data: {
          data: [
            {
              conversationId: 'test-conversation-id',
              status: 'USER_TYPING_STATUS_HAPPENING',
            },
          ],
        },
      },
    });

    const call = (createCustomEventListener as jest.Mock).mock.calls[0];
    const eventHandler = call && call.length > 1 ? call[1] : undefined;
    if (typeof eventHandler === 'function') {
      act(() => {
        eventHandler(typingEvent);
      });
    }

    // Check that the loading animation image is rendered
    const loadingImage = screen.getByTestId('image');
    expect(loadingImage).toBeInTheDocument();
    expect(loadingImage).toHaveAttribute('data-alt', 'loading');
    expect(loadingImage).toHaveAttribute('data-src', 'public/images/animate3dots.svg');
  });

  it('applies responsive styles when responsiveScreen is true', () => {
    mockUseAppContext.mockReturnValue({
      responsive: { responsiveScreen: true },
    });

    render(<UserTypingIndicator />);

    // Trigger typing to show the component
    const typingEvent = new CustomEvent('test', {
      detail: {
        data: {
          data: [
            {
              conversationId: 'test-conversation-id',
              status: 'USER_TYPING_STATUS_HAPPENING',
            },
          ],
        },
      },
    });

    const call = (createCustomEventListener as jest.Mock).mock.calls[0];
    const eventHandler = call && call.length > 1 ? call[1] : undefined;
    if (typeof eventHandler === 'function') {
      act(() => {
        eventHandler(typingEvent);
      });
    }

    const flexElements = screen.getAllByTestId('flex');
    expect(flexElements[1]).toHaveAttribute('data-gap', '9.5rem');
  });

  it('cleans up timeouts and event listeners on unmount', () => {
    const { unmount } = render(<UserTypingIndicator />);

    unmount();

    // The cleanup should happen in the useEffect cleanup function
    // We can't directly test the cleanup, but we can ensure the component unmounts without errors
    expect(createCustomEventListener).toHaveBeenCalled();
  });
});
