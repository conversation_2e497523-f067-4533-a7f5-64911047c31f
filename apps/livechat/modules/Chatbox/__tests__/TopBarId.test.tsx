import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import TopBarId from '../TopBarId';

// Mock the appContext
const mockUseAppContext = jest.fn();
jest.mock('../../appContext', () => ({
  useAppContext: () => mockUseAppContext(),
}));

// Mock Mantine components
const Menu = ({ children }: { children: React.ReactNode }) => (
  <div data-testid='menu'>{children}</div>
);
Menu.Target = ({ children }: { children: React.ReactNode }) => (
  <div data-testid='menu-target'>{children}</div>
);
Menu.Dropdown = ({ children }: { children: React.ReactNode }) => (
  <div data-testid='menu-dropdown'>{children}</div>
);
Menu.Item = ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
  <div data-testid='menu-item' onClick={onClick}>
    {children}
  </div>
);

jest.mock('@mantine/core', () => ({
  CopyButton: ({
    children,
    value,
  }: {
    children: (props: { copy: () => void }) => React.ReactNode;
    value: string;
  }) => (
    <div data-testid='copy-button' data-value={value}>
      {children({ copy: jest.fn() })}
    </div>
  ),
  Menu,
  Text: ({
    children,
    className,
    sx,
    maw,
    truncate,
  }: {
    children: React.ReactNode;
    className?: string;
    sx?: any;
    maw?: string;
    truncate?: string;
  }) => (
    <span
      data-testid='text'
      data-classname={className}
      data-maw={maw}
      data-truncate={truncate}
      style={sx}
    >
      {children}
    </span>
  ),
}));

describe('TopBarId', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAppContext.mockReturnValue({
      responsiveScreen: false,
      responsive: { tabletScreen: false },
    });
  });

  it('renders with default props', () => {
    render(<TopBarId />);

    expect(screen.getByTestId('menu')).toBeInTheDocument();
    expect(screen.getByTestId('menu-target')).toBeInTheDocument();
    expect(screen.getByTestId('text')).toBeInTheDocument();
    expect(screen.getByText(/ID:/)).toBeInTheDocument();
  });

  it('renders with provided id', () => {
    render(<TopBarId id='123456789' />);

    expect(screen.getByText('ID: 12345')).toBeInTheDocument();
  });

  it('renders with custom textIdStyle', () => {
    render(<TopBarId id='123456789' textIdStyle='custom-style' />);

    expect(screen.getByTestId('text')).toHaveAttribute('data-classname', 'custom-style');
  });

  it('shows only "ID" on tablet screen', () => {
    mockUseAppContext.mockReturnValue({
      responsiveScreen: false,
      responsive: { tabletScreen: true },
    });

    render(<TopBarId id='123456789' />);

    expect(screen.getByText('ID')).toBeInTheDocument();
  });

  it('applies responsive styles when responsiveScreen is true', () => {
    mockUseAppContext.mockReturnValue({
      responsiveScreen: true,
      responsive: { tabletScreen: false },
    });

    render(<TopBarId id='123456789' />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-maw', '8vw');
    expect(textElement.style.fontSize).toBe('10px');
  });

  it('applies default styles when responsiveScreen is false', () => {
    mockUseAppContext.mockReturnValue({
      responsiveScreen: false,
      responsive: { tabletScreen: false },
    });

    render(<TopBarId id='123456789' />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-maw', 'auto');
    expect(textElement.style.fontSize).toBe('12px');
  });

  it('renders copy button with full id', () => {
    render(<TopBarId id='123456789' />);

    expect(screen.getByTestId('copy-button')).toHaveAttribute('data-value', '123456789');
  });

  it('handles empty id gracefully', () => {
    render(<TopBarId id='' />);

    expect(screen.getByText(/ID:/)).toBeInTheDocument();
  });

  it('handles undefined id gracefully', () => {
    render(<TopBarId id={undefined} />);

    expect(screen.getByText(/ID:/)).toBeInTheDocument();
  });
});
