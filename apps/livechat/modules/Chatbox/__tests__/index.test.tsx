import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import ChatBox from '../index';

// Mock any dependencies if needed
jest.mock('react', () => ({
  ...jest.requireActual('react'),
}));

describe('ChatBox', () => {
  it('renders without crashing', () => {
    render(<ChatBox params={{}} />);
    expect(screen.getByText('ChatBox')).toBeInTheDocument();
  });

  it('renders with h2 heading', () => {
    render(<ChatBox params={{}} />);
    const heading = screen.getByRole('heading', { level: 2 });
    expect(heading).toHaveTextContent('ChatBox');
  });

  it('accepts params prop', () => {
    const testParams = { test: 'value' };
    render(<ChatBox params={testParams} />);
    expect(screen.getByText('ChatBox')).toBeInTheDocument();
  });
});
