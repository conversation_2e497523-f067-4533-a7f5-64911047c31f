import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import TextRenderer from '../MessageHolders/TextRenderer';

// Mock the models
jest.mock('@resola-ai/models', () => ({
  IMessage: {},
}));

// Mock the utils function
jest.mock('../../../utils/message_helper', () => ({
  convertTextLineEmojisSupport: ({ originalText, emojis }: { originalText: string; emojis: any }) =>
    `converted-${originalText}-${JSON.stringify(emojis)}`,
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Text: ({
    children,
    className,
    sx,
    dangerouslySetInnerHTML,
  }: {
    children?: React.ReactNode;
    className?: string;
    sx?: any;
    dangerouslySetInnerHTML?: { __html: string };
  }) => (
    <div data-testid='text' data-classname={className} style={sx}>
      {dangerouslySetInnerHTML ? dangerouslySetInnerHTML.__html : children}
    </div>
  ),
}));

// Mock the emotion createStyles
jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      textBox: 'text-box-class',
    },
  }),
}));

describe('TextRenderer', () => {
  const mockMessage: any = {
    id: '1',
    conversationId: 'conv1',
    created: '2024-01-15T10:30:00Z',
    sender: 'user',
    data: {
      text: 'Hello world',
      emojis: [{ id: 1, name: 'smile' }],
    },
  };

  it('renders without crashing', () => {
    render(<TextRenderer message={mockMessage} />);

    expect(screen.getByTestId('text')).toBeInTheDocument();
  });

  it('applies correct styles', () => {
    render(<TextRenderer message={mockMessage} />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-classname', 'text-box-class');
    expect(textElement.style.whiteSpace).toBe('pre-wrap');
    expect(textElement.style.overflowWrap).toBe('break-word');
  });

  it('converts text with emoji support', () => {
    render(<TextRenderer message={mockMessage} />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveTextContent('converted-Hello world-[{"id":1,"name":"smile"}]');
  });

  it('applies responsive font size when responsive is true', () => {
    render(<TextRenderer message={mockMessage} responsive={true} />);

    const textElement = screen.getByTestId('text');
    expect(textElement.style.fontSize).toBe('12px');
  });

  it('does not apply responsive font size when responsive is false', () => {
    render(<TextRenderer message={mockMessage} responsive={false} />);

    const textElement = screen.getByTestId('text');
    expect(textElement.style.fontSize).toBe('');
  });

  it('handles message without text', () => {
    const messageWithoutText: any = {
      id: '2',
      conversationId: 'conv1',
      created: '2024-01-15T10:30:00Z',
      sender: 'user',
      data: {
        text: '',
        emojis: [],
      },
    };

    render(<TextRenderer message={messageWithoutText} />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveTextContent('converted--[]');
  });

  it('handles message without emojis', () => {
    const messageWithoutEmojis: any = {
      id: '3',
      conversationId: 'conv1',
      created: '2024-01-15T10:30:00Z',
      sender: 'user',
      data: {
        text: 'Hello world',
        emojis: null,
      },
    };

    render(<TextRenderer message={messageWithoutEmojis} />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveTextContent('converted-Hello world-null');
  });

  it('handles message with undefined data', () => {
    const messageWithUndefinedData: any = {
      id: '4',
      conversationId: 'conv1',
      created: '2024-01-15T10:30:00Z',
      sender: 'user',
      data: undefined,
    };

    render(<TextRenderer message={messageWithUndefinedData} />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveTextContent('converted-undefined-undefined');
  });
});
