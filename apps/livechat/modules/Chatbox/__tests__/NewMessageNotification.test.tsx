import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import '@testing-library/jest-dom';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import {
  GLOBAL_REALTIME_EVENT_NAME,
  MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID,
} from '../../../constants';
import { NewMessageNotification } from '../NewMessageNotification';

// Mock dependencies
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => (key === 'new_message_received' ? 'New message received' : key),
  }),
}));

jest.mock('../../conversationActionContext', () => ({
  useConversationActionContext: () => ({
    currentConversationId: 'test-conversation-id',
  }),
}));

jest.mock('@resola-ai/utils', () => ({
  createCustomEventListener: jest.fn(),
}));

// Mock DOM elements
const mockScrollTo = jest.fn();
const mockAddEventListener = jest.fn();
const mockRemoveEventListener = jest.fn();

const mockMessageList = {
  scrollTop: 0,
  scrollHeight: 1000,
  clientHeight: 500,
  scrollTo: mockScrollTo,
  addEventListener: mockAddEventListener,
  removeEventListener: mockRemoveEventListener,
};

// Mock document.getElementById
const originalGetElementById = document.getElementById;
const mockGetElementById = jest.fn();

const customTheme = {
  colors: {
    decaBlue: [
      '#e3f2fd',
      '#90caf9',
      '#64b5f6',
      '#42a5f5',
      '#2196f3',
      '#1e88e5',
      '#1976d2',
      '#1565c0',
      '#0d47a1',
      '#08306b',
    ],
  },
} as any;

describe('NewMessageNotification', () => {
  let mockEventListener: (event: CustomEvent) => void;
  let mockScrollHandler: () => void;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Setup DOM mocks
    document.getElementById = mockGetElementById;
    mockGetElementById.mockImplementation((id: string) => {
      if (id === MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID) {
        return { parentElement: mockMessageList } as any;
      }
      return null;
    });

    // Setup event listener mock
    const { createCustomEventListener } = require('@resola-ai/utils');
    createCustomEventListener.mockImplementation((eventName: string, callback: any) => {
      mockEventListener = callback;
      return () => {}; // Return cleanup function
    });

    // Setup scroll listener mock
    mockAddEventListener.mockImplementation((event: string, handler: any) => {
      if (event === 'scroll') {
        mockScrollHandler = handler;
      }
    });
  });

  afterEach(() => {
    jest.useRealTimers();
    document.getElementById = originalGetElementById;
  });

  const renderComponent = (props = {}) => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider theme={customTheme as any}>
          <NewMessageNotification {...props} />
        </MantineProvider>
      </MantineEmotionProvider>
    );
  };

  const triggerNewMessage = (conversationId = 'test-conversation-id') => {
    act(() => {
      mockEventListener({
        detail: {
          data: {
            type: 'message.new',
            data: {
              conversationId,
            },
          },
        },
      } as CustomEvent);
    });
  };

  const simulateScrollToBottom = () => {
    mockMessageList.scrollTop = 490; // Within 10px of bottom
    act(() => {
      mockScrollHandler();
    });
  };

  const simulateScrollAwayFromBottom = () => {
    mockMessageList.scrollTop = 100;
    act(() => {
      mockScrollHandler();
    });
  };

  describe('Initial render', () => {
    it('should not show notification initially', () => {
      renderComponent();
      const notification = screen.queryByText('New message received');
      expect(notification).toBeInTheDocument();
      expect(notification).toHaveStyle('display: none');
    });

    it('should set up event listeners on mount', () => {
      renderComponent();
      const { createCustomEventListener } = require('@resola-ai/utils');
      expect(createCustomEventListener).toHaveBeenCalledWith(
        GLOBAL_REALTIME_EVENT_NAME,
        expect.any(Function),
        expect.any(Object)
      );
    });

    it('should add scroll listener to message list', () => {
      renderComponent();
      expect(mockAddEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
    });
  });

  describe('Message handling', () => {
    it('should show notification when new message arrives and user is not at bottom', async () => {
      // Mock user not at bottom
      mockMessageList.scrollTop = 100;

      renderComponent();
      triggerNewMessage();

      // Wait for transition to complete
      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });
    });

    it('should not show notification for messages from other conversations', () => {
      renderComponent();
      triggerNewMessage('other-conversation-id');

      act(() => {
        jest.advanceTimersByTime(400);
      });

      const notification = screen.queryByText('New message received');
      expect(notification).toBeInTheDocument();
      expect(notification).toHaveStyle('display: none');
    });

    it('should not show notification for non-message events', () => {
      renderComponent();

      act(() => {
        mockEventListener({
          detail: {
            data: {
              type: 'other.event',
              data: {},
            },
          },
        } as CustomEvent);
      });

      act(() => {
        jest.advanceTimersByTime(400);
      });

      const notification = screen.queryByText('New message received');
      expect(notification).toBeInTheDocument();
      expect(notification).toHaveStyle('display: none');
    });
  });

  describe('Click handling', () => {
    it('should scroll to bottom and hide notification when clicked', async () => {
      // Show notification first
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      // Click notification
      fireEvent.click(screen.getByText('New message received'));

      // Wait for scroll delay
      act(() => {
        jest.advanceTimersByTime(50);
      });

      expect(mockScrollTo).toHaveBeenCalledWith({
        top: 1000,
        behavior: 'smooth',
      });

      // Wait for transition to hide
      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        const notification = screen.queryByText('New message received');
        expect(notification).toBeInTheDocument();
        expect(notification).toHaveStyle('display: none');
      });
    });
  });

  describe('Keyboard accessibility', () => {
    it('should handle Enter key press', async () => {
      // Show notification first
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      const notification = screen.getByText('New message received');
      fireEvent.keyDown(notification, { key: 'Enter' });

      act(() => {
        jest.advanceTimersByTime(50);
      });

      expect(mockScrollTo).toHaveBeenCalledWith({
        top: 1000,
        behavior: 'smooth',
      });
    });

    it('should handle Space key press', async () => {
      // Show notification first
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      const notification = screen.getByText('New message received');
      fireEvent.keyDown(notification, { key: ' ' });

      act(() => {
        jest.advanceTimersByTime(50);
      });

      expect(mockScrollTo).toHaveBeenCalledWith({
        top: 1000,
        behavior: 'smooth',
      });
    });

    it('should not handle other key presses', async () => {
      // Show notification first
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      const notification = screen.getByText('New message received');
      fireEvent.keyDown(notification, { key: 'Tab' });

      expect(mockScrollTo).not.toHaveBeenCalled();
    });
  });

  describe('Scroll handling', () => {
    it('should not hide notification when user scrolls but not to bottom', async () => {
      // Show notification first
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      // Simulate scroll but not to bottom
      simulateScrollAwayFromBottom();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });
    });
  });

  describe('Callback handling', () => {
    it('should call onNewMessageChange when notification appears', () => {
      const mockOnNewMessageChange = jest.fn();
      mockMessageList.scrollTop = 100;

      renderComponent({ onNewMessageChange: mockOnNewMessageChange });
      triggerNewMessage();

      expect(mockOnNewMessageChange).toHaveBeenCalledWith(true);
    });

    it('should call onNewMessageChange when notification is dismissed', async () => {
      const mockOnNewMessageChange = jest.fn();
      mockMessageList.scrollTop = 100;

      renderComponent({ onNewMessageChange: mockOnNewMessageChange });
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        expect(screen.getByText('New message received')).toBeInTheDocument();
      });

      // Click to dismiss
      fireEvent.click(screen.getByText('New message received'));

      expect(mockOnNewMessageChange).toHaveBeenCalledWith(false);
    });

    it('should not call onNewMessageChange if not provided', () => {
      mockMessageList.scrollTop = 100;

      renderComponent();
      triggerNewMessage();

      // Should not throw error
      expect(screen.queryByText('New message received')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility attributes', async () => {
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        const notification = screen.getByText('New message received');
        expect(notification).toHaveAttribute('role', 'button');
        expect(notification).toHaveAttribute('tabIndex', '0');
        expect(notification).toHaveAttribute('aria-label', 'New message received');
      });
    });

    it('should be focusable when visible', async () => {
      mockMessageList.scrollTop = 100;
      renderComponent();
      triggerNewMessage();

      act(() => {
        jest.advanceTimersByTime(400);
      });

      await waitFor(() => {
        const notification = screen.getByText('New message received');
        notification.focus();
        expect(notification).toHaveFocus();
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle missing message list element gracefully', () => {
      mockGetElementById.mockReturnValue(null);

      renderComponent();
      triggerNewMessage();

      // Should not throw error
      const notification = screen.queryByText('New message received');
      expect(notification).toBeInTheDocument();
      expect(notification).toHaveStyle('display: none');
    });

    it('should handle missing parent element gracefully', () => {
      mockGetElementById.mockReturnValue({ parentElement: null });

      renderComponent();
      triggerNewMessage();

      // Should not throw error
      const notification = screen.queryByText('New message received');
      expect(notification).toBeInTheDocument();
      expect(notification).toHaveStyle('display: none');
    });

    it('should handle scroll position calculation edge cases', () => {
      // Test with very small scroll container
      mockMessageList.scrollHeight = 100;
      mockMessageList.clientHeight = 100;
      mockMessageList.scrollTop = 0;

      renderComponent();
      triggerNewMessage();

      // Should auto-scroll when at bottom
      act(() => {
        jest.advanceTimersByTime(150);
      });

      expect(mockScrollTo).toHaveBeenCalledWith({
        top: 100,
        behavior: 'smooth',
      });
    });
  });

  describe('Cleanup', () => {
    it('should clean up event listeners on unmount', () => {
      const { unmount } = renderComponent();

      unmount();

      expect(mockRemoveEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
    });
  });
});
