import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import MessageEditing from '../MessageEditing';

// Mock the Mantine hooks
jest.mock('@mantine/hooks', () => ({
  getHotkeyHandler: (handlers: any[]) => jest.fn(),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  ActionIcon: ({
    children,
    onClick,
    variant,
    size,
    disabled,
  }: {
    children?: React.ReactNode;
    onClick?: () => void;
    variant?: string;
    size?: string;
    disabled?: boolean;
  }) => (
    <button
      data-testid='action-icon'
      data-variant={variant}
      data-size={size}
      data-disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  ),
  Group: ({ children, justify, p }: { children: React.ReactNode; justify: string; p: string }) => (
    <div data-testid='group' data-justify={justify} data-p={p}>
      {children}
    </div>
  ),
  Stack: ({
    children,
    sx,
    justify,
    p,
  }: {
    children: React.ReactNode;
    sx?: any;
    justify: string;
    p: number;
  }) => (
    <div data-testid='stack' data-justify={justify} data-p={p} style={sx}>
      {children}
    </div>
  ),
  TextInput: ({
    value,
    onChange,
    sx,
    placeholder,
    rightSection,
    onKeyDown,
  }: {
    value: string;
    onChange?: (event: any) => void;
    sx?: any;
    placeholder?: string;
    rightSection?: React.ReactNode;
    onKeyDown?: (event: any) => void;
  }) => (
    <div data-testid='text-input' style={sx}>
      <input
        data-testid='input'
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        onKeyDown={onKeyDown}
      />
      {rightSection && <div data-testid='right-section'>{rightSection}</div>}
    </div>
  ),
}));

describe('MessageEditing', () => {
  it('renders without crashing', () => {
    render(<MessageEditing />);

    expect(screen.getByTestId('stack')).toBeInTheDocument();
    expect(screen.getByTestId('group')).toBeInTheDocument();
    expect(screen.getByTestId('text-input')).toBeInTheDocument();
    expect(screen.getByTestId('input')).toBeInTheDocument();
  });

  it('renders with correct placeholder', () => {
    render(<MessageEditing />);

    expect(screen.getByTestId('input')).toHaveAttribute('placeholder', 'Say something nice . . . ');
  });

  it('renders action icons', () => {
    render(<MessageEditing />);

    const actionIcons = screen.getAllByTestId('action-icon');
    expect(actionIcons).toHaveLength(2); // One in rightSection, one standalone
  });

  it('handles input changes', () => {
    render(<MessageEditing />);

    const input = screen.getByTestId('input');
    fireEvent.change(input, { target: { value: 'test message' } });

    expect(input).toHaveValue('test message');
  });

  it('renders with correct stack props', () => {
    render(<MessageEditing />);

    const stack = screen.getByTestId('stack');
    expect(stack).toHaveAttribute('data-justify', 'center');
    expect(stack).toHaveAttribute('data-p', '0');
  });

  it('renders with correct group props', () => {
    render(<MessageEditing />);

    const group = screen.getByTestId('group');
    expect(group).toHaveAttribute('data-justify', 'flex-end');
    expect(group).toHaveAttribute('data-p', 'xs');
  });

  it('renders right section with action icon', () => {
    render(<MessageEditing />);

    expect(screen.getByTestId('right-section')).toBeInTheDocument();
    expect(screen.getAllByTestId('action-icon')).toHaveLength(2);
  });

  it('accepts props', () => {
    const testProps = { test: 'value', another: 'prop' };
    render(<MessageEditing {...testProps} />);

    // Should render without errors
    expect(screen.getByTestId('stack')).toBeInTheDocument();
  });
});
