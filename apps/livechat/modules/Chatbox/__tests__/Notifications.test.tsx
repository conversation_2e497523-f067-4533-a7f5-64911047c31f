import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import NotificationArea from '../Notifications';

describe('NotificationArea', () => {
  it('renders without crashing', () => {
    render(<NotificationArea />);
    expect(screen.getByText('Notification Area')).toBeInTheDocument();
  });

  it('renders the correct text content', () => {
    render(<NotificationArea />);
    expect(screen.getByText('Notification Area')).toBeInTheDocument();
  });

  it('renders as a fragment', () => {
    const { container } = render(<NotificationArea />);
    // The component returns a React Fragment, so the first child should be the text node
    expect(container.firstChild?.textContent).toBe('Notification Area');
  });
});
