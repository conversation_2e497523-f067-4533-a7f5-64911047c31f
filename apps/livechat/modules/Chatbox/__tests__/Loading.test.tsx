import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import Loading from '../Loading';

// Mock the messageContext
const mockUseMessageContext = jest.fn();
jest.mock('../../messageContext', () => ({
  useMessageContext: () => mockUseMessageContext(),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Center: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='center'>{children}</div>
  ),
  Loader: ({ type, color }: { type: string; color: string }) => (
    <div data-testid='loader' data-type={type} data-color={color}>
      Loading...
    </div>
  ),
}));

describe('Loading', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loader when loading is true', () => {
    mockUseMessageContext.mockReturnValue({ loading: true });

    render(<Loading />);

    expect(screen.getByTestId('center')).toBeInTheDocument();
    expect(screen.getByTestId('loader')).toBeInTheDocument();
    expect(screen.getByTestId('loader')).toHaveAttribute('data-type', 'dots');
    expect(screen.getByTestId('loader')).toHaveAttribute('data-color', 'navy.0');
  });

  it('renders nothing when loading is false', () => {
    mockUseMessageContext.mockReturnValue({ loading: false });

    const { container } = render(<Loading />);

    expect(container.firstChild).toBeNull();
  });

  it('renders nothing when loading is undefined', () => {
    mockUseMessageContext.mockReturnValue({ loading: undefined });

    const { container } = render(<Loading />);

    expect(container.firstChild).toBeNull();
  });
});
