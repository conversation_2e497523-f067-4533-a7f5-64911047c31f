import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import DateHolder from '../MessageHolders/DateHolder';

// Mock the appContext
const mockUseAppContext = jest.fn();
jest.mock('../../appContext', () => ({
  useAppContext: () => mockUseAppContext(),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Text: ({
    children,
    ta,
    size,
    fw,
    color,
  }: {
    children: React.ReactNode;
    ta: string;
    size: string;
    fw: number;
    color: string;
  }) => (
    <span data-testid='text' data-ta={ta} data-size={size} data-fw={fw} data-color={color}>
      {children}
    </span>
  ),
}));

describe('DateHolder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAppContext.mockReturnValue({
      responsiveScreen: false,
    });
  });

  it('renders with provided date', () => {
    render(<DateHolder date='2024-01-15' />);

    expect(screen.getByTestId('text')).toBeInTheDocument();
    expect(screen.getByText('2024-01-15')).toBeInTheDocument();
  });

  it('applies correct default styles', () => {
    render(<DateHolder date='2024-01-15' />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-ta', 'center');
    expect(textElement).toHaveAttribute('data-size', '12px');
    expect(textElement).toHaveAttribute('data-fw', '400');
    expect(textElement).toHaveAttribute('data-color', '#5C5F66');
  });

  it('applies responsive styles when responsiveScreen is true', () => {
    mockUseAppContext.mockReturnValue({
      responsiveScreen: true,
    });

    render(<DateHolder date='2024-01-15' />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-size', '10px');
  });

  it('applies default styles when responsiveScreen is false', () => {
    mockUseAppContext.mockReturnValue({
      responsiveScreen: false,
    });

    render(<DateHolder date='2024-01-15' />);

    const textElement = screen.getByTestId('text');
    expect(textElement).toHaveAttribute('data-size', '12px');
  });

  it('handles empty date string', () => {
    render(<DateHolder date='' />);

    expect(screen.getByTestId('text')).toBeInTheDocument();
  });

  it('handles long date strings', () => {
    const longDate = 'January 15, 2024 at 3:30 PM';
    render(<DateHolder date={longDate} />);

    expect(screen.getByText(longDate)).toBeInTheDocument();
  });
});
