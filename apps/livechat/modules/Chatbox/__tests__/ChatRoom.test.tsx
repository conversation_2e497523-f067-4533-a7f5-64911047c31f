import '@testing-library/jest-dom';
import { act, render, screen } from '@testing-library/react';
import React from 'react';
import ChatRoom from '../ChatRoom';

// Mock the conversationActionContext
const mockUseConversationActionContext = jest.fn();
jest.mock('../../conversationActionContext', () => ({
  useConversationActionContext: () => mockUseConversationActionContext(),
}));

// Mock the utils functions
jest.mock('@resola-ai/utils', () => ({
  createCustomEventListener: () => jest.fn(),
  scrollToMessageListBottom: jest.fn(),
}));

// Mock the constants
jest.mock('../../../constants', () => ({
  CHAT_ROOM_MODULE_HEIGHT: '500px',
  NAVBAR_ID_NAME: 'navbar-id',
  SELF_ASSIGN_ID_NAME: 'self-assign-id',
  TEXT_EDITOR_ID_NAME: 'text-editor-id',
}));

// Mock the utils functions
jest.mock('../../../utils/scroll', () => ({
  scrollToMessageListBottom: jest.fn(),
}));

// Mock Mantine components and hooks
const mockUseViewportSize = jest.fn();
jest.mock('@mantine/hooks', () => ({
  useViewportSize: () => mockUseViewportSize(),
}));

jest.mock('@mantine/core', () => ({
  Flex: ({
    children,
    className,
    sx,
  }: {
    children: React.ReactNode;
    className?: string;
    sx?: any;
  }) => (
    <div data-testid='flex' data-classname={className} style={sx}>
      {children}
    </div>
  ),
}));

jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      flexStyle: 'flex-style-class',
    },
  }),
}));

// Mock the UI components
jest.mock('@resola-ai/ui', () => ({
  Show: ({ children, condition }: { children: React.ReactNode; condition: boolean }) => (
    <div data-testid='show' data-condition={condition}>
      {condition ? children : null}
    </div>
  ),
}));

// Mock the child components
jest.mock('../ChatMessagesArea', () => ({
  __esModule: true,
  default: ({ height }: { height: number }) => (
    <div data-testid='chat-messages-area' data-height={height}>
      ChatMessagesArea
    </div>
  ),
}));

jest.mock('../TopBar', () => ({
  __esModule: true,
  default: () => <div data-testid='top-bar'>TopBar</div>,
}));

jest.mock('../../TextEditor', () => ({
  __esModule: true,
  default: ({ lang }: { lang: string }) => <div data-testid='text-editor' data-lang={lang} />,
}));

jest.mock('../../SelfAssign', () => ({
  __esModule: true,
  default: ({ lang }: { lang: string }) => <div data-testid='self-assign' data-lang={lang} />,
}));

// Mock document.getElementById
const mockGetElementById = jest.fn();
Object.defineProperty(document, 'getElementById', {
  value: mockGetElementById,
  writable: true,
});

describe('ChatRoom', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: false,
      isCompleted: false,
      haveAssignee: false,
      isWrapUp: false,
    });

    mockUseViewportSize.mockReturnValue({
      height: 800,
    });

    // Mock DOM elements
    mockGetElementById.mockImplementation((id) => {
      if (id === 'navbar-id') return { clientHeight: 60 };
      if (id === 'self-assign-id') return { clientHeight: 40 };
      if (id === 'text-editor-id') return { clientHeight: 100 };
      return null;
    });
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders all main components', () => {
    render(<ChatRoom lang='en' />);

    expect(screen.getByTestId('top-bar')).toBeInTheDocument();
    expect(screen.getByTestId('flex')).toBeInTheDocument();
    expect(screen.getByTestId('chat-messages-area')).toBeInTheDocument();
  });

  it('calculates correct chat message area height', () => {
    render(<ChatRoom lang='en' />);

    // height - 60 - selfAssignHeight - textEditorHeight - navbarHeight
    // 800 - 60 - 40 - 100 - 60 = 540
    expect(screen.getByTestId('chat-messages-area')).toHaveAttribute('data-height', '540');
  });

  it('shows TextEditor when user is assignee and conversation is not completed', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: true,
      isCompleted: false,
      haveAssignee: true,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    const showElements = screen.getAllByTestId('show');
    expect(showElements.some((el) => el.getAttribute('data-condition') === 'true')).toBe(true);
    expect(screen.getByTestId('text-editor')).toBeInTheDocument();
  });

  it('hides TextEditor when user is not assignee', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: false,
      isCompleted: false,
      haveAssignee: true,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    const showElements = screen.getAllByTestId('show');
    expect(showElements.every((el) => el.getAttribute('data-condition') === 'false')).toBe(true);
    expect(screen.queryByTestId('text-editor')).not.toBeInTheDocument();
  });

  it('hides TextEditor when conversation is completed', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: true,
      isCompleted: true,
      haveAssignee: true,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    const showElements = screen.getAllByTestId('show');
    expect(showElements.every((el) => el.getAttribute('data-condition') === 'false')).toBe(true);
    expect(screen.queryByTestId('text-editor')).not.toBeInTheDocument();
  });

  it('hides TextEditor when conversation is in wrap up', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: true,
      isCompleted: false,
      haveAssignee: true,
      isWrapUp: true,
    });

    render(<ChatRoom lang='en' />);

    const showElements = screen.getAllByTestId('show');
    expect(showElements.every((el) => el.getAttribute('data-condition') === 'false')).toBe(true);
    expect(screen.queryByTestId('text-editor')).not.toBeInTheDocument();
  });

  it('shows SelfAssign when no assignee and not completed', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: false,
      isCompleted: false,
      haveAssignee: false,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    expect(screen.getByTestId('self-assign')).toBeInTheDocument();
  });

  it('hides SelfAssign when there is an assignee', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: false,
      isCompleted: false,
      haveAssignee: true,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    expect(screen.queryByTestId('self-assign')).not.toBeInTheDocument();
  });

  it('hides SelfAssign when conversation is completed', () => {
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: false,
      isCompleted: true,
      haveAssignee: false,
      isWrapUp: false,
    });

    render(<ChatRoom lang='en' />);

    expect(screen.queryByTestId('self-assign')).not.toBeInTheDocument();
  });

  it('passes lang prop to child components', () => {
    render(<ChatRoom lang='es' />);

    // Trigger conditions to show components
    mockUseConversationActionContext.mockReturnValue({
      isTheAssignee: true,
      isCompleted: false,
      haveAssignee: false,
      isWrapUp: false,
    });

    render(<ChatRoom lang='es' />);

    expect(screen.getByTestId('text-editor')).toHaveAttribute('data-lang', 'es');
    const selfAssigns = screen.getAllByTestId('self-assign');
    expect(selfAssigns.some((el) => el.getAttribute('data-lang') === 'es')).toBe(true);
  });

  it('applies correct flex styles', () => {
    render(<ChatRoom lang='en' />);

    const flexElement = screen.getByTestId('flex');
    expect(flexElement).toHaveAttribute('data-classname', 'flex-style-class');
    expect(flexElement.style.maxHeight).toBe('500px');
    expect(flexElement.style.height).toBe('500px');
  });

  it('handles missing DOM elements gracefully', () => {
    mockGetElementById.mockReturnValue(null);

    render(<ChatRoom lang='en' />);

    // Should still render without errors
    expect(screen.getByTestId('chat-messages-area')).toBeInTheDocument();
    expect(screen.getByTestId('chat-messages-area')).toHaveAttribute('data-height', 'NaN'); // 800 - 60 - 0 - 0 - 60
  });
});
