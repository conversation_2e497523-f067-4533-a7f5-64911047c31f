import { Button, Center, Flex, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React, { useState } from 'react';
import MessageIconSvg from '../../components/MessageIconSvg';
import { MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID } from '../../constants';
import AiSummaryContent from '../AiSummary/AiSummaryContent';
import { useAppContext } from '../appContext';
import { useConversationActionContext } from '../conversationActionContext';
import { useOperationSettingContext } from '../operationSettingContext';
import MessagesHistoryList from './MessageHolders/MessageHistoryList';
import { NewMessageNotification } from './NewMessageNotification';
import { UserTypingIndicator } from './UserTypingIndicator';

const useStyle = createStyles(() => ({
  flexContainer: {
    borderTop: 'none',
    borderBottom: 'none',
    flexGrow: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  scrollStyle: {
    overflow: 'auto',
    marginRight: '-10px',
    paddingRight: '20px',
    paddingLeft: '20px',
    paddingTop: '10px',
    overscrollBehavior: 'contain',
  },
  flexList: {
    height: '100%',
    overflowY: 'auto',
    overflowX: 'hidden',
    flexDirection: 'column',
    marginRight: '-10px',
    paddingRight: '20px',
    paddingTop: '10px',
    paddingBottom: '80px',
    overscrollBehavior: 'contain',
    scrollbarWidth: 'none' /* Hides scrollbar in Firefox */,
    msOverflowStyle: 'none',
    '::-webkit-scrollbar': {
      display: 'none',
    },
    justifyContent: 'start',
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40px',
    backgroundColor: '#FFFFFF',
    display: 'flex',
    alignItems: 'center',
    padding: '0 20px',
    zIndex: 100,
  },
  leftContainer: {
    display: 'flex',
    justifyContent: 'flex-start',
    flex: 1,
  },
  rightContainer: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
}));

interface Props {
  height: number;
}

const ChatMessagesArea: React.FC<Props> = ({ height }) => {
  const { classes } = useStyle();
  const { responsiveScreen } = useAppContext();
  const { preferences } = useOperationSettingContext();
  const { enableCreateNewConversation = false } = preferences;
  const {
    isCompleted,
    visibleConfirmModal,
    closeConfirmModal,
    confirmMoveToOtherConversation,
    handleCreateConversationFromEndOne,
  } = useConversationActionContext();

  const [isTyping, setIsTyping] = useState(false);
  const [hasNewMessages, setHasNewMessages] = useState(false);

  const shouldShowBottomBar = isTyping || hasNewMessages;

  return (
    <>
      <Flex
        gap='md'
        h={height}
        p={'0 10px'}
        mih={height}
        wrap='nowrap'
        justify='center'
        direction='column'
        className={classes.flexContainer}
      >
        <Flex
          className={classes.flexList}
          style={{ paddingInline: responsiveScreen ? rem(10) : undefined }}
        >
          <MessagesHistoryList />
          <AiSummaryContent />
          <div id={MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID}></div>
        </Flex>
        <div
          className={classes.bottomBar}
          style={{ display: shouldShowBottomBar ? 'flex' : 'none' }}
        >
          <div className={classes.leftContainer}>
            <UserTypingIndicator onTypingChange={setIsTyping} />
          </div>
          <div className={classes.rightContainer}>
            <NewMessageNotification onNewMessageChange={setHasNewMessages} />
          </div>
        </div>
        <ButtonCreateNewConversation
          show={enableCreateNewConversation && isCompleted}
          onClick={handleCreateConversationFromEndOne}
        />
      </Flex>
      <ModalConfirmMoveToOtherConversation
        visibleConfirmModal={visibleConfirmModal}
        closeConfirmModal={closeConfirmModal}
        confirmMoveToOtherConversation={confirmMoveToOtherConversation}
      />
    </>
  );
};

export default ChatMessagesArea;

function ButtonCreateNewConversation({ show, onClick }: { show: boolean; onClick: () => void }) {
  const { t } = useTranslate('workspace');
  return (
    <Center my={'lg'} h={'36px'} style={{ display: show ? 'flex' : 'none' }}>
      <Button color='indigo.1' onClick={onClick}>
        <MessageIconSvg color={'#4C6EF5'} />
        <Text ml={'md'} fw={700} size={rem(14)} color='indigo.6'>
          {t('create_new_conversation')}
        </Text>
      </Button>
    </Center>
  );
}

function ModalConfirmMoveToOtherConversation({
  visibleConfirmModal,
  closeConfirmModal,
  confirmMoveToOtherConversation,
}: {
  visibleConfirmModal: boolean;
  confirmMoveToOtherConversation: () => void;
  closeConfirmModal: () => void;
}) {
  const { t } = useTranslate('workspace');
  return (
    <Modal
      opened={visibleConfirmModal}
      centered
      withCloseButton={false}
      onClose={closeConfirmModal}
    >
      <Modal.Header pt={0} pr={0}>
        <Modal.CloseButton iconSize={30} color='dark.7' />
      </Modal.Header>
      <Modal.Body>
        <Center>
          <Text style={{ whiteSpace: 'pre-wrap', textAlign: 'center' }}>
            {t('confirm_move_to_new_conversation')}
          </Text>
        </Center>
        <Center mt={30}>
          <Button
            onClick={closeConfirmModal}
            variant='outline'
            color='gray'
            size='compact-md'
            radius={'md'}
          >
            {t('cancelClosedButton')}
          </Button>
          <Button
            onClick={confirmMoveToOtherConversation}
            color='navy.0'
            radius={'md'}
            size='compact-md'
            ml={'md'}
          >
            {t('goto_other_conversation')}
          </Button>
        </Center>
      </Modal.Body>
    </Modal>
  );
}
