import { Flex } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useViewportSize } from '@mantine/hooks';
import { Show } from '@resola-ai/ui';
import { createCustomEventListener } from '@resola-ai/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  CHAT_ROOM_MODULE_HEIGHT,
  NAVBAR_ID_NAME,
  SELF_ASSIGN_ID_NAME,
  TEXT_EDITOR_ID_NAME,
} from '../../constants';
import { scrollToMessageListBottom } from '../../utils/scroll';
import SelfAssign from '../SelfAssign';
import TextEditor from '../TextEditor';
import { useConversationActionContext } from '../conversationActionContext';
import ChatMessagesArea from './ChatMessagesArea';
import TopBar from './TopBar';

const useStyles = createStyles(() => ({
  flexStyle: {
    justifyContent: 'flex-end',
    backgroundColor: 'white',
    flexDirection: 'column',
  },
}));

const ChatRoom = ({ lang }) => {
  const { classes } = useStyles();
  const { height } = useViewportSize();
  const [navbarHeight, setNavbarHeight] = useState(0);
  const [selfAssignHeight, setSelfAssignHeight] = useState(0);
  const [textEditorHeight, setTextEditorHeight] = useState(0);

  const { isTheAssignee, isCompleted, haveAssignee, isWrapUp } = useConversationActionContext();

  const getSelfAssignHeight = useCallback(() => {
    const selfAssignElement = document.getElementById(SELF_ASSIGN_ID_NAME);
    const selfAssignElementHeight = selfAssignElement?.clientHeight;
    setSelfAssignHeight(selfAssignElementHeight);
  }, []);

  const getTextEditorHeight = useCallback(() => {
    const textEditorElement = document.getElementById(TEXT_EDITOR_ID_NAME);
    if (!textEditorElement) {
      setTextEditorHeight(0);
      return;
    }
    const textEditorElementHeight = textEditorElement?.clientHeight;
    setTextEditorHeight(textEditorElementHeight);
  }, []);

  const getNavbarHeight = useCallback(() => {
    // NAVBAR_ID_NAME
    const navbarElement = document.getElementById(NAVBAR_ID_NAME);
    const navbarElementHeight = navbarElement?.clientHeight;
    setNavbarHeight(navbarElementHeight);
  }, []);

  const syncHeight = useCallback(() => {
    getSelfAssignHeight();
    getTextEditorHeight();
    getNavbarHeight();
  }, [getNavbarHeight, getSelfAssignHeight, getTextEditorHeight]);

  useEffect(() => {
    // auto adjust message list height on change
    syncHeight();
  });

  useEffect(() => {
    // auto scroll to bottom on change
    scrollToMessageListBottom();
  }, [isTheAssignee, isCompleted]);

  useEffect(() => {
    const syncMessagesHeightHandler = () => {
      setTimeout(() => {
        syncHeight();
        setTimeout(() => {
          scrollToMessageListBottom();
        });
      });
    };

    const unregisteredEvent = createCustomEventListener(
      'deca-livechat-sync-chat-message-height',
      syncMessagesHeightHandler
    );

    return () => unregisteredEvent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const chatMessageAreaHeight = useMemo(
    () => height - 60 - selfAssignHeight - textEditorHeight - navbarHeight,
    [height, navbarHeight, selfAssignHeight, textEditorHeight]
  );

  return (
    <>
      <TopBar />
      <Flex
        className={classes.flexStyle}
        sx={{
          maxHeight: CHAT_ROOM_MODULE_HEIGHT,
          height: CHAT_ROOM_MODULE_HEIGHT,
        }}
      >
        <ChatMessagesArea height={chatMessageAreaHeight} />
        <Show condition={isTheAssignee && !isCompleted && !isWrapUp}>
          {isTheAssignee && !isCompleted && !isWrapUp ? <TextEditor lang={lang} /> : null}
        </Show>
        <Show condition={!haveAssignee && !isCompleted}>
          <SelfAssign lang={lang} />
        </Show>
      </Flex>
    </>
  );
};

export default ChatRoom;
