import { Box, Flex, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
  TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
  TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
  USER_TYPING_STATUS_HAPPENING,
  USER_TYPING_STATUS_STOP,
} from '../../constants';
import { MESSAGE_PERCENT } from '../../constants/grid';
import { TypingReturnDataType } from '../../hooks/useTypingEventManager';
import { getPublicUrl } from '../../utils/public';
import { useAppContext } from '../appContext';
import { useConversationActionContext } from '../conversationActionContext';

const MILISECONDS_TO_CALL_INITIAL_STATUS = 200;

const useStyle = createStyles((theme) => ({
  boxAvatar: {
    display: 'flex',
    alignItems: 'flex-start',
  },
  boxContent: {
    color: 'white',
    padding: '12px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    backgroundColor: theme.colors.navy[1],
    fontSize: rem(14),
    fontWeight: 600,
  },
  boxContentResponsive: {
    color: 'white',
    padding: '10px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    backgroundColor: theme.colors.navy[1],
    fontSize: rem(14),
    fontWeight: 600,
  },
}));

export const UserTypingIndicator = ({
  onTypingChange,
}: {
  onTypingChange?: (isTyping: boolean) => void;
}) => {
  const {
    responsive: { responsiveScreen },
  } = useAppContext();
  const { classes } = useStyle();
  const { currentCustomer, currentConversationId } = useConversationActionContext();
  const [isTyping, setIsTyping] = useState(false);
  const timeoutScrollRef = useRef<number>();

  const updateTypingStatus = useCallback(
    (incomingStatus?: string) => {
      if (!incomingStatus) {
        setIsTyping(false);
        onTypingChange?.(false);
        return;
      }

      if (incomingStatus === USER_TYPING_STATUS_STOP) {
        setIsTyping(false);
        onTypingChange?.(false);
      }

      if (incomingStatus === USER_TYPING_STATUS_HAPPENING) {
        setIsTyping(true);
        onTypingChange?.(true);
      }
    },
    [onTypingChange]
  );

  useEffect(() => {
    const controller = new AbortController();

    const timeoutInitial = setTimeout(() => {
      if (controller.signal.aborted) return;
      sendCustomEvent(TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST, {
        callerId: currentConversationId,
        conversationId: currentConversationId,
      });
    }, MILISECONDS_TO_CALL_INITIAL_STATUS);

    const handleTypingEvent = (e: CustomEvent<{ data: TypingReturnDataType }>) => {
      const { data } = e.detail;
      if (!data?.data?.length) return;
      const found = data?.data?.find((item) => item?.conversationId === currentConversationId);
      updateTypingStatus(found?.status);
    };

    createCustomEventListener(
      TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
      handleTypingEvent,
      controller.signal
    );

    createCustomEventListener(
      TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
      handleTypingEvent,
      controller.signal
    );

    return () => {
      setIsTyping(false);
      controller.abort();
      timeoutInitial && clearTimeout(timeoutInitial);
      timeoutScrollRef.current && clearTimeout(timeoutScrollRef.current);
    };
  }, [currentConversationId, updateTypingStatus]);

  return (
    <>
      <Transition
        keepMounted
        duration={400}
        mounted={isTyping}
        timingFunction='ease'
        transition={'fade-up'}
      >
        {(transitionStyle) => (
          <Flex
            align='center'
            gap='xs'
            style={{
              ...transitionStyle,
            }}
          >
            <Flex w={'100%'} direction={'row'} gap={!!responsiveScreen ? rem(9.5) : 'md'}>
              <Flex direction={'column'} gap={'4px'}>
                <Box
                  className={!!responsiveScreen ? classes.boxContentResponsive : classes.boxContent}
                  style={{
                    color: 'white',
                    paddingLeft: 15,
                    paddingRight: 15,
                    paddingTop: 0,
                    paddingBottom: 0,
                  }}
                >
                  <Image
                    width={30}
                    height={20}
                    alt='loading'
                    src={getPublicUrl('/images/animate3dots.svg')}
                  />
                </Box>
              </Flex>
            </Flex>
          </Flex>
        )}
      </Transition>
    </>
  );
};
