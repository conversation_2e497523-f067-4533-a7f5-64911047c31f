import { Box, Flex, Text, rem } from '@mantine/core';
import { displayChatTime } from '@resola-ai/utils';
import React from 'react';
import { useAppContext } from '../../appContext';
import useStyleMessageHolder from './useStylesForMessageHolderHooks';

interface Props {
  activity: string;
  created: string;
}
const ActivityHolder: React.FC<Props> = ({ activity, created }) => {
  const { responsiveScreen } = useAppContext();
  const { classes } = useStyleMessageHolder();

  return (
    <Flex justify={'center'} gap={responsiveScreen ? rem(9.5) : 'md'} align={'center'}>
      <Box className={classes.boxTimeSeen} sx={{ alignItems: 'center' }}>
        <Text
          className={classes.timeSeen}
          sx={{
            fontSize: responsiveScreen ? '10px' : '12px',
          }}
        >
          {displayChatTime(created)}
        </Text>
      </Box>
      <Text ta='center' size={responsiveScreen ? '10px' : '12px'} fw={400} color={'#5C5F66'}>
        {activity}
      </Text>
    </Flex>
  );
};

export default ActivityHolder;
