import { AspectRatio, Avatar, Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { FeatureFlagName, IMessage } from '@resola-ai/models';
import { If } from '@resola-ai/ui';
import { displayChatTime, sendCustomEvent } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import { ReactNode } from 'react';
import Preview from '../../../components/Preview';
import CustomMedia from '../../../components/customMedia';
import { MESSAGE_PERCENT } from '../../../constants/grid';
import useFeatureFlag from '../../../hooks/useFeatureFlag';
import { hasOneAndOnlyOneLineEmojiNoText } from '../../../utils/message_helper';
import AiSummary from '../../AiSummary';
import { useAppContext } from '../../appContext';
import { useConversationActionContext } from '../../conversationActionContext';
import DocumentDisplay from './DocumentDisplay';
import TextRenderer from './TextRenderer';

const useStyles = createStyles((theme) => ({
  flexContainer: {
    justifyContent: 'flex-start',
  },
  senderName: {
    fontSize: '12px',
    color: '#5C5F66',
  },
  boxAvatar: {
    display: 'flex',
    alignItems: 'flex-start',
  },
  boxEmoji: {
    color: 'white',
    padding: '12px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    '& ::selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-moz-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-webkit-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
  },
  boxEmojiResponsive: {
    color: 'white',
    padding: '10px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    '& ::selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-moz-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-webkit-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
  },
  boxContent: {
    color: 'white',
    padding: '12px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    backgroundColor: theme.colors.navy[1],
    '& ::selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-moz-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-webkit-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
  },
  boxContentResponsive: {
    color: 'white',
    padding: '10px',
    borderRadius: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
    backgroundColor: theme.colors.navy[1],
    '& ::selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-moz-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
    '& ::-webkit-selection': {
      backgroundColor: 'rgba(132, 133, 199, calc(254 / 255))',
      color: 'white',
    },
  },
  boxTimeSeen: {
    display: 'flex',
    alignItems: 'flex-end',
    marginBottom: '12px',
  },
  textBox: {
    fontSize: '14px',
    '&  a': {
      color: 'white',
    },
    '& a:hover': {
      color: '#d1d1d1',
    },
  },
  timeSeen: {
    fontSize: '12px',
    fontWeight: 400,
    color: '#5C5F66',
  },
  boxContentNoPadding: {
    padding: 0,
    borderRadius: 0,
    maxWidth: `${MESSAGE_PERCENT}vw`,
  },
}));

interface Props {
  message: IMessage;
  isTheLastMessage?: boolean;
}

export default function GuessMessageHolder({ message, isTheLastMessage }: Props) {
  const {
    responsive: { responsiveScreen },
  } = useAppContext();
  const { enabled: aiFeatures } = useFeatureFlag(FeatureFlagName.aiFeatures);
  const { currentCustomer, currentConversation } = useConversationActionContext();

  return (
    <GuessMessageDisplayItem
      message={message}
      responsive={responsiveScreen}
      picture={currentCustomer?.picture || ''}
      guessName={currentConversation?.enduser?.name || ''}
    >
      {!!aiFeatures && isTheLastMessage && <AiSummary />}
    </GuessMessageDisplayItem>
  );
}
type GuessMessageDisplayItemType = {
  message: IMessage;
  responsive: boolean;
  children?: ReactNode;
  picture: string;
  guessName: string;
};
export const GuessMessageDisplayItem = ({
  message,
  picture,
  guessName,
  responsive,
  children,
}: GuessMessageDisplayItemType) => {
  const { classes, theme } = useStyles();
  const { t } = useTranslate('workspace');
  const messageType = message.data.type;
  const messageText = message.data?.text || '';
  return (
    <Flex w={'100%'} gap={'xs'} justify={'flex-start'}>
      <Box w={'40px'} className={classes.boxAvatar}>
        <AspectRatio ratio={38 / 38} w={38} maw={300} mx='auto'>
          <Preview imageUrl={picture} showDownloadButton={false} showFileName={false}>
            <Avatar radius={'xl'} src={picture} alt="it's me" />
          </Preview>
        </AspectRatio>
      </Box>
      <Flex w={'100%'} direction={'row'} gap={responsive ? rem(9.5) : 'md'}>
        <Flex direction={'column'} gap={'4px'}>
          <Text className={classes.senderName}>{guessName}</Text>
          {messageType !== 'text' ? (
            <Box
              className={
                messageType === 'image' || messageType === 'video' || messageType === 'document'
                  ? classes.boxContentNoPadding
                  : responsive
                    ? classes.boxContentResponsive
                    : classes.boxContent
              }
            >
              <If condition={messageType === 'image'}>
                <If condition={messageText !== undefined && !!messageText?.length}>
                  <If condition={messageText?.includes('https://')}>
                    <CustomMedia
                      url={messageText}
                      onLoaded={() => {
                        sendCustomEvent('deca-livechat-image-loaded', {
                          message,
                        });
                      }}
                    />
                  </If>
                  <If condition={!messageText?.includes('https://')}>
                    <Text
                      className={classes.textBox}
                      sx={{
                        fontSize: responsive ? '12px' : undefined,
                        lineHeight: responsive ? '18.6px' : '21.7px',
                      }}
                    >
                      {messageText === 'Enduser sent image'
                        ? t('unsupported.image.placeholder')
                        : messageText}
                    </Text>
                  </If>
                </If>
                <If condition={messageText === undefined || !messageText?.length}>
                  <Text
                    className={classes.textBox}
                    sx={{
                      fontSize: responsive ? '12px' : undefined,
                      lineHeight: responsive ? '18.6px' : '21.7px',
                    }}
                  >
                    {t('unsupported.placeholder')}
                  </Text>
                </If>
              </If>
              <If condition={messageType === 'video'}>
                <If condition={messageText !== undefined && !!messageText?.length}>
                  <If condition={messageText?.includes('https://')}>
                    <CustomMedia
                      url={messageText}
                      onLoaded={() => {
                        sendCustomEvent('deca-livechat-image-loaded', {
                          message,
                        });
                      }}
                    />
                  </If>
                  <If condition={!messageText?.includes('https://')}>
                    <Text
                      className={classes.textBox}
                      sx={{
                        fontSize: responsive ? '12px' : undefined,
                        lineHeight: responsive ? '18.6px' : '21.7px',
                      }}
                    >
                      {messageText === 'Enduser sent image'
                        ? t('unsupported.image.placeholder')
                        : messageText}
                    </Text>
                  </If>
                </If>
                <If condition={messageText === undefined || !messageText?.length}>
                  <Text
                    className={classes.textBox}
                    sx={{
                      fontSize: responsive ? '12px' : undefined,
                      lineHeight: responsive ? '18.6px' : '21.7px',
                    }}
                  >
                    {t('unsupported.placeholder')}
                  </Text>
                </If>
              </If>
              <If condition={messageType === 'text'}>
                <TextRenderer message={message} responsive={responsive} />
              </If>
              <If condition={messageType === 'sticker'}>
                <Text
                  className={classes.textBox}
                  sx={{
                    fontSize: responsive ? '12px' : undefined,
                  }}
                >
                  {t('unsupported.sticker.placeholder')}
                </Text>
              </If>
              <If condition={messageType === 'document'}>
                <DocumentDisplay url={messageText} />
              </If>
              <If
                condition={!['text', 'image', 'video', 'sticker', 'document'].includes(messageType)}
              >
                <Text
                  className={classes.textBox}
                  sx={{
                    fontSize: responsive ? '12px' : undefined,
                  }}
                >
                  {t('unsupported.placeholder')}
                </Text>
              </If>
            </Box>
          ) : (
            <Box
              className={responsive ? classes.boxEmojiResponsive : classes.boxEmoji}
              style={
                hasOneAndOnlyOneLineEmojiNoText(message?.data?.text, message?.data?.emojis)
                  ? { padding: 0 }
                  : {
                      backgroundColor: theme.colors.navy[1],
                    }
              }
            >
              <TextRenderer message={message} responsive={responsive} />
            </Box>
          )}
        </Flex>

        <Box className={classes.boxTimeSeen}>
          <Text
            className={classes.timeSeen}
            sx={{
              fontSize: responsive ? '10px' : '12px',
            }}
          >
            {displayChatTime(message.created)}
          </Text>
        </Box>
        <Flex
          align={'center'}
          mt={0}
          sx={{
            transform: 'translateX(2px)',
          }}
        >
          {children}
        </Flex>
      </Flex>
    </Flex>
  );
};
