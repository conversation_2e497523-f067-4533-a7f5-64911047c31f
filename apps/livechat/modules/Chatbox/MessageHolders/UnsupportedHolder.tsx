'use client';
import { AspectRatio, Avatar, Box, Flex, Text, rem } from '@mantine/core';
import { IMessage } from '@resola-ai/models';
import { displayChatTime } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useAppContext } from '../../appContext';
import BotSvg from './BotSvg';
import useStyleMessageHolder from './useStylesForMessageHolderHooks';

interface Props {
  message: IMessage;
}
export default function UnsupportedHolder(props: Props) {
  const { message } = props;
  const { classes } = useStyleMessageHolder();
  const { responsiveScreen } = useAppContext();
  const { t } = useTranslate('workspace');

  return (
    <>
      <Flex
        w={'100%'}
        direction={'row'}
        gap={responsiveScreen ? rem(9.5) : 'md'}
        className={classes.flexContainer}
      >
        <Box className={classes.boxTimeSeen}>
          <Text
            className={classes.timeSeen}
            sx={{
              fontSize: responsiveScreen ? '10px' : undefined,
            }}
          >
            {displayChatTime(message.created)}
          </Text>
        </Box>

        <Box
          className={classes.boxContent}
          sx={{
            lineHeight: responsiveScreen ? '18.6px' : '21.7px',
          }}
        >
          <Text
            sx={{
              lineHeight: responsiveScreen ? '18.6px' : '21.7px',
            }}
          >
            {t('unsupported.placeholder')}
          </Text>
        </Box>

        <Box w={'40px'} className={classes.boxAvatar}>
          <AspectRatio ratio={38 / 38} w={38} maw={300} mx='auto'>
            <Avatar
              radius={'xl'}
              // src={}
              alt="it's me"
              color={'gray'}
            >
              <BotSvg />
            </Avatar>
          </AspectRatio>
        </Box>
      </Flex>
    </>
  );
}
