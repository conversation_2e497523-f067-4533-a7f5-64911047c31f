import { Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDownload } from '@tabler/icons-react';
import dayjs from 'dayjs';
import React, { useEffect, useState, useRef, useMemo } from 'react';
import { KB, MB } from '../../Preview/constants';
import ErrorDoc from './ErrorIcons/ErrorDoc';
import CsvIcon from './icons/CsvIcon';
import DocIcon from './icons/DocIcon';
import DocxIcon from './icons/DocxIcon';
import MdIcon from './icons/MdIcon';
import PdfIcon from './icons/PdfIcon';
import TxtIcon from './icons/TxtIcon';

interface DocumentDisplayProps {
  url: string;
}

const getIconForFileType = (fileType: string) => {
  switch (fileType) {
    case 'txt':
      return <TxtIcon width={24} height={24} />;
    case 'pdf':
      return <PdfIcon width={24} height={24} />;
    case 'md':
      return <MdIcon width={24} height={24} />;
    case 'docx':
      return <DocxIcon width={24} height={24} />;
    case 'csv':
      return <CsvIcon width={24} height={24} />;
    case 'doc':
      return <DocIcon width={24} height={24} />;
  }
};

const useStyles = createStyles((theme) => ({
  documentContainer: {
    padding: '10px',
    border: `1px solid ${theme.colors.documentDisplayColors[0]}`,
    borderColor: theme.colors.documentDisplayColors[0],
    borderRadius: '8px',
    '&:hover': {
      backgroundColor: theme.colors.documentDisplayColors[5],
      borderColor: theme.colors.documentDisplayColors[9],
    },
  },
  iconContainer: {
    marginRight: '10px',
  },
  downloadIcon: {
    cursor: 'pointer',
    display: 'flex',
    alignSelf: 'center',
    marginLeft: '10px',
  },
}));

const DocumentDisplay: React.FC<DocumentDisplayProps> = ({ url }) => {
  const [fileSize, setFileSize] = useState<string | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const fileExtension = url.split('.').pop()?.toLowerCase() || 'file';
  const documentRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const fetchFileSize = async () => {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const contentLength = response.headers.get('Content-Length');
        if (contentLength) {
          const sizeInBytes = parseInt(contentLength, 10);
          const formattedSize =
            sizeInBytes < MB
              ? `${(sizeInBytes / KB).toFixed(0)} KB`
              : `${(sizeInBytes / MB).toFixed(0)} MB`;
          setFileSize(formattedSize);
        } else {
          console.warn('Content-Length header is missing');
          // Fallback to GET request
          const getResponse = await fetch(url);
          const blob = await getResponse.blob();
          const sizeInBytes = blob.size;
          const formattedSize =
            sizeInBytes < MB
              ? `${(sizeInBytes / KB).toFixed(0)} KB`
              : `${(sizeInBytes / MB).toFixed(0)} MB`;
          setFileSize(formattedSize);
        }
      } catch (error) {
        console.error('Error fetching file size:', error);
        setHasError(true);
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            fetchFileSize();
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    if (documentRef.current) {
      observer.observe(documentRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [url]);

  const isValidUrl = (urlString: string) => {
    new URL(urlString);
    return true;
  };

  const handleDownload = () => {
    if (isValidUrl(url)) {
      window.open(url.toString(), '_blank', 'noopener,noreferrer');
    } else {
      console.error('Invalid URL');
    }
  };

  const fileName = useMemo(() => {
    const now = dayjs();
    const formattedDate = now.format('YYYYMMDD_HHmm');
    const defaultName = `${formattedDate}.${fileExtension}`;
    try {
      const urlObj = new URL(url);
      const extractedFileName = urlObj.searchParams.get('fileName');
      return extractedFileName ?? defaultName;
    } catch (error) {
      console.error('Invalid URL:', error);
      return defaultName;
    }
  }, [url, fileExtension]);

  const { classes } = useStyles();

  return hasError ? (
    <ErrorDoc />
  ) : (
    <Flex
      align='start'
      justify='space-between'
      className={classes.documentContainer}
      ref={documentRef}
    >
      <Flex align='center' className={classes.iconContainer}>
        {getIconForFileType(fileExtension)}
      </Flex>
      <div>
        <Text size='md' fw={500}>
          {fileName}
        </Text>
        {fileSize && (
          <Text size='sm' c='gray'>
            {fileSize} {fileExtension.toUpperCase()}
          </Text>
        )}
      </div>
      <div
        role='button'
        onClick={handleDownload}
        onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && handleDownload()}
        className={classes.downloadIcon}
        tabIndex={0}
        aria-label='Download'
      >
        <IconDownload size={18} />
      </div>
    </Flex>
  );
};

export default DocumentDisplay;
