'use client';
import styled from '@emotion/styled';
import { AspectRatio, Box, Flex, Text, rem } from '@mantine/core';
import { IMessage } from '@resola-ai/models';
import { BotAvatarIcon } from '@resola-ai/ui';
import { displayChatTime } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useAppContext } from '../../appContext';
import { RenderReadStatus } from './RenderReadStatus';
import useStyleMessageHolder from './useStylesForMessageHolderHooks';

const Wrapper = styled.div`
    background-color: #ced4da;
    border-radius: 50%;
`;
interface Props {
  message: IMessage;
}
export default function BotMessageHolder(props: Props) {
  const { message } = props;
  const { classes } = useStyleMessageHolder();
  const { responsiveScreen } = useAppContext();
  const { t } = useTranslate('workspace');
  return (
    <Flex w={'100%'} gap={'xs'} align={'flex-start'}>
      <Flex
        w={'100%'}
        direction={'row'}
        gap={responsiveScreen ? rem(9.5) : 'md'}
        className={classes.flexContainer}
      >
        <Box className={classes.boxTimeSeen}>
          <Text
            className={classes.timeSeen}
            sx={{
              fontSize: responsiveScreen ? '10px' : '12px',
            }}
          >
            {displayChatTime(message.created)}
          </Text>
          <RenderReadStatus hasRead={message?.hasRead} />
        </Box>

        <Flex direction={'column'} gap={'4px'}>
          <Text className={classes.senderName}>{t('bot')}</Text>
          <Box className={responsiveScreen ? classes.boxContentResponsive : classes.boxContent}>
            <Text
              className={classes.textBox}
              sx={{
                fontSize: responsiveScreen ? '12px' : undefined,
                lineHeight: responsiveScreen ? '18.6px' : '21.7px',
              }}
            >
              {message.data.text}
            </Text>
          </Box>
        </Flex>
      </Flex>
      <Box w={'40px'} className={classes.boxAvatar}>
        <AspectRatio ratio={38 / 38} w={38} maw={300} mx='auto'>
          <Wrapper className={classes.botAIAvatar}>
            <BotAvatarIcon />
          </Wrapper>
        </AspectRatio>
      </Box>
    </Flex>
  );
}
