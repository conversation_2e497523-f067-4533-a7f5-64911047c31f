import { Group, Text, Transition, useMantineTheme } from '@mantine/core';
import { IconCheck } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect } from 'react';
import useVisibilityControl from '../../../hooks/useVisibilityControl';

export const RenderReadStatus = ({ hasRead = false }: { hasRead: boolean }) => {
  const theme = useMantineTheme();
  const { t } = useTranslate('common');
  const { visible, open } = useVisibilityControl(hasRead);
  useEffect(() => {
    let timeout;
    if (hasRead) {
      timeout = setTimeout(() => {
        open();
      });
    }

    return () => timeout && clearTimeout(timeout);
  }, [open, hasRead]);

  return (
    <Transition
      keepMounted
      duration={200}
      mounted={visible}
      enterDelay={1000}
      transition={'fade'}
      timingFunction='ease'
    >
      {(transitionStyle) => (
        <Group gap='0' justify='right' style={transitionStyle}>
          <IconCheck color={theme.colors.green[6]} size={20} stroke={4} />
          <Text ml={5} c='gray.6' fz={12}>
            {t('end_user_read_status')}
          </Text>
        </Group>
      )}
    </Transition>
  );
};
