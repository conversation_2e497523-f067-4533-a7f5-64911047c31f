import { createStyles } from '@mantine/emotion';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { useMemo } from 'react';
import { MESSAGE_AI_ADJUSTMENT_PERCENT, MESSAGE_PERCENT } from '../../../constants/grid';

const useStyle = createStyles(() => ({
  flexContainer: {
    flexDirection: 'row-reverse',
    justifyContent: 'flex-end',
  },
  senderName: {
    textAlign: 'right',
    fontSize: '12px',
    color: '#5C5F66',
  },
  boxAvatar: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  avatar: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    img: {
      width: '24px',
      height: '24px',
    },
  },
  boxContent: {
    borderRadius: '10px',
    backgroundColor: '#F1F3F5',
    padding: '15px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
  },
  boxContentResponsive: {
    borderRadius: '10px',
    backgroundColor: '#F1F3F5',
    padding: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
  },
  boxTimeSeen: {
    display: 'flex',
    flexDirection: 'column-reverse',
    alignItems: 'flex-end',
  },
  textBox: {
    fontSize: '14px',
    overflowWrap: 'anywhere',
    whiteSpace: 'pre-wrap',
  },
  timeSeen: {
    fontSize: '12px',
    fontWeight: 400,
    color: '#5C5F66',
    marginLeft: '10px',
  },
  botAIMessageBox: {
    backgroundColor: '#F3F0FF',
    color: COLOR_DEFAULT_BLACK,
    maxWidth: `${MESSAGE_AI_ADJUSTMENT_PERCENT}px`,
    width: 'fit-content',
  },
  botAIAvatar: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cursorPointer: {
    cursor: 'pointer',
  },
}));
export default function useStyleMessageHolder() {
  const { classes, cx } = useStyle();

  return useMemo(
    () => ({
      classes,
      cx,
    }),
    [classes, cx]
  );
}
