import { IMessage } from '@resola-ai/models';
// import UnsupportedHolder from './UnsupportedHolder';
import { LeftToRightTween, RightToLeftTween } from '@resola-ai/ui';
import { displayChatDate } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React, { CSSProperties, memo } from 'react';
import { createMessageId } from '../../../utils/message';
import { useAppContext } from '../../appContext';
import { useUserContext } from '../../userContext';
import ActivityHolder from './ActivityHolder';
import BotMessageHolder from './BotMessageHolder';
import DateHolder from './DateHolder';
import GuessMessageHolder, { GuessMessageDisplayItem } from './GuessMessageHolder';
import OperatorMessageHolder, { OperatorMessageDisplayItem } from './OperatorMessageHolder';

const HIGHLIGHT_COLOR = '#FFF1CC80';
interface Props {
  idx: number;
  message: IMessage;
  isTheLastMessage: boolean;
  typeDisplay?: 'searchBox';
  lang?: string;
  highlight?: boolean;
}
const MessageItemComponent: React.FC<Props> = ({
  message,
  isTheLastMessage,
  lang = 'ja',
  typeDisplay,
}) => {
  const { t } = useTranslate('workspace');
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const { userId, operatorIdToFinalNameMap } = useUserContext();
  if (!message) {
    return null;
  }
  if (!message.conversationId) {
    // console.log('this message does not have conversationId', message);
    return null;
  }
  if (message.data.type === 'custom-client-date') {
    return (
      <DateHolder key={createMessageId(message)} date={displayChatDate(message.created, lang)} />
    );
  }
  if (!message.sender) {
    return null;
  }
  if (message.sender.type === 'enduser') {
    return (
      <div key={createMessageId(message)} id={createMessageId(message)}>
        {!typeDisplay ? (
          <GuessMessageHolder message={message} isTheLastMessage={isTheLastMessage} />
        ) : (
          <GuessMessageDisplayItem
            message={message}
            responsive={responsiveScreen}
            guessName={message.sender?.name || ''}
            picture={message?.sender?.picture || ''}
          />
        )}
      </div>
    );
  }
  if (message.sender.type === 'operator') {
    return (
      <div key={createMessageId(message)} id={createMessageId(message)}>
        {!typeDisplay ? (
          <OperatorMessageHolder message={message} isTheLastMessage={isTheLastMessage} />
        ) : (
          <OperatorMessageDisplayItem message={message} responsive={responsiveScreen} />
        )}
      </div>
    );
  }
  if (message.sender.type === 'system') {
    if (message.data.type === 'text') {
      return (
        <div
          key={createMessageId(message)}
          id={createMessageId(message)}
          style={{ marginBottom: '10px' }}
        >
          <BotMessageHolder message={message} />
        </div>
      );
    }
    if (message.data.type === 'activity') {
      try {
        if (['conversation.completed', 'conversation_completed'].includes(message?.data?.text)) {
          const text = t('activity.completed', {
            operatorName:
              operatorIdToFinalNameMap?.[message?.data?.metadata?.assignee?.id || ''] ||
              message.data.metadata?.assignee?.name,
          });
          return (
            <ActivityHolder
              activity={text}
              created={message.created}
              key={createMessageId(message)}
            />
          );
        }

        if (message.data.text === 'conversation.operator.assigned') {
          if (userId == message.data.metadata?.assignee?.id) {
            const text = t('activity.assigned.you');
            return (
              <ActivityHolder
                activity={text}
                created={message.created}
                key={createMessageId(message)}
              />
            );
          }
          const assigneeId = message?.data?.metadata?.assignee?.id;
          const isUnknownInAssigneeId = assigneeId.includes('UNKNOWN');

          let text = t('activity.assigned.operator', {
            operatorName:
              operatorIdToFinalNameMap?.[assigneeId || ''] || message.data.metadata?.assignee?.name,
          });

          if (isUnknownInAssigneeId) {
            const oldAssigneeName =
              operatorIdToFinalNameMap?.[message?.data?.metadata?.oldAssignee?.id || ''] ||
              message.data.metadata?.oldAssignee?.name;
            text = t('activity.assigned.operator.unknown', {
              operatorName: oldAssigneeName,
            });
          }

          return (
            <ActivityHolder
              activity={text}
              created={message.created}
              key={createMessageId(message)}
            />
          );
        }
        if (message.data.text === 'conversation.team.assigned') {
          const text = t('activity.assigned.team', {
            teamName: message.data.metadata?.team?.name,
          });
          return (
            <ActivityHolder
              activity={text}
              created={message.created}
              key={createMessageId(message)}
            />
          );
        }

        if (['conversation.created', 'conversation.new'].includes(message.data.text)) {
          const text = t('new');
          return (
            <ActivityHolder
              activity={text}
              created={message.created}
              key={createMessageId(message)}
            />
          );
        }

        if (message.data.text === 'conversation.inwrapup') {
          const text = t('activity.inwrapup', {
            name:
              operatorIdToFinalNameMap?.[message?.data?.metadata?.assignee?.id || ''] ||
              message.data.metadata?.assignee?.name,
          });
          return (
            <ActivityHolder
              activity={text}
              created={message.created}
              key={createMessageId(message)}
            />
          );
        }

        if (message.data.text === 'conversation.inwrapup.system') {
          return (
            <ActivityHolder
              key={createMessageId(message)}
              activity={t('activity.inwrapup.system')}
              created={message.created}
            />
          );
        }

        if (message.data.text === 'conversation.completed.enduser') {
          return (
            <ActivityHolder
              created={message.created}
              key={createMessageId(message)}
              activity={t('conversationCompletedByUser')}
            />
          );
        }

        if (message.data.text === 'conversation.completed.system') {
          return (
            <ActivityHolder
              created={message.created}
              key={createMessageId(message)}
              activity={t('conversationCompletedBySystem')}
            />
          );
        }
      } catch (error) {
        console.log('error', error);
        return (
          <ActivityHolder
            created={message.created}
            activity={message.data.text}
            key={createMessageId(message)}
          />
        );
      }
    }
    // return null;
  }
  return null;
  // return (
  //     <div key={createMessageId(message)} id={createMessageId(message)}>
  //         <UnsupportedHolder message={message} />
  //     </div>
  // );
  // return <div key={createMessageId(message)} id={createMessageId(message)}>
  //   <OperatorMessageHolder message={message} />
  // </div>
};

const DELAY_TIME = 0.01;

const MessageItem: React.FC<Props> = ({
  idx,
  message,
  typeDisplay,
  isTheLastMessage,
  lang = 'ja',
  highlight,
}) => {
  const messageItem = (
    <MessageItemComponent
      idx={idx}
      lang={lang}
      message={message}
      highlight={highlight}
      typeDisplay={typeDisplay}
      isTheLastMessage={isTheLastMessage}
    />
  );
  const ItemRender = ({ style }: { style: CSSProperties }) =>
    message?.sender?.type === 'enduser' ? (
      <LeftToRightTween delay={DELAY_TIME * idx} style={style}>
        {messageItem}
      </LeftToRightTween>
    ) : (
      <RightToLeftTween delay={DELAY_TIME * idx} style={style}>
        {messageItem}
      </RightToLeftTween>
    );
  return (
    <ItemRender
      style={
        !!highlight
          ? { background: HIGHLIGHT_COLOR, padding: '8px 0', marginBottom: '30px' }
          : { marginBottom: '30px' }
      }
    />
  );
};

export default memo(MessageItem);
