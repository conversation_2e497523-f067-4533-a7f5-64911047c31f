import { AspectRatio, Avatar, Box, Flex, Text, rem } from '@mantine/core';
import { FeatureFlagName, IMessage } from '@resola-ai/models';
import { If } from '@resola-ai/ui';
import { displayChatTime } from '@resola-ai/utils';
import { sendCustomEvent } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React, { ReactNode, useMemo } from 'react';
import Preview from '../../../components/Preview';
import CustomMedia from '../../../components/customMedia';
import { orgIconUrl, teamIconUrl } from '../../../constants';
import useFeatureFlag from '../../../hooks/useFeatureFlag';
import { transformTextHyperLinks } from '../../../utils/message_helper';
import AiSummary from '../../AiSummary';
import { useAppContext } from '../../appContext';
import { useUserContext } from '../../userContext';
import DocumentDisplay from './DocumentDisplay';
import { RenderReadStatus } from './RenderReadStatus';
import useStyleMessageHolder from './useStylesForMessageHolderHooks';

interface Props {
  message: IMessage;
  isTheLastMessage: boolean;
}
export default function OperatorMessageHolder({ message, isTheLastMessage }: Props) {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const { enabled: aiFeatures } = useFeatureFlag(FeatureFlagName.aiFeatures);

  return (
    <OperatorMessageDisplayItem message={message} responsive={responsiveScreen}>
      {!!aiFeatures && isTheLastMessage && <AiSummary />}
    </OperatorMessageDisplayItem>
  );
}

type MessageDisplayItemType = {
  message: IMessage;
  responsive: boolean;
  children?: ReactNode;
};
export const OperatorMessageDisplayItem = ({
  responsive,
  message,
  children,
}: MessageDisplayItemType) => {
  const { classes } = useStyleMessageHolder();
  const { getUserById } = useUserContext();
  const { t } = useTranslate('workspace');

  const avatar = useMemo(() => {
    if (message.data?.metadata?.sender?.iconUrl) {
      return message.data.metadata.sender.iconUrl;
    }
    if (message.sender.picture) {
      return message.sender.picture;
    }
    const user = getUserById(message.sender.id);
    return user?.picture;
  }, [
    getUserById,
    message.sender.id,
    message.sender.picture,
    message.data.metadata.sender.iconUrl,
  ]);

  const isOrgIcon = [teamIconUrl, orgIconUrl].includes(avatar);

  return (
    <Flex w={'100%'} gap={'xs'} align={'flex-start'}>
      <Flex
        w={'100%'}
        direction={'row'}
        gap={responsive ? rem(9.5) : 'md'}
        className={classes.flexContainer}
      >
        <Flex
          sx={{
            transform: 'translateX(8px)',
          }}
        >
          {children}
        </Flex>
        <Box className={classes.boxTimeSeen}>
          <Text
            className={classes.timeSeen}
            sx={{
              fontSize: responsive ? '10px' : '12px',
            }}
          >
            {displayChatTime(message.created)}
          </Text>
          <RenderReadStatus hasRead={message?.hasRead} />
        </Box>

        <Flex direction={'column'} gap={'4px'}>
          <Text className={classes.senderName}>{message.data?.metadata?.sender?.name}</Text>

          <Box className={responsive ? classes.boxContentResponsive : classes.boxContent}>
            <If condition={message.data.type === 'image'}>
              <If
                condition={
                  message.data.text !== undefined &&
                  message.data.text.length &&
                  message.data.text.length > 0
                }
              >
                <If condition={message.data?.text?.includes('https://')}>
                  <CustomMedia
                    url={message.data.text}
                    onLoaded={() => {
                      sendCustomEvent('deca-livechat-image-loaded', {
                        message,
                      });
                    }}
                  />
                </If>
                <If condition={!message.data?.text?.includes('https://')}>
                  <Text
                    className={classes.textBox}
                    sx={{
                      fontSize: responsive ? '12px' : undefined,
                      lineHeight: responsive ? '18.6px' : '21.7px',
                    }}
                  >
                    {message.data.text}
                  </Text>
                </If>
              </If>
            </If>
            <If condition={message.data.type === 'document'}>
              <DocumentDisplay url={message.data.text} />
            </If>
            <If condition={message.data.type === 'video'}>
              <If
                condition={
                  message.data.text !== undefined &&
                  message.data.text.length &&
                  message.data.text.length > 0
                }
              >
                <If condition={message.data?.text?.includes('https://')}>
                  <CustomMedia
                    url={message.data.text}
                    onLoaded={() => {
                      sendCustomEvent('deca-livechat-image-loaded', {
                        message,
                      });
                    }}
                  />
                </If>
                <If condition={!message.data?.text?.includes('https://')}>
                  <Text
                    className={classes.textBox}
                    sx={{
                      fontSize: responsive ? '12px' : undefined,
                      lineHeight: responsive ? '18.6px' : '21.7px',
                    }}
                  >
                    {message.data.text}
                  </Text>
                </If>
              </If>
            </If>
            <If condition={!['image', 'document', 'video'].includes(message.data.type)}>
              <Text
                className={classes.textBox}
                sx={{
                  fontSize: responsive ? '12px' : undefined,
                  lineHeight: responsive ? '18.6px' : '21.7px',
                  whiteSpace: 'pre-wrap',
                }}
                dangerouslySetInnerHTML={{
                  __html: transformTextHyperLinks(message?.data?.text || ''),
                }}
              ></Text>
            </If>
          </Box>
        </Flex>
      </Flex>
      <Box w={'40px'} className={classes.boxAvatar}>
        <AspectRatio ratio={38 / 38} w={38} maw={300} mx='auto'>
          <Preview imageUrl={avatar} showDownloadButton={false} showFileName={false}>
            <Avatar
              radius={'xl'}
              src={avatar}
              alt="it's operator"
              className={isOrgIcon ? classes.avatar : undefined}
            />
          </Preview>
        </AspectRatio>
      </Box>
    </Flex>
  );
};
