import {
    AspectRatio,
    Avatar,
    Button,
    Center,
    Container,
    CopyButton,
    Flex,
    Group,
    Image,
    Menu,
    Modal,
    NativeSelect,
    Paper,
    Text,
    Tooltip,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconBookmark, IconBookmarkFilled } from '@tabler/icons-react';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { If } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import FocusingAvatarIndicator from '../../components/FosusingAvatarIndicator';
import Preview from '../../components/Preview';
import {
    FOCUS_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
    FOCUS_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
    FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
    TOP_BAR_ID_NAME,
    USER_FOCUS_IN_CONVERSATION,
} from '../../constants';
import { FocusReturnDataType } from '../../hooks/useFocusEventManager';
import useVisibilityControl from '../../hooks/useVisibilityControl';
import { getPublicUrl } from '../../utils/public';
import { useAppContext } from '../appContext';
import { useConversationActionContext } from '../conversationActionContext';
import { useOperationSettingContext } from '../operationSettingContext';
import { useUserContext } from '../userContext';
import TopBarId from './TopBarId';

const useStyle = createStyles((theme) => ({
    paperStyle: {
        direction: 'ltr',
        width: '100%',
        zIndex: 1,
        right: '15px',
        height: '60px',
    },
    textName: {
        color: COLOR_DEFAULT_BLACK,
        fontWeight: 700,
    },
    textId: {
        fontSize: '12px',
        fontWeight: 400,
        marginLeft: '8px',
        color: '#5C5F66',
        lineHeight: '18.6px',
    },
    buttonWrapUp: {
        borderRadius: '6px',
        backgroundColor: '#1F84F4B',
        fontWeight: 600,
        fontSize: '16px',
        paddingLeft: '10px',
        paddingRight: '10px',
        '&:hover': {
            backgroundColor: '#0077de',
        },
    },
    buttonSave: {
        backgroundColor: theme.colors.navy[6],
    },
    buttonEnd: {
        borderRadius: '6px',
        backgroundColor: '#FF6B6B',
        fontWeight: 600,
        fontSize: '16px',
        '&:hover': {
            backgroundColor: '#E64343',
        },
    },
    bookMarkAction: {
        color: '#FAB005',
    },
    messageConfirmClose: {
        fontSize: '16px',
        fontWeight: 500,
        color: 'black',
    },
    messageExplain: {
        fontSize: '14px',
        fontWeight: 400,
        color: '#495057',
    },
    buttonConfirm: {
        minWidth: '84px',
        backgroundColor: '#FF6B6B',
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px',
        '&:hover': {
            backgroundColor: '#E64343',
        },
    },
    buttonCancel: {
        minWidth: '86px',
        backgroundColor: 'white',
        borderWidth: '1.5px',
        fontWeight: 600,
        fontSize: '14px',
        color: '#5C5F66',
        borderColor: '#5C5F66',
        '&:hover': {
            backgroundColor: '#f0f0f0',
        },
    },
    filledBookmark: {
        path: {
            fill: '#FAB007',
        },
    },
    modalTitle: {
        fontSize: '20px',
        fontWeight: 700,
        color: theme.colors.navy[6],
        paddingTop: '5px',
    },
    autoCompleteModalXBtn: {
        color: theme.colors.navy[6],
    },
    overflow: {
        overflow: 'visible',
    },
}));

const TopBar = () => {
    const { responsive } = useAppContext();
    const { enabledWrapUpStatus } = useUserContext();
    const { responsiveScreen } = responsive;
    const {
        isNew,
        customer,
        isCompleted,
        isInProgress,
        isTheAssignee,
        hasTeamAssign,
        toggleBookmark,
        closeConversation,
        wrapUpConversation,
        currentConversation,
        currentConversationId,
        changeAutoCompleteParams,
        isBookmarkedConversation,
    } = useConversationActionContext();

    const { classes } = useStyle();
    const [opened, { open, close }] = useDisclosure(false);
    const {
        visible: openWrapUpConfirm,
        close: closeWrapUpModal,
        open: openWrapUpModal,
    } = useVisibilityControl();
    const { t } = useTranslate('workspace');
    const { preferences } = useOperationSettingContext();
    const { closeConversationOnlyInprogress = false } = preferences;

    const presetAutoCompleteToggle = currentConversation?.autoCompleted?.enable || false;
    const presetAutoCompleteInterval = currentConversation?.autoCompleted?.interval;
    const [autoCompleteToggle, setAutoCompleteToggle] = useState(presetAutoCompleteToggle);
    const [autoCompleteInterval, setAutoCompleteInterval] = useState(presetAutoCompleteInterval);
    const [focusingStatus, setFocusingStatus] = useState(false);

    const showWrapUpButton = useMemo(() => {
        return isTheAssignee && enabledWrapUpStatus && isInProgress;
    }, [enabledWrapUpStatus, isInProgress, isTheAssignee]);

    const showFinishButton = useMemo(() => {
        if ([isCompleted, showWrapUpButton].some(Boolean)) return false;
        if ([hasTeamAssign, isTheAssignee].some((item) => !item)) return false;
        if ([isNew && !closeConversationOnlyInprogress].some(Boolean)) return false;
        return true;
    }, [
        isNew,
        isCompleted,
        hasTeamAssign,
        isTheAssignee,
        showWrapUpButton,
        closeConversationOnlyInprogress,
    ]);

    const nameDisplayed = useMemo(() => {
        return (
            customer?.name ||
            (currentConversation?.enduser?.platform === 'web' ? currentConversation?.enduserId : '')
        );
    }, [customer, currentConversation]);

    const handleWrapUpConversation = useCallback(async () => {
        const okay = await wrapUpConversation(currentConversation?.id);
        okay && closeWrapUpModal();
    }, [closeWrapUpModal, currentConversation?.id, wrapUpConversation]);

    const handleCloseConversation = useCallback(async () => {
        const closed = await closeConversation(currentConversation?.id);
        if (closed) {
            close();
        }
    }, [close, closeConversation, currentConversation?.id]);

    const [autoCompleteModalOpen, setAutoCompleteModalOpen] = useState(false);

    const updateAutoComplete = (enable: boolean, interval: number) => {
        setAutoCompleteToggle(enable);
        setAutoCompleteInterval(interval);
        changeAutoCompleteParams(currentConversation?.id, enable, interval);
        setAutoCompleteModalOpen(false);
    };

    const toggleAutoComplete = (currentState) => {
        switch (currentState) {
            case true:
                setAutoCompleteToggle(false);
                changeAutoCompleteParams(
                    currentConversation?.id,
                    false,
                    autoCompleteInterval as number,
                );
                break;
            case false:
                setAutoCompleteToggle(true);
                changeAutoCompleteParams(
                    currentConversation?.id,
                    true,
                    autoCompleteInterval as number,
                );
                break;
        }
    };

    const handleIntervalChange = (interval: number) => {
        setAutoCompleteInterval(interval);
        changeAutoCompleteParams(currentConversation?.id, autoCompleteToggle, interval);
    };

    const fileName =
        autoCompleteToggle === true ? '/images/clock-play.svg' : '/images/clock-stop.svg';

    useEffect(() => {
        setAutoCompleteToggle(presetAutoCompleteToggle);
        switch (presetAutoCompleteInterval) {
            case 0:
            case null:
            case undefined:
                setAutoCompleteInterval(preferences?.autoCompletedInactiveConversation?.interval);
                break;
            default:
                setAutoCompleteInterval(presetAutoCompleteInterval);
        }
    }, [
        presetAutoCompleteToggle,
        presetAutoCompleteInterval,
        preferences?.autoCompletedInactiveConversation?.interval,
    ]);

    useEffect(() => {
        const controller = new AbortController();
        const timeout = setTimeout(() => {
            if (controller.signal.aborted) return;
            sendCustomEvent(FOCUS_STATUS_MANAGER_GET_REQUEST_DATA_LIST, {
                callerId: currentConversationId,
                filter: USER_FOCUS_IN_CONVERSATION,
                conversationId: currentConversationId,
            });
        }, 500);

        const interval = setInterval(() => {
            if (controller.signal.aborted) return;
            sendCustomEvent(FOCUS_STATUS_MANAGER_GET_REQUEST_DATA_LIST, {
                callerId: currentConversationId,
                conversationId: currentConversationId,
            });
        }, 15000); // 15 seconds to pull again.

        const handleReceiveFocusStatusEvent = (e: CustomEvent<{ data: FocusReturnDataType }>) => {
            const { data } = e.detail;
            if (data?.callerId !== currentConversationId) return;
            if (!data?.data?.length) {
                setFocusingStatus((pre) => {
                    if (pre) return !pre;
                    return pre;
                });
                return;
            }

            const focusStatus =
                data?.data?.find((item) => item.conversationId)?.status ===
                USER_FOCUS_IN_CONVERSATION;
            setFocusingStatus(focusStatus);
        };

        const handleReceivePushFocusStatusManager = (
            e: CustomEvent<{ data: FocusReturnDataType }>,
        ) => {
            const { data } = e.detail;
            if (!data?.data?.length) return;
            const found = data?.data?.find((item) => item.conversationId === currentConversationId);
            if (found) {
                setFocusingStatus(() => {
                    return found?.status === USER_FOCUS_IN_CONVERSATION;
                });
            }
        };

        createCustomEventListener(
            FOCUS_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
            handleReceiveFocusStatusEvent,
            controller.signal,
        );

        createCustomEventListener(
            FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
            handleReceivePushFocusStatusManager,
            controller.signal,
        );

        controller.signal.addEventListener('abort', () => {
            clearInterval(interval);
            clearTimeout(timeout);
        });

        return () => {
            controller.abort();
        };
    }, [currentConversationId]);

    if (!customer) return null;

    return (
        <>
            <Paper
                id={TOP_BAR_ID_NAME}
                className={classes.paperStyle}
                radius={0}
                sx={{
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderBottom: '1px solid #f0f0f0',
                }}
            >
                <Flex
                    sx={{
                        width: '100%',
                        display: 'grid',
                        position: 'relative',
                        gridTemplateColumns: '1fr auto',
                    }}
                >
                    <Flex
                        p="sm"
                        pl={10}
                        align="center"
                        justify={'flex-start'}
                        sx={{
                            height: '60px',
                            display: 'grid',
                            position: 'relative',
                            gridTemplateColumns: 'auto auto 35%',
                        }}
                    >
                        <AspectRatio ratio={38 / 38} w={38} maw={300} mx={'0px'}>
                            <Preview
                                showFileName={false}
                                imageUrl={customer.picture}
                                showDownloadButton={false}
                            >
                                <FocusingAvatarIndicator focusing={focusingStatus}>
                                    <Avatar radius="xl" alt="it's me" src={customer.picture} />
                                </FocusingAvatarIndicator>
                            </Preview>
                        </AspectRatio>
                        <Menu trigger={'hover'}>
                            <Menu.Target>
                                <Text
                                    ml={10}
                                    truncate
                                    className={classes.textName}
                                    sx={{
                                        fontSize: responsiveScreen ? '14px' : undefined,
                                    }}
                                >
                                    {nameDisplayed}
                                </Text>
                            </Menu.Target>
                            <Menu.Dropdown>
                                <CopyButton value={nameDisplayed}>
                                    {({ copy }) => (
                                        <Menu.Item onClick={copy}>{nameDisplayed}</Menu.Item>
                                    )}
                                </CopyButton>
                            </Menu.Dropdown>
                        </Menu>
                        <TopBarId id={customer?.id} textIdStyle={classes.textId} />
                    </Flex>
                    <Flex
                        p={responsiveScreen ? 'xs' : 'sm'}
                        pr={responsiveScreen ? 10 : 23}
                        align="center"
                        sx={{
                            height: '60px',
                        }}
                        gap={10}
                    >
                        {isBookmarkedConversation ? (
                            <IconBookmarkFilled
                                cursor="pointer"
                                className={classes.filledBookmark}
                                onClick={() => toggleBookmark('remove')}
                            />
                        ) : (
                            <IconBookmark
                                cursor="pointer"
                                color="#1D2088"
                                onClick={() => toggleBookmark('add')}
                            />
                        )}
                        {preferences?.autoCompletedInactiveConversation?.enable && (
                            <Menu shadow="md" width={228} position="bottom-end">
                                <Tooltip
                                    label={
                                        t('autoCloseTooltipText') +
                                        (autoCompleteToggle === true ? 'ON' : 'OFF')
                                    }
                                    withArrow
                                    position={'bottom'}
                                    arrowSize={6}
                                    offset={10}
                                    transitionProps={{ transition: 'pop', duration: 300 }}
                                    radius={6}
                                >
                                    <Menu.Target>
                                        <Image
                                            src={getPublicUrl(fileName)}
                                            width={24}
                                            height={24}
                                            alt="LOGO"
                                            style={{ width: 24, height: 24 }}
                                        />
                                    </Menu.Target>
                                </Tooltip>
                                <Menu.Dropdown
                                    sx={{
                                        borderRadius: '6px',
                                        maxWidth: '200px',
                                        minWidth: '200px',
                                        width: '200px',
                                    }}
                                >
                                    <Menu.Item
                                        sx={{
                                            textAlign: 'left',
                                            fontSize: '0.8rem',
                                        }}
                                        onClick={() => setAutoCompleteModalOpen(true)}
                                        disabled={!autoCompleteToggle}
                                    >
                                        {t('autoCloseMenuChangeTime')}
                                    </Menu.Item>
                                    <Menu.Item
                                        sx={{
                                            textAlign: 'left',
                                            fontSize: '0.8rem',
                                        }}
                                        onClick={() => toggleAutoComplete(autoCompleteToggle)}
                                    >
                                        {autoCompleteToggle === true
                                            ? t('autoCloseMenuToggleOff')
                                            : t('autoCloseMenuToggleOn')}
                                    </Menu.Item>
                                </Menu.Dropdown>
                            </Menu>
                        )}
                        <If condition={showWrapUpButton}>
                            <Button
                                onClick={openWrapUpModal}
                                className={classes.buttonWrapUp}
                                sx={{
                                    fontSize: responsiveScreen ? '12px' : undefined,
                                    height: responsiveScreen ? '30px' : undefined,
                                }}
                            >
                                {t('wrapUpBtn')}
                            </Button>
                        </If>

                        <If condition={showFinishButton}>
                            <Button
                                className={classes.buttonEnd}
                                onClick={open}
                                sx={{
                                    fontSize: responsiveScreen ? '12px' : undefined,
                                    height: responsiveScreen ? '30px' : undefined,
                                    // width: '100%'
                                }}
                            >
                                {t('closeRoomBtn')}
                            </Button>
                        </If>
                        <If condition={isCompleted}>
                            <Text
                                truncate
                                fw={700}
                                maw={'auto'}
                                size={'14px'}
                                c={'#25262B'}
                                sx={{
                                    lineHeight: '21.7px',
                                }}
                            >
                                {t('completed')}
                            </Text>
                        </If>
                    </Flex>
                </Flex>
            </Paper>
            <Modal opened={opened} size={'400px'} onClose={close} centered withCloseButton={false}>
                <Modal.Header style={{ padding: 0, height: '36px', minHeight: 'unset' }}>
                    <Modal.CloseButton className="" size={'lg'} />
                </Modal.Header>
                <Modal.Body p={0}>
                    <Flex
                        pl={0}
                        pr={0}
                        gap={10}
                        mb={'lg'}
                        direction={'column'}
                        justify={'flex-start'}
                    >
                        <Text className={classes.messageConfirmClose}>
                            {t('confirmCloseConversationMessage')}
                        </Text>
                        <Text className={classes.messageExplain}>
                            {t('explainMessageCloseConversation')}
                        </Text>
                    </Flex>
                    <Group gap={'lg'} justify="flex-end">
                        <Button className={classes.buttonConfirm} onClick={handleCloseConversation}>
                            {t('confirmClosedButton')}
                        </Button>
                        <Button className={classes.buttonCancel} onClick={close}>
                            {t('cancelClosedButton')}
                        </Button>
                    </Group>
                </Modal.Body>
            </Modal>
            <Modal
                centered
                size={'400px'}
                withCloseButton={false}
                onClose={closeWrapUpModal}
                opened={openWrapUpConfirm}
            >
                <Modal.Header style={{ padding: 0, height: '36px', minHeight: 'unset' }}>
                    <Modal.CloseButton className="" size={'lg'} />
                </Modal.Header>
                <Modal.Body p={0}>
                    <Flex
                        pl={0}
                        pr={0}
                        gap={10}
                        mb={'lg'}
                        direction={'column'}
                        justify={'flex-start'}
                    >
                        <Text className={classes.messageConfirmClose}>
                            {t('confirmWrapUpConversation')}
                        </Text>
                        <Text className={classes.messageExplain} ta="left">
                            {t('explainWrapUpConversation')}
                        </Text>
                    </Flex>
                    <Group gap={'lg'} justify="flex-end">
                        <Button
                            className={classes.buttonWrapUp}
                            onClick={handleWrapUpConversation}
                            color="navy.0"
                        >
                            {t('wrapUpButton')}
                        </Button>
                        <Button className={classes.buttonCancel} onClick={closeWrapUpModal}>
                            {t('cancelClosedButton')}
                        </Button>
                    </Group>
                </Modal.Body>
            </Modal>
            <Modal
                centered
                size={'600px'}
                withCloseButton={false}
                onClose={closeWrapUpModal}
                opened={autoCompleteModalOpen}
            >
                <Container style={{ display: 'flex', alignItems: 'self-start', padding: 0 }}>
                    <Modal.Title
                        title={t('autoCloseMenuTitle')}
                        mb={'lg'}
                        pl={'md'}
                        className={classes.modalTitle}
                    >
                        {t('autoCloseMenuTitle')}
                    </Modal.Title>
                    <Modal.CloseButton
                        size={'lg'}
                        className={classes.autoCompleteModalXBtn}
                        onClick={() => setAutoCompleteModalOpen(false)}
                    />
                </Container>
                <Modal.Body mt={'lg'}>
                    <Container pl={0} pr={0} mb={'lg'} style={{ overflowY: 'visible' }}>
                        <Center mb={'lg'} style={{ display: 'flex', alignItems: 'baseline' }}>
                            <Text className={classes.messageExplain} ta="center">
                                {t('autoCloseMenuTextOne')}
                            </Text>
                            <NativeSelect
                                mt="md"
                                data={[
                                    { label: '5' + t('minutes'), value: '5' },
                                    {
                                        label: '10' + t('minutes'),
                                        value: '10',
                                    },
                                    {
                                        label: '15' + t('minutes'),
                                        value: '15',
                                    },
                                    {
                                        label: '20' + t('minutes'),
                                        value: '20',
                                    },
                                    {
                                        label: '25' + t('minutes'),
                                        value: '25',
                                    },
                                    {
                                        label: '30' + t('minutes'),
                                        value: '30',
                                    },
                                    {
                                        label: '35' + t('minutes'),
                                        value: '35',
                                    },
                                    {
                                        label: '40' + t('minutes'),
                                        value: '40',
                                    },
                                    {
                                        label: '45' + t('minutes'),
                                        value: '45',
                                    },
                                    {
                                        label: '50' + t('minutes'),
                                        value: '50',
                                    },
                                    {
                                        label: '55' + t('minutes'),
                                        value: '55',
                                    },
                                    { label: '1' + t('hours'), value: '60' },
                                    { label: '2' + t('hours'), value: '120' },
                                    { label: '3' + t('hours'), value: '180' },
                                ]}
                                w={100}
                                style={{ margin: '0 10px' }}
                                defaultValue={String(autoCompleteInterval)}
                                onChange={(e) => handleIntervalChange(Number(e.target.value))}
                            />{' '}
                            <Text>{t('autoCloseMenuTextTwo')}</Text>
                        </Center>
                        <Group gap={'lg'} justify="flex-end" style={{ paddingTop: '20px' }}>
                            <Button
                                className={classes.buttonCancel}
                                onClick={() => setAutoCompleteModalOpen(false)}
                            >
                                {t('cancelClosedButton')}
                            </Button>
                            <Button
                                className={classes.buttonSave}
                                onClick={() =>
                                    updateAutoComplete(true, autoCompleteInterval as number)
                                }
                            >
                                {t('save')}
                            </Button>
                        </Group>
                    </Container>
                </Modal.Body>
            </Modal>
        </>
    );
};

export default TopBar;
