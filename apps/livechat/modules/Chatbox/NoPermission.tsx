import { Flex, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';

const noPermission = false;
const NoPermission: React.FC<any> = () => {
  const { t } = useTranslate('workspace');

  if (noPermission) {
    return (
      <Flex h={'100%'} align={'center'} justify={'center'}>
        <Text size={rem(14)}>{t('noPermission')}</Text>
      </Flex>
    );
  }
  return null;
};

export default NoPermission;
