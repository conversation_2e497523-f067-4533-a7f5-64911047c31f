import { Button } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTolgee } from '@tolgee/react';
import dayjs from 'dayjs';
// Just some common buttons used in team and user settings
import { CSSProperties, ReactNode } from 'react';

export const jaDisplayFormat = 'YYYY年M月D日';
export const enDisplayFormat = 'YYYY/MM/DD';

const useStyle = createStyles((theme) => ({
  buttonConfirm: {
    minWidth: '86px',
    // backgroundColor: theme.colors.navy[0],
    borderRadius: theme.radius.md,
    fontWeight: 600,
    fontSize: '14px',
    padding: '3px 10px 3px 10px',
    '&:hover': {
      backgroundColor: '#101155',
    },
  },
  buttonCancel: {
    minWidth: '86px',
    backgroundColor: 'white',
    borderWidth: '1.5px',
    borderRadius: '8px',
    fontWeight: 600,
    fontSize: '14px',
    color: theme.colors.gray[7],
    padding: '3px 10px 3px 10px',
    borderColor: '#5C5F66',
    '&:hover': {
      backgroundColor: '#f0f0f0',
    },
  },
}));

type CommonButtonProps = {
  label?: ReactNode;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  style?: CSSProperties;
  loading?: boolean;
};

function Cancel({
  label = '',
  onClick,
  disabled = false,
  className = '',
  style,
}: CommonButtonProps) {
  const { classes } = useStyle();
  return (
    <Button
      className={`${classes.buttonCancel} ${className}`}
      onClick={onClick}
      disabled={disabled}
      style={style}
      styles={(theme) => ({
        root: {
          '&:hover': {
            color: theme.colors.gray[7],
          },
        },
      })}
    >
      {label ?? ''}
    </Button>
  );
}

function Confirm({
  label = '',
  onClick,
  disabled = false,
  className = '',
  style,
  loading = false,
}: CommonButtonProps) {
  const { classes } = useStyle();
  return (
    <Button
      color='navy.0'
      variant='filled'
      className={`${classes.buttonConfirm} ${className}`}
      onClick={onClick}
      disabled={disabled}
      style={style}
      loading={loading}
      styles={(theme) => ({
        root: {
          '&:hover:disabled': {
            backgroundColor: theme.colors.gray[1],
          },
        },
      })}
    >
      {label ?? ''}
    </Button>
  );
}

export const Buttons = {
  Cancel,
  Confirm,
};

export function isDate(date: string | number | Date) {
  const dateParse = new Date(date);
  return !isNaN(dateParse as any);
}

export function useDayFormat() {
  const tolgee = useTolgee();
  const lang = tolgee.getLanguage();
  return lang === 'ja' ? jaDisplayFormat : enDisplayFormat;
}

export function useFuncFormatDate() {
  const format = useDayFormat();
  return (date: Date | string | null) => {
    if (date !== null && isDate(date)) return dayjs(date).format(format);
    return null;
  };
}
