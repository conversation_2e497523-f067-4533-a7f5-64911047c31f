import { Group, MantineSize, Modal } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { Buttons } from '..';

type MantineNumberSize = number | MantineSize | (string & {});
const useStyle = createStyles((theme) => ({
    buttonNavy: {
        width: 'fit-content',
        backgroundColor: '#1D2088',
        fontWeight: 700,
        '&:hover': {
            backgroundColor: '#3539BC',
        },
    },
    buttonRed: {
        width: 'fit-content',
        backgroundColor: theme.colors.red[5],
        fontWeight: 700,
        '&:hover': {
            backgroundColor: `${theme.colors.red[8]} !important`,
        },
    },
    divPreLine: {
        textAlign: 'left',
        lineHeight: '1.6em',
        whiteSpace: 'pre-line',
    },
    buttonCancel: {
        width: 'fit-content',
    },
    content: {
        color: theme.colors.dark[3],
        textAlign: 'left',
        whiteSpace: 'pre-line',
        marginBottom: '20px',
    },
}));

type ConfirmDisableTeamType = {
    opened: boolean;
    // eslint-disable-next-line no-unused-vars
    onClose: (confirm?: boolean) => void;
    titleConfirm: string;
    content?: string;
    btnConfirmLabel?: string;
    btnCancelLabel?: string;
    btnConfirmColor?: 'red' | 'violet';
    reverse?: boolean;
    size?: MantineNumberSize;
    loadingConfirm?: boolean;
};

export default function ConfirmModal({
    opened,
    onClose,
    titleConfirm = '',
    content = '',
    btnCancelLabel = null,
    btnConfirmLabel = null,
    btnConfirmColor = 'violet',
    reverse = false,
    size = 'lg',
    loadingConfirm = false,
}: ConfirmDisableTeamType) {
    const { t } = useTranslate('workspace');
    const { classes } = useStyle();
    const handleOnCancel = () => {
        onClose();
    };

    const handleOnConfirm = () => {
        onClose(true);
    };

    return (
        <Modal
            opened={opened}
            centered
            onClose={handleOnCancel}
            withCloseButton={false}
            size={size}
            radius={'md'}
        >
            <Modal.CloseButton className="" size={'lg'} style={{ right: '-94%' }} />
            <Modal.Title
                title={titleConfirm ?? ''}
                mt={'sm'}
                mb={'md'}
                sx={{ fontSize: '16px', fontWeight: 600, color: '#000000' }}
            >
                <div className={classes.divPreLine}>{titleConfirm ?? ''}</div>
            </Modal.Title>
            {content && <div className={classes.content}>{content}</div>}
            <Group
                justify="flex-start"
                mt={'30px'}
                style={reverse ? { flexDirection: 'row-reverse' } : {}}
            >
                <Buttons.Cancel
                    className={classes.buttonCancel}
                    onClick={handleOnCancel}
                    label={btnCancelLabel || t('modal_operator.button_cancel')}
                    disabled={loadingConfirm}
                />
                <Buttons.Confirm
                    className={btnConfirmColor === 'red' ? classes.buttonRed : classes.buttonNavy}
                    loading={loadingConfirm}
                    onClick={handleOnConfirm}
                    label={btnConfirmLabel || t('confirm_label')}
                />
            </Group>
        </Modal>
    );
}
