import React from 'react';
import { AiAutoAdjustmentContextProvider } from '../AIAutoAdjustment/context';
import Editor from './Editor';
import { TextEditorContextProvider } from './context';
import { ThemeContextProvider } from './themeContext';

interface Props {
  lang: string;
}

const TextEditor: React.FC<Props> = ({ lang }) => {
  return (
    <TextEditorContextProvider>
      <AiAutoAdjustmentContextProvider>
        <ThemeContextProvider>
          <Editor lang={lang} />
        </ThemeContextProvider>
      </AiAutoAdjustmentContextProvider>
    </TextEditorContextProvider>
  );
};

export default TextEditor;
