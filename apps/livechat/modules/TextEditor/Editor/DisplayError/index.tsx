import { Grid } from '@mantine/core';
import { useDebouncedState } from '@mantine/hooks';
import React, { useEffect } from 'react';
import { useUploadAssetContext } from '../../../uploadAssetContext';
import { useTextEditorContext } from '../../context';
import ErrorDetail from './ErrorDetail';

const useUploadAssetContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { rejectErrors, LIMIT_SIZE, LIMIT_FILE_PER_UPLOAD } = useUploadAssetContext();
    return {
      rejectErrors,
      LIMIT_SIZE,
      LIMIT_FILE_PER_UPLOAD,
    };
  } catch (e) {
    return {
      rejectErrors: {},
      LIMIT_SIZE: 0,
      LIMIT_FILE_PER_UPLOAD: 0,
    };
  }
};

const convertBytesToMegaBytes = (bytes: number) => {
  return bytes / (1024 * 1024);
};
const DisplayError: React.FC<any> = () => {
  const { t } = useTextEditorContext();
  const { rejectErrors, LIMIT_SIZE, LIMIT_FILE_PER_UPLOAD } = useUploadAssetContextIfExist();
  const [oldData, setOldData] = useDebouncedState([], 500);
  const array = Object.entries(rejectErrors)
    // get the error array with key and value
    .map(([key, value]: [string, boolean]) => {
      // if the value is true, then return the key
      if (value) {
        return key;
      }
      // else return null
      return null;
    })
    // remove the null
    .filter((error: string | null) => error !== null)
    // flatten the array
    .flat(1);
  const uniqueSet = new Set(array);
  // convert a set to array &
  const data = Array.from(uniqueSet);

  useEffect(() => {
    if (data.length === 0 && oldData.length === 0) {
      return;
    }
    setOldData(data);
  }, [data, oldData, setOldData]);

  if (oldData.length === 0) {
    return null;
  }
  return (
    <Grid
      grow
      mt={'0'}
      ml={'1rem'}
      mr={'1rem'}
      w={'auto'}
      sx={{
        position: 'relative',
      }}
    >
      {/* show the reject error here */}
      {oldData.map((error: string, idx: number) => {
        // remove the "-" in the error code to make it work with i18n
        const errorCode = error.split('-').join('');
        return (
          <Grid.Col key={'error' + idx} h={'100%'} w={'auto'} pt={0} pl={0} pr={0}>
            <ErrorDetail
              code={error}
              message={t('asset.upload.error.' + errorCode, {
                LIMIT_SIZE: convertBytesToMegaBytes(LIMIT_SIZE),
                LIMIT_FILE_PER_UPLOAD: LIMIT_FILE_PER_UPLOAD,
              })}
            />
          </Grid.Col>
        );
      })}
    </Grid>
  );
};

export default DisplayError;
