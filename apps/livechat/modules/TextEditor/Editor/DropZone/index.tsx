import { Flex, Group, Text, rem, useMantineColorScheme, useMantineTheme } from '@mantine/core';
import { Dropzone } from '@mantine/dropzone';
import { createStyles } from '@mantine/emotion';
import { IconDragDrop, IconPhoto, IconUpload } from '@tabler/icons-react';
import React from 'react';
import { useMessageContext } from '../../../messageContext';
import { useUploadAssetContext } from '../../../uploadAssetContext';
import { useTextEditorContext } from '../../context';

const useStyles = createStyles((theme) => ({
  dropzoneRoot: {
    padding: '0',
    '&[data-reject]': {
      backgroundColor: '#FFFFFF',
      borderColor: theme.colors.gray[4],
    },
  },
}));

const DropZone: React.FC<any> = () => {
  const { t } = useTextEditorContext();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const { setRejectErrors, LIMIT_SIZE, ALLOW_MIME_TYPES } = useUploadAssetContext();
  const { addAttachmentFiles } = useMessageContext();
  const { classes } = useStyles();

  return (
    <>
      <Dropzone
        onClick={() => null}
        multiple={true}
        onDrop={(files) => {
          addAttachmentFiles(files);
        }}
        onReject={(files) => {
          const errorList = files
            .map((file) => file.errors)
            .map((error) => {
              return error.map((err) => err.code);
            })
            .flat(1);
          setRejectErrors((prev: Record<string, boolean>) => {
            const newRejectErrors = { ...prev };
            errorList.forEach((error) => {
              newRejectErrors[error] = true;
            });
            return newRejectErrors;
          });
        }}
        maxSize={LIMIT_SIZE}
        accept={ALLOW_MIME_TYPES}
        activateOnClick={false}
        classNames={{
          root: classes.dropzoneRoot,
        }}
      >
        <Group justify='center' gap='xl' style={{ minHeight: rem(180), pointerEvents: 'none' }}>
          <Dropzone.Accept>
            <IconUpload
              size='2.3rem'
              stroke={1.5}
              color={theme.colors[theme.primaryColor][colorScheme === 'dark' ? 4 : 6]}
            />
          </Dropzone.Accept>
          <Dropzone.Reject>
            <Flex direction={'column'} justify={'center'} align={'center'}>
              <IconDragDrop size='1.5rem' stroke={1.5} />
              <Text size='sm' c={'dark.3'} inline mt={20} fw={600}>
                {t('dropZone.description')}
              </Text>
              <Text size='xs' c={'dark.2'} inline mt={20} fw={400}>
                {t('dropZone.warning1')}
              </Text>
              <Text size='xs' c={'dark.2'} inline mt={8} fw={400}>
                {t('dropZone.warning2')}
              </Text>
              <Text size='xs' c={'dark.2'} inline mt={8} fw={400}>
                {t('dropZone.warning3')}
              </Text>
            </Flex>
          </Dropzone.Reject>

          <Dropzone.Idle>
            <Flex direction={'column'} justify={'center'} align={'center'}>
              <IconPhoto size='2rem' stroke={1.5} />
              <Text size='md' c={'dark.3'} inline mt={20} fw={600}>
                {t('dropZone.description')}
              </Text>
            </Flex>
          </Dropzone.Idle>
        </Group>
      </Dropzone>
    </>
  );
};

export default DropZone;
