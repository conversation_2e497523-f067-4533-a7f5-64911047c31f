import { ActionIcon, Box, Flex, Image, LoadingOverlay } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlayerPlay } from '@tabler/icons-react';
import React, { useEffect, useMemo } from 'react';
import Preview from '../../../../components/Preview';
import { VIDEO_EXTENSIONS } from '../../../Preview/constants';
import CloseSvg from './CloseSvg';
import PdfPreview from './PdfPreview';

interface Props {
  resourceId: string;
  resourceType: any;
  removeAttachmentFile: any;
  attachmentUrl: any;
  resourceName: string;
  isUploading: boolean;
}

const useStyles = createStyles((theme) => ({
  attachmentContainer: {
    marginLeft: theme.spacing.lg,
  },
  attachmentWrapper: {
    position: 'relative',
  },
  pdfContainer: {
    maxWidth: 180,
    position: 'relative',
  },
  imageContainer: {
    maxWidth: 100,
    position: 'relative',
  },
  videoContainer: {
    maxWidth: 100,
    maxHeight: 100,
    overflow: 'hidden',
    borderRadius: '10px',
    backgroundColor: '#000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    borderRadius: '10px',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    color: 'white',
    zIndex: 1,
  },
  image: {
    overflow: 'hidden',
  },
  closeButton: {
    top: -10,
    zIndex: 1,
    right: -10,
    transition: 'none',
    position: 'absolute',
  },
}));

const DisplayAttachment: React.FC<Props> = ({
  resourceId,
  resourceType,
  removeAttachmentFile,
  attachmentUrl,
  resourceName,
  isUploading = false,
}) => {
  const [hovered, setHovered] = React.useState(false);
  const [isLoaded, setIsLoaded] = React.useState(false);
  const { classes } = useStyles();
  const isImage = resourceType.includes('image');
  const isVideo = resourceType.includes('video');
  const isPdf = resourceType.includes('document') || resourceType.includes('pdf');
  const showLoadingOverlay = !!isUploading;

  // Check if the file is a video based on URL extension
  const isVideoByUrl = useMemo(() => {
    return VIDEO_EXTENSIONS.some((ext) => attachmentUrl?.endsWith(ext));
  }, [attachmentUrl]);

  // Check if the file is a PDF based on URL extension
  const isPdfByUrl = useMemo(() => {
    return attachmentUrl?.toLowerCase().endsWith('.pdf');
  }, [attachmentUrl]);

  useEffect(() => {
    setIsLoaded(false);
  }, [resourceId]);

  useEffect(() => {
    if (isPdf || isPdfByUrl) {
      setIsLoaded(true);
    }
  }, [isPdf, isPdfByUrl]);

  if (!isImage && !isVideo && !isVideoByUrl && !isPdf && !isPdfByUrl) {
    return null;
  }

  return (
    <Flex className={classes.attachmentContainer}>
      <Flex
        align={'center'}
        justify={'center'}
        direction={'column'}
        className={classes.attachmentWrapper}
        sx={{
          visibility: isLoaded ? 'visible' : 'hidden',
        }}
        onMouseEnter={() => !isUploading && setHovered(true)}
        onMouseLeave={() => !isUploading && setHovered(false)}
      >
        {isPdf || isPdfByUrl ? (
          <Box className={classes.pdfContainer}>
            <LoadingOverlay
              visible={showLoadingOverlay}
              overlayProps={{ blur: 2 }}
              loaderProps={{ color: 'navy.0' }}
            />
            <PdfPreview
              fileName={resourceName}
              fileSize={0} // TODO: Get actual file size when available
              maxWidth={180}
              maxHeight={100}
              url={attachmentUrl}
            />
          </Box>
        ) : (
          <Preview imageUrl={attachmentUrl} imageName={resourceName}>
            <Box className={classes.imageContainer}>
              <LoadingOverlay
                visible={showLoadingOverlay}
                overlayProps={{ blur: 2 }}
                loaderProps={{ color: 'navy.0' }}
              />
              {isVideo || isVideoByUrl ? (
                <Box className={classes.videoContainer}>
                  <video
                    src={attachmentUrl}
                    className={classes.video}
                    muted
                    onLoadedData={() => {
                      setIsLoaded(true);
                    }}
                  />
                  <IconPlayerPlay size={24} className={classes.playIcon} />
                </Box>
              ) : (
                <Image
                  maw={100}
                  mah={100}
                  className={classes.image}
                  radius={'10px'}
                  alt='attachment'
                  fit='contain'
                  src={attachmentUrl}
                  onLoad={() => {
                    setIsLoaded(true);
                  }}
                />
              )}
            </Box>
          </Preview>
        )}
        <ActionIcon
          variant='transparent'
          color={'red'}
          className={classes.closeButton}
          sx={{
            visibility: hovered ? 'visible' : 'hidden',
          }}
          onClick={removeAttachmentFile}
        >
          <CloseSvg />
        </ActionIcon>
      </Flex>
    </Flex>
  );
};

export default DisplayAttachment;
