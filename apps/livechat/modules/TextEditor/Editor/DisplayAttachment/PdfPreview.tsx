import { Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import dayjs from 'dayjs';
import React, { useEffect, useState, useRef, useMemo } from 'react';
import PdfIcon from '../../../Chatbox/MessageHolders/icons/PdfIcon';
import { KB, MB } from '../../../Preview/constants';

interface PdfPreviewProps {
  fileName: string;
  fileSize?: number; // in bytes
  maxWidth?: number;
  maxHeight?: number;
  url?: string;
}

const useStyles = createStyles((theme) => ({
  documentContainer: {
    padding: '10px',
    border: `1px solid ${theme.colors.documentDisplayColors[0]}`,
    borderColor: theme.colors.documentDisplayColors[0],
    borderRadius: '8px',
    '&:hover': {
      backgroundColor: theme.colors.documentDisplayColors[5],
      borderColor: theme.colors.documentDisplayColors[9],
    },
  },
  iconContainer: {
    marginRight: '10px',
  },
  downloadIcon: {
    cursor: 'pointer',
    display: 'flex',
    alignSelf: 'center',
    marginLeft: '10px',
  },
  textContainer: {
    maxWidth: 200,
    overflow: 'hidden',
  },
  fileName: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
}));

const PdfPreview: React.FC<PdfPreviewProps> = ({
  fileName,
  fileSize: propFileSize,
  maxWidth = 100,
  maxHeight = 100,
  url,
}) => {
  const [fileSize, setFileSize] = useState<string | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const documentRef = useRef<HTMLDivElement | null>(null);
  const { classes } = useStyles();

  useEffect(() => {
    if (propFileSize) {
      const formattedSize =
        propFileSize < MB
          ? `${(propFileSize / KB).toFixed(0)} KB`
          : `${(propFileSize / MB).toFixed(0)} MB`;
      setFileSize(formattedSize);
    } else if (url) {
      const fetchFileSize = async () => {
        try {
          const response = await fetch(url, { method: 'HEAD' });
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const contentLength = response.headers.get('Content-Length');
          if (contentLength) {
            const sizeInBytes = parseInt(contentLength, 10);
            const formattedSize =
              sizeInBytes < MB
                ? `${(sizeInBytes / KB).toFixed(0)} KB`
                : `${(sizeInBytes / MB).toFixed(0)} MB`;
            setFileSize(formattedSize);
          } else {
            console.warn('Content-Length header is missing');
            // Fallback to GET request
            const getResponse = await fetch(url);
            const blob = await getResponse.blob();
            const sizeInBytes = blob.size;
            const formattedSize =
              sizeInBytes < MB
                ? `${(sizeInBytes / KB).toFixed(0)} KB`
                : `${(sizeInBytes / MB).toFixed(0)} MB`;
            setFileSize(formattedSize);
          }
        } catch (error) {
          console.error('Error fetching file size:', error);
          setHasError(true);
        }
      };

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              fetchFileSize();
              observer.disconnect();
            }
          });
        },
        { threshold: 0.1 }
      );

      if (documentRef.current) {
        observer.observe(documentRef.current);
      }

      return () => {
        observer.disconnect();
      };
    }
  }, [url, propFileSize]);

  const isValidUrl = (urlString: string) => {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  };

  const displayFileName = useMemo(() => {
    if (fileName) {
      return fileName;
    }
    const now = dayjs();
    const formattedDate = now.format('YYYYMMDD_HHmm');
    return `${formattedDate}.pdf`;
  }, [fileName]);

  return (
    <Flex
      align='start'
      justify='space-between'
      className={classes.documentContainer}
      ref={documentRef}
      style={{ maxWidth, maxHeight }}
      onClick={(e) => e.stopPropagation()}
    >
      <Flex align='center' className={classes.iconContainer}>
        <PdfIcon width={24} height={24} />
      </Flex>
      <div className={classes.textContainer}>
        <Text size='md' fw={500} className={classes.fileName}>
          {displayFileName}
        </Text>
        {fileSize && (
          <Text size='sm' c='gray'>
            {fileSize} PDF
          </Text>
        )}
      </div>
    </Flex>
  );
};

export default PdfPreview;
