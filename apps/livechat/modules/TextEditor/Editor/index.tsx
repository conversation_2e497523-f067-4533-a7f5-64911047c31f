import { Container, Select } from '@mantine/core';
import { useDebouncedState } from '@mantine/hooks';
import { RichTextEditor } from '@mantine/tiptap'; // Link
import { If } from '@resola-ai/ui';
import { LocalStorageUtils, createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import Placeholder from '@tiptap/extension-placeholder';
import { Extension, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import {
  SENDER_REPLY_TYPE,
  TEXT_EDITOR_ID_NAME,
  orgIconUrl,
  teamIconUrl,
} from '../../../constants';
import { LAST_SENDER_TYPE } from '../../../constants/menu';
import { useAppContext } from '../../appContext';
import { useConversationActionContext } from '../../conversationActionContext';
import { useMessageContext } from '../../messageContext';
import { useOperationSettingContext } from '../../operationSettingContext';
import { useUserContext } from '../../userContext';
import { CONVERSATION_PLATFORM_LINE } from '../constant';
import { useTextEditorContext } from '../context';
import DisplayAttachments from './DisplayAttachments';
import DisplayError from './DisplayError';
import DropZone from './DropZone';
import Tools from './Tools';
import { useDraftContent } from './hooks/useDraftContent';

interface Props {
  lang: string;
  autoReplyEditor?: boolean;
}

const borderColor = '#CED4DA';
const backgroundColor = '#FFFFFF';
const containerBackgroundColor = '#F8F9FA';

// scrollbar style is custom, so leave it separate from the theme for now.
const customStyleForScrollBar = {
  // customize scrollbar
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#e9e9e9',
    borderRadius: '8px',
    backgroundClip: 'padding-box',
    border: '1px solid transparent',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: '#c7c7c7',
  },
};

const DisableEnter = Extension.create({
  addKeyboardShortcuts() {
    return {
      Enter: () => true,
    };
  },
});

const useDropZone = () => {
  const [activeDropZone, setActiveDropZone] = useDebouncedState(false, 0);
  return {
    activeDropZone,
    setActiveDropZone,
  };
};

const mainContentZIndex = 2;

const useConversationContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { currentConversation, platformLine } = useConversationActionContext();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useMemo(
      () => ({
        currentConversation,
        platformLine,
      }),
      [currentConversation, platformLine]
    );
  } catch (e) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useMemo(
      () => ({
        platformLine: false,
        currentConversation: null,
      }),
      []
    );
  }
};
const useMessageContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendMessage, uploadResourceLoading, sender, setSender } = useMessageContext();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useMemo(
      () => ({
        sender,
        uploadResourceLoading,
        setSender,
        sendMessage,
      }),
      [sendMessage, uploadResourceLoading, sender, setSender]
    );
  } catch (e) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    return useMemo(
      () => ({
        sender: null,
        uploadResourceLoading: false,
        setSender: () => null,
        sendMessage: () => null,
      }),
      []
    );
  }
};

const Editor: React.FC<Props> = ({ autoReplyEditor }) => {
  const isComposingRef = useRef(false);
  const editorRef = useRef<HTMLDivElement | null>(null);
  const [replyBy, setReplyBy] = useState<string>(SENDER_REPLY_TYPE.OPERATOR);
  const { isOperationSettingPage } = useAppContext();
  const { userProfile, updateLastSenderType, sendMessageHotKey } = useUserContext();
  const { replyOutsideBusinessHour } = useOperationSettingContext();
  const { currentConversation = null, platformLine } = useConversationContextIfExist();
  const { sendMessage, uploadResourceLoading, sender, setSender } = useMessageContextIfExist();
  const { getDraftContent, updateDraftContent, clearDraftContent } = useDraftContent();
  const { t } = useTextEditorContext();
  const { activeDropZone, setActiveDropZone } = useDropZone();

  const editor = useEditor({
    extensions: [
      StarterKit,
      (Placeholder as any).configure({ placeholder: t('placeholder') }),
      // Link,
      DisableEnter,
    ],
    content: isOperationSettingPage ? replyOutsideBusinessHour.message : '',
    immediatelyRender: false,
    editorProps: {
      handlePaste(view, event, slice) {
        const json = slice.content.toJSON();
        if (
          (Array.isArray(json) && json.length && json.every((item) => item.type === 'text')) ||
          json.every(
            (item) =>
              item.type === 'paragraph' && item.content.every((subItem) => subItem.type === 'text')
          )
        ) {
          const pastedText = event.clipboardData.getData('text/plain');
          const convertedText = pastedText.replace(/<([^>]+)>/g, '$1');
          const newText = convertedText.replace(/\r\n|\r|\n/g, '<br>');
          const { state, dispatch } = view;
          const { selection } = state;
          dispatch(state.tr.insertText(newText, selection.from, selection.to));
          return true;
        }

        return false;
      },
    },
  });

  const containerPositionStyle = useMemo(
    () => (isOperationSettingPage ? 'static' : 'absolute'),
    [isOperationSettingPage]
  );

  const onChangeSender = useCallback(
    (sender: string) => {
      setReplyBy(sender);
      switch (sender) {
        case SENDER_REPLY_TYPE.TEAM:
          setSender({
            name: currentConversation?.team?.name,
            iconUrl: teamIconUrl,
            type: sender,
          });
          break;
        case SENDER_REPLY_TYPE.OPERATOR:
          setSender({
            name: userProfile?.displayName || userProfile?.name,
            iconUrl: userProfile?.picture,
            type: sender,
          });
          break;
        case SENDER_REPLY_TYPE.ORGANIZATION:
        default:
          setSender({
            name: userProfile?.org?.displayName,
            iconUrl: orgIconUrl,
            type: SENDER_REPLY_TYPE.ORGANIZATION,
          });
          break;
      }
    },
    [currentConversation, setSender, userProfile]
  );

  const handleSelectSender = useCallback(
    (sender: string) => {
      onChangeSender(sender);
      updateLastSenderType(sender);
    },
    [onChangeSender, updateLastSenderType]
  );
  const isHitSendMessageHotKey = useCallback(
    (e: KeyboardEvent) => {
      if (sendMessageHotKey === 'enter_shift') return e.key === 'Enter' && e.shiftKey;
      return e.key === 'Enter' && !e.metaKey && !e.ctrlKey && !e.altKey && !e.shiftKey;
    },
    [sendMessageHotKey]
  );
  const isHitOnlyEnterKey = useCallback(
    (e: KeyboardEvent) => {
      return (
        sendMessageHotKey === 'enter_shift' &&
        e.key === 'Enter' &&
        !e.metaKey &&
        !e.ctrlKey &&
        !e.altKey &&
        !e.shiftKey &&
        !isComposingRef.current
      );
    },
    [sendMessageHotKey]
  );

  const isHitOnlyShiftEnterKey = useCallback(
    (e: KeyboardEvent) => {
      return (
        sendMessageHotKey === 'enter_shift' &&
        e.key == 'Enter' &&
        e.shiftKey &&
        !e.metaKey &&
        !e.ctrlKey &&
        !e.altKey &&
        !isComposingRef.current
      );
    },
    [sendMessageHotKey]
  );

  const isHitAltEnterOnly = useCallback(
    (e: KeyboardEvent) => {
      return (
        sendMessageHotKey === 'enter' &&
        e.key == 'Enter' &&
        e.altKey &&
        !e.shiftKey &&
        !e.metaKey &&
        !e.ctrlKey &&
        !isComposingRef.current
      );
    },
    [sendMessageHotKey]
  );

  const handleSendMessageOnKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // THis line to handle the case :
      // Hit shift + enter to not create new line before send.
      if (isHitOnlyShiftEnterKey(e)) {
        e.preventDefault();
        // This is the way to prevent shift+ enter
        // from inserting new line from sending message.
        (editor.commands as any).undo();
      }
      // THis line to handle the case :
      // Hit enter to create new line .
      if (isHitOnlyEnterKey(e) || isHitAltEnterOnly(e)) {
        editor.commands.insertContent('<br>');
      }

      if (!isHitSendMessageHotKey(e)) {
        return;
      }

      if (isComposingRef.current) {
        // console.log('is using IME - typing in japanese or something');
        return;
      }

      if ((e as any).isComposing || (e as any).keyCode === 229) {
        // console.log('is using IME - typing in japanese or something');
        return;
      }

      const rawContent = editor?.getText();
      clearDraftContent(currentConversation?.id);
      editor?.commands.setContent('');

      let trimmedContent = rawContent.trim();

      if (trimmedContent === '') {
        return;
      }
      sendMessage(currentConversation?.id, trimmedContent, {
        sender,
      });
      sendCustomEvent('deca-livechat-sync-chat-message-height', {});
    },
    [
      isHitSendMessageHotKey,
      isHitOnlyShiftEnterKey,
      clearDraftContent,
      isHitOnlyEnterKey,
      isHitAltEnterOnly,
      sendMessage,
      currentConversation?.id,
      editor,
      sender,
    ]
  );

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // No apply enter keyboard in operation setting page
      if (isOperationSettingPage || uploadResourceLoading) return;
      handleSendMessageOnKeyDown(e);
    },
    [handleSendMessageOnKeyDown, isOperationSettingPage, uploadResourceLoading]
  );

  const handlePasteContent = useCallback(async () => {
    const newText = editor.getText();
    sendCustomEvent('deca-livechat-editor-message-update', {
      message: newText.replace(/\r\n|\r|\n/g, '<br>'),
    });
  }, [editor]);

  const propDragAndDrop = useMemo(() => {
    if (!platformLine) return {};
    return {
      onDragStart: () => {
        setActiveDropZone(true);
      },
      onDragOver: () => {
        setActiveDropZone(true);
      },
      onDragLeave: () => {
        setActiveDropZone(false);
      },
      onDrop: () => {
        setActiveDropZone(false);
      },
    };
  }, [platformLine, setActiveDropZone]);

  useEffect(() => {
    /* for OperationSetting page: Update content in editor whenever replyOutside data changes. */
    if (isOperationSettingPage) {
      editor?.commands.setContent(replyOutsideBusinessHour.message);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOperationSettingPage, replyOutsideBusinessHour]);

  useEffect(() => {
    const senderType =
      LocalStorageUtils.get(LAST_SENDER_TYPE) || userProfile?.conversationSetting?.lastSenderType;
    if (senderType) {
      // check if WEB platform and senderType is organization => change to operator
      if (
        currentConversation?.platform.name !== CONVERSATION_PLATFORM_LINE &&
        senderType === SENDER_REPLY_TYPE.ORGANIZATION
      ) {
        onChangeSender(SENDER_REPLY_TYPE.OPERATOR);
      } else {
        onChangeSender(senderType);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userProfile?.conversationSetting?.lastSenderType, currentConversation]);

  useEffect(() => {
    if (!platformLine) setActiveDropZone(false);
  }, [platformLine, setActiveDropZone]);

  useEffect(() => {
    const messageUpdateHandler = (e: any) => {
      if (e.detail.message !== undefined) {
        // set with line break \n
        editor?.commands.setContent(e.detail.message, true, {
          preserveWhitespace: 'full',
        });
      }
    };
    const unregisterEvent = createCustomEventListener(
      'deca-livechat-editor-message-update',
      messageUpdateHandler
    );
    return () => unregisterEvent();
  }, [editor]);

  useEffect(() => {
    const setupDraftContent = () => {
      const draftContent = getDraftContent(currentConversation?.id);
      editor?.commands?.setContent(draftContent || '');
    };
    setupDraftContent();

    const onUpdateEventHandler = () => {
      updateDraftContent(currentConversation?.id, editor?.getHTML());
      sendCustomEvent('deca-livechat-sync-chat-message-height', {});
    };
    // on editor - text changed
    const updateEvent = editor?.on('update', onUpdateEventHandler);
    return () => {
      updateEvent?.off('update', onUpdateEventHandler);
    };
  }, [currentConversation?.id, editor, getDraftContent, updateDraftContent]);

  useEffect(() => {
    const editorRefDiv = editorRef.current;
    if (editorRefDiv) {
      editorRefDiv.addEventListener('paste', handlePasteContent);
    }
    return () => {
      editorRefDiv?.removeEventListener('paste', handlePasteContent);
    };
  }, [handlePasteContent]);

  return (
    <Container
      id={TEXT_EDITOR_ID_NAME}
      fluid
      sx={{
        width: '100%',
        padding: '1.2rem',
        borderRadius: '0.625rem',
        borderTopLeftRadius: '0',
        borderTopRightRadius: '0',
        backgroundColor: containerBackgroundColor,
        // position: "relative",
        position: containerPositionStyle,
        bottom: 0,
        // width: '100%'
      }}
      {...propDragAndDrop}
    >
      <SelectSender
        replyBy={replyBy}
        onSelectSender={handleSelectSender}
        isLinePlatform={currentConversation?.platform?.name === CONVERSATION_PLATFORM_LINE}
      />
      <If condition={activeDropZone}>
        <DropZone />
      </If>
      <If condition={!activeDropZone}>
        <Container
          fluid
          sx={{
            width: '100%',
            paddingInline: 4,
            borderRadius: 10,
            position: 'relative',
            zIndex: mainContentZIndex,
            backgroundColor: backgroundColor,
            border: `1px solid ${borderColor}`,
          }}
        >
          <RichTextEditor
            editor={editor}
            sx={() => ({
              position: 'relative',
              marginTop: '0.2rem',
              borderColor: 'transparent',
              '.mantine-RichTextEditor-content': {
                backgroundColor: 'transparent',
                fontSize: '0.875rem',
                color: '#373A40',
              },
              '.mantine-RichTextEditor-typographyStylesProvider': {
                maxHeight: '150px',
                overflowY: 'auto',
              },
              '.ProseMirror': {
                overflowY: 'auto',
                padding: '1rem',
                outline: 0,
                ...customStyleForScrollBar,
              },
              '.is-editor-empty:first-of-type::before': {
                pointerEvents: 'none',
                userSelect: 'none',
                float: 'left',
                height: '0px',
                color: 'rgb(173, 181, 189)',
                content: 'attr(data-placeholder)',
              },
              zIndex: mainContentZIndex,
            })}
          >
            <RichTextEditor.Content
              ref={editorRef}
              sx={{
                zIndex: mainContentZIndex,
              }}
              onCompositionStart={() => {
                // console.log('composition start');
                isComposingRef.current = true;
              }}
              onCompositionEnd={() => {
                // console.log('composition end');
                isComposingRef.current = false;
              }}
              onKeyDown={(e) => handleKeyDown(e as any)}
              {...propDragAndDrop}
            />
            <DisplayAttachments />
            <DisplayError />
            <Tools autoReplyEditor={autoReplyEditor} />
          </RichTextEditor>
        </Container>
      </If>
    </Container>
  );
};

export default Editor;

const SelectSender = ({
  replyBy,
  isLinePlatform,
  onSelectSender,
}: {
  replyBy: string;
  isLinePlatform: boolean;
  // eslint-disable-next-line no-unused-vars
  onSelectSender: (str: string) => void;
}) => {
  const { isInboxPage } = useAppContext();
  const { t } = useTextEditorContext();

  const senderOptions = useMemo(() => {
    return [
      {
        value: SENDER_REPLY_TYPE.TEAM,
        label: t('reply.team'),
      },
      {
        value: SENDER_REPLY_TYPE.OPERATOR,
        label: t('reply.user'),
      },
      ...(isLinePlatform
        ? [
            {
              value: SENDER_REPLY_TYPE.ORGANIZATION,
              label: t('reply.organization'),
            },
          ]
        : []),
    ];
  }, [t, isLinePlatform]);

  if (!isInboxPage) return null;

  return (
    <Select
      mb={10}
      value={replyBy}
      withCheckIcon={false}
      allowDeselect={false}
      styles={(theme) => ({
        option: {
          '&[data-checked="true"]': {
            '&, &:hover': {
              backgroundColor: theme.colors.gray[1],
              color: theme.black,
            },
          },
          '&[data-checked=true]': {
            backgroundColor: theme.colors.gray[1],
            color: theme.black,
          },
        },
        input: {
          '&:focus': {
            borderColor: theme.colors.navy[5],
          },
        },
      })}
      data={senderOptions}
      onChange={onSelectSender}
    ></Select>
  );
};
