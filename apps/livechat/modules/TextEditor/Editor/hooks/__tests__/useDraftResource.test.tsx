// Mock the utils functions FIRST
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
}));

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

import createCache from '@emotion/cache';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import '@testing-library/jest-dom';
import { act, renderHook } from '@testing-library/react';
import React from 'react';
import { DraftResource, useDraftResources } from '../useDraftResource';

// Mock the useDraftContent hook
const mockUseDraftContent = jest.fn();
jest.mock('../useDraftContent', () => ({
  useDraftContent: () => mockUseDraftContent(),
}));

// Create emotion cache for testing
const emotionCache = createCache({ key: 'mantine' });

// Create a custom theme for testing
const testTheme = {
  colors: {
    decaBlue: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
    gray: [
      '#f8f9fa',
      '#f1f3f4',
      '#e8eaed',
      '#dadce0',
      '#bdc1c6',
      '#9aa0a6',
      '#80868b',
      '#5f6368',
      '#3c4043',
      '#202124',
    ] as const,
    navy: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
};

// Wrapper component for testing hooks
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MantineEmotionProvider cache={emotionCache}>
    <MantineProvider theme={testTheme}>{children}</MantineProvider>
  </MantineEmotionProvider>
);

describe('useDraftResources', () => {
  const mockDraftContent = {
    getDraftContent: jest.fn(),
    clearDraftContent: jest.fn(),
    updateDraftContent: jest.fn(),
  };

  const sampleResource: DraftResource = {
    id: 'test-id-1',
    ref: 'test-conversation-id',
    name: 'test-file.jpg',
    type: 'image',
    statusCreated: 'status-1',
    url: 'https://example.com/test-file.jpg',
  };

  const sampleResource2: DraftResource = {
    id: 'test-id-2',
    ref: 'test-conversation-id',
    name: 'test-file-2.jpg',
    type: 'image',
    statusCreated: 'status-2',
    url: 'https://example.com/test-file-2.jpg',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDraftContent.mockReturnValue(mockDraftContent);
    mockDraftContent.getDraftContent.mockReturnValue('');
    mockDraftContent.clearDraftContent.mockReturnValue(undefined);
    mockDraftContent.updateDraftContent.mockReturnValue(undefined);
  });

  const renderHookWithWrapper = () => {
    return renderHook(() => useDraftResources(), {
      wrapper: TestWrapper,
    });
  };

  describe('initial state', () => {
    it('should initialize with all required functions', () => {
      const { result } = renderHookWithWrapper();

      expect(typeof result.current.addDraftResource).toBe('function');
      expect(typeof result.current.addDraftResources).toBe('function');
      expect(typeof result.current.getDraftResources).toBe('function');
      expect(typeof result.current.clearDraftResources).toBe('function');
      expect(typeof result.current.clearDraftSpecifiedResource).toBe('function');
    });
  });

  describe('getDraftResources', () => {
    it('should return empty array when no content exists', () => {
      const { result } = renderHookWithWrapper();

      const resources = result.current.getDraftResources('test-conversation-id');

      expect(resources).toEqual([]);
      expect(mockDraftContent.getDraftContent).toHaveBeenCalledWith('test-conversation-id');
    });

    it('should deserialize resources correctly', () => {
      const { result } = renderHookWithWrapper();

      // Mock serialized content
      const serializedContent =
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg|||test-id-2___test-conversation-id___test-file-2.jpg___image___status-2___https://example.com/test-file-2.jpg';
      mockDraftContent.getDraftContent.mockReturnValue(serializedContent);

      const resources = result.current.getDraftResources('test-conversation-id');

      expect(resources).toHaveLength(2);
      expect(resources[0]).toEqual(sampleResource);
      expect(resources[1]).toEqual(sampleResource2);
    });

    it('should handle empty string content', () => {
      const { result } = renderHookWithWrapper();

      mockDraftContent.getDraftContent.mockReturnValue('');

      const resources = result.current.getDraftResources('test-conversation-id');

      expect(resources).toEqual([]);
    });

    it('should handle null content', () => {
      const { result } = renderHookWithWrapper();

      mockDraftContent.getDraftContent.mockReturnValue(null);

      const resources = result.current.getDraftResources('test-conversation-id');

      expect(resources).toEqual([]);
    });
  });

  describe('addDraftResource', () => {
    it('should add a single resource correctly', () => {
      const { result } = renderHookWithWrapper();

      // Mock existing resources
      const existingSerialized =
        'existing-id___test-conversation-id___existing-file.jpg___image___existing-status___https://example.com/existing-file.jpg';
      mockDraftContent.getDraftContent.mockReturnValue(existingSerialized);

      act(() => {
        result.current.addDraftResource('test-conversation-id', sampleResource);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        'test-conversation-id',
        expect.stringContaining(
          'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg'
        )
      );
    });

    it('should handle undefined conversation ID', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.addDraftResource(undefined, sampleResource);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        undefined,
        expect.any(String)
      );
    });

    it('should handle empty existing resources', () => {
      const { result } = renderHookWithWrapper();

      mockDraftContent.getDraftContent.mockReturnValue('');

      act(() => {
        result.current.addDraftResource('test-conversation-id', sampleResource);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        'test-conversation-id',
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg'
      );
    });
  });

  describe('addDraftResources', () => {
    it('should add multiple resources correctly', () => {
      const { result } = renderHookWithWrapper();

      const resources = [sampleResource, sampleResource2];

      act(() => {
        result.current.addDraftResources('test-conversation-id', resources);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        'test-conversation-id',
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg|||test-id-2___test-conversation-id___test-file-2.jpg___image___status-2___https://example.com/test-file-2.jpg'
      );
    });

    it('should handle empty resources array', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.addDraftResources('test-conversation-id', []);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith('test-conversation-id', '');
    });

    it('should handle undefined conversation ID', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.addDraftResources(undefined, [sampleResource]);
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        undefined,
        expect.any(String)
      );
    });
  });

  describe('clearDraftResources', () => {
    it('should clear resources for a conversation', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.clearDraftResources('test-conversation-id');
      });

      expect(mockDraftContent.clearDraftContent).toHaveBeenCalledWith('test-conversation-id');
    });

    it('should handle undefined conversation ID', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.clearDraftResources(undefined);
      });

      expect(mockDraftContent.clearDraftContent).toHaveBeenCalledWith(undefined);
    });
  });

  describe('clearDraftSpecifiedResource', () => {
    it('should remove a specific resource', () => {
      const { result } = renderHookWithWrapper();

      // Mock existing resources
      const existingSerialized =
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg|||test-id-2___test-conversation-id___test-file-2.jpg___image___status-2___https://example.com/test-file-2.jpg';
      mockDraftContent.getDraftContent.mockReturnValue(existingSerialized);

      act(() => {
        result.current.clearDraftSpecifiedResource('test-conversation-id', 'test-id-1');
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        'test-conversation-id',
        'test-id-2___test-conversation-id___test-file-2.jpg___image___status-2___https://example.com/test-file-2.jpg'
      );
    });

    it('should handle resource not found', () => {
      const { result } = renderHookWithWrapper();

      // Mock existing resources
      const existingSerialized =
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg';
      mockDraftContent.getDraftContent.mockReturnValue(existingSerialized);

      act(() => {
        result.current.clearDraftSpecifiedResource('test-conversation-id', 'non-existent-id');
      });

      expect(mockDraftContent.updateDraftContent).toHaveBeenCalledWith(
        'test-conversation-id',
        'test-id-1___test-conversation-id___test-file.jpg___image___status-1___https://example.com/test-file.jpg'
      );
    });

    it('should handle undefined conversation ID', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.clearDraftSpecifiedResource(undefined, 'test-id-1');
      });

      expect(mockDraftContent.updateDraftContent).not.toHaveBeenCalled();
    });

    it('should handle undefined resource ID', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.clearDraftSpecifiedResource('test-conversation-id', undefined);
      });

      expect(mockDraftContent.updateDraftContent).not.toHaveBeenCalled();
    });

    it('should handle empty resources', () => {
      const { result } = renderHookWithWrapper();

      mockDraftContent.getDraftContent.mockReturnValue('');

      act(() => {
        result.current.clearDraftSpecifiedResource('test-conversation-id', 'test-id-1');
      });

      // When there are no resources, the function should return early without calling updateDraftContent
      expect(mockDraftContent.updateDraftContent).not.toHaveBeenCalled();
    });
  });

  describe('serialization and deserialization', () => {
    it('should correctly serialize and deserialize resources', () => {
      const { result } = renderHookWithWrapper();

      const resources = [sampleResource, sampleResource2];

      // Add resources
      act(() => {
        result.current.addDraftResources('test-conversation-id', resources);
      });

      // Get the serialized content that was passed to updateDraftContent
      const updateCall = mockDraftContent.updateDraftContent.mock.calls[0];
      const serializedContent = updateCall[1];

      // Mock getDraftContent to return the serialized content
      mockDraftContent.getDraftContent.mockReturnValue(serializedContent);

      // Retrieve the resources
      const retrievedResources = result.current.getDraftResources('test-conversation-id');

      expect(retrievedResources).toEqual(resources);
    });

    it('should handle resources with special characters in names', () => {
      const { result } = renderHookWithWrapper();

      const resourceWithSpecialChars: DraftResource = {
        id: 'test-id-3',
        ref: 'test-conversation-id',
        name: 'test-file with spaces & special chars.jpg',
        type: 'image',
        statusCreated: 'status-3',
        url: 'https://example.com/test-file%20with%20spaces%20%26%20special%20chars.jpg',
      };

      act(() => {
        result.current.addDraftResources('test-conversation-id', [resourceWithSpecialChars]);
      });

      const updateCall = mockDraftContent.updateDraftContent.mock.calls[0];
      const serializedContent = updateCall[1];

      mockDraftContent.getDraftContent.mockReturnValue(serializedContent);

      const retrievedResources = result.current.getDraftResources('test-conversation-id');

      expect(retrievedResources).toEqual([resourceWithSpecialChars]);
    });
  });

  describe('edge cases', () => {
    it('should handle malformed serialized content', () => {
      const { result } = renderHookWithWrapper();

      // Mock malformed content (missing parts)
      const malformedContent = 'test-id-1___test-conversation-id___test-file.jpg___image';
      mockDraftContent.getDraftContent.mockReturnValue(malformedContent);

      const resources = result.current.getDraftResources('test-conversation-id');

      // Should handle gracefully and return what it can parse
      expect(Array.isArray(resources)).toBe(true);
    });

    it('should handle empty delimiter sequences', () => {
      const { result } = renderHookWithWrapper();

      // Mock content with empty parts
      const emptyContent =
        'test-id-1______test-file.jpg___image___status-1___https://example.com/test-file.jpg';
      mockDraftContent.getDraftContent.mockReturnValue(emptyContent);

      const resources = result.current.getDraftResources('test-conversation-id');

      expect(Array.isArray(resources)).toBe(true);
    });
  });
});
