import { ActionIcon } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { sendCustomEvent } from '@resola-ai/utils';
import { IconArrowBearLeft, IconArrowBearRight } from '@tabler/icons-react';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTextEditorContext } from '../../../../context';

const useStyles = createStyles(() => ({
  rotationArrowLeft: {
    transform: 'rotate(-90deg)',
  },
  rotationArrowRight: {
    transform: 'rotate(90deg)',
  },
}));

interface Props {
  size: number;
  stroke: number;
  color: string;
}

// eslint-disable-next-line no-unused-vars
const AiAdjustedArrows: React.FC<Props> = ({ size, stroke, color }) => {
  const { classes } = useStyles();
  const { editor } = useRichTextEditorContext();
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const { appliedAIMsgList, setAppliedAIMsgList, isUsingAICorrect, setIsUsingAICorrect } =
    useTextEditorContext();

  const disabledAction = useMemo(() => appliedAIMsgList.length < 2, [appliedAIMsgList.length]);
  const contentFromEditor = useMemo(() => editor?.getText(), [editor]);
  const displayAIArrowsIcon = useMemo(
    () => isUsingAICorrect && contentFromEditor,
    [contentFromEditor, isUsingAICorrect]
  );

  useEffect(() => {
    setCurrentIndex(appliedAIMsgList.length - 1);
  }, [appliedAIMsgList]);

  useEffect(() => {
    if (!contentFromEditor) {
      setIsUsingAICorrect(false);
      setAppliedAIMsgList([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentFromEditor]);

  const handleForward = useCallback(() => {
    if (currentIndex < appliedAIMsgList.length - 1) {
      const index = currentIndex + 1;
      sendCustomEvent('deca-livechat-editor-message-update', {
        message: appliedAIMsgList[index],
      });
      setCurrentIndex(index);
    }
  }, [appliedAIMsgList, currentIndex]);

  const handleBackward = useCallback(() => {
    if (currentIndex > 0) {
      const index = currentIndex - 1;
      sendCustomEvent('deca-livechat-editor-message-update', {
        message: appliedAIMsgList[index],
      });
      setCurrentIndex(index);
    }
  }, [appliedAIMsgList, currentIndex]);

  return (
    <>
      {displayAIArrowsIcon && (
        <>
          <ActionIcon
            disabled={disabledAction}
            variant='transparent'
            color='gray'
            sx={{
              ':hover': {
                color: '#000000',
              },
            }}
            onClick={handleBackward}
          >
            <IconArrowBearLeft
              className={classes.rotationArrowLeft}
              size={size}
              strokeWidth={stroke}
            />
          </ActionIcon>
          <ActionIcon
            disabled={disabledAction}
            variant='transparent'
            color='gray'
            sx={{
              ':hover': {
                color: '#000000',
              },
            }}
            onClick={handleForward}
          >
            <IconArrowBearRight
              className={classes.rotationArrowRight}
              size={size}
              strokeWidth={stroke}
            />
          </ActionIcon>
        </>
      )}
    </>
  );
};

export default AiAdjustedArrows;
