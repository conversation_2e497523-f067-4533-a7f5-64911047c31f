import styled from '@emotion/styled';
import { Flex } from '@mantine/core';
import { FeatureFlagName } from '@resola-ai/models';
import React from 'react';
import useFeatureFlag from '../../../../../hooks/useFeatureFlag';
import { useAppContext } from '../../../../appContext';
import { useConversationActionContext } from '../../../../conversationActionContext';
import { useThemeContext } from '../../../themeContext';
import AiAdjustedArrows from './AiAdjustedArrows';
import AiAutoCorrect from './AiAutoCorrect';
import Attachment from './Attachment';
import Emoji from './Emoji';
const SIZE = 18;
const STROKE = 1.5;
// eslint-disable-next-line no-unused-vars
const Separator = styled.div`
    width: 1px;
    height: 20px;
    background-color: ${(props) => props.color};
`;

const useConversationContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { currentConversation, platformLine, platformWeb } = useConversationActionContext();
    return { currentConversation, platformLine, platformWeb };
  } catch (e) {
    return {
      platformLine: false,
      platformWeb: false,
      currentConversation: null,
    };
  }
};

interface ActionsProps {
  autoReplyEditor?: boolean;
}

const Actions: React.FC<ActionsProps> = ({ autoReplyEditor }) => {
  const { platformLine, platformWeb } = useConversationContextIfExist();
  const { actionIconColor } = useThemeContext();
  const { isOperationSettingPage } = useAppContext();
  const { enabled: aiFeatures } = useFeatureFlag(FeatureFlagName.aiFeatures);

  if (isOperationSettingPage || autoReplyEditor) {
    // Customize action controls for the AutomaticReply part in the OperationSetting page
    return (
      <Flex
        gap={10}
        align={'center'}
        onDrop={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        <Emoji size={SIZE} stroke={STROKE} color={actionIconColor} />
      </Flex>
    );
  }

  return (
    <Flex
      gap={10}
      align={'center'}
      onDrop={(e) => {
        // console.log('drop tools action');
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      {/* Comment for MVP version */}
      {/* <IconAt size={SIZE} stroke={STROKE} color={actionIconColor} /> */}
      {platformLine && <Attachment size={SIZE} stroke={STROKE} color={actionIconColor} />}
      {platformWeb && <Attachment size={SIZE} stroke={STROKE} color={actionIconColor} />}
      {/* Comment for MVP version */}
      {/* <IconTemplate size={SIZE} stroke={STROKE} color={actionIconColor} /> */}
      <Emoji size={SIZE} stroke={STROKE} color={actionIconColor} />
      {aiFeatures && (
        <>
          <AiAutoCorrect size={SIZE} stroke={STROKE} color={actionIconColor} />
          <AiAdjustedArrows size={SIZE} stroke={STROKE} color={actionIconColor} />
        </>
      )}
    </Flex>
  );
};

export default Actions;
