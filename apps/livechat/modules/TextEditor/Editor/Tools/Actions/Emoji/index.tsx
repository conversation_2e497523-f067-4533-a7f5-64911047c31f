import { ActionIcon, Popover } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { IconMoodSmile } from '@tabler/icons-react';
import 'emoji-mart/css/emoji-mart.css';
import React, { useEffect, useState } from 'react';
import { EmojiPicker } from '../../../../../../components/TextAreaEditor/EmojiPicker';
interface Props {
  size: number;
  stroke: number;
  color: string;
}

const useStyle = createStyles(() => ({
  popoverDropdown: {
    padding: '0',
  },
}));

// eslint-disable-next-line no-unused-vars
const Emoji: React.FC<Props> = ({ size, stroke, color }) => {
  const { classes } = useStyle();
  const [opened, setOpened] = useState(false);
  const { editor } = useRichTextEditorContext();
  const handleSelectEmoji = (emoji) => {
    editor?.commands.insertContent(emoji);
    setOpened(false);
  };

  useEffect(() => {
    if (opened) {
      const containerClass = 'emoji-mart-search';
      const interval = setInterval(() => {
        // if focus, then just end the interval
        const input = document.querySelector(`.${containerClass} input`);
        if (input) {
          (input as any)?.focus();
          // add input event listener to check if the enter is press, if it is,
          // then close the emoji picker, and do not pass this event down to the editor
          input.addEventListener('keydown', (e) => {
            if ((e as any).key === 'Enter') {
              e.preventDefault();
              setOpened(false);
              setTimeout(() => {
                editor.view.focus();
              }, 500);
            }
          });

          clearInterval(interval);
          return () => {
            input.removeEventListener('keydown', () => {});
            clearInterval(interval);
          };
        }
      }, 100);
      return () => clearInterval(interval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [opened]);

  return (
    <Popover withArrow shadow='md' position='top' opened={opened} onChange={setOpened}>
      <Popover.Target>
        <ActionIcon
          variant='transparent'
          color='gray'
          onClick={() => setOpened((o) => !o)}
          sx={{
            ':hover': {
              color: '#000000',
            },
          }}
        >
          <IconMoodSmile size={size} />
        </ActionIcon>
      </Popover.Target>
      <Popover.Dropdown className={classes.popoverDropdown}>
        <EmojiPicker onSelect={handleSelectEmoji} />
      </Popover.Dropdown>
    </Popover>
  );
};

export default Emoji;
