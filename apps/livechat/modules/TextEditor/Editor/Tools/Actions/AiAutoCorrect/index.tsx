import { ActionIcon } from '@mantine/core';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { IconComet } from '@tabler/icons-react';
import React, { useCallback } from 'react';
import useVisibilityControl from '../../../../../../hooks/useVisibilityControl';
import AssistantDialog from '../../../../../AIAutoAdjustment/AssistantDialog';
import { useOpenAiContext } from '../../../../../openaiContext';
import { useTextEditorContext } from '../../../../context';

interface Props {
  size: number;
  stroke: number;
  color: string;
}

const useOpenAiIfExist = () => {
  try {
    const {
      resetConversationAIAdjustmentList,
      resetIsLoadingAIAdjustment,
      exitProgressAIAdjustmentRef,
      // eslint-disable-next-line react-hooks/rules-of-hooks
    } = useOpenAiContext();
    return {
      resetConversationAIAdjustmentList,
      resetIsLoadingAIAdjustment,
      exitProgressAIAdjustmentRef,
    };
  } catch (e) {
    return {
      resetConversationAIAdjustmentList: () => null,
      resetIsLoadingAIAdjustment: () => null,
      exitProgressAIAdjustmentRef: () => null,
    };
  }
};

// eslint-disable-next-line no-unused-vars
const AiAutoCorrect: React.FC<Props> = ({ size, stroke, color }) => {
  const {
    visible: visibleDialog,
    toggle: toggleDialog,
    close: closeDialog,
  } = useVisibilityControl();

  const { setIsOpenAssistantDialog: setIsOpenAssistantDialog } = useTextEditorContext();
  const { editor } = useRichTextEditorContext();
  const {
    resetConversationAIAdjustmentList,
    resetIsLoadingAIAdjustment,
    exitProgressAIAdjustmentRef,
  } = useOpenAiIfExist();

  const disabledIcon = !editor?.getText();

  const handleToggleDialog = useCallback(() => {
    if (!visibleDialog) {
      exitProgressAIAdjustmentRef();
    }
    resetConversationAIAdjustmentList();
    resetIsLoadingAIAdjustment();
    setIsOpenAssistantDialog(!visibleDialog);
    toggleDialog();
  }, [
    visibleDialog,
    toggleDialog,
    setIsOpenAssistantDialog,
    resetIsLoadingAIAdjustment,
    exitProgressAIAdjustmentRef,
    resetConversationAIAdjustmentList,
  ]);

  const handleCloseDialog = useCallback(() => {
    resetConversationAIAdjustmentList();
    resetIsLoadingAIAdjustment();
    setIsOpenAssistantDialog(false);
    exitProgressAIAdjustmentRef();
    closeDialog();
  }, [
    closeDialog,
    setIsOpenAssistantDialog,
    resetIsLoadingAIAdjustment,
    exitProgressAIAdjustmentRef,
    resetConversationAIAdjustmentList,
  ]);

  return (
    <>
      <ActionIcon
        disabled={disabledIcon}
        variant='transparent'
        color='gray'
        sx={{
          ':hover.not(:disabled)': {
            color: '#000000',
          },
          '&:disabled': {
            backgroundColor: 'transparent',
          },
        }}
      >
        <IconComet size={size} strokeWidth={stroke} onClick={handleToggleDialog} />
      </ActionIcon>
      <AssistantDialog opened={visibleDialog} onClose={handleCloseDialog} />
    </>
  );
};

export default AiAutoCorrect;
