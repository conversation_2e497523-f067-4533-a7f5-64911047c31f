import { Flex } from '@mantine/core';
import React from 'react';
import SaveAutoReplyMessageButton from '../../../Settings/Operations/Components/SaveAutoReplyMessageButton';
import { useAppContext } from '../../../appContext';
import Actions from './Actions';
import SendMessageButton from './SendMessageButton';

interface ToolsProps {
  autoReplyEditor?: boolean;
}

const Tools: React.FC<ToolsProps> = ({ autoReplyEditor }) => {
  const { isOperationSettingPage } = useAppContext();

  let buttonComponent = <SendMessageButton />;

  if (autoReplyEditor) {
    buttonComponent = null;
  } else if (isOperationSettingPage) {
    buttonComponent = <SaveAutoReplyMessageButton />;
  }

  return (
    <Flex py={20} px={15} justify={'space-between'}>
      <Actions autoReplyEditor={autoReplyEditor} />
      {buttonComponent}
    </Flex>
  );
};

export default Tools;
