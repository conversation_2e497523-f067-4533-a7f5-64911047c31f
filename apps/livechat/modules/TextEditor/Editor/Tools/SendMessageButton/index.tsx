import { Button } from '@mantine/core';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { sendCustomEvent } from '@resola-ai/utils';
import { IconSend } from '@tabler/icons-react';
import React from 'react';
import { useAppContext } from '../../../../appContext';
import { useConversationActionContext } from '../../../../conversationActionContext';
import { useMessageContext } from '../../../../messageContext';
import { useTextEditorContext } from '../../../context';
import { useDraftContent } from '../../hooks/useDraftContent';

const useMessageContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { sendMessage, resources, uploadResourceLoading, sender } = useMessageContext();
    return {
      sender,
      resources,
      sendMessage,
      uploadResourceLoading,
    };
  } catch (e) {
    return {
      sender: null,
      resources: [],
      sendMessage: () => null,
      uploadResourceLoading: false,
    };
  }
};

const useConversationContextIfExist = () => {
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { currentConversation } = useConversationActionContext();
    return {
      currentConversation,
    };
  } catch (e) {
    return {
      currentConversation: null,
    };
  }
};

const SendMessageButton: React.FC<any> = () => {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const { currentConversation } = useConversationContextIfExist();
  const { clearDraftContent } = useDraftContent();
  const { sendMessage, resources, uploadResourceLoading, sender } = useMessageContextIfExist();
  const { editor } = useRichTextEditorContext();
  const { t } = useTextEditorContext();

  const disableSendButton = (() => {
    if (!!uploadResourceLoading) return true;
    return resources.length > 0 ? false : editor && editor.getText().length === 0;
  })();

  return (
    <Button
      leftSection={<IconSend size={15} />}
      radius={10}
      sx={(theme) => ({
        backgroundColor: theme.colors.navy[0],
        ':hover': {
          backgroundColor: theme.colors.navy[1],
        },
        // set disable style for this button
        '&&:disabled': {
          backgroundColor: theme.colors.navy[2],
          color: '#fff',
        },
        fontSize: responsiveScreen ? '12px' : undefined,
      })}
      onClick={() => {
        if (uploadResourceLoading) return;
        const rawContent = editor?.getText();
        clearDraftContent(currentConversation?.id);
        editor?.commands.setContent('');
        const trimmedContent = rawContent.trim();
        sendCustomEvent('deca-livechat-sync-chat-message-height', {});
        sendMessage(currentConversation?.id, trimmedContent, {
          sender,
        });
      }}
      disabled={disableSendButton}
    >
      {t('sendMessage')}
    </Button>
  );
};

export default SendMessageButton;
