import { Flex } from '@mantine/core';
import React from 'react';
import { useMessageContext } from '../../../messageContext';
import DisplayAttachment from '../DisplayAttachment';
import { DraftResource } from '../hooks/useDraftResource';

const useMessageContextIfExist = () => {
  try {
    const { removeAttachmentFiles, uploadResourceLoading, resources } =
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useMessageContext();
    return { removeAttachmentFiles, uploadResourceLoading, resources };
  } catch (e) {
    return {
      // eslint-disable-next-line no-unused-vars
      removeAttachmentFiles: () => null,
      uploadResourceLoading: false,
      resources: [],
    };
  }
};

const DisplayAttachments: React.FC<any> = () => {
  const { removeAttachmentFiles, uploadResourceLoading, resources } = useMessageContextIfExist();
  return (
    <Flex gap={10} wrap={'wrap'}>
      {resources?.map((resource: DraftResource) => {
        return (
          <div key={resource.id}>
            <DisplayAttachment
              resourceId={resource.id}
              resourceName={resource.name}
              resourceType={resource.type}
              attachmentUrl={resource.url}
              isUploading={uploadResourceLoading}
              removeAttachmentFile={() => removeAttachmentFiles([resource.id])}
            />
          </div>
        );
      })}
    </Flex>
  );
};

export default DisplayAttachments;
