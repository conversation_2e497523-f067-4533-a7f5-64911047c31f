// Mock the utils functions FIRST
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
}));

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

import createCache from '@emotion/cache';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { DraftResource } from '../../hooks/useDraftResource';
import DisplayAttachments from '../index';

// Mock the message context
const mockUseMessageContext = jest.fn();
jest.mock('../../../../messageContext', () => ({
  useMessageContext: () => mockUseMessageContext(),
}));

// Mock the DisplayAttachment component
jest.mock('../../DisplayAttachment', () => ({
  __esModule: true,
  default: ({
    resourceId,
    resourceName,
    resourceType,
    attachmentUrl,
    isUploading,
    removeAttachmentFile,
  }: {
    resourceId: string;
    resourceName: string;
    resourceType: string;
    attachmentUrl: string;
    isUploading: boolean;
    removeAttachmentFile: () => void;
  }) => (
    <div
      data-testid='display-attachment'
      data-resource-id={resourceId}
      data-resource-name={resourceName}
      data-resource-type={resourceType}
      data-attachment-url={attachmentUrl}
      data-is-uploading={isUploading}
      onClick={removeAttachmentFile}
    >
      {resourceName}
    </div>
  ),
}));

// Create emotion cache for testing
const emotionCache = createCache({ key: 'mantine' });

// Create a custom theme for testing
const testTheme = {
  colors: {
    decaBlue: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
    gray: [
      '#f8f9fa',
      '#f1f3f4',
      '#e8eaed',
      '#dadce0',
      '#bdc1c6',
      '#9aa0a6',
      '#80868b',
      '#5f6368',
      '#3c4043',
      '#202124',
    ] as const,
    navy: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
};

// Wrapper component for testing
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MantineEmotionProvider cache={emotionCache}>
    <MantineProvider theme={testTheme}>{children}</MantineProvider>
  </MantineEmotionProvider>
);

describe('DisplayAttachments', () => {
  const mockResources: DraftResource[] = [
    {
      id: 'resource-1',
      ref: 'conversation-1',
      name: 'test-image.jpg',
      type: 'image',
      statusCreated: 'status-1',
      url: 'https://example.com/test-image.jpg',
    },
    {
      id: 'resource-2',
      ref: 'conversation-1',
      name: 'test-document.pdf',
      type: 'document',
      statusCreated: 'status-2',
      url: 'https://example.com/test-document.pdf',
    },
    {
      id: 'resource-3',
      ref: 'conversation-1',
      name: 'test-video.mp4',
      type: 'video',
      statusCreated: 'status-3',
      url: 'https://example.com/test-video.mp4',
    },
  ];

  const mockMessageContext = {
    removeAttachmentFiles: jest.fn(),
    uploadResourceLoading: false,
    resources: mockResources,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMessageContext.mockReturnValue(mockMessageContext);
  });

  describe('when message context is available', () => {
    it('should render all attachments', () => {
      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.getAllByTestId('display-attachment');
      expect(attachments).toHaveLength(3);
    });

    it('should render attachments with correct props', () => {
      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.getAllByTestId('display-attachment');

      // Check first attachment
      expect(attachments[0]).toHaveAttribute('data-resource-id', 'resource-1');
      expect(attachments[0]).toHaveAttribute('data-resource-name', 'test-image.jpg');
      expect(attachments[0]).toHaveAttribute('data-resource-type', 'image');
      expect(attachments[0]).toHaveAttribute(
        'data-attachment-url',
        'https://example.com/test-image.jpg'
      );
      expect(attachments[0]).toHaveAttribute('data-is-uploading', 'false');

      // Check second attachment
      expect(attachments[1]).toHaveAttribute('data-resource-id', 'resource-2');
      expect(attachments[1]).toHaveAttribute('data-resource-name', 'test-document.pdf');
      expect(attachments[1]).toHaveAttribute('data-resource-type', 'document');
      expect(attachments[1]).toHaveAttribute(
        'data-attachment-url',
        'https://example.com/test-document.pdf'
      );
      expect(attachments[1]).toHaveAttribute('data-is-uploading', 'false');

      // Check third attachment
      expect(attachments[2]).toHaveAttribute('data-resource-id', 'resource-3');
      expect(attachments[2]).toHaveAttribute('data-resource-name', 'test-video.mp4');
      expect(attachments[2]).toHaveAttribute('data-resource-type', 'video');
      expect(attachments[2]).toHaveAttribute(
        'data-attachment-url',
        'https://example.com/test-video.mp4'
      );
      expect(attachments[2]).toHaveAttribute('data-is-uploading', 'false');
    });

    it('should handle upload loading state', () => {
      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        uploadResourceLoading: true,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.getAllByTestId('display-attachment');
      attachments.forEach((attachment) => {
        expect(attachment).toHaveAttribute('data-is-uploading', 'true');
      });
    });

    it('should call removeAttachmentFiles when attachment is clicked', () => {
      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.getAllByTestId('display-attachment');
      attachments[0].click();

      expect(mockMessageContext.removeAttachmentFiles).toHaveBeenCalledWith(['resource-1']);
    });

    it('should handle empty resources array', () => {
      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: [],
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.queryAllByTestId('display-attachment');
      expect(attachments).toHaveLength(0);
    });

    it('should handle undefined resources', () => {
      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: undefined,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.queryAllByTestId('display-attachment');
      expect(attachments).toHaveLength(0);
    });

    it('should handle null resources', () => {
      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: null,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.queryAllByTestId('display-attachment');
      expect(attachments).toHaveLength(0);
    });
  });

  describe('when message context is not available', () => {
    it('should render with default values when context throws error', () => {
      mockUseMessageContext.mockImplementation(() => {
        throw new Error('Context not available');
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.queryAllByTestId('display-attachment');
      expect(attachments).toHaveLength(0);
    });

    it('should handle missing context functions gracefully', () => {
      mockUseMessageContext.mockReturnValue({
        removeAttachmentFiles: () => null,
        uploadResourceLoading: false,
        resources: mockResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachments = screen.getAllByTestId('display-attachment');
      expect(attachments).toHaveLength(3);

      // Should not crash when clicking
      attachments[0].click();
      // No assertion needed as we're just checking it doesn't crash
    });
  });

  describe('different resource types', () => {
    it('should handle image resources', () => {
      const imageResources: DraftResource[] = [
        {
          id: 'image-1',
          ref: 'conversation-1',
          name: 'photo.jpg',
          type: 'image',
          statusCreated: 'status-1',
          url: 'https://example.com/photo.jpg',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: imageResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute('data-resource-type', 'image');
    });

    it('should handle document resources', () => {
      const documentResources: DraftResource[] = [
        {
          id: 'doc-1',
          ref: 'conversation-1',
          name: 'report.pdf',
          type: 'document',
          statusCreated: 'status-1',
          url: 'https://example.com/report.pdf',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: documentResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute('data-resource-type', 'document');
    });

    it('should handle video resources', () => {
      const videoResources: DraftResource[] = [
        {
          id: 'video-1',
          ref: 'conversation-1',
          name: 'presentation.mp4',
          type: 'video',
          statusCreated: 'status-1',
          url: 'https://example.com/presentation.mp4',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: videoResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute('data-resource-type', 'video');
    });

    it('should handle unknown resource types', () => {
      const unknownResources: DraftResource[] = [
        {
          id: 'unknown-1',
          ref: 'conversation-1',
          name: 'file.xyz',
          type: 'unknown',
          statusCreated: 'status-1',
          url: 'https://example.com/file.xyz',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: unknownResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute('data-resource-type', 'unknown');
    });
  });

  describe('edge cases', () => {
    it('should handle resources with missing properties', () => {
      const incompleteResources = [
        {
          id: 'incomplete-1',
          ref: 'conversation-1',
          name: 'file.jpg',
          type: 'image',
          statusCreated: '',
          url: '',
        },
      ] as DraftResource[];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: incompleteResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute('data-resource-id', 'incomplete-1');
      expect(attachment).toHaveAttribute('data-resource-name', 'file.jpg');
      expect(attachment).toHaveAttribute('data-resource-type', 'image');
      expect(attachment).toHaveAttribute('data-attachment-url', '');
    });

    it('should handle resources with special characters in names', () => {
      const specialCharResources: DraftResource[] = [
        {
          id: 'special-1',
          ref: 'conversation-1',
          name: 'file with spaces & special chars.jpg',
          type: 'image',
          statusCreated: 'status-1',
          url: 'https://example.com/file%20with%20spaces%20%26%20special%20chars.jpg',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: specialCharResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute(
        'data-resource-name',
        'file with spaces & special chars.jpg'
      );
      expect(attachment).toHaveTextContent('file with spaces & special chars.jpg');
    });

    it('should handle very long resource names', () => {
      const longNameResources: DraftResource[] = [
        {
          id: 'long-1',
          ref: 'conversation-1',
          name: 'very-long-file-name-that-exceeds-normal-length-limits-and-should-still-be-displayed-correctly.jpg',
          type: 'image',
          statusCreated: 'status-1',
          url: 'https://example.com/very-long-file-name.jpg',
        },
      ];

      mockUseMessageContext.mockReturnValue({
        ...mockMessageContext,
        resources: longNameResources,
      });

      render(
        <TestWrapper>
          <DisplayAttachments />
        </TestWrapper>
      );

      const attachment = screen.getByTestId('display-attachment');
      expect(attachment).toHaveAttribute(
        'data-resource-name',
        'very-long-file-name-that-exceeds-normal-length-limits-and-should-still-be-displayed-correctly.jpg'
      );
      expect(attachment).toHaveTextContent(
        'very-long-file-name-that-exceeds-normal-length-limits-and-should-still-be-displayed-correctly.jpg'
      );
    });
  });
});
