import { Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
import { useTextEditorContext } from '../../TextEditor/context';
import TagItem from './TagItem';

const useStyles = createStyles((theme) => ({
  dialog: {
    border: '1px solid #CED4DA',
  },
  header: {
    borderBottom: '1px solid #C1C2C5',
    padding: '16px',
  },
  pointerCursor: {
    cursor: 'pointer',
  },
  tagTitle: {
    marginRight: '12px',
    marginTop: '8px',
    fontSize: '12px',
    color: theme.colors.gray[8],
  },
}));

const TagsList = () => {
  const { classes } = useStyles();
  const { t } = useTextEditorContext();
  const tagsList = [
    {
      id: 1,
      label: t('tag.correction.label'),
      value: t('tag.correction.value'),
    },
    {
      id: 2,
      label: t('tag.polite.label'),
      value: t('tag.polite.value'),
    },
    {
      id: 3,
      label: t('tag.friendly.label'),
      value: t('tag.friendly.value'),
    },
    {
      id: 4,
      label: t('tag.softer.label'),
      value: t('tag.softer.value'),
    },
    {
      id: 5,
      label: t('tag.brighter.label'),
      value: t('tag.brighter.value'),
    },
    {
      id: 6,
      label: t('tag.longer.label'),
      value: t('tag.longer.value'),
    },
    {
      id: 7,
      label: t('tag.shorter.label'),
      value: t('tag.shorter.value'),
    },
  ];

  return (
    <Flex align='center' justify='flex-start' wrap='wrap' pt={16}>
      <Text className={classes.tagTitle}>
        <b>{t('autoAdjustment')}</b>
      </Text>
      {tagsList.map((tag, index) => (
        <TagItem label={tag.label} command={tag.value} key={index} />
      ))}
    </Flex>
  );
};

export default TagsList;
