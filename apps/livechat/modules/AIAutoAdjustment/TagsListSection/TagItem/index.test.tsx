import { MantineProvider } from '@mantine/core';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { useConversationActionContext } from '../../../conversationActionContext';
import { useOpenAiContext } from '../../../openaiContext';
import TagItem from './index';

// Mock <PERSON>'s createStyles
jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      tagItem: 'tagItem',
      itemSelected: 'itemSelected',
      disabledBox: 'disabledBox',
    },
    cx: (...args: string[]) => args.filter(Boolean).join(' '),
  }),
}));

// Mock contexts
jest.mock('@mantine/tiptap', () => ({
  useRichTextEditorContext: jest.fn(),
}));
jest.mock('../../../openaiContext', () => ({
  useOpenAiContext: jest.fn(),
}));
jest.mock('../../../conversationActionContext', () => ({
  useConversationActionContext: jest.fn(),
}));

const renderWithMantine = (ui: React.ReactElement) => {
  return render(<MantineProvider>{ui}</MantineProvider>);
};

describe('TagItem', () => {
  const mockUpdateConversationAIAdjustmentList = jest.fn();
  const mockAiAutoAdjustment = jest.fn();
  const mockEditor = { getText: jest.fn().mockReturnValue('editor content') };
  const mockCurrentConversationId = 'conv-123';

  beforeEach(() => {
    jest.clearAllMocks();
    (useRichTextEditorContext as jest.Mock).mockReturnValue({ editor: mockEditor });
    (useOpenAiContext as jest.Mock).mockReturnValue({
      aiAutoAdjustment: mockAiAutoAdjustment,
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      isLoadingAIAdjustment: false,
    });
    (useConversationActionContext as jest.Mock).mockReturnValue({
      currentConversationId: mockCurrentConversationId,
    });
  });

  it('renders with the correct label', () => {
    renderWithMantine(<TagItem label='Test Label' command='/test' />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('applies disabledBox class when isLoadingAIAdjustment is true', () => {
    (useOpenAiContext as jest.Mock).mockReturnValue({
      aiAutoAdjustment: mockAiAutoAdjustment,
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      isLoadingAIAdjustment: true,
    });
    renderWithMantine(<TagItem label='Test Label' command='/test' />);
    const box = screen.getByText('Test Label');
    expect(box.className).toContain('disabledBox');
  });

  it('calls updateConversationAIAdjustmentList and aiAutoAdjustment on click', async () => {
    renderWithMantine(<TagItem label='Test Label' command='/test-cmd' />);
    const box = screen.getByText('Test Label');
    fireEvent.click(box);
    expect(mockUpdateConversationAIAdjustmentList).toHaveBeenCalledWith({
      message: '',
      sender: 'bot',
      start: false,
      end: false,
    });
    expect(mockAiAutoAdjustment).toHaveBeenCalledWith(
      'editor content',
      mockCurrentConversationId,
      '/test-cmd'
    );
  });

  it('does not call handlers when isLoadingAIAdjustment is true', () => {
    (useOpenAiContext as jest.Mock).mockReturnValue({
      aiAutoAdjustment: mockAiAutoAdjustment,
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      isLoadingAIAdjustment: true,
    });
    renderWithMantine(<TagItem label='Test Label' command='/test-cmd' />);
    const box = screen.getByText('Test Label');
    fireEvent.click(box);
    expect(mockUpdateConversationAIAdjustmentList).not.toHaveBeenCalled();
    expect(mockAiAutoAdjustment).not.toHaveBeenCalled();
  });
});
