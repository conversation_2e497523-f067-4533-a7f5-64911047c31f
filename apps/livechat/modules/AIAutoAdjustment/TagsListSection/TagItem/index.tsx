import { Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import React from 'react';
import { useConversationActionContext } from '../../../conversationActionContext';
import { useOpenAiContext } from '../../../openaiContext';

const useStyles = createStyles((theme) => ({
  tagItem: {
    border: `1px solid ${theme.colors.navy[0]}`,
    color: theme.colors.navy[0],
    borderRadius: '6px',
    padding: '2px 7px',
    marginRight: '8px',
    marginTop: '8px',
    fontSize: '12px',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.navy[0],
      color: '#FFFFFF',
    },
    width: 'fit-content',
  },
  itemSelected: {
    backgroundColor: theme.colors.navy[0],
    color: '#FFFFFF',
  },
  disabledBox: {
    backgroundColor: theme.colors.gray[3],
    color: '#FFFFFF',
    border: 'none',
    cursor: 'inherit',
    '&:hover': {
      backgroundColor: theme.colors.gray[3],
    },
  },
}));

interface TagItemProps {
  label: string;
  command: string;
}

const TagItem: React.FC<TagItemProps> = ({ label, command }) => {
  const { classes, cx } = useStyles();
  const { editor } = useRichTextEditorContext();
  const { aiAutoAdjustment, updateConversationAIAdjustmentList, isLoadingAIAdjustment } =
    useOpenAiContext();
  const { currentConversationId } = useConversationActionContext();
  const contentFromEditor = editor?.getText();

  const handleSendCommand = async () => {
    if (isLoadingAIAdjustment) return;
    const item = {
      message: '',
      sender: 'bot',
      start: false,
      end: false,
    };

    updateConversationAIAdjustmentList(item);
    await aiAutoAdjustment(contentFromEditor, currentConversationId, command);
  };

  return (
    <Box
      className={cx(classes.tagItem, isLoadingAIAdjustment && classes.disabledBox)}
      onClick={handleSendCommand}
    >
      {label}
    </Box>
  );
};

export default TagItem;
