import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../../utils/testing';
import BotAIMessage from './index';

// Mock Mantine components
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Text: ({ children, ...props }: any) => (
      <span data-testid='text' {...props}>
        {children}
      </span>
    ),
    Flex: ({ children, ...props }: any) => (
      <div data-testid='flex' {...props}>
        {children}
      </div>
    ),
    Box: ({ children, ...props }: any) => (
      <div data-testid='box' {...props}>
        {children}
      </div>
    ),
    AspectRatio: ({ children, ...props }: any) => (
      <div data-testid='aspect-ratio' {...props}>
        {children}
      </div>
    ),
    Avatar: (props: any) => <img data-testid='avatar' {...props} />,
    Group: ({ children, ...props }: any) => (
      <div data-testid='group' {...props}>
        {children}
      </div>
    ),
    Loader: (props: any) => <div data-testid='loader' {...props} />,
  };
});

// Mock useStyleMessageHolder
jest.mock('../../Chatbox/MessageHolders/useStylesForMessageHolderHooks', () => () => ({
  classes: {
    flexContainer: 'flexContainer',
    boxContentResponsive: 'boxContentResponsive',
    boxContent: 'boxContent',
    botAIMessageBox: 'botAIMessageBox',
    textBox: 'textBox',
    cursorPointer: 'cursorPointer',
    boxAvatar: 'boxAvatar',
  },
  cx: (...args: any[]) => args.join(' '),
}));

// Mock useAppContext
jest.mock('../../appContext', () => ({
  useAppContext: () => ({ responsiveScreen: false }),
}));

// Mock useOpenAiContext
jest.mock('../../openaiContext', () => {
  const mockResetConversationAIAdjustmentList = jest.fn();
  return {
    useOpenAiContext: () => ({
      resetConversationAIAdjustmentList: mockResetConversationAIAdjustmentList,
    }),
    __esModule: true,
    mockResetConversationAIAdjustmentList,
  };
});

// Mock useTextEditorContext
jest.mock('../../TextEditor/context', () => {
  const mockSetAppliedAIMsgList = jest.fn();
  const mockSetIsUsingAICorrect = jest.fn();
  return {
    useTextEditorContext: () => ({
      appliedAIMsgList: [],
      setAppliedAIMsgList: mockSetAppliedAIMsgList,
      setIsUsingAICorrect: mockSetIsUsingAICorrect,
    }),
    __esModule: true,
    mockSetAppliedAIMsgList,
    mockSetIsUsingAICorrect,
  };
});

// Mock useTranslate
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock getPublicUrl
jest.mock('../../../utils/public', () => ({
  getPublicUrl: (path: string) => path,
}));

// Mock sendCustomEvent
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
}));

describe('BotAIMessage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders message and avatar', () => {
    renderWithProvider(
      <BotAIMessage message='Hello AI' start={false} end={true} onClose={jest.fn()} />
    );
    // The first .text is the message, the second is the apply button
    const textNodes = screen.getAllByTestId('text');
    expect(textNodes[0]).toHaveTextContent('Hello AI');
    expect(screen.getByTestId('avatar')).toBeInTheDocument();
  });

  it('shows apply button when not start and end is true', () => {
    renderWithProvider(
      <BotAIMessage message='Apply me' start={false} end={true} onClose={jest.fn()} />
    );
    // The second .text is the apply button
    const textNodes = screen.getAllByTestId('text');
    expect(textNodes[1]).toHaveTextContent('apply');
  });

  it('shows loader when start is true or end is false', () => {
    renderWithProvider(
      <BotAIMessage message='Loading...' start={true} end={false} onClose={jest.fn()} />
    );
    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  it('calls all callbacks and updates state when apply is clicked', () => {
    const onClose = jest.fn();
    // Get the mocks from the modules
    const {
      mockSetAppliedAIMsgList,
      mockSetIsUsingAICorrect,
    } = require('../../TextEditor/context');
    const { mockResetConversationAIAdjustmentList } = require('../../openaiContext');
    renderWithProvider(
      <BotAIMessage message='Apply this' start={false} end={true} onClose={onClose} />
    );
    fireEvent.click(screen.getByText('apply'));
    expect(mockSetAppliedAIMsgList).toHaveBeenCalledWith(['Apply this']);
    expect(mockSetIsUsingAICorrect).toHaveBeenCalledWith(true);
    expect(mockResetConversationAIAdjustmentList).toHaveBeenCalled();
    expect(onClose).toHaveBeenCalled();
  });
});
