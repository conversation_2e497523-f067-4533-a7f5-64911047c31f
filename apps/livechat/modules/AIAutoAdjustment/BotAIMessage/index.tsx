import { AspectRatio, Avatar, Box, Flex, Group, Loader, Text, rem } from '@mantine/core';
import { sendCustomEvent } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { getPublicUrl } from '../../../utils/public';
import useStyleMessageHolder from '../../Chatbox/MessageHolders/useStylesForMessageHolderHooks';
import { useTextEditorContext } from '../../TextEditor/context';
import { useAppContext } from '../../appContext';
import { useOpenAiContext } from '../../openaiContext';

interface Props {
  message: string;
  start: boolean;
  end: boolean;
  onClose: () => void;
}

const MAX_LENGTH_AI_MESSAGE_STORAGE = 4;

const BotAIMessage: React.FC<Props> = ({ message, onClose, start, end }) => {
  const { t } = useTranslate('workspace');
  const { classes, cx } = useStyleMessageHolder();
  const { responsiveScreen } = useAppContext();
  const { resetConversationAIAdjustmentList } = useOpenAiContext();
  const { appliedAIMsgList, setAppliedAIMsgList, setIsUsingAICorrect } = useTextEditorContext();

  const handleApplyMessage = () => {
    if (appliedAIMsgList.length === MAX_LENGTH_AI_MESSAGE_STORAGE) {
      const updatedAppliedList = [...appliedAIMsgList];
      updatedAppliedList.shift();
      updatedAppliedList.push(message);
      setAppliedAIMsgList(updatedAppliedList);
    } else {
      setAppliedAIMsgList([...appliedAIMsgList, message]);
    }

    setIsUsingAICorrect(true);
    sendCustomEvent('deca-livechat-editor-message-update', { message });
    resetConversationAIAdjustmentList();
    onClose();
  };

  return (
    <Flex
      w={'100%'}
      direction={'row'}
      gap={responsiveScreen ? rem(9.5) : 'md'}
      className={classes.flexContainer}
    >
      <Box
        className={cx(
          responsiveScreen ? classes.boxContentResponsive : classes.boxContent,
          classes.botAIMessageBox
        )}
      >
        <Text
          className={classes.textBox}
          sx={{
            fontSize: responsiveScreen ? '12px' : undefined,
            lineHeight: responsiveScreen ? '18.6px' : '21.7px',
          }}
        >
          {message}
        </Text>
        <Group justify='flex-end' className={classes.cursorPointer}>
          {!start && end ? (
            <Text c='#845EF7' fz='xs' mt={16} onClick={handleApplyMessage}>
              {t('apply')}
            </Text>
          ) : (
            <Loader size='xs' type='dots' color='#845ef7' mt={8} />
          )}
        </Group>
      </Box>
      <Box pr={10} className={classes.boxAvatar}>
        <AspectRatio ratio={38 / 38} w={26} maw={300} mx='auto'>
          <Avatar
            radius={'xl'}
            src={getPublicUrl('/images/botAIAvatar.svg')}
            alt='AI Bot'
            size='sm'
          />
        </AspectRatio>
      </Box>
    </Flex>
  );
};

export default BotAIMessage;
