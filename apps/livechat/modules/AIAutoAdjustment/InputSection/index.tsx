import { ActionIcon, Box, Container, Flex, Text, Textarea, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { IconSend } from '@tabler/icons-react';
import React from 'react';
import { useTextEditorContext } from '../../TextEditor/context';
import { useConversationActionContext } from '../../conversationActionContext';
import { useOpenAiContext } from '../../openaiContext';
import { useAIAutoAdjustmentContext } from '../context';

const ENTER_CODE = 13;

const useStyles = createStyles((theme) => ({
  pointerCursor: {
    cursor: 'pointer',
  },
  tagTitle: {
    marginRight: '12px',
    marginTop: '8px',
    fontSize: '12px',
    color: theme.colors.gray[8],
  },
  inputWrapper: {
    border: '1px solid #CED4DA',
    borderRadius: '6px',
    minHeight: '87px',
    width: '100%',
    padding: '12px',
  },
  customTextarea: {
    outline: 'none',
    border: 'none',
    padding: '0',
    minHeight: '70px',
    '&::-webkit-scrollbar': {
      width: '0.5em',
    },
  },
}));

const InputSection = () => {
  const { classes } = useStyles();
  const { t } = useTextEditorContext();
  const { editor } = useRichTextEditorContext();
  const { messageText, setMessageText } = useAIAutoAdjustmentContext();
  const contentFromEditor = editor?.getText();

  const { aiAutoAdjustment, isLoadingAIAdjustment, updateConversationAIAdjustmentList } =
    useOpenAiContext();
  const { currentConversationId } = useConversationActionContext();

  const handleSendMessages = async () => {
    if (isLoadingAIAdjustment) return;
    if (!messageText) return;

    const messagePrompt = `${messageText} ${contentFromEditor}`;
    const item = {
      message: '',
      sender: 'bot',
      start: false,
      end: false,
    };
    updateConversationAIAdjustmentList(item);
    setMessageText('');
    await aiAutoAdjustment(messagePrompt, currentConversationId, '');
  };

  const handleSendKeyUp = (e: { keyCode: any }) => {
    if (e.keyCode === ENTER_CODE) {
      handleSendMessages();
    }
  };

  return (
    <Container fluid p={0} mt={24}>
      <Title className={classes.tagTitle} mb={14}>
        {t('freeToSpecifyTitle')}
      </Title>
      <Box className={classes.inputWrapper}>
        <Textarea
          classNames={{
            input: classes.customTextarea,
          }}
          maxLength={100}
          placeholder={t('freeToEnterPlaceHolder')}
          value={messageText}
          onChange={(event) => setMessageText(event.currentTarget.value)}
          onKeyUp={handleSendKeyUp}
        />
        <Flex align='center' justify='space-between'>
          <Text c='gray.6' fz={'10px'}>
            {messageText.length}/100{t('letter')}
          </Text>
          <ActionIcon disabled={isLoadingAIAdjustment} variant='transparent' color='navy.1'>
            <IconSend
              size={20}
              strokeWidth={2}
              className={classes.pointerCursor}
              onClick={handleSendMessages}
            />
          </ActionIcon>
        </Flex>
      </Box>
    </Container>
  );
};

export default InputSection;
