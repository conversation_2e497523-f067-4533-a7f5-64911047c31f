import { act, fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../../utils/testing';
import InputSection from './index';

// Mock Mantine components
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Box: ({ children, ...props }: any) => (
      <div data-testid='box' {...props}>
        {children}
      </div>
    ),
    Container: ({ children, ...props }: any) => (
      <div data-testid='container' {...props}>
        {children}
      </div>
    ),
    Textarea: ({ classNames, maxLength, placeholder, value, onChange, onKeyUp }: any) => (
      <input
        data-testid='textarea'
        maxLength={maxLength}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyUp={onKeyUp}
      />
    ),
    Title: ({ children, ...props }: any) => (
      <h5 data-testid='title' {...props}>
        {children}
      </h5>
    ),
    Flex: ({ children, ...props }: any) => (
      <div data-testid='flex' {...props}>
        {children}
      </div>
    ),
    Text: ({ children, ...props }: any) => (
      <span data-testid='text' {...props}>
        {children}
      </span>
    ),
    ActionIcon: ({ children, ...props }: any) => (
      <button data-testid='action-icon' {...props}>
        {children}
      </button>
    ),
  };
});

jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      pointerCursor: 'pointerCursor',
      tagTitle: 'tagTitle',
      inputWrapper: 'inputWrapper',
      customTextarea: 'customTextarea',
    },
  }),
}));

// Mock IconSend
jest.mock('@tabler/icons-react', () => ({
  IconSend: (props: any) => <svg data-testid='icon-send' {...props} />,
}));

// Mock useTextEditorContext
jest.mock('../../TextEditor/context', () => ({
  useTextEditorContext: () => ({ t: (key: string) => key }),
}));

// Mock useAIAutoAdjustmentContext
const mockSetMessageText = jest.fn();
jest.mock('../context', () => ({
  useAIAutoAdjustmentContext: () => ({
    messageText: 'hello',
    setMessageText: mockSetMessageText,
  }),
}));

// Mock useRichTextEditorContext
jest.mock('@mantine/tiptap', () => ({
  useRichTextEditorContext: () => ({ editor: { getText: () => 'editor content' } }),
}));

// Mock useOpenAiContext
const mockAiAutoAdjustment = jest.fn();
const mockUpdateConversationAIAdjustmentList = jest.fn();
jest.mock('../../openaiContext', () => ({
  useOpenAiContext: () => ({
    aiAutoAdjustment: mockAiAutoAdjustment,
    isLoadingAIAdjustment: false,
    updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
  }),
}));

// Mock useConversationActionContext
jest.mock('../../conversationActionContext', () => ({
  useConversationActionContext: () => ({ currentConversationId: 'conv-id' }),
}));

// Mock AppMantineEmotionProvider to avoid undefined error
jest.mock('../../../emotion', () => ({
  AppMantineEmotionProvider: ({ children }: any) => <div>{children}</div>,
}));

describe('InputSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders input, title, and character count', () => {
    renderWithProvider(<InputSection />);
    expect(screen.getByTestId('container')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('freeToSpecifyTitle');
    expect(screen.getByTestId('textarea')).toHaveValue('hello');
    expect(screen.getByTestId('text')).toHaveTextContent('5/100letter');
  });

  it('calls setMessageText on input change', () => {
    renderWithProvider(<InputSection />);
    fireEvent.change(screen.getByTestId('textarea'), { target: { value: 'new value' } });
    expect(mockSetMessageText).toHaveBeenCalledWith('new value');
  });

  it('calls handleSendMessages on send button click', async () => {
    renderWithProvider(<InputSection />);
    await act(async () => {
      fireEvent.click(screen.getByTestId('icon-send'));
    });
    // The send button should call updateConversationAIAdjustmentList and aiAutoAdjustment
    expect(mockUpdateConversationAIAdjustmentList).toHaveBeenCalled();
    expect(mockAiAutoAdjustment).toHaveBeenCalledWith('hello editor content', 'conv-id', '');
    expect(mockSetMessageText).toHaveBeenCalledWith('');
  });

  it('calls handleSendMessages on Enter key', async () => {
    renderWithProvider(<InputSection />);
    await act(async () => {
      fireEvent.keyUp(screen.getByTestId('textarea'), { keyCode: 13 });
    });
    expect(mockUpdateConversationAIAdjustmentList).toHaveBeenCalled();
    expect(mockAiAutoAdjustment).toHaveBeenCalledWith('hello editor content', 'conv-id', '');
    expect(mockSetMessageText).toHaveBeenCalledWith('');
  });
});
