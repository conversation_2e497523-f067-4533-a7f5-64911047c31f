import { Box, Dialog, Flex, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconX } from '@tabler/icons-react';
import React from 'react';
import AIAutoAdjustment from '..';
import { useTextEditorContext } from '../../TextEditor/context';

const useStyles = createStyles(() => ({
  dialog: {
    border: '1px solid #CED4DA',
  },
  header: {
    borderBottom: '1px solid #C1C2C5',
    padding: '16px',
  },
  pointerCursor: {
    cursor: 'pointer',
  },
}));

const AssistantDialog = ({ opened, onClose }: { opened: boolean; onClose: () => void }) => {
  const { classes } = useStyles();
  const { t } = useTextEditorContext();

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog
      className={classes.dialog}
      opened={opened}
      p={0}
      size='lg'
      radius='md'
      position={{ bottom: 120, left: '50%' }}
    >
      <Flex className={classes.header} align='center' justify='space-between'>
        <Title order={5}>{t('aiAutoAdjustmentTitle')}</Title>
        <IconX
          className={classes.pointerCursor}
          size={24}
          strokeWidth={2}
          color={'#5C5F66'}
          onClick={handleClose}
        />
      </Flex>
      <Box p={16}>
        <AIAutoAdjustment onClose={handleClose} />
      </Box>
    </Dialog>
  );
};

export default AssistantDialog;
