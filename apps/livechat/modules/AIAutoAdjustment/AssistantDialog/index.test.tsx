import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../../utils/testing';
import AssistantDialog from './index';

// Mock Mantine components
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Dialog: ({ children, ...props }: any) => (
      <div data-testid='dialog' {...props}>
        {children}
      </div>
    ),
    Flex: ({ children, ...props }: any) => (
      <div data-testid='flex' {...props}>
        {children}
      </div>
    ),
    Title: ({ children, ...props }: any) => (
      <h5 data-testid='title' {...props}>
        {children}
      </h5>
    ),
    Box: ({ children, ...props }: any) => (
      <div data-testid='box' {...props}>
        {children}
      </div>
    ),
  };
});

// <PERSON><PERSON> createStyles
jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: { dialog: 'dialog', header: 'header', pointerCursor: 'pointerCursor' },
  }),
}));

// Mock Tabler IconX
jest.mock('@tabler/icons-react', () => ({
  IconX: (props: any) => <button data-testid='icon-x' {...props} />,
}));

// Mock useTextEditorContext
jest.mock('../../TextEditor/context', () => ({
  useTextEditorContext: () => ({ t: (key: string) => key }),
}));

// Mock AIAutoAdjustment
jest.mock('..', () => ({
  __esModule: true,
  default: ({ onClose }: any) => (
    <div data-testid='ai-auto-adjustment' onClick={onClose}>
      AIAutoAdjustment
    </div>
  ),
}));

// Mock AppMantineEmotionProvider to avoid undefined error
jest.mock('../../../emotion', () => ({
  AppMantineEmotionProvider: ({ children }: any) => <div>{children}</div>,
}));

describe('AssistantDialog', () => {
  it('renders dialog and title when opened', () => {
    renderWithProvider(<AssistantDialog opened={true} onClose={jest.fn()} />);
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('aiAutoAdjustmentTitle');
    expect(screen.getByTestId('ai-auto-adjustment')).toBeInTheDocument();
  });

  it('does not render dialog when opened is false', () => {
    // The Dialog is always rendered, but you may want to check for visibility if your real Dialog unmounts
    renderWithProvider(<AssistantDialog opened={false} onClose={jest.fn()} />);
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
  });

  it('calls onClose when IconX is clicked', () => {
    const onClose = jest.fn();
    renderWithProvider(<AssistantDialog opened={true} onClose={onClose} />);
    fireEvent.click(screen.getByTestId('icon-x'));
    expect(onClose).toHaveBeenCalled();
  });

  it('calls onClose when AIAutoAdjustment triggers onClose', () => {
    const onClose = jest.fn();
    renderWithProvider(<AssistantDialog opened={true} onClose={onClose} />);
    fireEvent.click(screen.getByTestId('ai-auto-adjustment'));
    expect(onClose).toHaveBeenCalled();
  });
});
