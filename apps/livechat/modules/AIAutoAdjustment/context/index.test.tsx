import { act, fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { AiAutoAdjustmentContextProvider, useAIAutoAdjustmentContext } from './index';

// Helper component to consume the context
const Consumer: React.FC = () => {
  const { messageText, setMessageText } = useAIAutoAdjustmentContext();
  return (
    <>
      <span data-testid='messageText'>{messageText}</span>
      <button onClick={() => setMessageText('new text')}>Set Text</button>
    </>
  );
};

describe('AiAutoAdjustmentContext', () => {
  it('provides default value and allows updating messageText', async () => {
    render(
      <AiAutoAdjustmentContextProvider>
        <Consumer />
      </AiAutoAdjustmentContextProvider>
    );
    expect(screen.getByTestId('messageText')).toHaveTextContent('');
    await act(async () => {
      fireEvent.click(screen.getByText('Set Text'));
    });
    expect(screen.getByTestId('messageText')).toHaveTextContent('new text');
  });

  it('throws error if used outside provider', () => {
    // Suppress error output for this test
    const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const BrokenConsumer = () => {
      useAIAutoAdjustmentContext();
      return null;
    };
    expect(() => render(<BrokenConsumer />)).toThrow(
      'useAIAutoAdjustmentContext must be used inside AiAutoAdjustmentContextProvider'
    );
    spy.mockRestore();
  });
});
