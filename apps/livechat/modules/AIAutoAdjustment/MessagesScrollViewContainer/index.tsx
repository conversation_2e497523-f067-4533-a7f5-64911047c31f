import { Stack } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import React, { useEffect } from 'react';
import { MESSAGE_AI_ADJUSTMENT_LIST_SCROLL_POINT_ID } from '../../../constants';
import { useTextEditorContext } from '../../TextEditor/context';
import { useOpenAiContext } from '../../openaiContext';
import BotAIMessage from '../BotAIMessage';
import UserMessage from '../UserMessage';

const useStyles = createStyles((theme) => ({
  messagesScreenContainer: {
    overflowY: 'auto',
    maxHeight: '200px',
    WebkitScrollbar: 'width: 12px',
    scrollbarWidth: 'thin',
    scrollbarColor: `${theme.colors.gray[2]} ${theme.colors.gray[0]}`,
    '&::-webkit-scrollbar': {
      width: '12px',
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: theme.colors.gray[0],
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: theme.colors.gray[2],
      borderRadius: '6px',
      border: `3px solid ${theme.colors.gray[0]}`,
    },
    '&::-webkit-scrollbar-thumb:hover': {
      backgroundColor: theme.colors.gray[3],
    },
    '&::-webkit-scrollbar-thumb:active': {
      backgroundColor: theme.colors.gray[4],
    },
    '&::-webkit-scrollbar-corner': {
      backgroundColor: theme.colors.gray[0],
    },
    '&::-webkit-scrollbar-resizer': {
      backgroundColor: theme.colors.gray[0],
    },
  },
}));
interface Props {
  onClose: () => void;
}

const MessagesScrollViewContainer: React.FC<Props> = ({ onClose }) => {
  const { classes } = useStyles();
  const {
    conversationAIAdjustmentList,
    updateConversationAIAdjustmentList,
    resetConversationAIAdjustmentList,
  } = useOpenAiContext();

  const { isOpenAssistantDialog: isOpenAssisstantDialog } = useTextEditorContext();
  const { editor } = useRichTextEditorContext();
  const contentFromEditor = editor?.getText();

  useEffect(() => {
    if (isOpenAssisstantDialog) {
      resetConversationAIAdjustmentList();
      const item = {
        message: contentFromEditor,
        sender: 'user',
        start: false,
        end: false,
      };
      updateConversationAIAdjustmentList(item);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Stack className={classes.messagesScreenContainer}>
      {conversationAIAdjustmentList?.map((item, index) => (
        <React.Fragment key={index}>
          {item.sender === 'bot' ? (
            <BotAIMessage
              message={item.message}
              start={item.start}
              end={item.end}
              onClose={onClose}
            />
          ) : (
            <UserMessage message={item.message} />
          )}
        </React.Fragment>
      ))}
      <div id={MESSAGE_AI_ADJUSTMENT_LIST_SCROLL_POINT_ID}></div>
    </Stack>
  );
};

export default MessagesScrollViewContainer;
