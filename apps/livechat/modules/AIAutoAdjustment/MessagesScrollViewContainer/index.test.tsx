import { MantineProvider } from '@mantine/core';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { useTextEditorContext } from '../../TextEditor/context';
import { useOpenAiContext } from '../../openaiContext';
import MessagesScrollViewContainer from './index';

// Mock the contexts
jest.mock('../../openaiContext', () => ({
  useOpenAiContext: jest.fn(),
}));

jest.mock('../../TextEditor/context', () => ({
  useTextEditorContext: jest.fn(),
}));

jest.mock('@mantine/tiptap', () => ({
  useRichTextEditorContext: jest.fn(),
}));

// Mock the child components
jest.mock('../BotAIMessage', () => ({
  __esModule: true,
  default: ({ message, start, end, onClose }: any) => (
    <div data-testid='bot-message' data-start={start} data-end={end}>
      {message}
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

jest.mock('../UserMessage', () => ({
  __esModule: true,
  default: ({ message }: any) => <div data-testid='user-message'>{message}</div>,
}));

// Mock Mantine's createStyles
jest.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      messagesScreenContainer: 'messagesScreenContainer',
    },
  }),
}));

const renderWithMantine = (ui: React.ReactElement) => {
  return render(<MantineProvider>{ui}</MantineProvider>);
};

describe('MessagesScrollViewContainer', () => {
  const mockOnClose = jest.fn();
  const mockUpdateConversationAIAdjustmentList = jest.fn();
  const mockResetConversationAIAdjustmentList = jest.fn();
  const mockEditor = {
    getText: jest.fn().mockReturnValue('Test content'),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    (useOpenAiContext as jest.Mock).mockReturnValue({
      conversationAIAdjustmentList: [],
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      resetConversationAIAdjustmentList: mockResetConversationAIAdjustmentList,
    });

    (useTextEditorContext as jest.Mock).mockReturnValue({
      isOpenAssistantDialog: false,
    });

    (useRichTextEditorContext as jest.Mock).mockReturnValue({
      editor: mockEditor,
    });
  });

  it('renders empty container when no messages', () => {
    renderWithMantine(<MessagesScrollViewContainer onClose={mockOnClose} />);
    expect(screen.queryByTestId('bot-message')).not.toBeInTheDocument();
    expect(screen.queryByTestId('user-message')).not.toBeInTheDocument();
  });

  it('renders bot and user messages correctly', () => {
    const messages = [
      { message: 'Bot message', sender: 'bot', start: true, end: false },
      { message: 'User message', sender: 'user', start: false, end: false },
    ];

    (useOpenAiContext as jest.Mock).mockReturnValue({
      conversationAIAdjustmentList: messages,
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      resetConversationAIAdjustmentList: mockResetConversationAIAdjustmentList,
    });

    renderWithMantine(<MessagesScrollViewContainer onClose={mockOnClose} />);

    const botMessage = screen.getByTestId('bot-message');
    const userMessage = screen.getByTestId('user-message');

    expect(botMessage).toHaveTextContent('Bot message');
    expect(botMessage).toHaveAttribute('data-start', 'true');
    expect(botMessage).toHaveAttribute('data-end', 'false');
    expect(userMessage).toHaveTextContent('User message');
  });

  it('initializes conversation when assistant dialog is open', () => {
    (useTextEditorContext as jest.Mock).mockReturnValue({
      isOpenAssistantDialog: true,
    });

    renderWithMantine(<MessagesScrollViewContainer onClose={mockOnClose} />);

    expect(mockResetConversationAIAdjustmentList).toHaveBeenCalled();
    expect(mockUpdateConversationAIAdjustmentList).toHaveBeenCalledWith({
      message: 'Test content',
      sender: 'user',
      start: false,
      end: false,
    });
  });

  it('does not initialize conversation when assistant dialog is closed', () => {
    renderWithMantine(<MessagesScrollViewContainer onClose={mockOnClose} />);

    expect(mockResetConversationAIAdjustmentList).not.toHaveBeenCalled();
    expect(mockUpdateConversationAIAdjustmentList).not.toHaveBeenCalled();
  });

  it('calls onClose when bot message close button is clicked', () => {
    const messages = [{ message: 'Bot message', sender: 'bot', start: true, end: false }];

    (useOpenAiContext as jest.Mock).mockReturnValue({
      conversationAIAdjustmentList: messages,
      updateConversationAIAdjustmentList: mockUpdateConversationAIAdjustmentList,
      resetConversationAIAdjustmentList: mockResetConversationAIAdjustmentList,
    });

    renderWithMantine(<MessagesScrollViewContainer onClose={mockOnClose} />);

    const closeButton = screen.getByText('Close');
    closeButton.click();

    expect(mockOnClose).toHaveBeenCalled();
  });
});
