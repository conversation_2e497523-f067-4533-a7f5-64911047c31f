import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { MESSAGE_AI_ADJUSTMENT_PERCENT } from '../../../constants/grid';
import { useAppContext } from '../../appContext';
import { useConversationActionContext } from '../../conversationActionContext';
import { useUserContext } from '../../userContext';
import UserMessage from './index';

// Mock the contexts
jest.mock('../../appContext', () => ({
  useAppContext: jest.fn(),
}));

jest.mock('../../conversationActionContext', () => ({
  useConversationActionContext: jest.fn(),
}));

jest.mock('../../userContext', () => ({
  useUserContext: jest.fn(),
}));

// Mock the Preview component
jest.mock('../../../components/Preview', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='preview'>{children}</div>
  ),
}));

const renderWithMantine = (ui: React.ReactElement) => {
  return render(
    <MantineEmotionProvider>
      <MantineProvider>{ui}</MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('UserMessage', () => {
  const mockMessage = 'Test message';
  const mockUserProfile = {
    picture: 'user-picture-url',
  };
  const mockCurrentCustomer = {
    picture: 'customer-picture-url',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAppContext as jest.Mock).mockReturnValue({
      responsive: {
        responsiveScreen: false,
      },
    });
    (useConversationActionContext as jest.Mock).mockReturnValue({
      currentCustomer: mockCurrentCustomer,
    });
    (useUserContext as jest.Mock).mockReturnValue({
      userProfile: mockUserProfile,
    });
  });

  it('renders the message text correctly', () => {
    renderWithMantine(<UserMessage message={mockMessage} />);
    expect(screen.getByText(mockMessage)).toBeInTheDocument();
  });

  it('renders with responsive styles when responsiveScreen is true', () => {
    (useAppContext as jest.Mock).mockReturnValue({
      responsive: { responsiveScreen: true },
    });
    renderWithMantine(<UserMessage message={mockMessage} />);
    const messageBox = screen.getByText(mockMessage).parentElement;
    expect(messageBox).toHaveStyle({
      maxWidth: `${MESSAGE_AI_ADJUSTMENT_PERCENT}px`,
      width: 'fit-content',
      backgroundColor: '#F1F3F5',
      color: COLOR_DEFAULT_BLACK,
      padding: '10px',
    });
  });

  it('renders with default styles when responsiveScreen is false', () => {
    (useAppContext as jest.Mock).mockReturnValue({
      responsive: { responsiveScreen: false },
    });
    renderWithMantine(<UserMessage message={mockMessage} />);
    const messageBox = screen.getByText(mockMessage).parentElement;
    expect(messageBox).toHaveStyle({
      maxWidth: `${MESSAGE_AI_ADJUSTMENT_PERCENT}px`,
      width: 'fit-content',
      backgroundColor: '#F1F3F5',
      color: COLOR_DEFAULT_BLACK,
      padding: '12px',
    });
  });

  it('renders the user avatar with correct image', () => {
    renderWithMantine(<UserMessage message={mockMessage} />);
    const avatar = screen.getByAltText("it's me");
    expect(avatar).toHaveAttribute('src', mockUserProfile.picture);
  });

  it('renders the Preview component with correct customer picture', () => {
    renderWithMantine(<UserMessage message={mockMessage} />);
    const preview = screen.getByTestId('preview');
    expect(preview).toBeInTheDocument();
  });

  it('applies correct message styles', () => {
    renderWithMantine(<UserMessage message={mockMessage} />);
    const messageBox = screen.getByText(mockMessage).parentElement;
    expect(messageBox).toHaveStyle({
      backgroundColor: '#F1F3F5',
      color: COLOR_DEFAULT_BLACK,
      maxWidth: `${MESSAGE_AI_ADJUSTMENT_PERCENT}px`,
      width: 'fit-content',
    });
  });
});
