import { AspectRatio, Avatar, Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import Preview from '../../../components/Preview';
import { MESSAGE_AI_ADJUSTMENT_PERCENT, MESSAGE_PERCENT } from '../../../constants/grid';
import { useAppContext } from '../../appContext';
import { useConversationActionContext } from '../../conversationActionContext';
import { useUserContext } from '../../userContext';

const useStyles = createStyles(() => ({
  flexContainer: {
    justifyContent: 'flex-start',
  },
  boxAvatar: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  boxContent: {
    borderRadius: '10px',
    backgroundColor: '#845EF7',
    padding: '12px',
    color: 'white',
    maxWidth: `${MESSAGE_PERCENT}vw`,
  },
  boxContentResponsive: {
    borderRadius: '10px',
    backgroundColor: '#845EF7',
    color: 'white',
    padding: '10px',
    maxWidth: `${MESSAGE_PERCENT}vw`,
  },
  textBox: {
    fontSize: '14px',
  },
  messageInAIAdjustment: {
    backgroundColor: '#F1F3F5',
    color: COLOR_DEFAULT_BLACK,
    maxWidth: `${MESSAGE_AI_ADJUSTMENT_PERCENT}px`,
    width: 'fit-content',
  },
}));

interface Props {
  message: string;
}

const UserMessage = ({ message }: Props) => {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const { currentCustomer } = useConversationActionContext();
  const { classes, cx } = useStyles();
  const { userProfile } = useUserContext();

  return (
    <Flex
      w={'100%'}
      direction={'row'}
      gap={responsiveScreen ? rem(9.5) : 'md'}
      justify={'flex-end'}
    >
      <Box
        className={cx(
          responsiveScreen ? classes.boxContentResponsive : classes.boxContent,
          classes.messageInAIAdjustment
        )}
      >
        <Text
          className={classes.textBox}
          sx={{
            fontSize: responsiveScreen ? '12px' : undefined,
          }}
        >
          {message}
        </Text>
      </Box>
      <Box className={classes.boxAvatar} pr={10}>
        <AspectRatio ratio={38 / 38} w={26} maw={300} mx='auto'>
          <Preview
            imageUrl={currentCustomer.picture}
            showDownloadButton={false}
            showFileName={false}
          >
            <Avatar radius={'xl'} src={userProfile?.picture} alt="it's me" size='sm' />
          </Preview>
        </AspectRatio>
      </Box>
    </Flex>
  );
};

export default UserMessage;
