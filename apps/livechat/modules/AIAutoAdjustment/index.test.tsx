import { MantineProvider } from '@mantine/core';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { AiAutoAdjustmentContextProvider } from './context';
import AIAutoAdjustment from './index';

// Mock child components
jest.mock('./MessagesScrollViewContainer', () => ({
  __esModule: true,
  default: ({ onClose }: { onClose: () => void }) => (
    <div data-testid='messages-scroll-view' onClick={onClose}>
      MessagesScrollViewContainer
    </div>
  ),
}));

jest.mock('./TagsListSection', () => ({
  __esModule: true,
  default: () => <div data-testid='tags-list'>TagsList</div>,
}));

jest.mock('./InputSection', () => ({
  __esModule: true,
  default: () => <div data-testid='input-section'>InputSection</div>,
}));

// Mock the context provider
jest.mock('./context', () => ({
  AiAutoAdjustmentContextProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='context-provider'>{children}</div>
  ),
}));

const renderWithMantine = (ui: React.ReactElement) => {
  return render(<MantineProvider>{ui}</MantineProvider>);
};

describe('AIAutoAdjustment', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all child components', () => {
    renderWithMantine(<AIAutoAdjustment onClose={mockOnClose} />);

    expect(screen.getByTestId('context-provider')).toBeInTheDocument();
    expect(screen.getByTestId('messages-scroll-view')).toBeInTheDocument();
    expect(screen.getByTestId('tags-list')).toBeInTheDocument();
    expect(screen.getByTestId('input-section')).toBeInTheDocument();
  });

  it('passes onClose prop to MessagesScrollViewContainer', () => {
    renderWithMantine(<AIAutoAdjustment onClose={mockOnClose} />);

    const messagesScrollView = screen.getByTestId('messages-scroll-view');
    messagesScrollView.click();

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('wraps components in AiAutoAdjustmentContextProvider', () => {
    renderWithMantine(<AIAutoAdjustment onClose={mockOnClose} />);

    const contextProvider = screen.getByTestId('context-provider');
    expect(contextProvider).toBeInTheDocument();
    expect(contextProvider).toContainElement(screen.getByTestId('messages-scroll-view'));
    expect(contextProvider).toContainElement(screen.getByTestId('tags-list'));
    expect(contextProvider).toContainElement(screen.getByTestId('input-section'));
  });
});
