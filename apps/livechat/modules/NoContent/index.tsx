import { Flex, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';

const NoContent: React.FC<any> = () => {
  const { t } = useTranslate('common');
  return (
    <Flex align={'center'} justify={'center'} h={'100%'}>
      <Text
        c={'#909296'}
        size={rem(14)}
        fw={400}
        sx={{
          lineHeight: '21.7px',
        }}
      >
        {t('noContent')}
      </Text>
    </Flex>
  );
};

export default NoContent;
