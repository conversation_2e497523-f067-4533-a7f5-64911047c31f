import { Flex, rem } from '@mantine/core';
import useCustomAnimation from '@resola-ai/ui/hooks/useCustomAnimation';
import React from 'react';
import { useWarningContext } from '../warningContext';
const WARNING_CLASS_NAME = 'deca-livechat-warning';
const Warning: React.FC<any> = () => {
  const { warningText } = useWarningContext();
  const show = warningText.length > 0;
  useCustomAnimation(
    show,
    WARNING_CLASS_NAME,
    {
      rotate: 360,
      x: 100,
      y: -1000,
    },
    {
      rotate: 0,
      x: 50,
      y: -50,
    }
  );
  if (warningText === '') return null;
  return (
    <Flex
      sx={{
        position: 'fixed',
        zIndex: 999,
        bottom: 0,
      }}
    >
      <Flex
        className={WARNING_CLASS_NAME}
        w={'500px'}
        p={rem(20)}
        m={0}
        justify={'center'}
        sx={{
          backgroundColor: '#fca130',
          color: '#fff',
          borderRadius: rem(10),
        }}
      >
        {warningText}
      </Flex>
    </Flex>
  );
};

export default Warning;
