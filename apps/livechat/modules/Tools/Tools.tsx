import {
  WIDGET_ENGINE_WEB_APP_DEFAULT_CONTEXT_DATA,
  WIDGET_ENGINE_WEB_APP_DEFAULT_DATA,
} from '@resola-ai/shared-constants';
import { mergeTwoObjects } from '@resola-ai/utils';
import { Engine, EngineProps, WIDGET_ENGINE_CONTEXT_NAME } from '@resola-ai/widget-engine';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import AppConfig from '../../configs';
import { useAppContext } from '../appContext';
import { useConversationActionContext } from '../conversationActionContext';
import { useWidgetContext } from '../widgetContext';
import { ToolsContextProvider, useToolsContext } from './context';

const ToolsContainer = () => {
  const router = useRouter();
  const [widgets, setWidgets] = useState<EngineProps[]>([]);
  const [widgetSettings, setWidgetSettings] = useState<any>({});
  const [customContext, setCustomContext] = useState<any>(
    WIDGET_ENGINE_WEB_APP_DEFAULT_CONTEXT_DATA
  );
  const { responsiveScreen, accessToken, lang } = useAppContext();
  const { installedWidgetList, enabledTemplateWidget } = useWidgetContext();
  const customConfig = useCustomConfig();

  const {
    isNew,
    memos,
    isCompleted,
    currentTeam,
    isInProgress,
    aiSuggestionCall,
    currentConversation,
    currentConversationId,
    aiSuggestionDelayCall,
  } = useConversationActionContext();

  const {
    t,
    teamList,
    userProfile,
    rebotActions,
    listOperators,
    memoWidgetUtils,
    loadCrmWidgetData,
    activityWidgetUtils,
    operatorIdToImageMap,
    currentAssignedOperator,
    operatorIdToFinalNameMap,
    assignOperatorToConversation,
  } = useToolsContext();

  useEffect(() => {
    // when the data changes, we need to update it in widget engine too
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.APP_CONTEXT]: {
        accessToken,
        responsiveScreen,
        apiUrl: AppConfig.BASE_URL_API,
      },
    }));
  }, [setCustomContext, responsiveScreen, accessToken]);

  useEffect(() => {
    // when the data changes, we need to update it in widget engine too
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.CONVERSATION_CONTEXT]: {
        memos,
        isNew,
        currentTeam,
        isCompleted,
        isInProgress,
        currentConversation,
        currentConversationId,
      },
    }));
  }, [
    memos,
    isNew,
    isCompleted,
    currentTeam,
    isInProgress,
    setCustomContext,
    currentConversation,
    currentConversationId,
  ]);

  useEffect(() => {
    // when the data changes, we need to update it in widget engine too
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.TOOLS_CONTEXT]: {
        assignOperatorToConversation,
        operatorIdToFinalNameMap,
        currentAssignedOperator,
        operatorIdToImageMap,
        loadCrmWidgetData,
        memoWidgetUtils,
        listOperators,
        lang: lang,
        teamList,
        router,
        useSWR,
        t,
      },
    }));
  }, [
    assignOperatorToConversation,
    operatorIdToFinalNameMap,
    currentAssignedOperator,
    operatorIdToImageMap,
    loadCrmWidgetData,
    setCustomContext,
    memoWidgetUtils,
    listOperators,
    teamList,
    router,
    lang,
    t,
  ]);
  useEffect(() => {
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.USER_CONTEXT]: {
        userProfile,
      },
    }));
  }, [userProfile]);

  useEffect(() => {
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.OPEN_AI_CONTEXT]: {
        aiSuggestionCall,
        aiSuggestionDelayCall,
      },
    }));
  }, [setCustomContext, aiSuggestionCall, aiSuggestionDelayCall]);

  useEffect(() => {
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.REBOT_WIDGET_CONTEXT]: {
        ...rebotActions,
      },
    }));
  }, [rebotActions]);

  useEffect(() => {
    setCustomContext((prev) => ({
      ...prev,
      [WIDGET_ENGINE_CONTEXT_NAME.ACTIVITY_WIDGET_CONTEXT]: {
        t,
        ...activityWidgetUtils,
      },
    }));
  }, [activityWidgetUtils, t]);

  useEffect(() => {
    let _widgets = [WIDGET_ENGINE_WEB_APP_DEFAULT_DATA[0], WIDGET_ENGINE_WEB_APP_DEFAULT_DATA[3]];

    if (enabledTemplateWidget) {
      _widgets.push(WIDGET_ENGINE_WEB_APP_DEFAULT_DATA[1]);
    }

    const [rebot, others] = installedWidgetList?.reduce(
      (rs, el) => {
        rs[el?.engine?.component === 'chatbot' ? 0 : 1]?.push(el);
        return rs;
      },
      [[], []]
    );

    if (rebot.length) {
      _widgets = [
        {
          ...rebot[0].engine,
          name: rebot[0].name,
        },
        ..._widgets,
      ];
    }
    setWidgets([
      ..._widgets,
      ...others.map((w) => {
        return {
          ...w.engine,
          name: w.name,
        };
      }),
    ]);

    !!installedWidgetList?.length &&
      setWidgetSettings(
        installedWidgetList.reduce((acc, widget) => {
          acc[widget.component] = widget.data;
          return acc;
        }, {})
      );
  }, [enabledTemplateWidget, installedWidgetList]);

  // Clean up effect
  useEffect(() => {
    return () => {
      setWidgets([]);
      setWidgetSettings({});
      setCustomContext(WIDGET_ENGINE_WEB_APP_DEFAULT_CONTEXT_DATA);
    };
  }, []);
  // merge widget settings into custom config
  const finalConfig = useMemo(() => {
    return mergeTwoObjects(customConfig, widgetSettings);
  }, [customConfig, widgetSettings]);

  return <Engine data={widgets} context={customContext} config={finalConfig} />;
};

const useCustomConfig = () => {
  const { t } = useToolsContext();
  const { templateData } = useWidgetContext();

  return useMemo(() => {
    return {
      openAi: {
        title: t('label.ai'),
        textarea: {
          placeholder: t('label.ai.defaultText'),
        },
        copyButton: {
          label: t('label.ai.btn.copy'),
        },
        manualTriggerButton: {
          label: t('label.ai.btn.manualTrigger'),
        },
      },
      templates: {
        title: t('templates.title'),
        data: templateData,
      },
      chatDetail: {
        title: t('label.customer'),
        assignee: {
          label: t('label.customer.name'),
          noAssigneeLabel: t('chat.detail.operator.unassigned.title'),
        },
        team: {
          label: t('label.customer.team'),
          noAssignedTeamLabel: t('chat.detail.team.unassigned.title'),
        },
        latestChat: {
          label: t('label.customer.lastTimeChat'),
          elapsed: t('label.customer.equivelantTime'),
          convoCount: t('label.customer.convoCount'),
          convoCountTag: t('label.customer.convoCountTag'),
          convoCountTagOne: t('label.customer.convoCountTagOne'),
          convoCountTagTwo: t('label.customer.convoCountTagTwo'),
          convoCountTagThree: t('label.customer.convoCountTagThree'),
          convoCountTagTen: t('label.customer.convoCountTagTen'),
          detail: {
            suffix: ` ${t('chat.detail.latestChat')}`,
          },
          completed: {
            label: t('label.customer.lastTimeChatCompleted'),
          },
        },
        modal: {
          title: t('modal_operator.title'),
          description: t('modal_operator.description'),
          search: {
            placeholder: t('modal_operator.input_placeholder'),
            clearButton: t('modal_operator.button.clear'),
            countDataMatched: t('modal_operator.count_matched'),
            noResult: t('modal_operator.label.no_data_matched'),
            noAssignee: t('chat.detail.no.assignee'),
          },
          action: {
            cancel: t('modal_operator.button_cancel'),
            confirm: t('modal_operator.button_confirm'),
          },
        },
      },
      memos: {
        title: t('memo.title'),
        textLength: t('memo.length'),
        placeholder: t('memo.placeholder'),
        menuEdit: t('edit'),
        menuDelete: t('delete'),
        btnSave: t('save'),
        btnCancel: t('cancel'),
        deleteMessage: t('delete_message'),
      },
    };
  }, [t, templateData]);
};

export default function Tools() {
  return (
    <ToolsContextProvider>
      <ToolsContainer />
    </ToolsContextProvider>
  );
}
