import { Button, Space } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { useAppContext } from '../../appContext';
import { useToolsContext } from '../context';

export default function AddPluginButton() {
  const { responsiveScreen } = useAppContext();
  const { t } = useToolsContext();
  return (
    <Button
      variant='light'
      color='violet'
      radius='md'
      size={responsiveScreen ? '12px' : '14px'}
      py={7}
    >
      <IconPlus size={responsiveScreen ? 12 : 14}></IconPlus>
      <Space w={'md'} />
      {t('label.add_plugin.button_add_plugin')}
    </Button>
  );
}
