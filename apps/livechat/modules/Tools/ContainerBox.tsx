import { Box, rem } from '@mantine/core';
import { Text } from '@mantine/core';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import React from 'react';
import styled from 'styled-components';
import { useAppContext } from '../appContext';

const Container = styled(Box).attrs({ component: 'div' })<
  React.PropsWithChildren<{
    responsiveScreen?: boolean;
    color?: string;
  }>
>`
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    border: 0.5px solid #e6e6e6;
    padding: 18px 15px;
    width: 100%;
    text-align: left;
    color: ${({ color }) => color};
    gap: ${({ responsiveScreen }) => (responsiveScreen ? rem(13) : 'md')};
`;

interface Props {
  title: string;
  children: React.ReactNode;
}

const ContainerBox: React.FC<Props> = ({ title, children }) => {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  return (
    <Container responsiveScreen={responsiveScreen} color={COLOR_DEFAULT_BLACK}>
      <Text
        fw={700}
        sx={{
          fontSize: responsiveScreen ? '12px' : undefined,
        }}
      >
        {title}
      </Text>
      {children}
    </Container>
  );
};
export default ContainerBox;
