import { IGetConversationMessageParams } from '@resola-ai/models';
import { useCallback, useMemo } from 'react';
import useSWR from 'swr';
import ApiService from '../../../services/api';
import { useConversationActionContext } from '../../conversationActionContext';
import { useUserContext } from '../../userContext';

const loadConservationMessages = (conversationId: string, params?: IGetConversationMessageParams) =>
  ApiService.getConversationMessages(conversationId, params);

const loadConversationMessagesChatBot = (endUserId: string, integrationId: string, next?: string) =>
  ApiService.getChatBotMessages(endUserId, integrationId, next);

export function useActivityWidgetContainer() {
  const { userProfile } = useUserContext();
  const { currentConversation = null } = useConversationActionContext();

  const { endUserId, ocsChannelId, endUserImage, endUserName, currentUserId, integrationId } =
    useMemo(
      () => ({
        currentUserId: userProfile?.id || '',
        endUserId: currentConversation?.enduserId || null,
        ocsChannelId: currentConversation?.ocsChannel || null,
        integrationId: currentConversation?.integrationId || null,
        endUserName: currentConversation?.enduser?.name || '',
        endUserImage: currentConversation?.enduser?.picture || '',
      }),
      [
        userProfile?.id,
        currentConversation?.enduserId,
        currentConversation?.ocsChannel,
        currentConversation?.enduser?.name,
        currentConversation?.integrationId,
        currentConversation?.enduser?.picture,
      ]
    );

  const loadActivities = useCallback(
    (endUserId: string, integrationId: string, conversationId: string, nextFlag?: string) => {
      return ApiService.getActivityConversations(
        endUserId,
        integrationId,
        conversationId,
        nextFlag
      );
    },
    []
  );

  return useMemo(
    () => ({
      useSWR,
      endUserId,
      endUserName,
      ocsChannelId,
      endUserImage,
      currentUserId,
      integrationId,
      loadActivities,
      loadConservationMessages,
      loadConversationMessagesChatBot,
    }),
    [
      endUserId,
      endUserName,
      ocsChannelId,
      endUserImage,
      currentUserId,
      integrationId,
      loadActivities,
    ]
  );
}
