import { EnduserInfo } from '@resola-ai/models/collection/endusers';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import useSWR from 'swr';
import { EndUsersAPI } from '../../../services/api/endusers';
import { useConversationActionContext } from '../../conversationActionContext';

const loadEndUserInfo = (endUserId: string, integrationId: string) =>
  EndUsersAPI.getInfo(endUserId, integrationId);

const loadEndUserMessages = (endUserId: string, integrationId: string, next: string) =>
  EndUsersAPI.getMessages(endUserId, integrationId, next);

const updateInfo = async (endUserId: string, integrationId: string, payload: EnduserInfo) =>
  await EndUsersAPI.updateInfo(endUserId, integrationId, payload);

export const useRebotWidgetContainer = () => {
  const { t } = useTranslate('workspace');
  const { currentConversation = null } = useConversationActionContext();

  return useMemo(
    () => ({
      endUser: currentConversation?.enduser,
      conversationId: currentConversation?.id || '',
      endUserId: currentConversation?.enduserId || '',
      fromChatBot: currentConversation?.fromChatbot || false,
      integrationId: currentConversation?.integrationId || '',
      useSWR,
      updateInfo,
      loadEndUserInfo,
      rebotWidgetTrans: t,
      loadEndUserMessages,
    }),
    [
      currentConversation?.id,
      currentConversation?.enduser,
      currentConversation?.enduserId,
      currentConversation?.fromChatbot,
      currentConversation?.integrationId,
      t,
    ]
  );
};
