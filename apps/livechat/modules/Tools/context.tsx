import { useTranslate } from '@tolgee/react';
import { createContext, useCallback, useContext, useMemo } from 'react';
import ApiService from '../../services/api';
import { useConversationActionContext } from '../conversationActionContext';
import { useTeamContext } from '../teamContext';
import { useUserContext } from '../userContext';
import { useRebotWidgetContainer } from './hooks/rebotWidgetContainer';
import { useActivityWidgetContainer } from './hooks/useActivityWidgetContainer';
import { useMemoWidgetContainer } from './hooks/useMemoWidgetContainer';

const useTools = () => {
  const { t } = useTranslate('workspace');
  const { teamList, isLoadingTeamList, errorLoadingTeamList, reloadTeamList } = useTeamContext();
  const {
    userProfile,
    listOperators,
    getAssigneeName,
    reloadListOperator,
    operatorIdToImageMap,
    isLoadingListOperators,
    errorLoadingListOperator,
    operatorIdToFinalNameMap,
  } = useUserContext();

  const { currentConversation = null, handleAssignEntityInCharge } = useConversationActionContext();

  const rebotActions = useRebotWidgetContainer();
  const memoWidgetUtils = useMemoWidgetContainer();
  const activityWidgetUtils = useActivityWidgetContainer();
  const loadCrmWidgetData = useCallback((endUserId: string) => {
    return ApiService.getCrmEndUserInfo(endUserId);
  }, []);

  const assignOperatorToConversation = useCallback(
    async (operatorId: string | null, teamId?: string) => {
      if (!currentConversation || !operatorId) {
        return;
      }

      await handleAssignEntityInCharge(operatorId === 'no-operator' ? '' : operatorId, teamId);
    },
    [handleAssignEntityInCharge, currentConversation]
  );

  const currentAssignOperator = useMemo(() => {
    // check if the current conversation has assignee
    if (!currentConversation?.assignee) {
      return null;
    }
    // if it has assignee and that assignee exists in the list of operators
    const assigneeName = getAssigneeName(currentConversation?.assignee);
    const found = listOperators.find(
      (operator) => operator.id === currentConversation?.assignee?.id
    );
    if (found) {
      return { ...found, name: assigneeName };
    }
    // if it has assignee but that assignee is not in the list of operators
    // then mean  that assignee is not in the current team,
    return {
      ...currentConversation?.assignee,
      // add additional text into the name to express that this user is not belong to this team
      name: assigneeName,
    };
  }, [currentConversation?.assignee, getAssigneeName, listOperators]);

  return useMemo(
    () => ({
      teamList,
      userProfile,
      rebotActions,
      listOperators,
      memoWidgetUtils,
      isLoadingTeamList,
      activityWidgetUtils,
      errorLoadingTeamList,
      operatorIdToImageMap,
      isLoadingListOperators,
      operatorIdToFinalNameMap,
      errorLoadingListOperator,
      currentAssignedOperator: currentAssignOperator,
      currentTeam: currentConversation?.team || null,
      t,
      reloadTeamList,
      loadCrmWidgetData,
      reloadListOperator,
      assignOperatorToConversation,
    }),
    [
      teamList,
      userProfile,
      rebotActions,
      listOperators,
      memoWidgetUtils,
      isLoadingTeamList,
      activityWidgetUtils,
      operatorIdToImageMap,
      errorLoadingTeamList,
      currentAssignOperator,
      isLoadingListOperators,
      errorLoadingListOperator,
      operatorIdToFinalNameMap,
      currentConversation?.team,
      t,
      reloadTeamList,
      loadCrmWidgetData,
      reloadListOperator,
      assignOperatorToConversation,
    ]
  );
};

export type ToolsContextType = ReturnType<typeof useTools>;

const context = createContext<ToolsContextType | null>(null);

export const ToolsContextProvider: React.FC<any> = ({ children }) => {
  const value = useTools();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useToolsContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useToolsContext must be used inside ToolsContextProvider');
  }

  return value;
};
