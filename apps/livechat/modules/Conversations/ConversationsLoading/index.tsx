import { Container } from '@mantine/core';
import React from 'react';
import { useConversationContext } from '../../conversationContext';
import SkeletonLoading from '../Skeleton';
const ConversationsLoading: React.FC<any> = () => {
  const { isLoadingGetConversations } = useConversationContext();
  return (
    <Container
      sx={() => {
        return {
          width: '100%',
          padding: '0px',
          display: isLoadingGetConversations ? 'block' : 'none',
          // visibility: isLoadingGetConversations ? 'visible' : 'hidden',
        };
      }}
    >
      <SkeletonLoading />
    </Container>
  );
};

export default ConversationsLoading;
