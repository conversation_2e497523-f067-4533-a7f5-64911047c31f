import { Container, Flex } from '@mantine/core';
import { useMemo } from 'react';
import { CONVERSATION_LIST_MODULE_HEIGHT } from '../../constants';
import { useConversationContext } from '../conversationContext';
import ConversationsListWrapper from './ConversationList/ConservationList';
import Filter from './Filter';
import { ThemeConversationContextProvider } from './themeContext';

function ConversationsContainer() {
  const { paramsConversation } = useConversationContext();
  const hideFilter = useMemo(() => paramsConversation.bookmark, [paramsConversation.bookmark]);

  return (
    <Container
      sx={() => ({
        position: 'relative',
        height: `${CONVERSATION_LIST_MODULE_HEIGHT}`,
      })}
      p={0}
    >
      <Flex
        direction='column'
        sx={() => ({
          width: '100%',
          height: '100%',
          minWidth: '100%',
          position: 'relative',
        })}
      >
        {!hideFilter && <Filter />}
        {/* hide Actions for MVP version */}
        {/* <Actions /> */}
        {/* <ConversationsLoading /> */}
        <ConversationsListWrapper />
      </Flex>
    </Container>
  );
}

export default function ConversationsListContainer() {
  return (
    <ThemeConversationContextProvider>
      <ConversationsContainer />
    </ThemeConversationContextProvider>
  );
}
