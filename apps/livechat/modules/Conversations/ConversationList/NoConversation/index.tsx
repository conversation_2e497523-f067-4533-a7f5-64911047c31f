import { Flex, Text } from '@mantine/core';
import { BottomUpTween } from '@resola-ai/ui';
import React from 'react';
import { useAppContext } from '../../../appContext';
import { useConversationContext } from '../../../conversationContext';
import { useThemeConversationContext } from '../../themeContext';
import ConversationSvg from './ConversationSvg';
const NoConversation: React.FC<any> = () => {
  const { responsiveScreen } = useAppContext();
  const { listConversations, isLoadingCurrentConversation, isLoadingGetConversations } =
    useConversationContext();
  const { t } = useThemeConversationContext();

  if (isLoadingGetConversations) return null;
  if (isLoadingCurrentConversation) return null;
  if (listConversations.length > 0) return null;
  return (
    <Flex h={'100%'} direction={'column'} align={'center'} justify={'center'} gap={21}>
      <BottomUpTween>
        <ConversationSvg />
        <Text c={'#909296'} size={responsiveScreen ? '12px' : '14px'} fw={400}>
          {t('no.conversation')}
        </Text>
      </BottomUpTween>
    </Flex>
  );
};

export default NoConversation;
