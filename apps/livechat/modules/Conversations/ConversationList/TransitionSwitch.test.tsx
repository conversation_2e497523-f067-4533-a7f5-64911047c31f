import { MantineProvider, Transition } from '@mantine/core';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import TransitionSwitch from './TransitionSwitch';

jest.mock('@mantine/core', () => ({
  ...jest.requireActual('@mantine/core'),
  Transition: jest.fn(({ children, mounted }: any) => {
    return <div data-mounted={mounted}>{children({})}</div>;
  }),
}));

describe('TransitionSwitch', () => {
  it('should render offElement when switchState is false', async () => {
    render(
      <MantineProvider>
        <TransitionSwitch
          offElement={<div data-testid='off-element'>Off Element</div>}
          onElement={<div data-testid='on-element'>On Element</div>}
          switchState={false}
        />
      </MantineProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('off-element')).toBeInTheDocument();
      expect(screen.queryByTestId('on-element')).toBeInTheDocument();
    });
    const transitionOff = screen.getAllByTestId('off-element')[0].closest('div[data-mounted]');
    expect(transitionOff).toHaveAttribute('data-mounted', 'true');
  });

  it('should render onElement when switchState is true', async () => {
    render(
      <MantineProvider>
        <TransitionSwitch
          offElement={<div data-testid='off-element'>Off Element</div>}
          onElement={<div data-testid='on-element'>On Element</div>}
          switchState={true}
        />
      </MantineProvider>
    );
    await waitFor(() => {
      expect(screen.getByTestId('on-element')).toBeInTheDocument();
      expect(screen.queryByTestId('off-element')).toBeInTheDocument();
    });

    const transitionOn = screen.getAllByTestId('on-element')[0].closest('div[data-mounted]');
    expect(transitionOn).toHaveAttribute('data-mounted', 'true');
  });

  it('should not render onElement or offElement initially', async () => {
    render(
      <MantineProvider>
        <TransitionSwitch
          offElement={<div data-testid='off-element'>Off Element</div>}
          onElement={<div data-testid='on-element'>On Element</div>}
        />
      </MantineProvider>
    );
    await waitFor(() => {
      expect(screen.getByTestId('off-element')).toBeInTheDocument();
      expect(screen.queryByTestId('on-element')).toBeInTheDocument();
    });
    const transitionOff = screen.getAllByTestId('off-element')[0].closest('div[data-mounted]');
    expect(transitionOff).toHaveAttribute('data-mounted', 'true');
  });
  it('should render transition', async () => {
    render(
      <MantineProvider>
        <TransitionSwitch
          offElement={<div data-testid='off-element'>Off Element</div>}
          onElement={<div data-testid='on-element'>On Element</div>}
          switchState={true}
        />
      </MantineProvider>
    );
    await waitFor(() => {
      expect(Transition).toHaveBeenCalled();
    });
  });
  it('should apply correct styles', async () => {
    render(
      <MantineProvider>
        <TransitionSwitch
          offElement={<div data-testid='off-element'>Off Element</div>}
          onElement={<div data-testid='on-element'>On Element</div>}
          switchState={true}
        />
      </MantineProvider>
    );
    await waitFor(() => {
      const onElement = screen.getByTestId('on-element');
      const parent = onElement?.parentElement;
      const grandParent = parent?.parentElement?.parentElement;
      expect(grandParent).toHaveStyle('position: relative');
    });
  });
});
