import { Container, Loader } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import React, { useCallback, useEffect, useRef } from 'react';
import { useConversationContext } from '../../../conversationContext';
const LoadingMoreConversations: React.FC<any> = () => {
  const conversationListContainerRef = useRef(null);
  const { ref, entry } = useIntersection({
    root: conversationListContainerRef.current,
    threshold: 0.5,
  });
  const timeoutRef = useRef(undefined);
  const { conversationListInfinite, next, showLoadingMore, refetchConversListByNewParams } =
    useConversationContext();

  const handleLoadMoreWhenIntersecting = useCallback(() => {
    if (!showLoadingMore || !next) return;

    refetchConversListByNewParams({
      next: next,
    });
  }, [next, refetchConversListByNewParams, showLoadingMore]);

  useEffect(() => {
    if (entry?.isIntersecting) {
      timeoutRef.current = setTimeout(() => {
        handleLoadMoreWhenIntersecting();
      }, 500);
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [entry?.isIntersecting, handleLoadMoreWhenIntersecting]);

  const showLoadMore = conversationListInfinite?.isReachingEnd === false && next !== undefined;

  if (!showLoadMore) {
    return null;
  }

  return (
    <Container ref={ref} mt={10}>
      <Loader color='navy.0' type='dots' />
    </Container>
  );
};
export default LoadingMoreConversations;
