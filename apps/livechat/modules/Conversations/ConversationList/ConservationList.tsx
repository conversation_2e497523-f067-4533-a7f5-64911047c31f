import { Flex } from '@mantine/core';
import React, { memo } from 'react';
import { CONVERSATION_LIST_CONTAINER_ID } from '../../../constants';
import { useConversationContext } from '../../conversationContext';
import { useUserContext } from '../../userContext';
import { useThemeConversationContext } from '../themeContext';
import Conversation from './Conversation';
import AssigneeName from './Conversation/AssigneeName';
import Bookmarked from './Conversation/Bookmarked';
import Platform from './Conversation/Platform';
import LoadingMoreConversations from './LoadingMoreConversations';
import NoConversation from './NoConversation';

// scrollbar style is custom, so leave it separate from the theme for now.
const customStyleForScrollBar = {
  // customize scrollbar
  '&::-webkit-scrollbar': {
    width: '8px',
    display: 'none', // do not show the scrollbar for this conversation
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#e9e9e9',
    borderRadius: '8px',
    backgroundClip: 'padding-box',
    border: '1px solid transparent',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: '#c7c7c7',
  },
};

const ConversationsListWrapper: React.FC<any> = () => {
  const { borderColor } = useThemeConversationContext();
  return (
    <Flex
      id={CONVERSATION_LIST_CONTAINER_ID}
      gap={0}
      direction={'column'}
      sx={{
        height: '100%',
        overflowY: 'auto',
        overflowX: 'hidden',
        ...customStyleForScrollBar,
        overscrollBehavior: 'contain',
        scrollbarWidth: 'none',
        '& .conversation-item:first-of-type': {
          borderTop: `1px solid ${borderColor}`,
        },
        msOverflowStyle: 'none',
        '::-webkit-scrollbar': {
          display: 'none',
        },
      }}
    >
      <ConversationList />
      <NoConversation />
      <LoadingMoreConversations />
    </Flex>
  );
};

export default memo(ConversationsListWrapper);

const ConversationList: React.FC<any> = () => {
  const { getAssigneeName } = useUserContext();
  const { listConversations, paramsConversation } = useConversationContext();

  return (
    <>
      {listConversations?.map((data) => (
        <Conversation
          key={data.id}
          endUserId={data?.enduser?.id}
          endUserPicture={data?.enduser?.picture}
          conversationId={data.id}
          endUserName={
            data.enduser?.name || (data.enduser?.platform === 'web' ? data?.enduserId || '' : '')
          }
          lastMessageCreated={data.lastMessage?.created}
          lastMessage={data.lastMessage}
          unRead={data.unread}
          status={data?.status}
        >
          <Flex direction={'row'} justify={'space-between'}>
            <Platform platform={data.enduser?.platform} channelName={data.platform?.channelName} />
          </Flex>
          <Flex
            direction={'row'}
            sx={{
              position: 'relative',
              alignItems: 'center',
              justifyContent: 'flex-end',
              marginTop: '-4px',
            }}
          >
            <AssigneeName assigneeName={getAssigneeName(data?.assignee, data?.assigneeId)} />
            <Bookmarked show={Boolean(data.isBookmark || paramsConversation.bookmark)} />
          </Flex>
        </Conversation>
      ))}
    </>
  );
};
