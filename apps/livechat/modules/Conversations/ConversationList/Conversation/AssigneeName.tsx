import { Text } from '@mantine/core';
import { useCallback, useMemo } from 'react';
import { NO_ASSIGNEE_KEY_CHECK } from '../../../../constants';
import { useThemeConversationContext } from '../../themeContext';

export default function AssigneeName({
  assigneeName = NO_ASSIGNEE_KEY_CHECK,
}: {
  assigneeName?: string;
}) {
  const { t } = useThemeConversationContext();

  const name = useMemo(() => {
    return assigneeName === NO_ASSIGNEE_KEY_CHECK
      ? t('noAssignee')
      : t('hasAssigneeName', { name: assigneeName });
  }, [assigneeName, t]);
  return (
    <Text
      truncate
      style={{
        lineClamp: 1,
        fontWeight: 400,
        fontSize: '11px',
        color: '#A3A3A3',
        lineHeight: '16px',
      }}
      title={name}
    >
      {name}
    </Text>
  );
}
