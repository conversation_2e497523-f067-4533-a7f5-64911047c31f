import { Badge } from '@mantine/core';
import { PlatformType } from '@resola-ai/models';
import React from 'react';
import { useAppContext } from '../../../../appContext';
import { useThemeConversationContext } from '../../../themeContext';

interface Props {
  platform: PlatformType;
  channelName: string;
}
const Platform: React.FC<Props> = ({ platform, channelName }) => {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const {
    t,
    platformWebColor,
    platformLineColor,
    platformWebBackgroundColor,
    platformLineBackgroundColor,
  } = useThemeConversationContext();

  const texts = {
    web: t('platform.web'),
    line: t('platform.line'),
  };
  if (platform === 'line') {
    return (
      <Badge
        sx={() => ({
          top: 0,
          height: '16px',
          alignItems: 'end',
          lineHeight: '16px',
          position: 'absolute',
          color: platformLineColor,
          textTransform: 'inherit',
          borderRadius: '0px 0px 4px 4px',
          backgroundColor: platformLineBackgroundColor,
          fontSize: responsiveScreen ? '10px' : undefined,
        })}
      >
        {channelName ? channelName : texts.line}
      </Badge>
    );
  }
  if (platform === 'web') {
    return (
      <Badge
        sx={() => ({
          top: 0,
          height: '16px',
          alignItems: 'end',
          lineHeight: '16px',
          position: 'absolute',
          color: platformWebColor,
          textTransform: 'inherit',
          borderRadius: '0px 0px 4px 4px',
          backgroundColor: platformWebBackgroundColor,
          fontSize: responsiveScreen ? '10px' : undefined,
        })}
      >
        {channelName?.substring(0, 20) || texts.web}
      </Badge>
    );
  }
  return null;
};

export default Platform;
