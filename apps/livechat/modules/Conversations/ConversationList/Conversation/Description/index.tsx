import { Flex, HoverCard, Text } from '@mantine/core';
import { ILastMessage } from '@resola-ai/models';
import React, { useMemo } from 'react';
import { SIZE_IMAGE_SUIT_FOR_PREVIEW } from '../../../../../constants';
import { convertTextLineEmojisSupport } from '../../../../../utils/message_helper';
import { useAppContext } from '../../../../appContext';
import { useThemeConversationContext } from '../../../themeContext';
interface Props {
  message?: ILastMessage;
  unRead: boolean;
}

const Description: React.FC<Props> = ({ message, unRead }) => {
  const { responsive } = useAppContext();
  const { responsiveScreen } = responsive;
  const { readColor, unreadColor } = useThemeConversationContext();
  const textColor = unRead ? readColor : unreadColor;
  const textWeight = unRead ? 'bold' : 'normal';

  const text = useMemo(() => {
    const isTextMessage = message?.data?.type === 'text';
    let text = message?.data?.text || '';
    if (isTextMessage) {
      text = convertTextLineEmojisSupport({
        originalText: text,
        shouldDoTransform: false,
        emojis: message?.data?.emojis,
        forceImgSize: SIZE_IMAGE_SUIT_FOR_PREVIEW,
      });
    }
    return text;
  }, [message?.data?.emojis, message?.data?.text, message?.data?.type]);

  return (
    <Flex
      pt={5}
      align={'center'}
      wrap={'nowrap'}
      sx={() => ({ maxWidth: '100%', position: 'relative' })}
    >
      <HoverCard width={321} shadow='md' openDelay={1500}>
        <HoverCard.Target>
          <Text
            p={5}
            fz={14}
            truncate
            c={textColor}
            fw={textWeight}
            sx={{
              lineHeight: '20px',
              fontSize: responsiveScreen ? '12px' : undefined,
              p: {
                verticalAlign: 'baseline',
              },
              img: {
                verticalAlign: '-4px',
              },
            }}
            dangerouslySetInnerHTML={{
              __html: text,
            }}
          ></Text>
        </HoverCard.Target>
        <HoverCard.Dropdown>
          <Text
            fz={14}
            sx={{
              lineBreak: 'auto',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              fontSize: responsiveScreen ? '10px' : undefined,
              p: {
                verticalAlign: 'baseline',
              },
              img: {
                verticalAlign: '-4px',
              },
            }}
            dangerouslySetInnerHTML={{
              __html: text,
            }}
          ></Text>
        </HoverCard.Dropdown>
      </HoverCard>
    </Flex>
  );
};

export default Description;
