import { Avatar, Container, Flex, Group, Menu, Text, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useHover } from '@mantine/hooks';
import { ConversationListFilterType, ILastMessage } from '@resola-ai/models';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import {
  createCustomEventListener,
  displayFullDateTime,
  displayTimeFromNow,
  sendCustomEvent,
} from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React, { memo, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Animate3DotsSvg from '../../../../components/Animate3DotsSvg';
import FocusingAvatarIndicator from '../../../../components/FosusingAvatarIndicator';
import {
  FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
  TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
  USER_FOCUS_IN_CONVERSATION,
  USER_TYPING_STATUS_HAPPENING,
} from '../../../../constants';
import { FocusReturnDataType } from '../../../../hooks/useFocusEventManager';
import { TypingReturnDataType } from '../../../../hooks/useTypingEventManager';
import { useAppContext } from '../../../appContext';
import { useConversationContext } from '../../../conversationContext';
import { useThemeConversationContext } from '../../themeContext';
import TransitionSwitch from '../TransitionSwitch';
import Description from './Description';
interface Props {
  unRead: boolean;
  endUserId?: string;
  endUserName: string;
  children?: ReactNode;
  conversationId: string;
  endUserPicture: string;
  lastMessage?: ILastMessage;
  lastMessageCreated?: string;
  status?: ConversationListFilterType;
}

const TIME_TO_TRIGGER_PRELOAD = 200; // miliseconds

const Conversation: React.FC<Props> = ({
  unRead,
  status,
  children,
  endUserId = '',
  endUserName = '',
  lastMessageCreated,
  conversationId = '',
  endUserPicture = '',
  lastMessage,
}) => {
  const theme = useMantineTheme();
  const { t: tRebotWidget } = useTranslation('rebot_widget');
  const { responsive, endUserEditedRef, lang } = useAppContext();
  const { currentConversationId } = useConversationContext();
  const { timeColor, idColor, borderColor, selectedBackgroundColor, hoveredBackgroundColor } =
    useThemeConversationContext();

  const { hovered, ref } = useHover();
  const [isTyping, setIsTyping] = useState(false);
  const [focusingStatus, setFocusingStatus] = useState(false);

  const handleClick = useCallback(() => {
    if (endUserEditedRef.current && !window.confirm(tRebotWidget('change_warning'))) {
      return false;
    } else {
      endUserEditedRef.current = false;
      sendCustomEvent('deca-livechat-change-selected-conversation', { conversationId });
    }
  }, [conversationId, endUserEditedRef, tRebotWidget]);

  const displayId = useMemo(() => endUserId?.slice(0, 5), [endUserId]);

  const selected = useMemo(() => {
    return currentConversationId === conversationId;
  }, [currentConversationId, conversationId]);

  useEffect(() => {
    const controller = new AbortController();
    const handleFocusStatusPushUpdate = (e: CustomEvent<{ data: FocusReturnDataType }>) => {
      const { data } = e.detail;
      if (!data?.data.length) return;
      const found = data?.data.find((item) => item.conversationId === conversationId);
      if (!found) return;
      setFocusingStatus(() => {
        return found?.status === USER_FOCUS_IN_CONVERSATION;
      });
    };

    const handleUserTypingEvent = (e: CustomEvent<{ data: TypingReturnDataType }>) => {
      const { data } = e.detail;
      if (!data?.data.length) return;
      const found = data?.data.find((item) => item.conversationId === conversationId);
      setIsTyping(() => {
        return found?.status === USER_TYPING_STATUS_HAPPENING;
      });
    };

    createCustomEventListener(
      FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
      handleFocusStatusPushUpdate,
      controller.signal
    );

    createCustomEventListener(
      TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
      handleUserTypingEvent,
      controller.signal
    );

    return () => {
      controller.abort();
    };
  }, [conversationId]);

  useEffect(() => {
    let timeout;
    if (hovered) {
      timeout = setTimeout(() => {
        sendCustomEvent('prefetch-conversation-id-to-conversation-context', {
          conversationId,
        });
        sendCustomEvent('prefetch-first-message-list-to-message-context', {
          conversationId,
        });
      }, TIME_TO_TRIGGER_PRELOAD);
    }

    return () => {
      timeout && clearTimeout(timeout);
    };
  }, [hovered, conversationId]);

  return (
    <>
      <Container
        pb={5}
        p={'md'}
        ref={ref}
        id={conversationId}
        key={conversationId}
        onClick={handleClick}
        className='conversation-item'
        sx={{
          width: '100%',
          border: `1px solid ${borderColor}`,
          borderLeft: 'none',
          borderRight: 'none',
          borderTop: 'none',
          position: 'relative',
          backgroundColor: selected ? selectedBackgroundColor : 'transparent',
          '&:hover': {
            cursor: 'pointer',
            userSelect: 'none',
            backgroundColor: hoveredBackgroundColor,
          },
        }}
      >
        {children}
        <Flex
          mt={10}
          wrap={'nowrap'}
          justify={'space-between'}
          direction={responsive.tabletScreen ? 'column' : 'row'}
        >
          <AvatarAndName
            idColor={idColor}
            displayId={displayId}
            endUserId={endUserId}
            focusing={focusingStatus}
            endUserName={endUserName}
            endUserPicture={endUserPicture}
            responsiveScreen={responsive?.responsiveScreen}
            maxWidthGroup={status === 'inWrapUp' ? 'calc(100% - 115px)' : undefined}
          />
          <Group justify='flex-end' gap={'0'} style={{ flexWrap: 'nowrap' }}>
            {status === 'inWrapUp' && <WrapUpBadge />}
            <LastMessageTime
              lang={lang}
              timeColor={timeColor}
              lastMessageCreated={lastMessageCreated}
              responsiveScreen={responsive?.responsiveScreen}
            />
          </Group>
        </Flex>
        <Flex align={'center'} justify={'space-between'} mih={35} sx={{ overflow: 'hidden' }}>
          <TransitionSwitch
            switchState={isTyping}
            onElement={
              <Animate3DotsSvg
                color={theme.colors.dark[2]}
                style={{ width: '30px', height: '15px' }}
              />
            }
            offElement={<Description message={lastMessage} unRead={unRead} />}
          />
        </Flex>
      </Container>
    </>
  );
};

export default memo(Conversation);

function AvatarAndName({
  idColor,
  displayId,
  focusing,
  endUserId,
  endUserName,
  endUserPicture,
  responsiveScreen,
  maxWidthGroup,
}: {
  idColor: string;
  focusing: boolean;
  displayId: string;
  endUserId: string;
  endUserPicture: string;
  endUserName: string;
  responsiveScreen: boolean;
  maxWidthGroup?: string;
}) {
  return (
    <Group
      gap={0}
      justify='flex-start'
      style={{ flexWrap: 'nowrap', maxWidth: maxWidthGroup || 'calc(100% - 50px)' }}
    >
      <FocusingAvatarIndicator focusing={focusing}>
        <Avatar
          radius='xl'
          color={'cyan'}
          alt={'Avatar'}
          src={endUserPicture}
          // sx={() => ({ height: '38px', width: '38px' })}
        ></Avatar>
      </FocusingAvatarIndicator>
      <Text
        fz={14}
        fw={700}
        mx={'sm'}
        ta={'left'}
        truncate='end'
        c={COLOR_DEFAULT_BLACK}
        style={{ maxWidth: '140px' }}
        sx={{
          fontSize: responsiveScreen ? '12px' : undefined,
        }}
      >
        {endUserName}
      </Text>
      <Menu trigger={'hover'} width={'auto'} position={'top'} withArrow shadow='md'>
        <Menu.Target>
          <Text
            fz={11}
            truncate
            ta='left'
            c={idColor}
            display={responsiveScreen ? 'none' : 'inherit'}
          >{`ID ${displayId}`}</Text>
        </Menu.Target>
        <Menu.Dropdown maw={'500px'}>
          <Text
            p={5}
            fz={11}
            truncate
            ta='left'
            c={idColor}
            display={responsiveScreen ? 'none' : 'inherit'}
          >
            {endUserId}
          </Text>
        </Menu.Dropdown>
      </Menu>
    </Group>
  );
}

function LastMessageTime({
  timeColor,
  responsiveScreen,
  lastMessageCreated,
  lang = 'ja',
}: {
  timeColor: string;
  responsiveScreen: boolean;
  lastMessageCreated: string;
  lang: string;
}) {
  return (
    <Flex align={'center'}>
      <Menu trigger={'hover'}>
        <Menu.Target>
          <Text
            fz={12}
            truncate
            c={timeColor}
            sx={{
              fontSize: responsiveScreen ? '10px' : undefined,
            }}
          >
            {displayTimeFromNow(lastMessageCreated, lang)}
          </Text>
        </Menu.Target>
        <Menu.Dropdown>
          <Text
            fz={12}
            truncate
            c={timeColor}
            sx={{
              fontSize: responsiveScreen ? '10px' : undefined,
            }}
          >
            {displayFullDateTime(lastMessageCreated)}
          </Text>
        </Menu.Dropdown>
      </Menu>
    </Flex>
  );
}

const useStyle = createStyles(() => ({
  box: {
    border: '1px solid #339AF0',
    borderRadius: '20px',
    backgroundColor: '#D0EBFF',
    color: '#339AF0',
    fontSize: '14px',
  },
}));

const WrapUpBadge = () => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyle();

  return (
    <Flex align='center' gap='5px' px='8px' py='1px' mr={5} className={classes.box}>
      <Text fz={14} style={{ whiteSpace: 'nowrap' }}>
        {' '}
        {t('wrapUpBadge')}
      </Text>
    </Flex>
  );
};
