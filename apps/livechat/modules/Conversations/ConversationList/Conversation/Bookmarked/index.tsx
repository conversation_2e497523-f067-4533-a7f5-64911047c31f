import { Container } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconBookmarkFilled } from '@tabler/icons-react';
import React from 'react';

const useStyle = createStyles(() => ({
  filledBookmark: {
    path: {
      fill: '#FFD100',
    },
  },
}));

const Bookmarked = ({ show = false }: { show?: boolean }) => {
  const { classes } = useStyle();
  return (
    <Container
      pr={0}
      pl={'0.5rem'}
      mr={'-5px'}
      ml={'unset'}
      miw={'unset'}
      mah={22}
      sx={() => ({
        display: show ? 'block' : 'none',
        lineHeight: 0,
      })}
    >
      <IconBookmarkFilled cursor='pointer' className={classes.filledBookmark} size={22} />
    </Container>
  );
};

export default Bookmarked;
