import { Indicator, Text } from '@mantine/core';
import React from 'react';
import { useAppContext } from '../../../appContext';
import { useThemeConversationContext } from '../../themeContext';
interface Props {
  text: string;
  hasNewMessage?: boolean;
  selected?: boolean;
}
const Label: React.FC<Props> = ({ text, hasNewMessage = false, selected = false }) => {
  const { responsiveScreen } = useAppContext();
  const { filterIndicatorColor, filterTextColor } = useThemeConversationContext();
  const indicatorColor = filterIndicatorColor;
  const disabled = !hasNewMessage;
  const textWeight = selected ? 700 : 400;
  return (
    <Indicator
      inline
      disabled={disabled}
      color={indicatorColor}
      size={responsiveScreen ? 7 : 10}
      sx={() => ({
        // select the indicator
        '.mantine-Indicator-indicator': {
          right: responsiveScreen ? '-7px' : '-10px',
          top: '5px',
        },
      })}
    >
      <Text c={filterTextColor} fw={textWeight} fz={13}>
        {text}
      </Text>
    </Indicator>
  );
};

export default Label;
