import styled from '@emotion/styled';
import { Container, SegmentedControl, rem } from '@mantine/core';
import { ConversationListFilterType } from '@resola-ai/models';
import { sendCustomEvent } from '@resola-ai/utils';
import React, { useCallback } from 'react';
import { useAppContext } from '../../appContext';
import { useConversationContext } from '../../conversationContext';
import { useThemeConversationContext } from '../themeContext';
import Label from './Label';

const CustomizeSegmentedControl = styled<
  {
    backgroundcolor: string;
    selectedbackgroundcolor: string;
    padding: string;
    responsive: any;
  } & any
>(SegmentedControl)`
    background-color: ${(props) => props.backgroundcolor};
    transition: none;
    .mantine-SegmentedControl-indicator {
        background-color: ${(props) => props.selectedbackgroundcolor};
        transition: none;
    }
    .mantine-SegmentedControl-control {
        border-color: transparent;
        transition: none;
    }
    .mantine-SegmentedControl-indicator {
        box-shadow: none;
        border-radius: ${rem(6)};
        transition: none;
    }
    .mantine-SegmentedControl-label {
        padding: ${(props) => props.padding};
        font-size: 13px;
        line-height: 20.15px;
        transition: none;
    }
    .mantine-Indicator-root {
        transition: none;
    }
    label {
        transition: none;
    }
    // if responsive then set the fontsize to 12px
    // if not responsive then set the fontsize to 14px
    .mantine-Text-root {
        font-size: ${(props) => (props.responsive ? '12px' : '14px')};
        transition: none;
    }
`;

interface IFilter {
  value: ConversationListFilterType;
  label: React.ReactNode;
}

const FilterContainer = styled.div`
    padding-inline: 16px;
    margin-top: 20px;
`;

const Filter: React.FC<any> = () => {
  const { t } = useThemeConversationContext();
  const { responsive, endUserEditedRef } = useAppContext();
  const { filterBackgroundColor, filterSelectedBackgroundColor } = useThemeConversationContext();
  const {
    setNext,
    paramsConversation,
    hasUnreadNewConversation,
    refetchConversListByNewParams,
    hasUnreadCompletedConversation,
    hasUnreadInProgressConversation,
  } = useConversationContext();

  const filters: IFilter[] = [
    {
      value: 'new',
      label: (
        <Label
          key='new'
          text={t('filter.new')}
          hasNewMessage={hasUnreadNewConversation}
          selected={paramsConversation?.status === 'new'}
        />
      ),
    },
    {
      value: 'inProgress',
      label: (
        <Label
          key='inProgress'
          text={t('filter.processing')}
          hasNewMessage={hasUnreadInProgressConversation}
          selected={paramsConversation?.status === 'inProgress'}
        />
      ),
    },
    {
      value: 'completed',
      label: (
        <Label
          key='completed'
          text={t('filter.done')}
          hasNewMessage={hasUnreadCompletedConversation}
          selected={paramsConversation?.status === 'completed'}
        />
      ),
    },
  ];

  const getPadding = () => {
    if (responsive.responsiveScreen) {
      return '0px 0px';
    }
    if (responsive.mediumScreen) {
      return '5px 5px';
    }
    if (responsive.smallScreen) {
      return '5px 0px';
    }
  };

  const handleChangeFilter = useCallback(
    (value: ConversationListFilterType) => {
      if (endUserEditedRef.current && !window.confirm(t('change_warning'))) {
        return false;
      } else {
        endUserEditedRef.current = false;
        refetchConversListByNewParams({
          status: value,
          next: undefined,
        });
        setNext(undefined);
        sendCustomEvent('deca-livechat-conversation-detail-reset', {});
      }
    },
    [endUserEditedRef, t, refetchConversListByNewParams, setNext]
  );

  const { responsiveScreen = false } = responsive;
  return (
    <FilterContainer>
      <Container
        mb={20}
        w={'100%'}
        p={responsiveScreen ? 0 : 5}
        sx={() => ({
          borderRadius: 10,
          transition: 'none',
          backgroundColor: filterBackgroundColor,
        })}
      >
        <CustomizeSegmentedControl
          fullWidth
          radius={10}
          data={filters}
          padding={getPadding()}
          onChange={handleChangeFilter}
          key={paramsConversation.status}
          value={paramsConversation?.status}
          backgroundcolor={filterBackgroundColor}
          responsive={responsiveScreen?.toString()}
          selectedbackgroundcolor={filterSelectedBackgroundColor}
        />
      </Container>
    </FilterContainer>
  );
};

export default Filter;
