import { useDebouncedState } from '@mantine/hooks';
import axios from 'axios';
import { IConversation, ILastMessage, IMessage } from '@resola-ai/models';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import useSWR from 'swr';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import AppConfig from '../configs';
import useVisibilityControl from '../hooks/useVisibilityControl';
import ApiService from '../services/api';
import { getDefaultAssigneeId } from '../utils/assignee';
import { delay } from '../utils/common';
import { useAppContext } from './appContext';
import { useTeamContext } from './teamContext';
import { useUserContext } from './userContext';
import { DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR } from '../constants';
const DELAY_TIME = 2000;
const INTERNAL_EVENT_TO_TRIGGER_RELOAD = 'deca-livechat-reload-conversation-for-tool-column';

const useConversationState = (
  currConversation?: IConversation | null,
  lastMessageAccumulateByTime?: ILastMessage
) => {
  const { userProfile } = useUserContext();
  const { teamList } = useTeamContext();
  const assignee = useMemo(() => currConversation?.assignee, [currConversation?.assignee]);
  const currentTeam = useMemo(() => {
    if (currConversation?.team) {
      return typeof currConversation?.team === 'string'
        ? teamList.find((t) => t.id === (currConversation?.team as unknown as string))
        : currConversation?.team;
    }
    return teamList.find((t) => t.id === currConversation?.teamId);
  }, [currConversation?.team, currConversation?.teamId, teamList]);

  const lastMessage = useMemo(() => {
    if (!lastMessageAccumulateByTime) return currConversation?.lastMessage;

    const lastMes = currConversation?.lastMessage;
    if (!lastMes) return lastMessageAccumulateByTime;

    const lastTime = new Date(lastMessageAccumulateByTime.created || '').getTime();
    const currTime = new Date(lastMes.created || '').getTime();

    return lastTime > currTime ? lastMessageAccumulateByTime : lastMes;
  }, [currConversation?.lastMessage, lastMessageAccumulateByTime]);

  return useMemo(
    () => ({
      isAssigned: !!assignee,
      lastMessage: lastMessage,
      customer: currConversation?.enduser,
      isNew: currConversation?.status === 'new',
      isWrapUp: currConversation?.status === 'inWrapUp',
      isCompleted: currConversation?.status === 'completed',
      isInProgress: currConversation?.status === 'inProgress',
      isTheAssignee: !!assignee && !!userProfile && assignee.id === userProfile?.id,
      hasTeamAssign: !!currConversation?.teamId,
      haveAssignee:
        currConversation?.assigneeId &&
        userProfile?.orgId &&
        currConversation?.assigneeId !== getDefaultAssigneeId(userProfile.orgId),
      currentTeam: currentTeam,
      memos: currConversation?.memos || [],
      platformWeb: currConversation?.enduser?.platform === 'web',
      platformLine: currConversation?.enduser?.platform === 'line',
      currentCustomer: {
        ...currConversation?.enduser,
        lastTimeChat: currConversation?.lastMessage?.created,
      },
      focusing: !!currConversation?.focusing || false,
      endUserReadAt: currConversation?.enduserLastReadAt || undefined,
    }),
    [
      assignee,
      lastMessage,
      currentTeam,
      userProfile,
      currConversation?.memos,
      currConversation?.status,
      currConversation?.teamId,
      currConversation?.enduser,
      currConversation?.focusing,
      currConversation?.assigneeId,
      currConversation?.lastMessage?.created,
      currConversation?.enduserLastReadAt,
    ]
  );
};

const triggerConversationInContextReload = () =>
  sendCustomEvent(INTERNAL_EVENT_TO_TRIGGER_RELOAD, {});

const useConversationAction = () => {
  const { accessToken } = useAppContext();
  const newConversationRef = useRef<any>(null);
  const { userProfile } = useUserContext();
  const { visible: visibleConfirmModal, open, close: closeConfirmModal } = useVisibilityControl();
  const {
    visible: isBookmarkedConversation,
    close: unBookMarkConversationFlg,
    open: bookmarkConversationFlg,
  } = useVisibilityControl();
  const [currentConversationId, setCurrentConversationId] = useDebouncedState<string | null>(
    null,
    200
  );
  // We will compare this value to conversation.lastMessage to select the last message.
  const [lastMessageAccumulateByTime, updateLastMessageAccumulateByTime] = useState<ILastMessage>();

  const {
    data: currentConversation = null,
    isLoading: isLoadingConversation,
    mutate: reload,
  } = useSWR(
    !!currentConversationId ? currentConversationId : null,
    () => ApiService.getConversationById(currentConversationId),
    { revalidateIfStale: true }
  );

  const conversationState = useConversationState(currentConversation, lastMessageAccumulateByTime);

  const handleSetCurrentConversationId = useCallback(
    (newConversationId: string | null) => {
      if (newConversationId !== currentConversationId) setCurrentConversationId(newConversationId);
    },
    [currentConversationId, setCurrentConversationId]
  );

  const aiSuggestionCall = useCallback(
    async (conversationId: string, widgetSettingId: string) => {
      if (!widgetSettingId || !conversationId) {
        // console.log(
        //     'Do not provide widgetSettingId or conversationId, can not call api for ai suggestion',
        // );
        return {
          flag: 'NO_INFO',
        };
      }
      if (currentConversation?.status === 'completed') {
        return {
          flag: 'NO_CALLING',
        };
      }
      try {
        const response = await axios.post(
          `${AppConfig.BASE_URL_API}/ai/suggestions`,
          {
            widgetId: widgetSettingId,
            conversationId: conversationId,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        try {
          const receivedSuggestionText = response.data.data;
          return receivedSuggestionText;
        } catch (error) {
          console.log('get ai suggestion response handle have error', error);
        }
      } catch (error) {
        console.log('get ai suggestion response error', error);
      }
    },
    [accessToken, currentConversation?.status]
  );

  const aiSuggestionDelayCall = useCallback(
    async (conversationId: string, widgetSettingId: string, delayTime = DELAY_TIME) => {
      await delay(delayTime);
      return await aiSuggestionCall(conversationId, widgetSettingId);
    },
    [aiSuggestionCall]
  );

  // handle assign operator and team to a conversation
  const handleAssignEntityInCharge = useCallback(
    async (operatorId: string | null, teamId: string) => {
      if (!currentConversation) return;
      try {
        const result = await ApiService.assignOperatorToConversation({
          conversationId: currentConversation?.id,
          operatorId,
          teamId,
        });

        if ((result as { message: string })?.message) {
          sendCustomEvent(DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR, {
            message: (result as { message: string }).message,
          });
        }
        // Wait for the above call return
        // reload current conversation.
        // send event to conversation context to synchronize the data.
        sendCustomEvent('deca-livechat-update-entity-in-charge-to-conversation-from-tool-column', {
          operatorId,
          teamId,
        });

        // => this helps we can reuse cached value from the same call get conversation
        setTimeout(() => {
          triggerConversationInContextReload(); // => this will render the whole tool column
        }, 900);
      } catch (e) {}
    },
    [currentConversation]
  );

  const handleSelfAssignEntityInCharge = useCallback(() => {
    if (!currentConversation || !userProfile) return;
    sendCustomEvent(
      'deca-livechat-update-self-assign-entity-in-charge-to-conversation-from-tool-column',
      {
        conversationId: currentConversation.id,
        operatorId: userProfile.id,
        assignToMe: true,
      }
    );
    setTimeout(() => {
      triggerConversationInContextReload();
    }, 900);
  }, [currentConversation, userProfile]);

  const confirmMoveToOtherConversation = useCallback(() => {
    if (!newConversationRef.current) return;
    const origin = location?.origin;
    const path = location?.pathname;

    let link = `${origin}${path}?status=${newConversationRef.current?.status}&per_page=20&cId=${newConversationRef.current?.id}&team=${newConversationRef.current?.teamId}`;
    if (newConversationRef?.current?.assigneeId?.includes('UNKNOWN')) {
      link += '&assigned=false';
    }
    location.href = link;
  }, []);

  const handleCreateConversationFromEndOne = useCallback(async () => {
    if (!currentConversation?.id) return;
    const res = await ApiService.createNewConversationFromEndedOne(currentConversation?.id);

    if (!res) return;

    if (!res?.isNewConversation) {
      open();
      newConversationRef.current = res;
    } else {
      const origin = location?.origin;
      const path = location?.pathname;
      let link = `${origin}${path}?status=${res?.status}&per_page=20&cId=${res?.id}&team=${res?.teamId}`;
      if (res?.assigneeId?.includes('UNKNOWN')) {
        link += '&assigned=false';
      }
      location.href = link;
    }
  }, [currentConversation?.id, open]);

  const changeAutoCompleteParams = useCallback(
    async (conversationId: string, enable: boolean, interval: number) => {
      try {
        await ApiService.convoSetAutoComplete({ conversationId, enable, interval });
        return true;
      } catch (error) {
        return false;
      }
    },
    []
  );

  const toggleBookmark = useCallback(
    async (type: 'add' | 'remove') => {
      try {
        //Local context store this data.
        type === 'add' ? bookmarkConversationFlg() : unBookMarkConversationFlg();
        await ApiService.toggleBookmark(currentConversation?.id, type);
        // Move to conversation context to handle filter data
        sendCustomEvent('deca-livechat-update-bookmark-flag-to-conversation-context', {
          id: currentConversation?.id,
          action: type,
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    [bookmarkConversationFlg, currentConversation?.id, unBookMarkConversationFlg]
  );

  const closeConversation = useCallback(async (conversationId: string) => {
    try {
      await ApiService.changeConversationStatus(conversationId, 'completed');
      sendCustomEvent('deca-livechat-close-conversation-to-conversation-context', {
        conversationId,
      });
      setTimeout(() => triggerConversationInContextReload(), 700);
      return true;
    } catch (error) {
      return false;
    }
  }, []);

  const wrapUpConversation = useCallback(async (conversationId: string) => {
    try {
      await ApiService.changeConversationStatus(conversationId, 'inWrapUp');
      sendCustomEvent('deca-livechat-wrapup-conversation-to-conversation-context', {
        conversationId,
      });
      await delay(300);
      triggerConversationInContextReload();
      return true;
    } catch (e) {
      return false;
    }
  }, []);

  const handleAddMessageToConversationsContext = useCallback((message: IMessage) => {
    sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
      message,
    });
  }, []);

  useEffect(() => {
    if (currentConversation) {
      if (!!currentConversation?.isBookmark) bookmarkConversationFlg();
      else unBookMarkConversationFlg();
    }
  }, [bookmarkConversationFlg, currentConversation, unBookMarkConversationFlg]);

  useEffect(() => {
    const controller = new AbortController();
    const handleSetNewConversationId = (event: { detail: any }) => {
      updateLastMessageAccumulateByTime(() => undefined);
      handleSetCurrentConversationId(event.detail.conversationId);
      setTimeout(() => {
        reload?.();
      }, DELAY_TIME - 1000);
    };

    const handleReloadConversation = () => {
      reload?.();
    };
    // We need to listen an event that told us about the change of conversation id
    // This help us setup data for tool column (widgets)
    createCustomEventListener(
      'deca-livechat-setup-new-conversation-id-for-tool-column',
      handleSetNewConversationId,
      controller.signal
    );

    createCustomEventListener(
      INTERNAL_EVENT_TO_TRIGGER_RELOAD,
      handleReloadConversation,
      controller.signal
    );
    return () => {
      controller.abort();
    };
  }, [handleSetCurrentConversationId, reload]);

  return useMemo(
    () => ({
      ...conversationState,
      visibleConfirmModal,
      currentConversation,
      currentConversationId,
      isLoadingConversation,
      isBookmarkedConversation,
      shouldDisplay: currentConversationId && !isLoadingConversation && currentConversation,
      toggleBookmark,
      aiSuggestionCall,
      closeConfirmModal,
      closeConversation,
      wrapUpConversation,
      aiSuggestionDelayCall,
      changeAutoCompleteParams,
      setCurrentConversationId,
      handleAssignEntityInCharge,
      handleSelfAssignEntityInCharge,
      confirmMoveToOtherConversation,
      updateLastMessageAccumulateByTime,
      handleCreateConversationFromEndOne,
      handleAddMessageToConversationsContext,
    }),
    [
      conversationState,
      currentConversation,
      visibleConfirmModal,
      currentConversationId,
      isLoadingConversation,
      isBookmarkedConversation,
      toggleBookmark,
      aiSuggestionCall,
      closeConfirmModal,
      closeConversation,
      wrapUpConversation,
      aiSuggestionDelayCall,
      changeAutoCompleteParams,
      setCurrentConversationId,
      handleAssignEntityInCharge,
      handleSelfAssignEntityInCharge,
      confirmMoveToOtherConversation,
      handleCreateConversationFromEndOne,
      handleAddMessageToConversationsContext,
    ]
  );
};

export type ConversationContextType = ReturnType<typeof useConversationAction>;

const context = createContext<ConversationContextType | null>(null);

export const ConversationActionContextProvider: React.FC<any> = ({ children }) => {
  const value = useConversationAction();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useConversationActionContext = () => {
  const value = useContext(context);
  if (!value) {
    throw new Error(
      'useConversationActionContext must be used inside ConversationActionContextProvider'
    );
  }

  return value;
};
