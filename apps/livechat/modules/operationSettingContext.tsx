import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { fallbackLng } from '../i18n/settings';
import ApiService from '../services/api';
import {
  IHolidays,
  IOutBusinessHour,
  IOperationSettings,
  IOperationSettingsStatePayload,
  IBusinessHoursStatePayload,
  ICalendarHours,
  IHolidaysStatePayload,
  IOutBusinessHourStatePayload,
  IRules,
  IPreferences,
  IAutoReplyConfig,
  INotificationSounds,
} from './Settings/Operations/models';
import {
  initCalendarHours,
  initAutoReplyOutsideBusinessHours,
  initHolidays,
  initAutoReplyConfig,
} from './Settings/Operations/mockData';
import { HolidayType } from './Settings/Operations/types';
import { useUserContext } from './userContext';
import useSWR from 'swr';
import { throttle } from '../utils/throttle';

type IOperationSettingPayload =
  | IOperationSettingsStatePayload
  | IBusinessHoursStatePayload
  | IOutBusinessHourStatePayload
  | IHolidaysStatePayload
  | IAutoReplyConfig
  | { preferences: IPreferences };

const removeOldTagIfExist = (url: string) => {
  document.getElementById(url)?.remove();
};

const preloadSound = (url: string) => {
  const link = document.createElement('link');
  link.id = url;
  link.type = 'audio/mpeg';
  link.rel = 'preload';
  link.as = 'audio';
  link.href = url;
  document.head.appendChild(link);
};

const playSound = throttle((soundUrl: string) => {
  const audio = new Audio(soundUrl);
  audio.play();
}, 1000);

const useOperationSettings = () => {
  const [lang, setLang] = useState<string>(fallbackLng);
  const { userSoundSetting } = useUserContext();
  const [operationSettingsData, setOperationSettingsData] = useState<IOperationSettings | null>(
    null
  );
  const [operationStatus, setOperationStatus] = useState<boolean>(false);
  const [serviceOpen, setServiceOpen] = useState<boolean>(false);
  const [calendarHours, setCalendarHours] = useState<ICalendarHours>(initCalendarHours);
  const [payloadCalendarHours, setPayloadCalendarHours] =
    useState<ICalendarHours>(initCalendarHours);
  const [replyOutsideBusinessHour, setReplyOutsideBusinessHour] = useState<IOutBusinessHour>(
    initAutoReplyOutsideBusinessHours
  );
  const [holidays, setHolidays] = useState<IHolidays | null>(initHolidays);
  const [holidaySelected, setHolidaySelected] = useState<HolidayType | null>(null);
  const [holidayRulesList, setHolidayRulesList] = useState<HolidayType[]>([]);
  const [payloadHolidays, setPayloadHolidays] = useState<IHolidays | null>(initHolidays);
  const [currentHoliday, setCurrentHoliday] = useState<IRules | null>(null);
  const { isManager } = useUserContext();
  const [preferences, setPreferences] = useState<IPreferences | undefined>({});
  const [notificationSounds, setNotificationSounds] = useState<INotificationSounds[]>([]);

  const { data, mutate, isLoading } = useSWR(
    '/settings/operations',
    ApiService.getOperationSettings
  );

  useEffect(() => {
    if (data) {
      setOperationSettingsData(data);
      setOperationStatus(data.status);
      setServiceOpen(data.open);
      setCalendarHours(data.calendar);
      setPayloadCalendarHours(data.calendar);
      setReplyOutsideBusinessHour(() => ({ ...data.outBusinessHour }));
      setHolidays(data.holidays);
      setPayloadHolidays(data.holidays);
      setCurrentHoliday(data?.currentHoliday || null);
      setPreferences(() => ({ ...(data.preferences || {}) }));
      setNotificationSounds(data.notificationSounds || []);
    }
  }, [data]);

  useEffect(() => {
    if (notificationSounds?.length) {
      notificationSounds.forEach((sound) => removeOldTagIfExist(sound.url));
      notificationSounds.forEach((sound) => preloadSound(sound.url));
    }
  }, [notificationSounds]);

  const updateOperationSettings = useCallback(
    async (payload: IOperationSettingPayload) => {
      await ApiService.updateOperationSettings(payload);
      mutate();
    },
    [mutate]
  );

  const updateOperationSettingsOnly = useCallback(async (payload: IOperationSettingPayload) => {
    return await ApiService.updateOperationSettings(payload);
  }, []);

  const customTimePeriods = useCallback((closedTime) => {
    if (!closedTime) return [];

    let mappingTimePeriods = [];
    closedTime.forEach((time) => {
      const item = {
        fromTime: `${time.fromTime.hour}:${time.fromTime.minute}`,
        toTime: `${time.toTime.hour}:${time.toTime.minute}`,
      };
      mappingTimePeriods.push(item);
    });

    return mappingTimePeriods;
  }, []);

  const handleCustomRules = useCallback(
    (rules) => {
      if (!rules) return [];

      const mappingRules = [];
      rules.forEach((rule) => {
        let mappingTimePeriods = customTimePeriods(rule.closedTime);
        const item = {
          id: rule.id,
          name: rule.name,
          fromDate: new Date(rule.fromDate),
          toDate: new Date(rule.toDate),
          timePeriods: mappingTimePeriods,
        };

        mappingRules.push(item);
      });

      return mappingRules;
    },
    [customTimePeriods]
  );

  const customClosedTime = useCallback((timePeriods) => {
    if (!timePeriods) return [];

    const closedTime = [];
    timePeriods.forEach((time) => {
      const item = {
        fromTime: { hour: 0, minute: 0 },
        toTime: { hour: 0, minute: 0 },
      };
      const splitFromTime = time.fromTime?.split(':');
      const splitToTime = time.toTime?.split(':');
      item.fromTime.hour = Number(splitFromTime?.[0]);
      item.fromTime.minute = Number(splitFromTime?.[1]);
      item.toTime.hour = Number(splitToTime?.[0]);
      item.toTime.minute = Number(splitToTime?.[1]);

      closedTime.push(item);
    });

    return closedTime;
  }, []);

  const handlePlaySound = useCallback(() => {
    if (!userSoundSetting) return;
    const { selectedSoundId } = userSoundSetting;
    const sound = notificationSounds.find((s) => s.id === selectedSoundId);
    if (sound) {
      playSound(sound.url);
    } else {
      console.error(`Sound with ID ${selectedSoundId} not found in notificationSounds.`);
    }
  }, [notificationSounds, userSoundSetting]);

  const handlePlaySoundNotification = useCallback(
    (message?: any) => {
      if (!userSoundSetting) return;
      const { mode } = userSoundSetting;
      const shouldPlaySound =
        mode === 'allMessage' || (mode === 'onlyAtStart' && message?.isFirstEnduserMessage);
      if (shouldPlaySound) {
        handlePlaySound();
      }
    },
    [handlePlaySound, userSoundSetting]
  );

  const autoReplyConfig = useMemo(() => data?.autoReply || initAutoReplyConfig, [data?.autoReply]);

  return useMemo(
    () => ({
      lang,
      holidays,
      isManager,
      isLoading,
      serviceOpen,
      preferences,
      calendarHours,
      currentHoliday,
      operationStatus,
      payloadHolidays,
      holidaySelected,
      autoReplyConfig,
      holidayRulesList,
      notificationSounds,
      payloadCalendarHours,
      operationSettingsData,
      replyOutsideBusinessHour,
      setHolidays,
      setServiceOpen,
      handlePlaySound,
      setCalendarHours,
      customClosedTime,
      handleCustomRules,
      customTimePeriods,
      setOperationStatus,
      setHolidaySelected,
      setPayloadHolidays,
      setHolidayRulesList,
      setNotificationSounds,
      updateOperationSettings,
      setPayloadCalendarHours,
      setOperationSettingsData,
      setReplyOutsideBusinessHour,
      updateOperationSettingsOnly,
      handlePlaySoundNotification,
      reloadOperationSettings: mutate,
      updateLang: (lang: string) => setLang(lang),
    }),
    [
      lang,
      holidays,
      isLoading,
      isManager,
      serviceOpen,
      preferences,
      calendarHours,
      currentHoliday,
      payloadHolidays,
      holidaySelected,
      autoReplyConfig,
      operationStatus,
      holidayRulesList,
      notificationSounds,
      payloadCalendarHours,
      operationSettingsData,
      replyOutsideBusinessHour,
      mutate,
      handlePlaySound,
      customClosedTime,
      customTimePeriods,
      handleCustomRules,
      updateOperationSettings,
      updateOperationSettingsOnly,
      handlePlaySoundNotification,
    ]
  );
};

export type OperationSettingType = ReturnType<typeof useOperationSettings>;

const context = createContext<OperationSettingType | null>(null);

export const OperationSettingContextProvider: React.FC<any> = ({ children }) => {
  const value = useOperationSettings();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useOperationSettingContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error(
      'useOperationSettingContext must be used inside OperationSettingContextProvider'
    );
  }

  return value;
};
