const mockPush = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: jest.fn(),
    prefetch: jest.fn(),
    route: '/',
    pathname: '/',
    query: {},
    asPath: '/',
    events: { on: jest.fn(), off: jest.fn(), emit: jest.fn() },
    isFallback: false,
  }),
}));
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import React from 'react';
import { AutomationData, Status } from '../../../../models/automation';
import { useAutomationListContext } from '../contexts/AutomationListContext';
import { AutomationListScreen } from '../index';

// Mock dependencies
jest.mock('../contexts/AutomationListContext');
jest.mock('../../../../hooks/useVisibilityControl');

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseAutomationListContext = useAutomationListContext as jest.MockedFunction<
  typeof useAutomationListContext
>;

// Mock the useFuncFormatDate hook
jest.mock('../../../Common', () => ({
  useFuncFormatDate: () => jest.fn(() => '2023-01-01'),
}));

// Mock the ViewOnly component
jest.mock('../../../../components/ViewOnly', () => ({
  __esModule: true,
  default: () => <div data-testid='view-only'>View Only</div>,
}));

// Mock the BottomUpTween component
jest.mock('@resola-ai/ui', () => ({
  BottomUpTween: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='bottom-up-tween'>{children}</div>
  ),
}));

const customTheme = {
  colors: {
    navy: [
      '#001F3F',
      '#003366',
      '#004080',
      '#00509E',
      '#0066CC',
      '#3385FF',
      '#66A3FF',
      '#99C2FF',
      '#CCE0FF',
      '#E6F0FF',
    ],
    // ...other colors if needed
  },
};

describe('AutomationListScreen', () => {
  const mockToggleActiveState = jest.fn();
  const mockIntendToDeleteAutomation = jest.fn();
  const mockHandleCloseConfirmDeleteModal = jest.fn();
  const mockHandleDeleteAutomation = jest.fn();

  const mockAutomationData: AutomationData[] = [
    {
      id: '1',
      name: 'Test Automation 1',
      status: 'active',
      trigger: 'conversation.created',
      flow: '{}',
      created: '2023-01-01T00:00:00Z',
      desc: 'When new message comes',
      owner: { name: 'User 1', id: 'user1' },
      updated: '2023-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Test Automation 2',
      status: 'inactive',
      trigger: 'conversation.closed',
      flow: '{}',
      created: '2023-01-02T00:00:00Z',
      desc: 'When chat is closed',
      owner: { name: 'User 2', id: 'user2' },
      updated: '2023-01-02T00:00:00Z',
    },
  ];

  const mockContextValue = {
    t: jest.fn((key) => key),
    sortedData: mockAutomationData,
    toggleActiveState: mockToggleActiveState,
    isLoading: false,
    isManager: true,
    isLoadingProfile: false,
    data: mockAutomationData,
    showConfirmDeleteModal: false,
    intendToDeleteAutomation: mockIntendToDeleteAutomation,
    reloadAutomationList: jest.fn(),
    handleCloseConfirmDeleteModal: mockHandleCloseConfirmDeleteModal,
    handleDeleteAutomation: mockHandleDeleteAutomation,
  };

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <MantineProvider theme={customTheme as any}>
        <MantineEmotionProvider>{component}</MantineEmotionProvider>
      </MantineProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAutomationListContext.mockReturnValue(mockContextValue);
  });

  it('renders the automation list screen with title and description', () => {
    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByText('automationList.title')).toBeInTheDocument();
    expect(screen.getByText('automationList.description')).toBeInTheDocument();
  });

  it('renders loading state when isLoading is true', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isLoading: true,
    });

    renderWithProviders(<AutomationListScreen />);

    expect(document.querySelector('.mantine-Loader-root')).toBeInTheDocument();
  });

  it('renders automation items when data is available', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      sortedData: mockAutomationData,
    });

    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByText('Test Automation 1')).toBeInTheDocument();
    expect(screen.getByText('Test Automation 2')).toBeInTheDocument();
    expect(screen.getByText('condition.whenNewMessageCome')).toBeInTheDocument();
    expect(screen.getByText('condition.whenChatIsClosed')).toBeInTheDocument();
  });

  it('renders create automation button when data is available', () => {
    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByText('action.create')).toBeInTheDocument();
  });

  it('renders empty state when no automation data', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      sortedData: [],
    });

    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByText('no_automation')).toBeInTheDocument();
    expect(screen.getByText('action.create')).toBeInTheDocument();
  });

  it('handles automation item click to navigate to detail', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      sortedData: mockAutomationData,
    });

    renderWithProviders(<AutomationListScreen />);

    const automationTween = screen
      .getByText('Test Automation 1')
      .closest('[data-testid="bottom-up-tween"]');
    expect(automationTween).toBeInTheDocument();

    // Find the Card inside the tween and click it
    const card = automationTween?.querySelector('.mantine-Card-root');
    expect(card).toBeInTheDocument();
    fireEvent.click(card!);

    expect(mockPush).toHaveBeenCalledWith('/automation/edit/1');
  });

  it('handles automation status toggle', async () => {
    renderWithProviders(<AutomationListScreen />);

    const switchElement = screen.getAllByRole('switch')[0];
    fireEvent.click(switchElement);

    await waitFor(() => {
      expect(mockToggleActiveState).toHaveBeenCalled();
    });
  });

  it('handles delete automation button click', () => {
    renderWithProviders(<AutomationListScreen />);

    const deleteButtons = screen
      .getAllByRole('button')
      .filter((btn) => btn.className.includes('action-icon-delete'));
    fireEvent.click(deleteButtons[0]);

    expect(mockIntendToDeleteAutomation).toHaveBeenCalledWith('1');
  });

  it('renders view only badge for non-managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: false,
    });

    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByTestId('view-only')).toBeInTheDocument();
  });

  it('does not render view only badge for managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: true,
    });

    renderWithProviders(<AutomationListScreen />);

    expect(screen.queryByTestId('view-only')).not.toBeInTheDocument();
  });

  it('disables create button for non-managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: false,
    });

    renderWithProviders(<AutomationListScreen />);

    const createButton = screen.getByText('action.create').closest('button');
    expect(createButton).toBeDisabled();
  });

  it('enables create button for managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: true,
    });

    renderWithProviders(<AutomationListScreen />);

    const createButton = screen.getByText('action.create').closest('button');
    expect(createButton).not.toBeDisabled();
  });

  it('navigates to create page when create button is clicked by manager', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      sortedData: mockAutomationData,
      isManager: true,
    });

    renderWithProviders(<AutomationListScreen />);

    const createButton = screen.getByRole('button', { name: /action\.create/i });
    expect(createButton).toBeInTheDocument();

    fireEvent.click(createButton);

    expect(mockPush).toHaveBeenCalledWith('/automation/create');
  });

  it('renders confirm delete modal when showConfirmDeleteModal is true', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      showConfirmDeleteModal: true,
    });

    renderWithProviders(<AutomationListScreen />);

    expect(screen.getByText('delete_confirm_message')).toBeInTheDocument();
    expect(screen.getByText('delete_button')).toBeInTheDocument();
    expect(screen.getByText('cancel_button')).toBeInTheDocument();
  });

  it('handles confirm delete modal close', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      showConfirmDeleteModal: true,
    });

    renderWithProviders(<AutomationListScreen />);

    const cancelButton = screen.getByText('cancel_button');
    fireEvent.click(cancelButton);

    expect(mockHandleCloseConfirmDeleteModal).toHaveBeenCalled();
  });

  it('handles confirm delete modal delete action', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      showConfirmDeleteModal: true,
    });

    renderWithProviders(<AutomationListScreen />);

    const deleteButton = screen.getByText('delete_button');
    fireEvent.click(deleteButton);

    expect(mockHandleDeleteAutomation).toHaveBeenCalled();
  });

  it('disables delete button for non-managers in modal', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      showConfirmDeleteModal: true,
      isManager: false,
    });

    renderWithProviders(<AutomationListScreen />);

    const deleteButton = screen.getByText('delete_button').closest('button');
    expect(deleteButton).toBeDisabled();
  });

  it('disables automation switches for non-managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: false,
    });

    renderWithProviders(<AutomationListScreen />);

    const switches = screen.getAllByRole('switch');
    switches.forEach((switchElement) => {
      expect(switchElement).toBeDisabled();
    });
  });

  it('disables delete buttons for non-managers', () => {
    mockUseAutomationListContext.mockReturnValue({
      ...mockContextValue,
      isManager: false,
    });

    renderWithProviders(<AutomationListScreen />);

    const deleteButtons = screen
      .getAllByRole('button')
      .filter((btn) => btn.className.includes('action-icon-delete'));
    deleteButtons.forEach((button) => {
      expect(button).toBeDisabled();
    });
  });
});
