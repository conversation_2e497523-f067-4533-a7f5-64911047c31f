import { useDebouncedValue } from '@mantine/hooks';
import { act, renderHook } from '@testing-library/react';
import { useTranslate } from '@tolgee/react';
import useSWR from 'swr';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import { AutomationData } from '../../../../../models/automation';
import ApiService from '../../../../../services/api';
import { useUserContext } from '../../../../userContext';
import { useAutomationList, useAutomationListData } from '../useAutomationList';

// Mock all dependencies
jest.mock('swr');
jest.mock('../../../../../services/api');
jest.mock('../../../../../hooks/useVisibilityControl');
jest.mock('../../../../userContext');
jest.mock('@mantine/hooks');
jest.mock('@tolgee/react');

const mockUseSWR = useSWR as jest.MockedFunction<typeof useSWR>;
const mockApiService = ApiService as jest.Mocked<typeof ApiService>;
const mockUseVisibilityControl = useVisibilityControl as jest.MockedFunction<
  typeof useVisibilityControl
>;
const mockUseUserContext = useUserContext as jest.MockedFunction<typeof useUserContext>;
const mockUseDebouncedValue = useDebouncedValue as jest.MockedFunction<typeof useDebouncedValue>;
const mockUseTranslate = useTranslate as jest.MockedFunction<typeof useTranslate>;

describe('useAutomationList', () => {
  const mockAutomationData: AutomationData[] = [
    {
      id: '1',
      name: 'Automation 1',
      status: 'active',
      trigger: 'conversation.created',
      flow: '{}',
      created: '2023-01-01T00:00:00Z',
      desc: 'Test automation 1',
      owner: { name: 'User 1', id: 'user1' },
      updated: '2023-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Automation 2',
      status: 'inactive',
      trigger: 'conversation.closed',
      flow: '{}',
      created: '2023-01-02T00:00:00Z',
      desc: 'Test automation 2',
      owner: { name: 'User 2', id: 'user2' },
      updated: '2023-01-02T00:00:00Z',
    },
  ];

  const mockSWRData = {
    data: mockAutomationData,
    isLoading: false,
    error: null,
    mutate: jest.fn(),
    isValidating: false,
  };

  const mockVisibilityControl = {
    visible: false,
    toggle: jest.fn(),
    open: jest.fn(),
    close: jest.fn(),
  };

  const mockUserContext = {
    isManager: true,
    isLoadingProfile: false,
  } as any; // Use any to avoid complex type issues

  const mockTranslate = {
    t: jest.fn((key) => key),
    isLoading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseSWR.mockReturnValue(mockSWRData);
    mockUseVisibilityControl.mockReturnValue(mockVisibilityControl);
    mockUseUserContext.mockReturnValue(mockUserContext);
    mockUseDebouncedValue.mockReturnValue([false, jest.fn()]);
    mockUseTranslate.mockReturnValue(mockTranslate);
  });

  describe('useAutomationListData', () => {
    it('should return automation list data with correct structure', () => {
      const { result } = renderHook(() => useAutomationListData());

      expect(result.current.data).toEqual(mockAutomationData);
      expect(result.current.dataCopied).toEqual(mockAutomationData);
      expect(result.current.count).toBe(2);
      expect(result.current.isLoadingDebounce).toBe(false);
      expect(result.current.error).toBeNull();
      expect(typeof result.current.deleteItem).toBe('function');
      expect(typeof result.current.reload).toBe('function');
    });

    it('should handle empty data', () => {
      mockUseSWR.mockReturnValue({
        ...mockSWRData,
        data: null,
      });

      const { result } = renderHook(() => useAutomationListData());

      expect(result.current.data).toBeNull();
      expect(result.current.dataCopied).toEqual([]);
      expect(result.current.count).toBe(0);
    });

    it('should delete item and update local state', async () => {
      const mockMutate = jest.fn();
      mockUseSWR.mockReturnValue({
        ...mockSWRData,
        mutate: mockMutate,
      });

      const { result } = renderHook(() => useAutomationListData());

      await act(async () => {
        await result.current.deleteItem('1');
      });

      expect(mockApiService.deleteAutomation).toHaveBeenCalledWith('1');
      expect(mockMutate).toHaveBeenCalled();
    });

    it('should not delete item if not found', async () => {
      const { result } = renderHook(() => useAutomationListData());

      await act(async () => {
        await result.current.deleteItem('non-existent');
      });

      expect(mockApiService.deleteAutomation).not.toHaveBeenCalled();
    });
  });

  describe('useAutomationList', () => {
    it('should return sorted data and all required functions', () => {
      const { result } = renderHook(() => useAutomationList());

      expect(result.current.sortedData).toEqual([
        mockAutomationData[1], // More recent
        mockAutomationData[0], // Less recent
      ]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isManager).toBe(true);
      expect(result.current.isLoadingProfile).toBe(false);
      expect(result.current.showConfirmDeleteModal).toBe(false);
      expect(typeof result.current.toggleActiveState).toBe('function');
      expect(typeof result.current.handleDeleteAutomation).toBe('function');
      expect(typeof result.current.intendToDeleteAutomation).toBe('function');
      expect(typeof result.current.reloadAutomationList).toBe('function');
      expect(typeof result.current.handleCloseConfirmDeleteModal).toBe('function');
    });

    it('should handle toggle active state', async () => {
      const { result } = renderHook(() => useAutomationList());

      await act(async () => {
        await result.current.toggleActiveState('1', mockAutomationData[0]);
      });

      expect(mockApiService.putAutomationData).toHaveBeenCalledWith('1', mockAutomationData[0]);
    });

    it('should handle intend to delete automation', () => {
      const { result } = renderHook(() => useAutomationList());

      act(() => {
        result.current.intendToDeleteAutomation('1');
      });

      expect(mockVisibilityControl.toggle).toHaveBeenCalled();
    });

    it('should handle close confirm delete modal', () => {
      const { result } = renderHook(() => useAutomationList());

      act(() => {
        result.current.handleCloseConfirmDeleteModal();
      });

      expect(mockVisibilityControl.toggle).toHaveBeenCalled();
    });

    it('should handle delete automation', async () => {
      const { result } = renderHook(() => useAutomationList());

      // Set up the modal to be open and ensure the item is present in dataCopied
      mockUseVisibilityControl.mockReturnValue({
        ...mockVisibilityControl,
        visible: true,
      });

      // Call intendToDeleteAutomation to set the id
      act(() => {
        result.current.intendToDeleteAutomation('1');
      });

      await act(async () => {
        result.current.handleDeleteAutomation();
      });

      expect(mockApiService.deleteAutomation).toHaveBeenCalledWith('1');
    });

    it('should reload data on mount', () => {
      const mockMutate = jest.fn();
      mockUseSWR.mockReturnValue({
        ...mockSWRData,
        mutate: mockMutate,
      });

      renderHook(() => useAutomationList());

      expect(mockMutate).toHaveBeenCalled();
    });

    it('should sort data by creation date (latest first)', () => {
      const unsortedData: AutomationData[] = [
        {
          id: '1',
          name: 'Old Automation',
          status: 'active',
          trigger: 'conversation.created',
          flow: '{}',
          created: '2023-01-01T00:00:00Z',
          desc: 'Old automation',
          owner: { name: 'User 1', id: 'user1' },
          updated: '2023-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'New Automation',
          status: 'inactive',
          trigger: 'conversation.closed',
          flow: '{}',
          created: '2023-01-03T00:00:00Z',
          desc: 'New automation',
          owner: { name: 'User 2', id: 'user2' },
          updated: '2023-01-03T00:00:00Z',
        },
        {
          id: '3',
          name: 'Middle Automation',
          status: 'active',
          trigger: 'conversation.message.received',
          flow: '{}',
          created: '2023-01-02T00:00:00Z',
          desc: 'Middle automation',
          owner: { name: 'User 3', id: 'user3' },
          updated: '2023-01-02T00:00:00Z',
        },
      ];

      mockUseSWR.mockReturnValue({
        ...mockSWRData,
        data: unsortedData,
      });

      const { result } = renderHook(() => useAutomationList());

      expect(result.current.sortedData[0].id).toBe('2'); // Newest
      expect(result.current.sortedData[1].id).toBe('3'); // Middle
      expect(result.current.sortedData[2].id).toBe('1'); // Oldest
    });
  });
});
