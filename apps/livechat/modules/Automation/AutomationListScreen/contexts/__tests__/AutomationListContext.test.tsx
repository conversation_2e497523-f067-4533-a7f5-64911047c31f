import { render, screen } from '@testing-library/react';
import React from 'react';
import { AutomationListContextProvider, useAutomationListContext } from '../AutomationListContext';
import { useAutomationList } from '../useAutomationList';

// Mock the useAutomationList hook
jest.mock('../useAutomationList');
const mockUseAutomationList = useAutomationList as jest.MockedFunction<typeof useAutomationList>;

describe('AutomationListContext', () => {
  const mockAutomationListData = {
    t: jest.fn((key) => key),
    isLoading: false,
    sortedData: [],
    isManager: true,
    isLoadingProfile: false,
    data: [],
    showConfirmDeleteModal: false,
    toggleActiveState: jest.fn(),
    handleDeleteAutomation: jest.fn(),
    intendToDeleteAutomation: jest.fn(),
    reloadAutomationList: jest.fn(),
    handleCloseConfirmDeleteModal: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAutomationList.mockReturnValue(mockAutomationListData);
  });

  describe('AutomationListContextProvider', () => {
    it('renders children and provides context value', () => {
      const TestComponent = () => {
        const context = useAutomationListContext();
        return <div data-testid='test-component'>{context.isManager ? 'Manager' : 'User'}</div>;
      };
      render(
        <AutomationListContextProvider>
          <TestComponent />
        </AutomationListContextProvider>
      );
      expect(screen.getByTestId('test-component').textContent).toBe('Manager');
    });

    it('calls useAutomationList hook', () => {
      const TestComponent = () => <div>Test</div>;

      render(
        <AutomationListContextProvider>
          <TestComponent />
        </AutomationListContextProvider>
      );

      expect(mockUseAutomationList).toHaveBeenCalled();
    });
  });

  describe('useAutomationListContext', () => {
    it('returns context value when used within provider', () => {
      const TestComponent = () => {
        const context = useAutomationListContext();
        return <div data-testid='context-value'>{context.isManager ? 'Manager' : 'User'}</div>;
      };
      render(
        <AutomationListContextProvider>
          <TestComponent />
        </AutomationListContextProvider>
      );
      expect(screen.getByTestId('context-value').textContent).toBe('Manager');
    });

    it('throws error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const TestComponent = () => {
        useAutomationListContext();
        return <div>Should not render</div>;
      };

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAutomationListContext should be used in AutomationListContextProvider');

      consoleSpy.mockRestore();
    });
  });
});
