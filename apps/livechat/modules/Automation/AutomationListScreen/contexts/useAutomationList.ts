import { useDebouncedValue } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import useVisibilityControl from '../../../../hooks/useVisibilityControl';
import { AutomationData, AutomationList } from '../../../../models/automation';
import ApiService from '../../../../services/api';
import { useUserContext } from '../../../userContext';

function compareTimeCreatedLatest(autoA, autoB) {
  const date1 = new Date(autoA?.created || '');
  const date2 = new Date(autoB?.created || '');
  if (date1 < date2) {
    return 1;
  } else if (date1 > date2) {
    return -1;
  } else {
    return 0;
  }
}

export function useAutomationListData() {
  const [dataCopied, setDataCopied] = useState([]);
  const {
    data = null,
    isLoading,
    error,
    mutate,
  } = useSWR<AutomationList>('get-automation-list', () => ApiService.getAutomationList(), {
    revalidateOnMount: true,
  });
  const [isLoadingDebounce] = useDebouncedValue(isLoading, 100);

  useEffect(() => {
    if (data?.length) {
      setDataCopied([...data]);
    }
  }, [data]);

  const deleteItem = useCallback(
    async (id: string) => {
      if (!dataCopied.length) return;
      if (dataCopied.findIndex((item) => item.id === id) <= -1) return;

      const index = dataCopied.findIndex((item) => item.id === id);
      const newData = [...dataCopied];
      newData.splice(index, 1);
      setDataCopied(newData);

      await ApiService.deleteAutomation(id);
      mutate();
    },
    [dataCopied, mutate]
  );

  return useMemo(
    () => ({
      data,
      dataCopied,
      deleteItem,
      count: dataCopied.length || 0,
      isLoadingDebounce,
      error,
      reload: mutate,
    }),
    [data, dataCopied, deleteItem, error, mutate, isLoadingDebounce]
  );
}

export function useAutomationList() {
  const { t } = useTranslate('workspace');
  const { data, dataCopied, deleteItem, reload, isLoadingDebounce } = useAutomationListData();
  const { isManager, isLoadingProfile } = useUserContext();
  const [intendToDeleteAutomationId, setIntendToDeleteAutomationId] = useState<string>(null);
  const { visible, toggle } = useVisibilityControl();

  const toggleActiveState = useCallback(
    async (id: string, automation: AutomationData) => {
      await ApiService.putAutomationData(id, automation);
      reload();
    },
    [reload]
  );
  const intendToDeleteAutomation = useCallback(
    (id: string) => {
      setIntendToDeleteAutomationId(id);
      toggle();
    },
    [toggle]
  );

  const handleCloseConfirmDeleteAutomationModal = useCallback(() => {
    toggle();
    setIntendToDeleteAutomationId(null);
  }, [toggle]);

  const handleDeleteAutomation = useCallback(() => {
    if (!intendToDeleteAutomationId) return;
    handleCloseConfirmDeleteAutomationModal();
    deleteItem(intendToDeleteAutomationId);
  }, [deleteItem, handleCloseConfirmDeleteAutomationModal, intendToDeleteAutomationId]);

  useEffect(() => {
    reload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return useMemo(() => {
    const sortedData = dataCopied.sort((a, b) => compareTimeCreatedLatest(a, b));
    return {
      t,
      isLoading: isLoadingDebounce,
      sortedData,
      isManager,
      isLoadingProfile,
      data: data ?? [],
      showConfirmDeleteModal: visible,
      toggleActiveState,
      handleDeleteAutomation,
      intendToDeleteAutomation,
      reloadAutomationList: reload,
      handleCloseConfirmDeleteModal: handleCloseConfirmDeleteAutomationModal,
    };
  }, [
    t,
    visible,
    isLoadingDebounce,
    dataCopied,
    data,
    isManager,
    isLoadingProfile,
    reload,
    toggleActiveState,
    handleDeleteAutomation,
    intendToDeleteAutomation,
    handleCloseConfirmDeleteAutomationModal,
  ]);
}
