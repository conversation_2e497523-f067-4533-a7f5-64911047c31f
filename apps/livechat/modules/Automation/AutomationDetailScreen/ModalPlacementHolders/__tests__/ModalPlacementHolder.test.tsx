import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { ModalActivityType } from '../../types';
import { ModalPlacementHolder } from '../ModalPlacementHolder';

// Mock the context
const mockContextValue = {
  modalActivities: {
    visible: true,
    open: jest.fn(),
    close: jest.fn(),
  },
  currentModalType: ModalActivityType.TriggerActivityModal,
};

jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => mockContextValue,
}));

// Mock the modal components
jest.mock('../ModalTriggerNodeActivity', () => ({
  ModalTriggerNodeActivity: () => <div data-testid='trigger-modal'>Trigger Modal</div>,
}));

jest.mock('../ModalConditionNodeActivity', () => ({
  __esModule: true,
  default: () => <div data-testid='condition-modal'>Condition Modal</div>,
}));

jest.mock('../ModalActionNodeActivity', () => ({
  __esModule: true,
  default: () => <div data-testid='action-modal'>Action Modal</div>,
}));

// Create a test wrapper with Mantine providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

describe('ModalPlacementHolder', () => {
  const defaultProps = {
    parentDimension: {
      width: 1200,
      height: 800,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders trigger modal when currentModalType is TriggerActivityModal', () => {
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    expect(screen.getByTestId('trigger-modal')).toBeInTheDocument();
    expect(screen.queryByTestId('condition-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-modal')).not.toBeInTheDocument();
  });

  it('renders condition modal when currentModalType is ConditionActivityModal', () => {
    mockContextValue.currentModalType = ModalActivityType.ConditionActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    expect(screen.getByTestId('condition-modal')).toBeInTheDocument();
    expect(screen.queryByTestId('trigger-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-modal')).not.toBeInTheDocument();
  });

  it('renders action modal when currentModalType is ActionActivityModal', () => {
    mockContextValue.currentModalType = ModalActivityType.ActionActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    expect(screen.getByTestId('action-modal')).toBeInTheDocument();
    expect(screen.queryByTestId('trigger-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('condition-modal')).not.toBeInTheDocument();
  });

  it('does not render any modal when modalActivities.visible is false', () => {
    mockContextValue.modalActivities.visible = false;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    expect(screen.queryByTestId('trigger-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('condition-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-modal')).not.toBeInTheDocument();
  });

  it('renders modal when modalActivities.visible is true', () => {
    mockContextValue.modalActivities.visible = true;
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    expect(screen.getByTestId('trigger-modal')).toBeInTheDocument();
  });

  it('applies correct styling classes to the container', () => {
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    const container = screen.getByTestId('trigger-modal').closest('.mantine-Container-root');
    expect(container).toBeInTheDocument();
  });

  it('calculates height correctly based on parent dimensions', () => {
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    const container = screen.getByTestId('trigger-modal').closest('.mantine-Container-root');
    expect(container).toHaveStyle({ height: '760px' }); // 800 - 40 = 760
  });

  it('applies correct positioning styles', () => {
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    const container = screen.getByTestId('trigger-modal').closest('.mantine-Container-root');
    expect(container).toHaveStyle({
      position: 'absolute',
      top: '20px',
      left: '20px',
    });
  });

  it('handles different parent dimensions correctly', () => {
    const customProps = {
      parentDimension: {
        width: 1600,
        height: 1000,
      },
    };

    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...customProps} />);

    const container = screen.getByTestId('trigger-modal').closest('.mantine-Container-root');
    expect(container).toHaveStyle({ height: '960px' }); // 1000 - 40 = 960
  });

  it('renders with transition animation', () => {
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    // The component should be wrapped in a Transition component
    const transitionContainer = screen.getByTestId('trigger-modal').closest('[style*="transform"]');
    expect(transitionContainer).toBeInTheDocument();
  });

  it('handles null currentModalType gracefully', () => {
    mockContextValue.currentModalType = null;
    renderWithProviders(<ModalPlacementHolder {...defaultProps} />);

    // Should not render any modal
    expect(screen.queryByTestId('trigger-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('condition-modal')).not.toBeInTheDocument();
    expect(screen.queryByTestId('action-modal')).not.toBeInTheDocument();
  });

  it('maintains modal state when switching between modal types', () => {
    // Start with trigger modal
    mockContextValue.currentModalType = ModalActivityType.TriggerActivityModal;
    const { rerender } = renderWithProviders(<ModalPlacementHolder {...defaultProps} />);
    expect(screen.getByTestId('trigger-modal')).toBeInTheDocument();

    // Switch to condition modal
    mockContextValue.currentModalType = ModalActivityType.ConditionActivityModal;
    rerender(<ModalPlacementHolder {...defaultProps} />);
    expect(screen.getByTestId('condition-modal')).toBeInTheDocument();

    // Switch to action modal
    mockContextValue.currentModalType = ModalActivityType.ActionActivityModal;
    rerender(<ModalPlacementHolder {...defaultProps} />);
    expect(screen.getByTestId('action-modal')).toBeInTheDocument();
  });
});
