import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { ConditionType, FACTS, PATHS } from '../../types';
import ModalConditionNodeActivity from '../ModalConditionNodeActivity';

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock the translation hook
const mockT = jest.fn((key: string) => {
  const translations: Record<string, string> = {
    'detail.trigger.cancel': 'Cancel',
    'detail.trigger.save': 'Save',
    'detail.condition.title': 'Condition Settings',
    'detail.condition.add.more': 'Add More Conditions',
    'condition_type.and': 'All conditions must be met',
    'condition_type.or': 'Any condition can be met',
  };
  return translations[key] || key;
});

// Mock the context
const mockContextValue = {
  t: mockT,
  teamList: [
    { id: 'team1', name: 'Team 1' },
    { id: 'team2', name: 'Team 2' },
  ],
  isManager: true,
  listOperators: [
    { id: 'op1', name: 'Operator 1', teamId: 'team1' },
    { id: 'op2', name: 'Operator 2', teamId: 'team1' },
  ],
  crmFieldsByObject: [
    { value: 'field1', label: 'Field 1' },
    { value: 'field2', label: 'Field 2' },
  ],
  closeActivityModal: jest.fn(),
  setConditionNodeData: jest.fn(),
  currentEditDataCondition: {
    id: 'condition1',
    type: 'all' as ConditionType,
    conditions: [],
  },
};

// Mock the visibility control hook
const mockVisibilityControl = {
  visible: false,
  open: jest.fn(),
  close: jest.fn(),
  toggle: jest.fn(),
};

jest.mock('@tolgee/react', () => ({
  ...jest.requireActual('@tolgee/react'),
  useTranslate: () => ({ t: mockT }),
}));

jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => mockContextValue,
}));

// Mock the condition settings form component
jest.mock('../ModalConditionComponents/ConditionSettingForm', () => ({
  __esModule: true,
  default: ({ index, onChange, onRemove }: any) => (
    <div data-testid={`condition-form-${index}`}>
      <button onClick={() => onChange('fact', 'END_USER')}>Change Fact</button>
      <button onClick={() => onChange('path', 'CURRENT')}>Change Path</button>
      <button onClick={() => onChange('value', 'test value')}>Change Value</button>
      <button onClick={onRemove}>Remove</button>
    </div>
  ),
}));

// Mock the panels
jest.mock('../Panels/SelectAssignOperatorPanel', () => ({
  SelectAssignedOperatorPanel: () => <div data-testid='operator-panel'>Operator Panel</div>,
}));

jest.mock('../Panels/SelectAssignTeamPanel', () => ({
  __esModule: true,
  default: () => <div data-testid='team-panel'>Team Panel</div>,
}));

// Create a test wrapper with Mantine providers
const testTheme = {
  ...themeConfigurations,
  colors: {
    ...themeConfigurations.colors,
    navy: themeConfigurations.colors.decaNavy,
  },
};

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={testTheme}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

describe('ModalConditionNodeActivity', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the condition modal with title', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(screen.getByText('Condition Settings')).toBeInTheDocument();
  });

  it('renders action buttons', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('renders at least one condition form by default', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(screen.getByTestId('condition-form-0')).toBeInTheDocument();
  });

  it('renders condition type radio buttons', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(screen.getByText('All conditions must be met')).toBeInTheDocument();
    expect(screen.getByText('Any condition can be met')).toBeInTheDocument();
  });

  it('renders add more conditions button', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(screen.getByText('Add More Conditions')).toBeInTheDocument();
  });

  it('calls closeActivityModal when cancel button is clicked', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('calls setConditionNodeData and closeActivityModal when save button is clicked', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(mockContextValue.setConditionNodeData).toHaveBeenCalledTimes(1);
    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('disables save button when user is not manager', () => {
    mockContextValue.isManager = false;
    renderWithProviders(<ModalConditionNodeActivity />);

    const saveButton = screen.getByRole('button', { name: 'Save' });
    expect(saveButton).toBeDisabled();
  });

  it('enables save button when user is manager', () => {
    mockContextValue.isManager = true;
    renderWithProviders(<ModalConditionNodeActivity />);

    const saveButton = screen.getByRole('button', { name: 'Save' });
    expect(saveButton).not.toBeDisabled();
  });

  it('adds new condition when add more button is clicked', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    const addButton = screen.getByText('Add More Conditions');
    fireEvent.click(addButton);

    // Should have two condition forms now
    expect(screen.getByTestId('condition-form-0')).toBeInTheDocument();
    expect(screen.getByTestId('condition-form-1')).toBeInTheDocument();
  });

  it('removes condition when remove button is clicked', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    // Add a condition first
    const addButton = screen.getByText('Add More Conditions');
    fireEvent.click(addButton);

    // Remove the first condition
    const removeButton = screen.getByTestId('condition-form-0').querySelector('button:last-child');
    if (removeButton) {
      fireEvent.click(removeButton);
    }

    // Should only have one condition form left
    expect(screen.getByTestId('condition-form-0')).toBeInTheDocument();
    expect(screen.queryByTestId('condition-form-1')).not.toBeInTheDocument();
  });

  it('initializes with existing condition data', () => {
    mockContextValue.currentEditDataCondition = {
      id: 'condition1',
      type: 'any' as ConditionType,
      conditions: [
        {
          fact: FACTS.END_USER,
          path: PATHS.CURRENT,
          value: 'test',
        },
      ],
    };

    renderWithProviders(<ModalConditionNodeActivity />);

    // Should render the existing condition
    expect(screen.getByTestId('condition-form-0')).toBeInTheDocument();
  });

  it('translates all text correctly', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    expect(mockT).toHaveBeenCalledWith('detail.trigger.cancel');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.save');
    expect(mockT).toHaveBeenCalledWith('detail.condition.title');
    expect(mockT).toHaveBeenCalledWith('detail.condition.add.more');
    expect(mockT).toHaveBeenCalledWith('condition_type.and');
    expect(mockT).toHaveBeenCalledWith('condition_type.or');
  });

  it('handles empty condition data gracefully', () => {
    mockContextValue.currentEditDataCondition = {
      id: 'condition1',
      type: 'all' as const,
      conditions: [],
    };

    renderWithProviders(<ModalConditionNodeActivity />);

    // Should still render at least one condition form
    expect(screen.getByTestId('condition-form-0')).toBeInTheDocument();
  });

  it('applies correct navy color styling to the add more conditions icon', () => {
    renderWithProviders(<ModalConditionNodeActivity />);

    // Find the IconPlus icon within the "Add More Conditions" button
    const addButton = screen.getByText('Add More Conditions');
    const iconElement = addButton.querySelector('svg');

    // The icon should be present
    expect(iconElement).toBeInTheDocument();

    // Get the expected color from the test theme
    const expectedColor = testTheme.colors.navy[0];
    // Get the computed style of the icon
    const color = window.getComputedStyle(iconElement!).color;
    // Convert hex to rgb for comparison
    function hexToRgb(hex: string) {
      const h = hex.replace('#', '');
      const bigint = parseInt(h, 16);
      const r = (bigint >> 16) & 255;
      const g = (bigint >> 8) & 255;
      const b = bigint & 255;
      return `rgb(${r}, ${g}, ${b})`;
    }
    expect(color).toBe(hexToRgb(expectedColor));
  });
});
