import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { TriggerType } from '../../types';
import { ModalTriggerNodeActivity } from '../ModalTriggerNodeActivity';

// Mock the translation hook
const mockT = jest.fn((key: string) => {
  const translations: Record<string, string> = {
    'detail.trigger.cancel': 'Cancel',
    'detail.trigger.save': 'Save',
    'detail.trigger.title': 'Trigger Settings',
    'detail.trigger.description': 'Select when this automation should trigger',
    'condition.whenNewMessageCome': 'When new message comes',
    'condition.whenOperatorAssigned': 'When operator is assigned',
    'condition.whenChatIsClosed': 'When chat is closed',
  };
  return translations[key] || key;
});

// Mock the context
const mockContextValue = {
  t: mockT,
  closeActivityModal: jest.fn(),
  triggerNodeData: null,
  isManager: true,
  setTriggerNodeData: jest.fn(),
};

jest.mock('@tolgee/react', () => ({
  ...jest.requireActual('@tolgee/react'),
  useTranslate: () => ({ t: mockT }),
}));

jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => mockContextValue,
}));

// Create a test wrapper with Mantine providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

describe('ModalTriggerNodeActivity', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the trigger modal with title and description', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    expect(screen.getByText('Trigger Settings')).toBeInTheDocument();
    expect(screen.getByText('Select when this automation should trigger')).toBeInTheDocument();
  });

  it('renders all trigger option buttons', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    expect(screen.getByText('When new message comes')).toBeInTheDocument();
    expect(screen.getByText('When operator is assigned')).toBeInTheDocument();
    expect(screen.getByText('When chat is closed')).toBeInTheDocument();
  });

  it('renders cancel button but not save button when no trigger is selected', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.queryByText('Save')).not.toBeInTheDocument();
  });

  it('renders both cancel and save buttons when trigger is selected', () => {
    // First select a trigger
    renderWithProviders(<ModalTriggerNodeActivity />);

    const newMessageButton = screen.getByText('When new message comes').closest('button');
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }

    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('calls closeActivityModal when cancel button is clicked', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    const cancelButton = screen.getByText('Cancel').closest('button');
    if (cancelButton) {
      fireEvent.click(cancelButton);
    }

    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('calls setTriggerNodeData and closeActivityModal when save button is clicked', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    // First select a trigger type
    const newMessageButton = screen.getByText('When new message comes').closest('button');
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }

    // Then click save
    const saveButton = screen.getByText('Save').closest('button');
    if (saveButton) {
      fireEvent.click(saveButton);
    }

    expect(mockContextValue.setTriggerNodeData).toHaveBeenCalledWith(TriggerType.NewConversation);
    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('disables save button when no trigger is selected', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    // Save button should not be rendered when no trigger is selected
    expect(screen.queryByText('Save')).not.toBeInTheDocument();
  });

  it('enables save button when a trigger is selected', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    const newMessageButton = screen.getByText('When new message comes').closest('button');
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }

    const saveButton = screen.getByText('Save').closest('button');
    expect(saveButton).not.toBeDisabled();
  });

  it('disables save button when user is not manager', () => {
    mockContextValue.isManager = false;

    renderWithProviders(<ModalTriggerNodeActivity />);

    // Select a trigger
    const newMessageButton = screen.getByText('When new message comes').closest('button');
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }

    const saveButton = screen.getByText('Save').closest('button');
    expect(saveButton).toBeDisabled();
  });

  it('highlights selected trigger button with outline variant', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    const newMessageButton = screen.getByText('When new message comes').closest('button');
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }

    // The button should have outline variant when selected
    expect(newMessageButton).toHaveAttribute('data-variant', 'outline');
  });

  it('shows default variant for unselected trigger buttons', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    const newMessageButton = screen.getByText('When new message comes').closest('button');
    const operatorAssignedButton = screen.getByText('When operator is assigned').closest('button');

    // Initially no button should be selected
    expect(newMessageButton).toHaveAttribute('data-variant', 'default');
    expect(operatorAssignedButton).toHaveAttribute('data-variant', 'default');
  });

  it('updates selection when different trigger is clicked', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    const newMessageButton = screen.getByText('When new message comes').closest('button');
    const operatorAssignedButton = screen.getByText('When operator is assigned').closest('button');

    // Click first button
    if (newMessageButton) {
      fireEvent.click(newMessageButton);
    }
    expect(newMessageButton).toHaveAttribute('data-variant', 'outline');

    // Click second button
    if (operatorAssignedButton) {
      fireEvent.click(operatorAssignedButton);
    }
    expect(operatorAssignedButton).toHaveAttribute('data-variant', 'outline');
    expect(newMessageButton).toHaveAttribute('data-variant', 'default');
  });

  it('initializes with existing trigger data', () => {
    mockContextValue.triggerNodeData = TriggerType.CloseConversation;
    renderWithProviders(<ModalTriggerNodeActivity />);

    const closeChatButton = screen.getByText('When chat is closed').closest('button');
    expect(closeChatButton).toHaveAttribute('data-variant', 'outline');
  });

  it('translates all text correctly', () => {
    renderWithProviders(<ModalTriggerNodeActivity />);

    expect(mockT).toHaveBeenCalledWith('detail.trigger.cancel');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.save');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.title');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.description');
    expect(mockT).toHaveBeenCalledWith('condition.whenNewMessageCome');
    expect(mockT).toHaveBeenCalledWith('condition.whenOperatorAssigned');
    expect(mockT).toHaveBeenCalledWith('condition.whenChatIsClosed');
  });
});
