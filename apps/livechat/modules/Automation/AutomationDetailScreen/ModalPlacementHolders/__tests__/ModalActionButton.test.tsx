import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import ActionButtons from '../ModalActionButton';

// Mock the translation hook
const mockT = jest.fn((key: string) => {
  const translations: Record<string, string> = {
    'detail.trigger.cancel': 'Cancel',
    'detail.trigger.save': 'Save',
  };
  return translations[key] || key;
});

jest.mock('@tolgee/react', () => ({
  ...jest.requireActual('@tolgee/react'),
  useTranslate: () => ({ t: mockT }),
}));

// Create a test wrapper with Mantine providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

describe('ActionButtons', () => {
  const defaultProps = {
    isManager: true,
    onSave: jest.fn(),
    onClose: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders both cancel and save buttons', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onSave when save button is clicked', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(defaultProps.onSave).toHaveBeenCalledTimes(1);
  });

  it('disables save button when isManager is false', () => {
    renderWithProviders(<ActionButtons {...defaultProps} isManager={false} />);

    const saveButton = screen.getByText('Save').closest('button');
    expect(saveButton).toBeDisabled();
  });

  it('enables save button when isManager is true', () => {
    renderWithProviders(<ActionButtons {...defaultProps} isManager={true} />);

    const saveButton = screen.getByText('Save').closest('button');
    expect(saveButton).not.toBeDisabled();
  });

  it('renders buttons with correct styling classes', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    const cancelButton = screen.getByText('Cancel').closest('button');
    const saveButton = screen.getByText('Save').closest('button');

    // Check that buttons have the expected Mantine classes
    expect(cancelButton).toHaveClass('mantine-Button-root');
    expect(saveButton).toHaveClass('mantine-Button-root');
  });

  it('renders buttons in a group with correct layout', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    const group = screen.getByText('Cancel').closest('.mantine-Group-root');
    expect(group).toBeInTheDocument();
  });

  it('translates button text correctly', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    expect(mockT).toHaveBeenCalledWith('detail.trigger.cancel');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.save');
  });

  it('handles multiple rapid clicks correctly', () => {
    renderWithProviders(<ActionButtons {...defaultProps} />);

    const saveButton = screen.getByText('Save');
    const cancelButton = screen.getByText('Cancel');

    // Click multiple times rapidly
    fireEvent.click(saveButton);
    fireEvent.click(saveButton);
    fireEvent.click(cancelButton);
    fireEvent.click(cancelButton);

    expect(defaultProps.onSave).toHaveBeenCalledTimes(2);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(2);
  });
});
