import { <PERSON><PERSON>Provider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import {
  ACTION_ASSIGN,
  ACTION_SEND_MESSAGE,
  ACTION_WEBHOOK,
  DEFAULT_OCS_CHANNEL_ID,
  NO_SPECIFIED_OPERATOR_ID,
} from '../../constant';
import { RULES_SELECT_OPERATOR, ScenarioAssignType } from '../../types';
import ModalActionNodeActivity from '../ModalActionNodeActivity';

// Mock the translation hook
const mockT = jest.fn((key: string) => {
  const translations: Record<string, string> = {
    'detail.trigger.cancel': 'Cancel',
    'detail.trigger.save': 'Save',
    'detail.action.title': 'Action Settings',
    'detail.action.select.label': 'Select Action Type',
    'detail.action.select.label.1': 'Assign',
    'detail.action.select.label.2': 'Send Message',
    'action.webhook.option_label': 'Webhook',
  };
  return translations[key] || key;
});

// Mock the context
const mockContextValue = {
  t: mockT,
  teamList: [
    { id: 'team1', name: 'Team 1' },
    { id: 'team2', name: 'Team 2' },
  ],
  isManager: true,
  listOperators: [
    { id: 'op1', name: 'Operator 1', teamId: 'team1' },
    { id: 'op2', name: 'Operator 2', teamId: 'team1' },
  ],
  ocsChannelsOptionsWithDefaultOption: [
    { value: 'channel1', label: 'Channel 1' },
    { value: 'channel2', label: 'Channel 2' },
  ],
  closeActivityModal: jest.fn(),
  setActionNodeData: jest.fn(),
  currentEditDataAction: {
    id: 'action1',
    action: ACTION_ASSIGN,
    payload: {},
  },
};

// Mock the visibility control hook
const mockVisibilityControl = {
  visible: false,
  open: jest.fn(),
  close: jest.fn(),
  toggle: jest.fn(),
};

jest.mock('@tolgee/react', () => ({
  ...jest.requireActual('@tolgee/react'),
  useTranslate: () => ({ t: mockT }),
}));

jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => mockContextValue,
}));

// Mock the form components
jest.mock('../ModalActionComponents/AssignFormSettings', () => ({
  __esModule: true,
  AssignFormSettings: () => <div data-testid='assign-form'>Assign Form</div>,
}));

jest.mock('../ModalActionComponents/SendMessageForm', () => ({
  __esModule: true,
  SendMessageForm: () => <div data-testid='send-message-form'>Send Message Form</div>,
  types: ['day_after', 'hour_after', 'day_hour_after'],
}));

jest.mock('../ModalActionComponents/WebhookOptions', () => ({
  __esModule: true,
  WebhookOptions: () => <div data-testid='webhook-options'>Webhook Options</div>,
}));

// Mock the panels
jest.mock('../Panels/SelectAssignOperatorPanel', () => ({
  SelectAssignedOperatorPanel: () => <div data-testid='operator-panel'>Operator Panel</div>,
}));

jest.mock('../Panels/SelectAssignTeamPanel', () => ({
  __esModule: true,
  default: () => <div data-testid='team-panel'>Team Panel</div>,
}));

// Mock the custom form elements
jest.mock('../CustomFormElements/CustomFormElements', () => ({
  SelectStyled: ({ label, value, onChange, data }: any) => (
    <div data-testid='select-styled'>
      <label>{label}</label>
      <select value={value} onChange={(e) => onChange(e.target.value)}>
        {data.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  ),
  Visible: ({ displayIf, children }: any) => (displayIf ? <div>{children}</div> : null),
}));

// Create a test wrapper with Mantine providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithProviders = (component: React.ReactElement) => {
  return render(component, { wrapper: TestWrapper });
};

describe('ModalActionNodeActivity', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the action modal with title', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByText('Action Settings')).toBeInTheDocument();
  });

  it('renders action buttons', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('renders action type selector', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByText('Select Action Type')).toBeInTheDocument();
    expect(screen.getByTestId('select-styled')).toBeInTheDocument();
  });

  it('renders assign form when assign action is selected', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('assign-form')).toBeInTheDocument();
  });

  it('renders send message form when send message action is selected', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_SEND_MESSAGE,
      payload: {},
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('send-message-form')).toBeInTheDocument();
  });

  it('renders webhook options when webhook action is selected', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_WEBHOOK,
      payload: {},
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('webhook-options')).toBeInTheDocument();
  });

  it('calls closeActivityModal when cancel button is clicked', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('calls setActionNodeData and closeActivityModal when save button is clicked', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(mockContextValue.setActionNodeData).toHaveBeenCalledTimes(1);
    expect(mockContextValue.closeActivityModal).toHaveBeenCalledTimes(1);
  });

  it('disables save button when user is not manager', () => {
    mockContextValue.isManager = false;
    renderWithProviders(<ModalActionNodeActivity />);

    const saveButton = screen.getByRole('button', { name: 'Save' });
    expect(saveButton).toBeDisabled();
  });

  it('enables save button when user is manager', () => {
    mockContextValue.isManager = true;
    renderWithProviders(<ModalActionNodeActivity />);

    const saveButton = screen.getByRole('button', { name: 'Save' });
    expect(saveButton).not.toBeDisabled();
  });

  it('changes action type when selector is used', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    const select = screen.getByTestId('select-styled').querySelector('select');
    if (select) {
      fireEvent.change(select, { target: { value: ACTION_SEND_MESSAGE } });
    }

    // The component should re-render with the new action type
    expect(screen.getByTestId('send-message-form')).toBeInTheDocument();
  });

  it('initializes with existing action data', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_SEND_MESSAGE,
      payload: {
        message: 'Test message',
        ocsChannelId: 'channel1',
      },
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('send-message-form')).toBeInTheDocument();
  });

  it('handles assign action with team scenario', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_ASSIGN,
      payload: {
        scenario: ScenarioAssignType.TEAM,
        data: {
          teamId: 'team1',
          rule: RULES_SELECT_OPERATOR.ROUND_ROBIN,
        },
      },
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('assign-form')).toBeInTheDocument();
  });

  it('handles assign action with team and operator scenario', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_ASSIGN,
      payload: {
        scenario: ScenarioAssignType.TEAM_AND_OPERATOR,
        data: {
          teamId: 'team1',
          operatorId: 'op1',
        },
      },
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('assign-form')).toBeInTheDocument();
  });

  it('translates all text correctly', () => {
    renderWithProviders(<ModalActionNodeActivity />);

    expect(mockT).toHaveBeenCalledWith('detail.trigger.cancel');
    expect(mockT).toHaveBeenCalledWith('detail.trigger.save');
    expect(mockT).toHaveBeenCalledWith('detail.action.title');
    expect(mockT).toHaveBeenCalledWith('detail.action.select.label');
    expect(mockT).toHaveBeenCalledWith('detail.action.select.label.1');
    expect(mockT).toHaveBeenCalledWith('detail.action.select.label.2');
    expect(mockT).toHaveBeenCalledWith('action.webhook.option_label');
  });

  it('handles webhook action with integration data', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_WEBHOOK,
      payload: {
        integrationId: 'integration1',
      },
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('webhook-options')).toBeInTheDocument();
  });

  it('handles send message action with schedule', () => {
    mockContextValue.currentEditDataAction = {
      id: 'action1',
      action: ACTION_SEND_MESSAGE,
      payload: {
        message: 'Test message',
        ocsChannelId: 'channel1',
        schedule: '@next 1 days',
      },
    };

    renderWithProviders(<ModalActionNodeActivity />);

    expect(screen.getByTestId('send-message-form')).toBeInTheDocument();
  });
});
