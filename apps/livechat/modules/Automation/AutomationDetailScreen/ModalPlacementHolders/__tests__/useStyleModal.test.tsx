import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { renderHook } from '@testing-library/react';
import React, { type ReactNode } from 'react';
import { useStyleModal } from '../useStyleModal';

const wrapper = ({ children }: { children: ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

describe('useStyleModal', () => {
  it('returns the expected style classes', () => {
    const { result } = renderHook(() => useStyleModal(), { wrapper });

    expect(result.current.classes).toBeDefined();
    expect(result.current.classes.flexContainer).toBeDefined();
    expect(result.current.classes.formContainer).toBeDefined();
  });

  it('returns flexContainer class name', () => {
    const { result } = renderHook(() => useStyleModal(), { wrapper });

    expect(typeof result.current.classes.flexContainer).toBe('string');
    expect(result.current.classes.flexContainer).toMatch(/^css-/);
  });

  it('returns formContainer class name', () => {
    const { result } = renderHook(() => useStyleModal(), { wrapper });

    expect(typeof result.current.classes.formContainer).toBe('string');
    expect(result.current.classes.formContainer).toMatch(/^css-/);
  });

  it('returns consistent class names across renders', () => {
    const { result: result1 } = renderHook(() => useStyleModal(), { wrapper });
    const { result: result2 } = renderHook(() => useStyleModal(), { wrapper });

    expect(result1.current.classes.flexContainer).toBe(result2.current.classes.flexContainer);
    expect(result1.current.classes.formContainer).toBe(result2.current.classes.formContainer);
  });

  it('returns classes object with expected structure', () => {
    const { result } = renderHook(() => useStyleModal(), { wrapper });

    expect(result.current.classes).toEqual({
      flexContainer: expect.any(String),
      formContainer: expect.any(String),
    });
  });
});
