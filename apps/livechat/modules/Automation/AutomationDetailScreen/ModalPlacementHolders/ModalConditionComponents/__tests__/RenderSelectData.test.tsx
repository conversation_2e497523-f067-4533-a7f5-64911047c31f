import { MantineProvider } from '@mantine/core';
import { render, screen } from '@testing-library/react';
import { NO_SPECIFIED_OPERATOR_ID } from '../../../constant';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { PATHS } from '../../../types';
import RenderSelectData from '../RenderSelectData';

// Mock the context
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

describe('RenderSelectData', () => {
  const mockTeams = [
    { id: '1', name: 'Team 1' },
    { id: '2', name: 'Team 2' },
  ];

  const mockOperators = [
    { id: '1', name: 'Operator 1' },
    { id: '2', name: 'Operator 2' },
  ];

  beforeEach(() => {
    // Mock context values
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      teamList: mockTeams,
      listOperators: mockOperators,
      t: (key: string) => key,
    });
  });

  it('renders without crashing', () => {
    render(
      <MantineProvider>
        <RenderSelectData path={PATHS.TEAM_ID} value='1' />
      </MantineProvider>
    );
    expect(screen.getByText('Team 1')).toBeInTheDocument();
  });

  it('does not render when value is empty', () => {
    const { container } = render(
      <MantineProvider>
        <RenderSelectData path={PATHS.TEAM_ID} value='' />
      </MantineProvider>
    );
    const fallback = screen.getByText('detail.action.selected_info.title');
    expect(fallback).toBeInTheDocument();
    // The span inside should be empty
    const span = fallback.querySelector('span');
    expect(span).toBeInTheDocument();
    expect(span).toBeEmptyDOMElement();
  });

  it('displays team name when path is TEAM_ID', () => {
    render(
      <MantineProvider>
        <RenderSelectData path={PATHS.TEAM_ID} value='1' />
      </MantineProvider>
    );
    expect(screen.getByText('Team 1')).toBeInTheDocument();
  });

  it('displays operator name when path is OPERATOR_ID_TEAM_ID', () => {
    render(
      <MantineProvider>
        <RenderSelectData path={PATHS.OPERATOR_ID_TEAM_ID} value='1' />
      </MantineProvider>
    );
    expect(screen.getByText('Operator 1')).toBeInTheDocument();
  });

  it('shows unspecified assignee message when operator ID is NO_SPECIFIED_OPERATOR_ID', () => {
    render(
      <MantineProvider>
        <RenderSelectData path={PATHS.OPERATOR_ID_TEAM_ID} value={NO_SPECIFIED_OPERATOR_ID} />
      </MantineProvider>
    );
    expect(screen.getByText('detail.action.unspecified_assignee')).toBeInTheDocument();
  });

  it('returns empty string for team name when team is not found', () => {
    const { container } = render(
      <MantineProvider>
        <RenderSelectData path={PATHS.TEAM_ID} value='999' />
      </MantineProvider>
    );
    const fallback = screen.getByText('detail.action.selected_info.title');
    expect(fallback).toBeInTheDocument();
    const span = fallback.querySelector('span');
    expect(span).toBeInTheDocument();
    expect(span).toBeEmptyDOMElement();
  });

  it('returns empty string for operator name when operator is not found', () => {
    const { container } = render(
      <MantineProvider>
        <RenderSelectData path={PATHS.OPERATOR_ID_TEAM_ID} value='999' />
      </MantineProvider>
    );
    const fallback = screen.getByText('detail.action.selected_info.title');
    expect(fallback).toBeInTheDocument();
    const span = fallback.querySelector('span');
    expect(span).toBeInTheDocument();
    expect(span).toBeEmptyDOMElement();
  });
});
