import { MantineColorsTuple, MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { fireEvent, render, screen } from '@testing-library/react';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { FACTS, PATHS } from '../../../types';
import ConditionSettingForm from '../ConditionSettingForm';

// Mock ResizeObserver for Mantine/DOM usage in tests
beforeAll(() => {
  global.ResizeObserver =
    global.ResizeObserver ||
    class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
});

// Mock the context
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

describe('ConditionSettingForm', () => {
  const defaultProps = {
    index: 0,
    onRemove: jest.fn(),
    onChange: jest.fn(),
    handleToggleTeamPanel: jest.fn(),
    handleToggleOperatorPanel: jest.fn(),
    fact: FACTS.END_USER,
    value: true,
    severity: 'info',
    path: PATHS.CURRENT,
  };

  const navy: MantineColorsTuple = [
    '#1A365D',
    '#2C5282',
    '#2B6CB0',
    '#3182CE',
    '#4299E1',
    '#63B3ED',
    '#90CDF4',
    '#BEE3F8',
    '#EBF8FF',
    '#FFFFFF',
  ];

  const theme = {
    colors: {
      navy,
    },
  };

  beforeEach(() => {
    // Mock context values
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
      triggerNodeData: 'conversation.created',
      crmFieldsByObject: [],
      selectConditionModel: [
        {
          triggerNames: ['conversation.created'],
          config: {
            [FACTS.END_USER]: {
              label: 'End User',
              fact: FACTS.END_USER,
              attributes: [
                { path: PATHS.CURRENT, label: 'Current', disabled: false },
                { path: PATHS.OCS_CHANNEL, label: 'OCS Channel', disabled: false },
              ],
            },
            [FACTS.CONVERSATION]: {
              label: 'Conversation',
              fact: FACTS.CONVERSATION,
              attributes: [{ path: PATHS.CURRENT, label: 'Current', disabled: false }],
            },
          },
        },
      ],
      ocsChannelsOptionsWithDefaultOption: [
        { value: 'channel1', label: 'Channel 1' },
        { value: 'channel2', label: 'Channel 2' },
      ],
    });
  });

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider theme={theme}>{ui}</MantineProvider>
      </MantineEmotionProvider>
    );
  };

  it('renders without crashing', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} />);
    expect(screen.getByText('detail.condition.select.subject.label')).toBeInTheDocument();
  });

  it('shows remove button when index is greater than 0', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} index={1} />);
    expect(screen.getByTestId('remove-icon')).toBeInTheDocument();
  });

  it('does not show remove button when index is 0', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} index={0} />);
    expect(screen.queryByTestId('remove-icon')).not.toBeInTheDocument();
  });

  it('calls onRemove when remove button is clicked', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} index={1} />);
    fireEvent.click(screen.getByTestId('remove-icon'));
    expect(defaultProps.onRemove).toHaveBeenCalled();
  });

  it('renders subject select with correct options', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} />);
    expect(screen.getByText('detail.condition.select.subject.label')).toBeInTheDocument();
  });

  it('renders condition select with correct options', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} />);
    expect(screen.getByText('detail.condition.select.condition.label')).toBeInTheDocument();
  });

  it('renders radio buttons for CURRENT path', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} />);
    expect(screen.getByText('detail.condition.radio.value.radio.label.1')).toBeInTheDocument();
    expect(screen.getByText('detail.condition.radio.value.radio.label.2')).toBeInTheDocument();
  });

  it('renders channel select for OCS_CHANNEL path', () => {
    renderWithProviders(<ConditionSettingForm {...defaultProps} path={PATHS.OCS_CHANNEL} />);
    expect(screen.getByText('detail.condition.select.condition.label')).toBeInTheDocument();
  });
});
