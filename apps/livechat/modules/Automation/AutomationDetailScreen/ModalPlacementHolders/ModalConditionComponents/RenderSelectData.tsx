import { Container, Text, rem } from '@mantine/core';
import { useMemo } from 'react';
import { NO_SPECIFIED_OPERATOR_ID } from '../../constant';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { PATHS } from '../../types';
import { Visible } from '../CustomFormElements/CustomFormElements';

const RenderSelectData = ({ path, value }: { path: PATHS; value: any }) => {
  const { teamList, listOperators, t } = useAutomationDetailContext();

  const shouldDisplay = useMemo(() => {
    return !!value;
  }, [value]);

  const teamName = useMemo(() => {
    if (path !== PATHS.TEAM_ID) return '';
    const teamId = value as string;
    if (teamList.length) {
      return teamList.find((team) => team.id === teamId)?.name || '';
    }
    return '';
  }, [path, teamList, value]);

  const operatorName = useMemo(() => {
    if (path !== PATHS.OPERATOR_ID_TEAM_ID) return '';
    const teamIdOperatorId = value as string;
    if (!teamIdOperatorId) return '';
    const operatorId = teamIdOperatorId?.substring(teamIdOperatorId?.lastIndexOf('#') + 1);
    if (operatorId === NO_SPECIFIED_OPERATOR_ID) {
      return t('detail.action.unspecified_assignee');
    }
    if (listOperators.length) {
      return listOperators.find((operator) => operator.id === operatorId)?.name || '';
    }
    return '';
  }, [listOperators, path, t, value]);

  return (
    <Visible displayIf={shouldDisplay}>
      <Container pl={0} pr={0}>
        <Text fw={400} c='gray.6' size={rem(12)}>
          {t('detail.action.selected_info.title')}{' '}
          <span style={{ fontWeight: 700 }}>
            {path === PATHS.TEAM_ID ? teamName : operatorName}
          </span>
        </Text>
      </Container>
    </Visible>
  );
};

export default RenderSelectData;
