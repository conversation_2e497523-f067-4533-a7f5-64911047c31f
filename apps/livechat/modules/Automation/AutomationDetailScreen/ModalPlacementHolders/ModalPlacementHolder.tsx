import { Container, MantineTransition, Transition } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useAutomationDetailContext } from '../contexts/AutomationDetailContext';
import { ModalActivityType } from '../types';
import ModalActionNodeActivity from './ModalActionNodeActivity';
import ModalConditionNodeActivity from './ModalConditionNodeActivity';
import { ModalTriggerNodeActivity } from './ModalTriggerNodeActivity';

const useStyle = createStyles((theme) => ({
  modalPlacement: {
    backgroundColor: 'white',
    border: `2px solid ${theme.colors.gray[1]}`,
    borderRadius: theme.radius.md,
    paddingLeft: 0,
    paddingRight: 0,
    boxShadow: `0 0 4px -1px ${theme.colors.gray[4]}`,
  },
}));

type ModalPlacementHolderType = {
  parentDimension: {
    width: number;
    height: number;
  };
};

const modalPlacementTransition: MantineTransition = {
  in: { opacity: 1, transform: `translateX(0px)` },
  out: { opacity: 0, transform: `translateX(-100%)` },
  common: { transformOrigin: 'right center 0px' },
  transitionProperty: 'transform, opacity',
};

export function ModalPlacementHolder({ parentDimension }: ModalPlacementHolderType) {
  const { height } = parentDimension;
  const { modalActivities, currentModalType } = useAutomationDetailContext();
  const { classes } = useStyle();
  return (
    <Transition
      mounted={modalActivities.visible}
      transition={modalPlacementTransition}
      duration={200}
    >
      {(styles) => (
        <Container
          style={styles}
          sx={{
            display: 'flex',
            width: 'auto',
            height: `${Math.floor(height - 40)}px`,
            position: 'absolute',
            top: 20,
            left: 20,
          }}
          className={classes.modalPlacement}
        >
          {currentModalType === ModalActivityType.TriggerActivityModal ? (
            <ModalTriggerNodeActivity />
          ) : null}
          {currentModalType === ModalActivityType.ConditionActivityModal ? (
            <ModalConditionNodeActivity />
          ) : null}
          {currentModalType === ModalActivityType.ActionActivityModal ? (
            <ModalActionNodeActivity />
          ) : null}
        </Container>
      )}
    </Transition>
  );
}
