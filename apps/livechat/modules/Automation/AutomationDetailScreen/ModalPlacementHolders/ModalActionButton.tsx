import { Button, Group } from '@mantine/core';
import { useTranslate } from '@tolgee/react';

export default function ActionButtons({
  isManager,
  onSave,
  onClose,
}: {
  isManager: boolean;
  onSave: () => void;
  onClose: () => void;
}) {
  const { t } = useTranslate('workspace');
  return (
    <Group justify='flex-end' px={'md'} my={'md'}>
      <Button variant={'default'} onClick={onClose} size='compact-sm' radius={'sm'}>
        {t('detail.trigger.cancel')}
      </Button>
      <Button
        radius={'sm'}
        color={'navy.0'}
        onClick={onSave}
        size='compact-sm'
        variant={'filled'}
        disabled={!isManager}
      >
        {t('detail.trigger.save')}
      </Button>
    </Group>
  );
}
