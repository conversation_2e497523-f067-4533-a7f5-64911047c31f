import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { act, fireEvent, render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React, { createRef } from 'react';
import { EmojiSelector } from './EmojiSelector';

// Mock ResizeObserver for Mantine components
beforeAll(() => {
  global.ResizeObserver =
    global.ResizeObserver ||
    class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
});

// Optionally mock emoji-mart Picker if it causes issues
jest.mock('emoji-mart', () => ({
  Picker: (props: any) => (
    <div data-testid='emoji-picker-mock'>
      <button onClick={() => props.onSelect({ native: '😀' })}>😀</button>
    </div>
  ),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineEmotionProvider>
    <MantineProvider
      theme={{
        colors: {
          navy: [
            '#1A1B1E',
            '#25262B',
            '#2C2E33',
            '#373A40',
            '#4D4F55',
            '#5C5F66',
            '#909296',
            '#A6A7AB',
            '#C1C2C5',
            '#E9ECEF',
          ],
        },
      }}
    >
      {children}
    </MantineProvider>
  </MantineEmotionProvider>
);

const renderWithProvider = (ui: React.ReactElement) => {
  return render(ui, { wrapper: TestWrapper });
};

describe('EmojiSelector', () => {
  it('renders the displayAs node', () => {
    renderWithProvider(
      <EmojiSelector displayAs={<button>Open Emoji</button>} onSelect={jest.fn()} />
    );
    expect(screen.getByText('Open Emoji')).toBeInTheDocument();
  });

  it('opens the emoji picker when openEmojiBox is called via ref', async () => {
    const ref = createRef<any>();
    renderWithProvider(
      <EmojiSelector ref={ref} displayAs={<button>Open Emoji</button>} onSelect={jest.fn()} />
    );
    await act(async () => {
      ref.current.openEmojiBox();
    });
    expect(await within(document.body).findByTestId('emoji-picker-mock')).toBeInTheDocument();
  });

  it('calls onSelect when an emoji is picked', async () => {
    const onSelect = jest.fn();
    const ref = createRef<any>();
    renderWithProvider(
      <EmojiSelector ref={ref} displayAs={<button>Open Emoji</button>} onSelect={onSelect} />
    );
    await act(async () => {
      ref.current.openEmojiBox();
    });
    const emojiBtn = await within(document.body).findByText('😀');
    await userEvent.click(emojiBtn);
    expect(onSelect).toHaveBeenCalledWith('😀');
  });

  it('closes the picker when the close button is clicked', () => {
    const ref = createRef<any>();
    renderWithProvider(
      <EmojiSelector ref={ref} displayAs={<button>Open Emoji</button>} onSelect={jest.fn()} />
    );
    act(() => {
      ref.current.openEmojiBox();
    });
    // Find the close button (IconX is rendered inside ActionIcon)
    const closeBtn = within(document.body).getByRole('button', { hidden: true });
    fireEvent.click(closeBtn);
    // Picker should be removed from the DOM
    expect(within(document.body).queryByTestId('emoji-picker-mock')).not.toBeInTheDocument();
  });
});
