import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
  IconSearchStyled,
  NumberInputStyled,
  SelectStyled,
  TextInputStyled,
  TooltipRight,
  Visible,
} from './CustomFormElements';

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <MantineEmotionProvider>
      <MantineProvider
        theme={{
          colors: {
            navy: [
              '#1A1B1E',
              '#25262B',
              '#2C2E33',
              '#373A40',
              '#4D4F55',
              '#5C5F66',
              '#909296',
              '#A6A7AB',
              '#C1C2C5',
              '#E9ECEF',
            ],
          },
        }}
      >
        {children}
      </MantineProvider>
    </MantineEmotionProvider>
  );
};

const renderWithProvider = (ui: React.ReactElement) => {
  return render(ui, { wrapper: TestWrapper });
};

// Mock ResizeObserver for Mantine components
beforeAll(() => {
  global.ResizeObserver =
    global.ResizeObserver ||
    class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
});

describe('CustomFormElements', () => {
  describe('SelectStyled', () => {
    it('renders with default props', () => {
      renderWithProvider(<SelectStyled data={['Option 1', 'Option 2']} />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      const { container } = renderWithProvider(
        <SelectStyled data={['Option 1']} className='custom-class' />
      );
      // Mantine may not apply className to the native select, so check the wrapper
      expect(container.querySelector('.custom-class')).toBeInTheDocument();
    });
  });

  describe('NumberInputStyled', () => {
    it('renders with default props', () => {
      renderWithProvider(<NumberInputStyled />);
      // Mantine renders input as type="text" with role="textbox"
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      const { container } = renderWithProvider(<NumberInputStyled className='custom-class' />);
      expect(container.querySelector('.custom-class')).toBeInTheDocument();
    });
  });

  describe('TextInputStyled', () => {
    it('renders with default props', () => {
      renderWithProvider(<TextInputStyled />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      const { container } = renderWithProvider(<TextInputStyled className='custom-class' />);
      expect(container.querySelector('.custom-class')).toBeInTheDocument();
    });
  });

  describe('IconSearchStyled', () => {
    it('renders with default props', () => {
      renderWithProvider(<IconSearchStyled />);
      expect(screen.getByTestId('icon-search')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      renderWithProvider(<IconSearchStyled className='custom-class' />);
      const icon = screen.getByTestId('icon-search');
      expect(icon).toHaveClass('custom-class');
    });
  });

  describe('TooltipRight', () => {
    it('renders with default props', () => {
      renderWithProvider(
        <TooltipRight>
          <button>Hover me</button>
        </TooltipRight>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('renders with custom label', () => {
      renderWithProvider(
        <TooltipRight label='Custom tooltip'>
          <button>Hover me</button>
        </TooltipRight>
      );
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Visible', () => {
    it('renders children when displayIf is true', () => {
      renderWithProvider(
        <Visible displayIf={true}>
          <div>Visible content</div>
        </Visible>
      );
      expect(screen.getByText('Visible content')).toBeVisible();
    });

    it('hides children when displayIf is false', () => {
      renderWithProvider(
        <Visible displayIf={false}>
          <div>Hidden content</div>
        </Visible>
      );
      const content = screen.getByText('Hidden content');
      expect(content).not.toBeVisible();
    });
  });
});
