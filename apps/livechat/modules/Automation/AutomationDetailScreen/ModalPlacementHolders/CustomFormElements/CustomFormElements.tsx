import {
  NumberInput,
  NumberInputProps,
  Select,
  SelectProps,
  TextInput,
  TextInputProps,
  Tooltip,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconSearch } from '@tabler/icons-react';
import { ReactNode } from 'react';

const useStyle = createStyles((theme) => ({
  selectStyle: {
    '& input:focus:not(:disabled), & input:focus-within:not(:disabled)': {
      borderColor: theme.colors.navy[0],
    },
  },
  iconStyle: {
    color: theme.colors.navy[0],
  },
  textStyled: {
    '& input:focus': {
      borderColor: theme.colors.navy[0],
    },
  },
}));

export const SelectStyled = (props: SelectProps & React.RefAttributes<HTMLInputElement>) => {
  const { classes } = useStyle();
  return (
    <Select
      {...props}
      className={`${classes.selectStyle} ${props?.className || ''}`}
      allowDeselect={props?.allowDeselect || false}
      withCheckIcon={false}
      styles={(theme) => ({
        ...props.styles,
        option: {
          ...(props?.styles as any)?.option,
          '&[data-checked="true"]': {
            '&, &:hover': {
              backgroundColor: theme.colors.navy[0],
              color: theme.white,
            },
          },
        },
        input: {
          '&:focus': {
            borderColor: theme.colors.navy[5],
          },
        },
        item: {
          // applies styles to selected item
          '&[data-selected]': {
            '&, &:hover': {
              backgroundColor: theme.colors.navy[0],
              color: theme.white,
            },
          },

          // applies styles to hovered item (with mouse or keyboard)
          '&[data-hovered]': {},
        },
      })}
    />
  );
};

export const NumberInputStyled = (
  props: NumberInputProps & React.RefAttributes<HTMLInputElement>
) => {
  const { classes } = useStyle();
  return <NumberInput {...props} className={`${classes.textStyled} ${props?.className || ''}`} />;
};

export const TextInputStyled = (props: TextInputProps & React.RefAttributes<HTMLInputElement>) => {
  const { classes } = useStyle();
  return <TextInput {...props} className={`${classes.textStyled} ${props?.className || ''}`} />;
};

/**
 * TODO: fix any by `TablerIconsProps` that not exist in `@tabler/icons-react`
 */
export const IconSearchStyled = (props: any) => {
  const { classes } = useStyle();
  return (
    <IconSearch
      {...props}
      data-testid='icon-search'
      className={`${classes.iconStyle} ${props?.className ?? ''}`}
    />
  );
};

export const TooltipRight = ({
  label = '',
  children,
  className,
}: {
  label?: ReactNode;
  children: ReactNode;
  className?: string;
}) => {
  return (
    // TODO: fix width of tooltip, the width={220} is not supported in Mantine v7
    <Tooltip
      label={label}
      className={`tooltipStyle ${className || ''}`}
      color='white'
      position='right'
      withArrow
      arrowPosition='center'
      arrowSize={8}
      multiline
      styles={{
        tooltip: {
          boxShadow: '0 0 2px #a6a6a6',
        },
        arrow: {
          boxShadow: '-1px 1px 1px #a6a6a6',
        },
      }}
    >
      {children}
    </Tooltip>
  );
};
export const Visible = ({ displayIf, children }: { displayIf: boolean; children?: ReactNode }) => {
  return (
    <div
      style={
        displayIf ? { visibility: 'visible', height: 'auto' } : { visibility: 'hidden', height: 0 }
      }
    >
      {children}
    </div>
  );
};
