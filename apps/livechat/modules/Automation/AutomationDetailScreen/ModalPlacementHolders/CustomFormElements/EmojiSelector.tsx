import { ActionIcon, Menu } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconX } from '@tabler/icons-react';
import { BaseEmoji, Picker } from 'emoji-mart';
import 'emoji-mart/css/emoji-mart.css';
import { ReactNode, forwardRef, useCallback, useImperativeHandle, useState } from 'react';

type EmojiSelectorProps = {
  displayAs: ReactNode;
  // eslint-disable-next-line no-unused-vars
  onSelect: (emoji: string) => void;
};

type EmojiSelectorRef = {
  openEmojiBox: () => void;
};

const useStyle = createStyles(() => ({
  dropdown: {
    '& .mantine-Menu-dropdown.mantine-Menu-dropdown': {
      width: '350px',
    },
  },
  iconX: {},
}));

const EmojiConfig = {
  // use japanese
  search: '検索',
  categories: {
    search: '検索結果',
    recent: 'よく使う絵文字',
    people: '顔 & 人',
    nature: '動物 & 自然',
    foods: '食べ物 & 飲み物',
    activity: 'アクティビティ',
    places: '旅行 & 場所',
    objects: 'オブジェクト',
    symbols: '記号',
    flags: '旗',
    custom: 'カスタム',
  },
  notfound: '絵文字が見つかりませんでした',
};
export const EmojiSelector = forwardRef<EmojiSelectorRef, EmojiSelectorProps>(
  ({ displayAs, onSelect }, ref) => {
    const [opened, setOpened] = useState(false);
    const { classes } = useStyle();
    const handleSelectEmoji = useCallback(
      (emoji: BaseEmoji) => {
        onSelect(emoji.native);
        setOpened(false);
      },
      [onSelect]
    );

    useImperativeHandle(ref, () => ({
      openEmojiBox: () => setOpened(true),
    }));

    return (
      <Menu
        shadow='md'
        opened={opened}
        onChange={setOpened}
        position='top-start'
        classNames={classes}
      >
        <Menu.Target>{displayAs}</Menu.Target>
        <Menu.Dropdown>
          <ActionIcon
            variant='light'
            color='gray'
            sx={(theme) => ({
              position: 'absolute',
              border: `1px solid ${theme.colors.gray[2]}`,
              borderRadius: '50%',
              top: -10,
              right: -10,
              ':hover': {
                backgroundColor: 'white',
                color: '#000000',
              },
            })}
            onClick={() => setOpened(false)}
          >
            <IconX className={classes.iconX} size={18} />
          </ActionIcon>

          <Picker
            title=':grinning:'
            emoji='grinning'
            i18n={EmojiConfig}
            onSelect={handleSelectEmoji}
            autoFocus={true}
          />
        </Menu.Dropdown>
      </Menu>
    );
  }
);

EmojiSelector.displayName = 'EmojiSelector';
