import { Button, Container, Divider, Group, Text, Title, rem } from '@mantine/core';
import { useCallback, useState } from 'react';
import { useAutomationDetailContext } from '../contexts/AutomationDetailContext';
import { TriggerType } from '../types';

export function ModalTriggerNodeActivity() {
  const { t, closeActivityModal, triggerNodeData, isManager, setTriggerNodeData } =
    useAutomationDetailContext();
  const [currSelect, setCurrSelect] = useState<TriggerType | null>(triggerNodeData);
  const handleSave = useCallback(() => {
    setTriggerNodeData(currSelect);
    closeActivityModal();
  }, [setTriggerNodeData, closeActivityModal, currSelect]);

  return (
    <Container miw={360} maw={360} pl={0} pr={0}>
      <Group justify='flex-end' pl={'md'} pr={'md'} mt={'md'} mb={'md'}>
        <Button radius={'sm'} variant='default' size='compact-sm' onClick={closeActivityModal}>
          {t('detail.trigger.cancel')}
        </Button>
        {currSelect !== null && (
          <Button
            radius={'sm'}
            variant='filled'
            color={'navy.0'}
            size='compact-sm'
            onClick={handleSave}
            disabled={!isManager}
          >
            {t('detail.trigger.save')}
          </Button>
        )}
      </Group>
      <Divider />
      <Container fluid mt={'md'}>
        <Title order={4} mb={'md'}>
          {t('detail.trigger.title')}
        </Title>
        <Text mb={'md'} size={rem(14)}>
          {t('detail.trigger.description')}
        </Text>
        <Button
          mb={'md'}
          fullWidth
          variant={currSelect === TriggerType.NewConversation ? 'outline' : 'default'}
          color={'navy.0'}
          onClick={() => setCurrSelect(TriggerType.NewConversation)}
        >
          {t('condition.whenNewMessageCome')}
        </Button>
        <Button
          mb={'md'}
          fullWidth
          variant={currSelect === TriggerType.MessageReplyConversation ? 'outline' : 'default'}
          color={'navy.0'}
          onClick={() => setCurrSelect(TriggerType.MessageReplyConversation)}
        >
          {t('condition.whenOperatorAssigned')}
        </Button>
        <Button
          mb={'md'}
          fullWidth
          variant={currSelect === TriggerType.CloseConversation ? 'outline' : 'default'}
          color={'navy.0'}
          onClick={() => setCurrSelect(TriggerType.CloseConversation)}
        >
          {t('condition.whenChatIsClosed')}
        </Button>
      </Container>
    </Container>
  );
}
