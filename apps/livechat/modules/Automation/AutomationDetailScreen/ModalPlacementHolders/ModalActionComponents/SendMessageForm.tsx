import { Container, Text, rem } from '@mantine/core';
import { useCallback } from 'react';
import { TextAreaEditor } from '../../../../../components/TextAreaEditor/TextAreaEditor';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';

export const types = ['day_after', 'hour_after', 'day_hour_after'];

export type ActionSendData = {
  ocsChannelId: string;
  message: string;
  checkedSelectInterval: boolean;
  typeSelectInterval: (typeof types)[number];
  schedule: {
    [key: string]: {
      type: string;
      data: {
        dayAfter?: number;
        hourAfter?: number;
        atMinute?: number;
      };
    };
  };
};

type SendMessageFormProps = {
  actionType: string;
  dataForActionSend: ActionSendData;
  // eslint-disable-next-line no-unused-vars
  handleSetDataForActionSend: (data: any) => void;
};

export function SendMessageForm({
  // actionType,
  dataForActionSend,
  handleSetDataForActionSend,
}: SendMessageFormProps) {
  const { t } = useAutomationDetailContext();
  // **** COMMENT FOR LATER VERSION ***** //

  // const toggleCheckedSelectInterval = useCallback(
  //     (e) => {
  //         const checked = e.currentTarget.checked;
  //         handleSetDataForActionSend((pre) => {
  //             return { ...pre, checkedSelectInterval: checked };
  //         });
  //     },
  //     [handleSetDataForActionSend],
  // );

  // const handleChangeOcsChannel = useCallback(
  //     (ocsChannel) => {
  //         handleSetDataForActionSend((pre) => {
  //             pre.ocsChannelId = ocsChannel;
  //             return { ...pre };
  //         });
  //     },
  //     [handleSetDataForActionSend],
  // );

  // const handleChangeScheduleData = useCallback(
  //     (type) => {
  //         handleSetDataForActionSend((pre) => {
  //             pre.typeSelectInterval = type;
  //             return { ...pre };
  //         });
  //     },
  //     [handleSetDataForActionSend],
  // );
  // const handleChangeActionSendSchedule = useCallback(
  //     (type, props, val) => {
  //         handleSetDataForActionSend((pre) => {
  //             pre.schedule[type].data[props] = val;
  //             return { ...pre };
  //         });
  //     },
  //     [handleSetDataForActionSend],
  // );
  // **** COMMENT FOR LATER VERSION ***** //

  const handleChangeMessage = useCallback(
    (message) => {
      handleSetDataForActionSend((pre) => {
        pre.message = message;
        return { ...pre };
      });
    },
    [handleSetDataForActionSend]
  );
  return (
    <>
      {/* <SelectStyled
                mb={'md'}
                label={
                    <Text fw={700} mb={'xs'}>
                        {t('detail.action.select_2.label')}
                    </Text>
                }
                placeholder="Pick one"
                value={dataForActionSend.ocsChannelId}
                onChange={handleChangeOcsChannel}
                data={ocsChannelsOptionsWithDefaultOption}
            /> */}
      <Container fluid mb={'md'} pl={0} pr={0}>
        <Text fw={700} mb={'xs'} size={rem(14)}>
          {t('detail.action.text_editor.label')}
        </Text>
        <TextAreaEditor
          disableCountChar
          onChange={handleChangeMessage}
          value={dataForActionSend?.message || ''}
          placeholder={t('detail.action.text_editor.placeholder')}
        />
      </Container>
      {/* <Checkbox
                color="violet"
                label={
                    <Text fw={700} mb={'xs'}>
                        {t('detail.action.enable_period.label')}
                    </Text>
                }
                checked={dataForActionSend.checkedSelectInterval}
                onChange={(e) => toggleCheckedSelectInterval(e)}
            /> */}
      {/* <Visible
                displayIf={
                    actionType === ACTION_SEND_MESSAGE && dataForActionSend?.checkedSelectInterval
                }
            >
                <Radio
                    value="day_after"
                    color="violet"
                    checked={dataForActionSend.typeSelectInterval === types[0]}
                    onChange={() => handleChangeScheduleData('day_after')}
                    label={
                        <Flex direction={'row'} align={'center'}>
                            <NumberInputStyled
                                min={1}
                                mr={'xs'}
                                max={30}
                                defaultValue={1}
                                maw={60}
                                size="xs"
                                value={dataForActionSend.schedule[types[0]].data.dayAfter}
                                onChange={(val) =>
                                    handleChangeActionSendSchedule(types[0], 'dayAfter', val)
                                }
                            />
                            <Text fw={400} size={12} color="dark.4">
                                {t('detail.action.select_3.day_after.label')}
                            </Text>
                            <Text fw={400} size={12} color="gray.6">
                                {t('detail.action.select_3.day_after.description')}
                            </Text>
                        </Flex>
                    }
                    mb={'md'}
                />
                <Radio
                    value="hour_after"
                    color="violet"
                    checked={dataForActionSend.typeSelectInterval === types[1]}
                    onChange={() => handleChangeScheduleData('hour_after')}
                    label={
                        <Flex direction={'row'} align={'center'}>
                            <NumberInputStyled
                                min={1}
                                mr={'xs'}
                                max={23}
                                defaultValue={1}
                                maw={60}
                                size="xs"
                                value={dataForActionSend.schedule[types[1]].data.hourAfter}
                                onChange={(val) =>
                                    handleChangeActionSendSchedule(types[1], 'hourAfter', val)
                                }
                            />
                            <Text fw={400} size={12} color="dark.4">
                                {t('detail.action.select_3.hour_after.label')}
                            </Text>
                        </Flex>
                    }
                    mb={'md'}
                />
                <Radio
                    value="day_hour_after"
                    color="violet"
                    checked={dataForActionSend.typeSelectInterval === types[2]}
                    onChange={() => handleChangeScheduleData('day_hour_after')}
                    label={
                        <Flex direction={'row'} align={'center'}>
                            <NumberInputStyled
                                min={1}
                                max={30}
                                mr={7}
                                defaultValue={1}
                                maw={60}
                                size="xs"
                                value={dataForActionSend.schedule[types[2]].data.dayAfter}
                                onChange={(val) =>
                                    handleChangeActionSendSchedule(types[2], 'dayAfter', val)
                                }
                            />
                            <Text fw={400} size={12} color="dark.4" mr={3}>
                                {t('detail.action.select_3.day_hour_after.label_1')}
                            </Text>
                            <NumberInputStyled
                                min={0}
                                max={23}
                                mr={3}
                                defaultValue={1}
                                maw={60}
                                size="xs"
                                value={dataForActionSend.schedule[types[2]].data.hourAfter}
                                onChange={(val) =>
                                    handleChangeActionSendSchedule(types[2], 'hourAfter', val)
                                }
                            />
                            <Text fw={400} size={12} color="dark.4" mr={3}>
                                {t('detail.action.select_3.day_hour_after.label_2')}
                            </Text>
                            <NumberInputStyled
                                min={0}
                                max={59}
                                mr={3}
                                defaultValue={1}
                                maw={60}
                                size="xs"
                                value={dataForActionSend.schedule[types[2]].data.atMinute}
                                onChange={(val) =>
                                    handleChangeActionSendSchedule(types[2], 'atMinute', val)
                                }
                            />
                            <Text fw={400} size={12} color="dark.4">
                                {t('detail.action.select_3.day_hour_after.label_3')}
                            </Text>
                        </Flex>
                    }
                    mb={'md'}
                />
            </Visible> */}
    </>
  );
}
