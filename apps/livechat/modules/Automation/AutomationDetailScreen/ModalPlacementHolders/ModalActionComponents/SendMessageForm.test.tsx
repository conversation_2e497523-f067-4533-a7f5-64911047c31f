import { fireEvent, render, screen } from '@testing-library/react';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { SendMessageForm } from './SendMessageForm';

// Mock the context
jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

// Mock the TextAreaEditor component
jest.mock('../../../../../components/TextAreaEditor/TextAreaEditor', () => ({
  TextAreaEditor: ({
    value,
    onChange,
    placeholder,
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
  }) => (
    <textarea
      data-testid='text-area-editor'
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
    />
  ),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Container: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Text: ({ children }: { children: React.ReactNode }) => <span>{children}</span>,
  rem: (value: number) => value,
}));

describe('SendMessageForm', () => {
  const mockHandleSetDataForActionSend = jest.fn();
  const mockDataForActionSend = {
    ocsChannelId: 'channel1',
    message: 'Test message',
    checkedSelectInterval: false,
    typeSelectInterval: 'day_after',
    schedule: {
      day_after: {
        type: 'day_after',
        data: {
          dayAfter: 1,
        },
      },
      hour_after: {
        type: 'hour_after',
        data: {
          hourAfter: 1,
        },
      },
      day_hour_after: {
        type: 'day_hour_after',
        data: {
          dayAfter: 1,
          hourAfter: 1,
          atMinute: 0,
        },
      },
    },
  };

  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the form with initial values', () => {
    render(
      <SendMessageForm
        actionType='send_message'
        dataForActionSend={mockDataForActionSend}
        handleSetDataForActionSend={mockHandleSetDataForActionSend}
      />
    );

    expect(screen.getByText('detail.action.text_editor.label')).toBeInTheDocument();
    expect(screen.getByTestId('text-area-editor')).toHaveValue('Test message');
  });

  it('handles message change', () => {
    render(
      <SendMessageForm
        actionType='send_message'
        dataForActionSend={mockDataForActionSend}
        handleSetDataForActionSend={mockHandleSetDataForActionSend}
      />
    );

    const textArea = screen.getByTestId('text-area-editor');
    fireEvent.change(textArea, { target: { value: 'New message' } });

    expect(mockHandleSetDataForActionSend).toHaveBeenCalled();
  });

  it('renders with empty message', () => {
    const emptyData = {
      ...mockDataForActionSend,
      message: '',
    };

    render(
      <SendMessageForm
        actionType='send_message'
        dataForActionSend={emptyData}
        handleSetDataForActionSend={mockHandleSetDataForActionSend}
      />
    );

    expect(screen.getByTestId('text-area-editor')).toHaveValue('');
  });

  it('renders with placeholder text', () => {
    render(
      <SendMessageForm
        actionType='send_message'
        dataForActionSend={mockDataForActionSend}
        handleSetDataForActionSend={mockHandleSetDataForActionSend}
      />
    );

    expect(
      screen.getByPlaceholderText('detail.action.text_editor.placeholder')
    ).toBeInTheDocument();
  });
});
