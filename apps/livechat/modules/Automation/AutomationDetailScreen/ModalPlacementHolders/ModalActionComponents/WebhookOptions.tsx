import { Box, Container, Title } from '@mantine/core';
import { useMemo } from 'react';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { WebhookActionPayload } from '../../types';
import { SelectStyled } from '../CustomFormElements/CustomFormElements';
import { useStyleModal } from '../useStyleModal';

export function WebhookOptions({
  dataWebhook,
  handleChangeWebhookData,
}: {
  dataWebhook: WebhookActionPayload;
  // eslint-disable-next-line no-unused-vars
  handleChangeWebhookData: (integrationId: string) => void;
}) {
  const { classes } = useStyleModal();
  const { t, webhookOptions } = useAutomationDetailContext();
  const integrationId = useMemo(() => dataWebhook.integrationId, [dataWebhook]);
  return (
    <>
      <Box h={'34px'} sx={{ position: 'relative' }}>
        <Title size={14} my={'md'} ml={30} fw={700} sx={{ position: 'absolute' }}>
          {t('action.webhook.title')}
        </Title>
      </Box>

      <Container className={classes.formContainer} mx='sm' mt={'md'}>
        <SelectStyled
          value={integrationId}
          onChange={handleChangeWebhookData}
          placeholder='Pick one'
          data={webhookOptions}
        />
      </Container>
    </>
  );
}
