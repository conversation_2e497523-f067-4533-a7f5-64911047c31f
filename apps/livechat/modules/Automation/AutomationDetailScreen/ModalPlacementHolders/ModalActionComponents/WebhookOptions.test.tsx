import { fireEvent, render, screen } from '@testing-library/react';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { WebhookOptions } from './WebhookOptions';

// Mock the context
jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

// Mock the useStyleModal hook
jest.mock('../useStyleModal', () => ({
  useStyleModal: () => ({
    classes: {
      formContainer: 'form-container',
    },
  }),
}));

// Mock Mantine components
jest.mock('@mantine/core', () => ({
  Box: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Container: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Title: ({ children }: { children: React.ReactNode }) => <h2>{children}</h2>,
}));

// Mock SelectStyled component
jest.mock('../CustomFormElements/CustomFormElements', () => ({
  SelectStyled: ({
    value,
    onChange,
    data,
  }: {
    value: string;
    onChange: (value: string) => void;
    data: any[];
  }) => (
    <select data-testid='webhook-select' value={value} onChange={(e) => onChange(e.target.value)}>
      {data.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

describe('WebhookOptions', () => {
  const mockHandleChangeWebhookData = jest.fn();
  const mockDataWebhook = {
    integrationId: 'webhook1',
  };

  const mockWebhookOptions = [
    { value: 'webhook1', label: 'Webhook 1' },
    { value: 'webhook2', label: 'Webhook 2' },
  ];

  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
      webhookOptions: mockWebhookOptions,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the webhook options with initial value', () => {
    render(
      <WebhookOptions
        dataWebhook={mockDataWebhook}
        handleChangeWebhookData={mockHandleChangeWebhookData}
      />
    );

    expect(screen.getByText('action.webhook.title')).toBeInTheDocument();
    expect(screen.getByTestId('webhook-select')).toHaveValue('webhook1');
  });

  it('handles webhook selection change', () => {
    render(
      <WebhookOptions
        dataWebhook={mockDataWebhook}
        handleChangeWebhookData={mockHandleChangeWebhookData}
      />
    );

    const select = screen.getByTestId('webhook-select');
    fireEvent.change(select, { target: { value: 'webhook2' } });

    expect(mockHandleChangeWebhookData).toHaveBeenCalledWith('webhook2');
  });

  it('renders with empty webhook options', () => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
      webhookOptions: [],
    });

    render(
      <WebhookOptions
        dataWebhook={mockDataWebhook}
        handleChangeWebhookData={mockHandleChangeWebhookData}
      />
    );

    expect(screen.getByTestId('webhook-select')).toBeInTheDocument();
  });

  it('renders with different initial webhook value', () => {
    const differentDataWebhook = {
      integrationId: 'webhook2',
    };

    render(
      <WebhookOptions
        dataWebhook={differentDataWebhook}
        handleChangeWebhookData={mockHandleChangeWebhookData}
      />
    );

    expect(screen.getByTestId('webhook-select')).toHaveValue('webhook2');
  });
});
