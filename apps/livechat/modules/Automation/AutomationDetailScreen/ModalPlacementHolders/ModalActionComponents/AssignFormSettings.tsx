import {
  Box,
  Button,
  Checkbox,
  Container,
  Flex,
  Radio,
  Text,
  Title,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconInfoCircle } from '@tabler/icons-react';
import { BaseSyntheticEvent, useCallback, useMemo } from 'react';
import { VisibilityControl } from '../../../../../hooks/useVisibilityControl';
import { NO_SPECIFIED_OPERATOR_ID } from '../../constant';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { newObject } from '../../contexts/utils';
import {
  ActionAssignData,
  NewAssignPreviousAssigneeData,
  NewAssignTeamData,
  NewAssignTeamOperatorData,
  RULES_SELECT_OPERATOR,
  ScenarioAssignType,
} from '../../types';
import {
  NumberInputStyled,
  SelectStyled,
  TooltipRight,
} from '../CustomFormElements/CustomFormElements';
import { useStyleModal } from '../useStyleModal';

export function AssignFormSettings({
  errorState,
  dataForActionAssign,
  visibilityCtrSelectTeamPanel,
  visibilityCtrSelectOperatorPanel,
  handleDataAssign,
}: {
  errorState: ScenarioAssignType;
  dataForActionAssign: ActionAssignData;
  visibilityCtrSelectTeamPanel: VisibilityControl;
  visibilityCtrSelectOperatorPanel: VisibilityControl;
  // eslint-disable-next-line no-unused-vars
  handleDataAssign: (data: any) => void;
}) {
  const { classes } = useStyleModal();
  const theme = useMantineTheme();
  const { t } = useAutomationDetailContext();

  const scenario = useMemo(() => dataForActionAssign.scenario, [dataForActionAssign.scenario]);

  const showMaxConversationTeamHandleSession = useMemo(() => {
    return (
      scenario === ScenarioAssignType.TEAM &&
      [
        RULES_SELECT_OPERATOR.ROUND_ROBIN,
        RULES_SELECT_OPERATOR.LEAST_BUSY,
        RULES_SELECT_OPERATOR.RANDOM,
      ].includes((dataForActionAssign as NewAssignTeamData).data?.rule)
    );
  }, [dataForActionAssign, scenario]);

  const shouldShowButtonSelect = useMemo(
    () => [ScenarioAssignType.TEAM, ScenarioAssignType.TEAM_AND_OPERATOR].includes(scenario),
    [scenario]
  );

  const labelButton = useMemo(() => {
    switch (scenario) {
      case ScenarioAssignType.TEAM:
        return t('action.button.select_team.title');
      case ScenarioAssignType.TEAM_AND_OPERATOR:
        return t('detail.action.button.label.1');
      default:
        return '';
    }
  }, [scenario, t]);

  const buttonAction = useMemo(() => {
    switch (scenario) {
      case ScenarioAssignType.TEAM:
        return visibilityCtrSelectTeamPanel.toggle;
      case ScenarioAssignType.TEAM_AND_OPERATOR:
        return visibilityCtrSelectOperatorPanel.toggle;
      default:
        return () => undefined;
    }
  }, [scenario, visibilityCtrSelectOperatorPanel.toggle, visibilityCtrSelectTeamPanel.toggle]);

  const handleChangeAssignSetting = useCallback(
    (newScenario: ScenarioAssignType) => {
      if (scenario === newScenario) return;
      visibilityCtrSelectTeamPanel.close();
      visibilityCtrSelectOperatorPanel.close();
      let dataAssign: any = {};
      switch (newScenario) {
        case ScenarioAssignType.TEAM:
          dataAssign = {
            scenario: ScenarioAssignType.TEAM,
            data: {
              teamId: null,
              rule: RULES_SELECT_OPERATOR.ROUND_ROBIN,
            },
          } as NewAssignTeamData;
          break;
        case ScenarioAssignType.TEAM_AND_OPERATOR:
          dataAssign = {
            scenario: ScenarioAssignType.TEAM_AND_OPERATOR,
            data: {
              teamId: null,
              operatorId: null,
            },
          } as NewAssignTeamOperatorData;
          break;

        case ScenarioAssignType.PREVIOUS_ASSIGNEE:
          dataAssign = {
            scenario: ScenarioAssignType.PREVIOUS_ASSIGNEE,
            data: {
              doNotAssignIfAssigneeNotFound: true,
            },
          } as NewAssignPreviousAssigneeData;
          break;
      }
      handleDataAssign(dataAssign);
    },
    [scenario, handleDataAssign, visibilityCtrSelectOperatorPanel, visibilityCtrSelectTeamPanel]
  );

  const toggleNoNeedOperatorIfNot = useCallback(
    (e: BaseSyntheticEvent) => {
      const dataCopied = newObject(dataForActionAssign) as NewAssignPreviousAssigneeData;
      dataCopied.data.doNotAssignIfAssigneeNotFound = e.target.checked;
      handleDataAssign(dataCopied);
    },
    [dataForActionAssign, handleDataAssign]
  );

  const handleSetRuleAssignTeam = useCallback(
    (e: BaseSyntheticEvent) => {
      const value = e.target.value;
      if ((dataForActionAssign as NewAssignTeamData)?.data?.rule === value) return;
      const dataCopied = newObject(dataForActionAssign) as NewAssignTeamData;
      const data: NewAssignTeamData['data'] = newObject(dataCopied.data);
      data.rule = value;
      if (value === RULES_SELECT_OPERATOR.NO_SPECIFIED) {
        delete data?.['maxConversation'];
      }
      dataCopied.data = data;
      handleDataAssign(dataCopied);
    },
    [dataForActionAssign, handleDataAssign]
  );

  const toggleEnableMaxConversationTeamHandler = useCallback(
    (e: BaseSyntheticEvent) => {
      const dataCopied = newObject(dataForActionAssign) as NewAssignTeamData;
      const data: NewAssignTeamData['data'] = {
        teamId: dataCopied.data.teamId,
        rule: dataCopied.data.rule,
        fifo: dataCopied.data?.fifo || false,
      };
      if (e.target.checked) {
        data.maxConversation = 1;
      }
      dataCopied.data = data;
      handleDataAssign(dataCopied);
    },
    [dataForActionAssign, handleDataAssign]
  );

  const handleSetMaxConversationTeamHandler = useCallback(
    (val: number | string) => {
      const dataCopied = newObject(dataForActionAssign) as NewAssignTeamData;
      dataCopied.data.maxConversation = val !== '' ? Number(val) : 1;
      handleDataAssign(dataCopied);
    },
    [dataForActionAssign, handleDataAssign]
  );

  const handleToggleFifoAssignment = useCallback(
    (e: BaseSyntheticEvent) => {
      const dataCopied = newObject(dataForActionAssign) as NewAssignTeamData;
      dataCopied.data.fifo = e.target.checked;
      handleDataAssign(dataCopied);
    },
    [dataForActionAssign, handleDataAssign]
  );

  return (
    <>
      <Box h={'34px'} sx={{ position: 'relative' }}>
        <Title size={14} my={'md'} ml={30} fw={700} sx={{ position: 'absolute' }}>
          {t('action.assign.select_box_title')}
        </Title>
      </Box>

      <Container className={classes.formContainer} mx='sm' mt={'md'}>
        <SelectStyled
          value={scenario}
          onChange={handleChangeAssignSetting}
          placeholder='Pick one'
          data={[
            {
              value: ScenarioAssignType.TEAM,
              label: t('action.assign.select_box.option_1'),
            },
            {
              value: ScenarioAssignType.TEAM_AND_OPERATOR,
              label: t('action.assign.select_box.option_2'),
            },
            {
              value: ScenarioAssignType.PREVIOUS_ASSIGNEE,
              label: t('action.assign.select_box.option_3'),
            },
          ]}
        />
        {shouldShowButtonSelect && (
          <>
            <Button
              mt='md'
              mb={'sm'}
              variant='default'
              sx={{ color: 'gray.7' }}
              fw={400}
              onClick={buttonAction}
            >
              {labelButton}
            </Button>
            {errorState && (
              <Text c='red.5' size={rem(12)} fw={400}>
                {errorState === ScenarioAssignType.TEAM
                  ? t(`action.error.missing_team_id`)
                  : t('action.error.missing_operator_id')}
              </Text>
            )}
          </>
        )}
        {dataForActionAssign ? (
          <RenderAssignedActionData dataForActionAssign={dataForActionAssign} />
        ) : (
          <Text c='red' size={rem(12)} fz={12}>
            {t('detail.action.message.missing_params')}
          </Text>
        )}
        <Flex
          p={10}
          mt={10}
          gap={5}
          align={'flex-start'}
          justify={'flex-start'}
          sx={{ backgroundColor: theme.colors.blue[0], borderRadius: theme.radius.sm }}
        >
          <Text sx={{ width: 20 }}>
            <IconInfoCircle color={theme.colors.blue[8]} size={22} stroke={2} />
          </Text>
          <Text ml={5} fz={12} fw={400} c={theme.colors.blue[8]}>
            {t('crm_automation.action_node.warning')}
          </Text>
        </Flex>
      </Container>

      {scenario === ScenarioAssignType.TEAM && (
        <>
          <Title size={14} mt={'lg'} mb={'md'} ml={30} fw={700}>
            {t('action.assign_method.title')}
          </Title>
          <Container className={classes.formContainer} mx='sm' mb={'lg'} pt={'0.5rem'}>
            <RadioOption
              onChange={handleSetRuleAssignTeam}
              value={RULES_SELECT_OPERATOR.ROUND_ROBIN}
              text={t('action.assign_method.option_1')}
              tooltip={t('action.assign_method.option_1.tooltip')}
              checked={
                RULES_SELECT_OPERATOR.ROUND_ROBIN ===
                (dataForActionAssign as NewAssignTeamData)?.data?.rule
              }
            />
            <RadioOption
              onChange={handleSetRuleAssignTeam}
              value={RULES_SELECT_OPERATOR.RANDOM}
              text={t('action.assign_method.option_2')}
              tooltip={t('action.assign_method.option_2.tooltip')}
              checked={
                RULES_SELECT_OPERATOR.RANDOM ===
                (dataForActionAssign as NewAssignTeamData)?.data?.rule
              }
            />
            <RadioOption
              onChange={handleSetRuleAssignTeam}
              value={RULES_SELECT_OPERATOR.LEAST_BUSY}
              text={t('action.assign_method.option_3')}
              tooltip={t('action.assign_method.option_3.tooltip')}
              checked={
                RULES_SELECT_OPERATOR.LEAST_BUSY ===
                (dataForActionAssign as NewAssignTeamData)?.data?.rule
              }
            />
            <RadioOption
              onChange={handleSetRuleAssignTeam}
              value={RULES_SELECT_OPERATOR.NO_SPECIFIED}
              text={t('action.assign_method.option_5')}
              tooltip={t('action.assign_method.option_5.tooltip')}
              checked={
                RULES_SELECT_OPERATOR.NO_SPECIFIED ===
                (dataForActionAssign as NewAssignTeamData)?.data?.rule
              }
            />
          </Container>
        </>
      )}

      {showMaxConversationTeamHandleSession && (
        <>
          <Title size={14} mt={'lg'} mb={'md'} ml={30} fw={700}>
            {t('action.assign_team.max_conversation.title')}
          </Title>
          <Container className={classes.formContainer} mx='sm' mb={'lg'} pt={'0.5rem'}>
            <Checkbox
              mx={0}
              mt='sm'
              mb={'sm'}
              color={'navy.0'}
              label={
                <Text fz={14} fw={400} c='gray.7'>
                  {t('action.assign_team.max_conversation.description')}
                </Text>
              }
              checked={!!(dataForActionAssign as NewAssignTeamData)?.data?.maxConversation}
              onChange={(e) => toggleEnableMaxConversationTeamHandler(e)}
            />
            <Flex direction={'row'} align={'center'}>
              <NumberInputStyled
                disabled={isNaN((dataForActionAssign as NewAssignTeamData)?.data?.maxConversation)}
                min={1}
                maw={70}
                ml={30}
                size='xs'
                mr={'xs'}
                defaultValue={1}
                value={(dataForActionAssign as NewAssignTeamData).data?.maxConversation || 1}
                onChange={(val) => handleSetMaxConversationTeamHandler(val)}
              />
              <Text fw={400} size={rem(12)} c='gray.7'>
                {t('action.assign_team.max_conversation.limit_label')}
              </Text>
            </Flex>
          </Container>
        </>
      )}
      {scenario === ScenarioAssignType.TEAM && (
        <>
          <Title size={14} mt={'lg'} mb={'md'} ml={30} fw={700}>
            {t('action.assign_method.option_4.title')}
          </Title>
          <Container className={classes.formContainer} mx='sm' mb={'lg'} pt={'0.5rem'}>
            <Checkbox
              mx={0}
              mt='sm'
              mb={'sm'}
              color={'navy.0'}
              label={
                <Text fz={14} fw={400} c='gray.7'>
                  {t('action.assign_method.option_4')}
                </Text>
              }
              checked={!!(dataForActionAssign as NewAssignTeamData)?.data?.fifo}
              description={t('action.assign_method.option_4.tooltip')}
              onChange={(e) => handleToggleFifoAssignment(e)}
            />
          </Container>
        </>
      )}
      {scenario === ScenarioAssignType.PREVIOUS_ASSIGNEE && (
        <Checkbox
          mx='lg'
          mt='sm'
          color={'navy.0'}
          label={
            <Text fz={14} fw={400} c='gray.7'>
              {t('action.assign.select_box.option_3.checkbox_title')}
            </Text>
          }
          checked={
            (dataForActionAssign as NewAssignPreviousAssigneeData)?.data
              ?.doNotAssignIfAssigneeNotFound
          }
          onChange={(e) => toggleNoNeedOperatorIfNot(e)}
        />
      )}
    </>
  );
}

function RenderAssignedActionData({
  dataForActionAssign,
}: {
  dataForActionAssign: ActionAssignData;
}) {
  const { t, teamList, listOperators } = useAutomationDetailContext();

  const teamName = useMemo(() => {
    if (dataForActionAssign.scenario === ScenarioAssignType.PREVIOUS_ASSIGNEE) return '';
    if (teamList.length) {
      return teamList.find((team) => team.id === dataForActionAssign.data?.teamId)?.name || '';
    }
    return '';
  }, [dataForActionAssign.scenario, dataForActionAssign.data, teamList]);

  const userName = useMemo(() => {
    if (dataForActionAssign.scenario === ScenarioAssignType.PREVIOUS_ASSIGNEE) return '';
    if (
      (dataForActionAssign as NewAssignTeamOperatorData)?.data?.operatorId ===
      NO_SPECIFIED_OPERATOR_ID
    )
      return t('detail.action.unspecified_assignee');
    if (listOperators.length) {
      return listOperators.find(
        (operator) =>
          operator.id === (dataForActionAssign as NewAssignTeamOperatorData)?.data?.operatorId
      )?.name;
    }
    return '';
  }, [dataForActionAssign, listOperators, t]);

  if (dataForActionAssign.scenario === ScenarioAssignType.PREVIOUS_ASSIGNEE) return null;

  return (
    <Container pl={0} pr={0}>
      <Text fw={400} fz={12} c='gray.6'>
        {t('detail.action.selected_info.title')}{' '}
        <span style={{ fontWeight: 700 }}>
          {dataForActionAssign.scenario === ScenarioAssignType.TEAM ? teamName : userName}
        </span>
      </Text>
      {/* <Text fw={700} color="gray.6" size={rem(12)} lineClamp={1} title={teamName}>
                {t('detail.action.selected_info.team', { name: teamName })}
            </Text>
            <Text fw={700} color="gray.6" size={rem(12)} lineClamp={1} title={userName}>
                {t('detail.action.selected_info.assignee', {
                    name: userName,
                })}
            </Text> */}
    </Container>
  );
}

const useStyle = createStyles(() => ({
  customToolTip: {
    '& label .mantine-Tooltip-tooltip': {
      boxShadow: '0 0 2px #a6a6a6',
    },
    '& label .mantine-Tooltip-tooltip .mantine-Tooltip-arrow': {
      boxShadow: '-1px 1px 1px #a6a6a6',
    },
  },
}));

const RadioOption = ({
  checked = false,
  tooltip,
  text,
  value,
  onChange,
}: {
  checked: boolean;
  tooltip: string;
  text: string;
  value: RULES_SELECT_OPERATOR;
  // eslint-disable-next-line no-unused-vars
  onChange: (e: BaseSyntheticEvent) => void;
}) => {
  const { classes } = useStyle();
  return (
    <Radio
      value={value}
      color='navy.0'
      checked={checked}
      onChange={onChange}
      className={classes.customToolTip}
      label={
        <TooltipRight
          label={
            <Text
              size={rem(12)}
              fz={12}
              c='dark'
              style={{
                wordBreak: 'break-word',
                lineBreak: 'auto',
              }}
            >
              {tooltip}
            </Text>
          }
        >
          <Text fz={14} style={{ cursor: 'pointer' }}>
            {text}
          </Text>
        </TooltipRight>
      }
      mt='sm'
    />
  );
};
