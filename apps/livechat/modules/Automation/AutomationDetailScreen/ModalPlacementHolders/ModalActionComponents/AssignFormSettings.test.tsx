import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { fireEvent, render, screen } from '@testing-library/react';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { ActionAssignData, RULES_SELECT_OPERATOR, ScenarioAssignType } from '../../types';
import { AssignFormSettings } from './AssignFormSettings';

// Mock the context
jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

// Mock the useStyleModal hook
jest.mock('../useStyleModal', () => ({
  useStyleModal: () => ({
    classes: {
      formContainer: 'form-container',
    },
  }),
}));

// Mock the Mantine components
jest.mock('@mantine/core', () => {
  const rem = (value: number) => `${value}px`;
  return {
    Box: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Button: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
      <button onClick={onClick}>{children}</button>
    ),
    Checkbox: ({
      label,
      checked,
      onChange,
    }: {
      label: React.ReactNode;
      checked?: boolean;
      onChange?: (e: any) => void;
    }) => <input type='checkbox' checked={checked} onChange={onChange} />,
    Container: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Flex: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Radio: ({
      label,
      checked,
      onChange,
    }: {
      label: React.ReactNode;
      checked?: boolean;
      onChange?: (e: any) => void;
    }) => <input type='radio' checked={checked} onChange={onChange} />,
    Text: ({ children }: { children: React.ReactNode }) => <span>{children}</span>,
    Title: ({ children }: { children: React.ReactNode }) => <h2>{children}</h2>,
    rem,
    Select: ({ children, ...props }: any) => <select {...props}>{children}</select>,
    NumberInput: ({ ...props }: any) => <input type='number' {...props} />,
    useMantineTheme: () => ({
      colors: {
        blue: ['#E3F2FD', '#2196F3'],
        gray: ['#F5F5F5', '#9E9E9E', '#757575', '#616161', '#424242', '#212121'],
        navy: ['#E3F2FD', '#BBDEFB', '#90CAF9', '#64B5F6', '#42A5F5', '#2196F3'],
      },
      radius: {
        sm: '4px',
      },
      spacing: {
        md: '16px',
      },
    }),
    MantineProvider: jest.requireActual('@mantine/core').MantineProvider,
  };
});

jest.mock('@mantine/emotion', () => {
  const actual = jest.requireActual('@mantine/emotion');
  return {
    ...actual,
    MantineEmotionProvider: actual.MantineEmotionProvider,
  };
});

function renderWithProviders(ui: React.ReactElement) {
  return render(
    <MantineProvider>
      <MantineEmotionProvider>{ui}</MantineEmotionProvider>
    </MantineProvider>
  );
}

describe('AssignFormSettings', () => {
  const mockHandleDataAssign = jest.fn();
  const mockVisibilityCtrSelectTeamPanel = {
    visible: false,
    open: jest.fn(),
    toggle: jest.fn(),
    close: jest.fn(),
  };
  const mockVisibilityCtrSelectOperatorPanel = {
    visible: false,
    open: jest.fn(),
    toggle: jest.fn(),
    close: jest.fn(),
  };

  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
      teamList: [
        { id: '1', name: 'Team 1' },
        { id: '2', name: 'Team 2' },
      ],
      listOperators: [
        { id: '1', name: 'Operator 1' },
        { id: '2', name: 'Operator 2' },
      ],
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with team scenario', () => {
    const dataForActionAssign: ActionAssignData = {
      scenario: ScenarioAssignType.TEAM,
      data: {
        teamId: '1',
        rule: RULES_SELECT_OPERATOR.ROUND_ROBIN,
      },
    };

    renderWithProviders(
      <AssignFormSettings
        errorState={null}
        dataForActionAssign={dataForActionAssign}
        visibilityCtrSelectTeamPanel={mockVisibilityCtrSelectTeamPanel}
        visibilityCtrSelectOperatorPanel={mockVisibilityCtrSelectOperatorPanel}
        handleDataAssign={mockHandleDataAssign}
      />
    );

    expect(screen.getByText('action.assign.select_box_title')).toBeInTheDocument();
  });

  it('renders with team and operator scenario', () => {
    const dataForActionAssign: ActionAssignData = {
      scenario: ScenarioAssignType.TEAM_AND_OPERATOR,
      data: {
        teamId: '1',
        operatorId: '1',
      },
    };

    renderWithProviders(
      <AssignFormSettings
        errorState={null}
        dataForActionAssign={dataForActionAssign}
        visibilityCtrSelectTeamPanel={mockVisibilityCtrSelectTeamPanel}
        visibilityCtrSelectOperatorPanel={mockVisibilityCtrSelectOperatorPanel}
        handleDataAssign={mockHandleDataAssign}
      />
    );

    expect(screen.getByText('action.assign.select_box_title')).toBeInTheDocument();
  });

  it('renders with previous assignee scenario', () => {
    const dataForActionAssign: ActionAssignData = {
      scenario: ScenarioAssignType.PREVIOUS_ASSIGNEE,
      data: {
        doNotAssignIfAssigneeNotFound: true,
      },
    };

    renderWithProviders(
      <AssignFormSettings
        errorState={null}
        dataForActionAssign={dataForActionAssign}
        visibilityCtrSelectTeamPanel={mockVisibilityCtrSelectTeamPanel}
        visibilityCtrSelectOperatorPanel={mockVisibilityCtrSelectOperatorPanel}
        handleDataAssign={mockHandleDataAssign}
      />
    );

    expect(screen.getByText('action.assign.select_box_title')).toBeInTheDocument();
  });

  it('handles team selection button click', () => {
    const dataForActionAssign: ActionAssignData = {
      scenario: ScenarioAssignType.TEAM,
      data: {
        teamId: '1',
        rule: RULES_SELECT_OPERATOR.ROUND_ROBIN,
      },
    };

    renderWithProviders(
      <AssignFormSettings
        errorState={null}
        dataForActionAssign={dataForActionAssign}
        visibilityCtrSelectTeamPanel={mockVisibilityCtrSelectTeamPanel}
        visibilityCtrSelectOperatorPanel={mockVisibilityCtrSelectOperatorPanel}
        handleDataAssign={mockHandleDataAssign}
      />
    );

    const button = screen.getByText('action.button.select_team.title');
    fireEvent.click(button);
    expect(mockVisibilityCtrSelectTeamPanel.toggle).toHaveBeenCalled();
  });

  it('handles team and operator selection button click', () => {
    const dataForActionAssign: ActionAssignData = {
      scenario: ScenarioAssignType.TEAM_AND_OPERATOR,
      data: {
        teamId: '1',
        operatorId: '1',
      },
    };

    renderWithProviders(
      <AssignFormSettings
        errorState={null}
        dataForActionAssign={dataForActionAssign}
        visibilityCtrSelectTeamPanel={mockVisibilityCtrSelectTeamPanel}
        visibilityCtrSelectOperatorPanel={mockVisibilityCtrSelectOperatorPanel}
        handleDataAssign={mockHandleDataAssign}
      />
    );

    const button = screen.getByText('detail.action.button.label.1');
    fireEvent.click(button);
    expect(mockVisibilityCtrSelectOperatorPanel.toggle).toHaveBeenCalled();
  });
});
