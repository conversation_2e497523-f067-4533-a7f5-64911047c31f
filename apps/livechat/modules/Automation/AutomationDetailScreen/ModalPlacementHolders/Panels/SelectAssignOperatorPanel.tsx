import {
  Box,
  Container,
  Divider,
  Flex,
  Group,
  MantineTransition,
  Radio,
  ScrollArea,
  Text,
  Title,
  Transition,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IOperatorProfile, ITeamDetail, Status, TeamItem as TeamItemType } from '@resola-ai/models';
import { IconX } from '@tabler/icons-react';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ImageStatus } from '../../../../../components/ImageStatus';
import { Operator } from '../../../../Tools/models/types';
import { NO_SPECIFIED_OPERATOR_ID } from '../../constant';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { IconSearchStyled, TextInputStyled } from '../CustomFormElements/CustomFormElements';

const useStyle = createStyles((theme) => ({
  containerTop: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 0,
    paddingLeft: 0,
    paddingRight: 0,
    borderLeft: `1px solid ${theme.colors.gray[4]}`,
  },
  iconX: {
    position: 'absolute',
    right: '10px',
    top: '10px',
    color: theme.colors.dark[3],
    cursor: 'pointer',
  },
  iconSearch: {
    cursor: 'pointer',
    color: theme.colors.violet[6],
  },
  flexBody: {
    flexGrow: 1,
    flexDirection: 'row',
    overflow: 'hidden',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  boxHover: {
    '&:hover': {
      backgroundColor: '#F8F9FA',
    },
  },
}));

const modalPlacementTransition: MantineTransition = {
  in: { opacity: 1, transform: `translateX(0px)`, zIndex: 0 },
  out: { opacity: 0, transform: `translateX(-10%)`, zIndex: 0 },
  common: { transformOrigin: 'right center 0px' },
  transitionProperty: 'transform, opacity, z-index',
};

const noAssigneeItemFunc = (noAssigneeText) =>
  ({
    id: NO_SPECIFIED_OPERATOR_ID,
    name: noAssigneeText,
    email: '',
    org: {
      id: '',
      name: '',
    },
    orgId: '',
    picture: '',
    roles: [],
    status: Status.Away,
    teams: [],
  }) as IOperatorProfile;

type Props = {
  opened: boolean;
  // eslint-disable-next-line no-unused-vars
  onSelect: (data?: { operator: Operator; teamId: string }) => void;
  teamList: ITeamDetail[];
  operatorList: Operator[];
  selectedOperatorId: Operator['id'] | null;
  selectedTeamId: string | null;
};

export function SelectAssignedOperatorPanel({
  opened,
  onSelect,
  operatorList = [],
  teamList = [],
  selectedTeamId = null,
  selectedOperatorId = null,
}: Props) {
  const initRef = useRef(false);
  const { classes } = useStyle();
  const { t } = useAutomationDetailContext();

  const [selectedOperator, setSelectedOperator] = useState<Operator | null>(
    () => operatorList.find((oper) => oper.id === selectedOperatorId) || null
  );
  // const [isNewOpenState, setIsNewOpenState] = useState(true);

  const [searchOperator, setSearchOperator] = useState<string>('');

  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);

  const onInteraction = useCallback((flag = true) => {
    initRef.current = flag;
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchOperator('');
  }, []);

  const handleClose = useCallback(() => {
    onSelect();
    onInteraction(false);
  }, [onInteraction, onSelect]);

  const handleSelectOperator = useCallback(
    (id: Operator) => {
      setSelectedOperator(id);
      onInteraction();
    },
    [onInteraction]
  );

  const handleSelectTeam = useCallback((teamId: string) => {
    setSelectedOperator(null);
    return setSelectedTeam(teamId);
  }, []);

  const handleInputSearchOperator = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (value === '') {
        handleClearSearch();
        return;
      }
      setSearchOperator(value);
      setSelectedOperator(null);
    },
    [handleClearSearch]
  );

  const operatorListBySearch = useMemo(() => {
    let operators: Operator[] = [...operatorList];
    if (!searchOperator) return operatorList;
    if (searchOperator) {
      operators = operatorList.filter((item) =>
        item.name?.toLowerCase().includes(searchOperator.toLowerCase())
      );
    }
    return operators;
  }, [operatorList, searchOperator]);

  const teamListBySearch = useMemo(() => {
    let operatorIds: string[] = [];
    if (!searchOperator) {
      return teamList.filter((tem) => tem.members.length > 0);
    } else {
      if (operatorListBySearch.length) {
        operatorListBySearch.forEach((oper) => operatorIds.push(oper.id));
        operatorIds = Array.from(new Set(operatorIds));
      }
      const teams = teamList.filter(
        (team) => !!team.members.find((mem) => operatorIds.includes(mem.id))
      );
      return teams;
    }
  }, [operatorListBySearch, searchOperator, teamList]);

  const operatorListByTeamSelect = useMemo(() => {
    let operators = [...operatorListBySearch];
    if (selectedTeam) {
      let teamMemberIds: string[] = [];
      const team = teamList.find((team) => team.id === selectedTeam);
      team && (teamMemberIds = team.members.map((mem) => mem.id));
      operators = operators.filter((operator) => teamMemberIds.includes(operator.id));
    }
    return operators;
  }, [operatorListBySearch, selectedTeam, teamList]);

  const isNoDataMatched = useMemo(() => {
    return !operatorListBySearch.length;
  }, [operatorListBySearch.length]);

  const countDataMatched = useMemo(() => {
    if (searchOperator) return operatorListBySearch.length;
    return 0;
  }, [operatorListBySearch.length, searchOperator]);

  const showSearchDescription = useMemo(() => {
    return !!searchOperator && !isNoDataMatched;
  }, [isNoDataMatched, searchOperator]);

  useEffect(() => {
    if (teamListBySearch.length) {
      setSelectedTeam(teamListBySearch?.[0].id);
    }
  }, [teamListBySearch]);

  useEffect(() => {
    if (selectedOperator && selectedTeam && initRef.current) {
      onSelect({ operator: selectedOperator, teamId: selectedTeam });
      setTimeout(() => {
        onInteraction(false);
        setSelectedOperator(null);
      }, 300);
    }
  }, [selectedOperator, selectedTeam, onSelect, onInteraction, handleSelectOperator]);

  useEffect(() => {
    if (opened && !initRef.current) {
      if (selectedTeamId) {
        setSelectedTeam(() => selectedTeamId);
      }
      selectedOperatorId &&
        setSelectedOperator(
          () =>
            operatorList.find((oper) => oper.id === selectedOperatorId) ||
            noAssigneeItemFunc(t('detail.action.unspecified_assignee'))
        );
    }
  }, [selectedTeamId, selectedOperatorId, opened, t, operatorList, onInteraction]);

  return (
    <Transition
      mounted={opened}
      transition={modalPlacementTransition}
      duration={120}
      timingFunction='ease'
    >
      {(styles) => (
        <Container maw={583} miw={583} className={classes.containerTop} style={styles}>
          <Container mt={20} style={{ marginLeft: 'unset', marginRight: 'unset' }}>
            <IconX className={classes.iconX} onClick={handleClose} />
            <Title order={3} size={rem(20)} mb={'lg'}>
              {t('detail.action.panel_assign.title')}
            </Title>
            <Text c='gray.7' fw={400} size={rem(14)} mb={'lg'}>
              {t('detail.action.panel_assign.description')}
            </Text>
            <TextInputStyled
              mb={'sm'}
              maw={300}
              leftSection={<IconSearchStyled />}
              value={searchOperator}
              onChange={handleInputSearchOperator}
              placeholder={t('detail.action.panel_assign.search_placeholder')}
              rightSection={
                !!searchOperator ? (
                  <Text fz={14} c='navy.0' onClick={handleClearSearch}>
                    {t('detail.action.panel_assign.clear_search')}
                  </Text>
                ) : null
              }
              rightSectionWidth={60}
            />
            <Group
              sx={{
                display: showSearchDescription ? 'block' : 'none',
              }}
              mb={'sm'}
            >
              <Box>
                <Text>
                  {t('detail.action.search_count_message', {
                    search: searchOperator,
                    number: countDataMatched,
                  })}
                </Text>
              </Box>
            </Group>
          </Container>
          <Divider />
          <Flex direction={'row'} w={'100%'} h={35}>
            <Text
              size={rem(16)}
              fw={700}
              c='gray.7'
              mih={35}
              pl={10}
              w={206}
              sx={{ borderRight: '1px solid #CED4DA' }}
              style={{ display: 'flex', alignItems: 'center' }}
            >
              {t('team_panel.team_title')}
            </Text>
            <Text
              size={rem(16)}
              fw={700}
              c='gray.7'
              mih={35}
              pl={10}
              style={{ display: 'flex', alignItems: 'center' }}
            >
              {t('team_panel.operator_title')}
            </Text>
          </Flex>

          <Flex className={classes.flexBody}>
            {!isNoDataMatched ? (
              <>
                <TeamList
                  teamData={teamListBySearch}
                  teamIdSelected={selectedTeam}
                  onSelectTeam={handleSelectTeam}
                />
                <OperatorList
                  operatorData={operatorListByTeamSelect}
                  selectedOperator={selectedOperator}
                  onSelectOperator={handleSelectOperator}
                  noAssigneeText={t('detail.action.unspecified_assignee')}
                />
              </>
            ) : (
              <Text pl={12} fz={14}>
                {t('detail.action.no_result_search_operator')}
              </Text>
            )}
          </Flex>
        </Container>
      )}
    </Transition>
  );
}

type TeamListProps = {
  teamData: TeamItemType[];
  teamIdSelected: string | null;
  // eslint-disable-next-line no-unused-vars
  onSelectTeam: (teamId) => void;
};

function TeamList({ teamData = [], teamIdSelected = null, onSelectTeam }: TeamListProps) {
  const { t } = useAutomationDetailContext();

  const handleSelectTeam = useCallback(
    (id: TeamItemType['id']) => {
      onSelectTeam(id);
    },
    [onSelectTeam]
  );

  if (!teamData || !teamData?.length) {
    return (
      <Flex
        gap={'xs'}
        direction={'column'}
        align={'flex-start'}
        justify={'flex-start'}
        sx={{
          padding: '5px 10px 5px 0',
          width: '210px',
          height: '100%',
        }}
      >
        {t('No Data')}
      </Flex>
    );
  }

  return (
    <ScrollArea sx={{ width: '210px', height: '100%', borderRight: '1px solid #CED4DA' }}>
      <Flex
        gap={'xs'}
        direction={'column'}
        align={'flex-start'}
        justify={'flex-start'}
        sx={{
          width: '100%',
          padding: '5px 10px 5px 0',
        }}
      >
        {teamData.map((teamItem) => {
          return (
            <TeamItem
              key={teamItem.id}
              id={teamItem.id}
              selected={teamItem.id === teamIdSelected}
              name={teamItem.name}
              onClick={handleSelectTeam}
            />
          );
        })}
      </Flex>
    </ScrollArea>
  );
}

type TeamItemProps = {
  name: string;
  id: string;
  // eslint-disable-next-line no-unused-vars
  onClick: (id: string) => void;
  selected?: boolean;
};
export default function TeamItem({ name = '', id = '', selected, onClick }: TeamItemProps) {
  const { classes } = useStyle();

  const handleClick = useCallback(() => {
    return onClick(id);
  }, [id, onClick]);
  return (
    <Box
      onClick={handleClick}
      sx={{
        backgroundColor: selected ? '#F8F9FA' : '',
        width: '100%',
        height: '40px',
        padding: '10px 5px 10px 10px',
        fontSize: '15px',
        textOverflow: 'ellipsis',
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        whiteSpace: 'break-spaces',
      }}
      title={name}
      className={classes.boxHover}
    >
      {name}
    </Box>
  );
}

type OperatorListProps = {
  operatorData: Operator[];
  selectedOperator: Operator | null;
  // eslint-disable-next-line no-unused-vars
  onSelectOperator: (id: Operator) => void;
  noAssigneeText: string;
};

function OperatorList({
  operatorData = [],
  selectedOperator = null,
  onSelectOperator,
  noAssigneeText,
}: OperatorListProps) {
  const handleSelectOperator = useCallback(
    (operator: Operator) => {
      onSelectOperator(operator);
    },
    [onSelectOperator]
  );

  const noAssigneeItem = useMemo(() => {
    return noAssigneeItemFunc(noAssigneeText);
  }, [noAssigneeText]);

  if (!operatorData.length) return null;
  return (
    <ScrollArea sx={{ width: '100%', height: '100%', maxWidth: '376px' }}>
      <Flex
        gap={'xs'}
        direction={'column'}
        align={'flex-start'}
        justify={'flex-start'}
        sx={{
          width: '100%',
          padding: '5px 10px 5px 0',
        }}
      >
        <OperatorItem
          operator={noAssigneeItem}
          selected={NO_SPECIFIED_OPERATOR_ID === selectedOperator?.id}
          onClick={() => handleSelectOperator(noAssigneeItem)}
        />
        {operatorData.map((operator, index) => (
          <OperatorItem
            key={operator.id + index}
            operator={operator}
            selected={operator.id === selectedOperator?.id}
            onClick={() => handleSelectOperator(operator)}
          />
        ))}
      </Flex>
    </ScrollArea>
  );
}

type OperatorItemProps = {
  operator: Operator;
  selected: boolean;
  // eslint-disable-next-line no-unused-vars
  onClick: (id: string) => void;
};

function OperatorItem({ operator, selected, onClick }: OperatorItemProps) {
  const { id = null, name = null, picture = null, status = Status.Available } = operator;
  const { classes } = useStyle();
  const handleClick = useCallback(() => {
    return onClick(id);
  }, [id, onClick]);

  return (
    <Box
      onClick={handleClick}
      sx={{
        backgroundColor: selected ? '#F8F9FA' : '',
        width: '100%',
        height: '40px',
        padding: '10px 5px 10px 10px',
        cursor: 'pointer',
      }}
      className={classes.boxHover}
    >
      <Radio
        sx={{ position: 'relative', cursor: 'pointer' }}
        label={<ImageAndName name={name} picture={picture} status={status} />}
        checked={selected}
        onChange={handleClick}
        color='navy.0'
      />
    </Box>
  );
}

type ImageAndNameProps = {
  name: string;
  picture?: string;
  status: Status;
};

function ImageAndName({ name, picture, status }: ImageAndNameProps) {
  return (
    <Box
      display={'flex'}
      sx={{
        position: 'absolute',
        justifyContent: 'center',
        alignItems: 'center',
        top: '-10px',
      }}
    >
      <ImageStatus picture={picture} status={status} />
      <Text fz={14}>{name}</Text>
    </Box>
  );
}
