import {
  Box,
  Container,
  Divider,
  Flex,
  Group,
  MantineTransition,
  ScrollArea,
  Text,
  Title,
  Transition,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { ITeamDetail, TeamItem as TeamItemType } from '@resola-ai/models';
import { IconCheck, IconX } from '@tabler/icons-react';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { IconSearchStyled, TextInputStyled } from '../CustomFormElements/CustomFormElements';

const useStyle = createStyles((theme) => ({
  containerTop: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 0,
    paddingLeft: 0,
    paddingRight: 0,
    borderLeft: `1px solid ${theme.colors.gray[4]}`,
  },
  iconX: {
    position: 'absolute',
    right: '10px',
    top: '10px',
    color: theme.colors.dark[3],
    cursor: 'pointer',
  },
  iconSearch: {
    cursor: 'pointer',
    color: theme.colors.violet[6],
  },
  flexBody: {
    flexGrow: 1,
    flexDirection: 'column',
    overflow: 'hidden',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  boxHover: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#F8F9FA',
    },
  },
}));

const modalPlacementTransition: MantineTransition = {
  in: { opacity: 1, transform: `translateX(0px)`, zIndex: 0 },
  out: { opacity: 0, transform: `translateX(-10%)`, zIndex: 0 },
  common: { transformOrigin: 'right center 0px' },
  transitionProperty: 'transform, opacity, z-index',
};

type Props = {
  opened: boolean;
  // eslint-disable-next-line no-unused-vars
  onSelect: (data?: { teamId: string }) => void;
  teamList: ITeamDetail[];
  selectedTeamId: string | null;
};

export default function SelectAssignTeamPanel({
  opened,
  onSelect,
  teamList = [],
  selectedTeamId = null,
}: Props) {
  const { classes } = useStyle();
  const { t } = useAutomationDetailContext();

  const [searchOperator, setSearchOperator] = useState<string>('');

  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);

  const initRef = useRef(false);

  const onInteraction = useCallback((flag = true) => {
    initRef.current = flag;
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchOperator('');
  }, []);

  const handleSelectTeam = useCallback(
    (teamId: string) => {
      setSelectedTeam(teamId);
      onSelect({ teamId });
      onInteraction(false);
    },
    [onInteraction, onSelect]
  );

  const handleInputSearchOperator = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (value === '') {
        handleClearSearch();
        return;
      }
      setSearchOperator(value);
    },
    [handleClearSearch]
  );

  const teamListBySearch = useMemo(() => {
    if (!searchOperator) {
      return teamList;
    } else {
      const searchLower = searchOperator.toLowerCase();
      const teams = teamList.filter((team) => team.name.toLowerCase().includes(searchLower));
      return teams;
    }
  }, [searchOperator, teamList]);

  const isNoDataMatched = useMemo(() => {
    return !teamListBySearch.length;
  }, [teamListBySearch.length]);

  useEffect(() => {
    if (opened && !initRef.current) {
      setSelectedTeam(selectedTeamId);
      return;
    }
  }, [opened, selectedTeamId]);

  useEffect(() => {
    if (!opened) {
      setSelectedTeam(null);
      onInteraction(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [opened]);

  return (
    <Transition
      mounted={opened}
      transition={modalPlacementTransition}
      duration={120}
      timingFunction='ease'
    >
      {(styles) => (
        <Container maw={583} className={classes.containerTop} style={styles}>
          <Container mt={20}>
            <IconX className={classes.iconX} onClick={() => onSelect()} />
            <Title order={3} size={rem(20)} mb={'md'}>
              {t('team_panel.title')}
            </Title>
            <TextInputStyled
              mb={'sm'}
              maw={300}
              leftSection={<IconSearchStyled />}
              value={searchOperator}
              onChange={handleInputSearchOperator}
              placeholder={t('detail.action.panel_assign.search_placeholder')}
              rightSection={
                !!searchOperator ? (
                  <Text fz={14} c='navy.0' onClick={handleClearSearch}>
                    {t('detail.action.panel_assign.clear_search')}
                  </Text>
                ) : null
              }
              rightSectionWidth={60}
            />
          </Container>
          <Divider />
          <Flex className={classes.flexBody}>
            <Text
              size={rem(16)}
              fw={700}
              color='gray.7'
              mih={35}
              pl={10}
              style={{ display: 'flex', alignItems: 'center' }}
            >
              {t('team_panel.team_title')}
            </Text>
            {!isNoDataMatched ? (
              <>
                <TeamList
                  teamData={teamListBySearch}
                  teamIdSelected={selectedTeam}
                  onSelectTeam={handleSelectTeam}
                />
              </>
            ) : (
              <Text pl={12} c='gray.7' size={rem(14)}>
                {t('detail.action.no_result_search_operator')}
              </Text>
            )}
          </Flex>
        </Container>
      )}
    </Transition>
  );
}

type TeamListProps = {
  teamData: TeamItemType[];
  teamIdSelected: string | null;
  // eslint-disable-next-line no-unused-vars
  onSelectTeam: (teamId) => void;
};

function TeamList({ teamData = [], teamIdSelected = null, onSelectTeam }: TeamListProps) {
  const { t } = useAutomationDetailContext();

  const handleSelectTeam = useCallback(
    (id: TeamItemType['id']) => {
      onSelectTeam(id);
    },
    [onSelectTeam]
  );

  if (!teamData || !teamData?.length) {
    return (
      <Flex
        gap={'xs'}
        direction={'column'}
        align={'flex-start'}
        justify={'flex-start'}
        sx={{
          padding: '5px 10px 5px 0',
          // width: '210px',
          height: '100%',
        }}
      >
        {t('No Data')}
      </Flex>
    );
  }

  return (
    <ScrollArea sx={{ width: '100%', height: '100%' }}>
      <Flex
        gap={'xs'}
        direction={'column'}
        align={'flex-start'}
        justify={'flex-start'}
        sx={{
          width: '100%',
          padding: '5px 0px 5px 0',
        }}
      >
        {teamData.map((teamItem) => {
          return (
            <TeamItem
              key={teamItem.id}
              id={teamItem.id}
              selected={teamItem.id === teamIdSelected}
              name={teamItem.name}
              onClick={handleSelectTeam}
            />
          );
        })}
      </Flex>
    </ScrollArea>
  );
}

type TeamItemProps = {
  name: string;
  id: string;
  // eslint-disable-next-line no-unused-vars
  onClick: (id: string) => void;
  selected?: boolean;
};
function TeamItem({ name = '', id = '', selected, onClick }: TeamItemProps) {
  const { classes } = useStyle();

  const handleClick = useCallback(() => {
    return onClick(id);
  }, [id, onClick]);
  return (
    <Box
      onClick={handleClick}
      sx={{
        width: '100%',
        height: '40px',
        display: 'flex',
        fontSize: '15px',
        alignItems: 'center',
        textOverflow: 'ellipsis',
        whiteSpace: 'break-spaces',
        justifyContent: 'flex-start',
        padding: '10px 5px 10px 10px',
        backgroundColor: selected ? '#F8F9FA' : '',
      }}
      title={name}
      className={classes.boxHover}
    >
      <Group justify={selected ? 'space-between' : 'flex-start'} style={{ flexGrow: 1 }} pr={'xs'}>
        <Text fz={15} lineClamp={1} truncate='end' fw={500}>
          {name}
        </Text>
        {selected && <IconCheck size={18} color='#1D2088' />}
      </Group>
    </Box>
  );
}
