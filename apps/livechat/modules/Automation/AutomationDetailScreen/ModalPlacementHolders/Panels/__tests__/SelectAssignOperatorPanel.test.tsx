import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { Status } from '@resola-ai/models';
import { IOperatorProfile, ITeamDetail, ITeamMemberDetail } from '@resola-ai/models';
import { fireEvent, render, screen } from '@testing-library/react';
import { NO_SPECIFIED_OPERATOR_ID } from '../../../constant';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { SelectAssignedOperatorPanel } from '../SelectAssignOperatorPanel';

// Mock ResizeObserver for Mantine ScrollArea
beforeAll(() => {
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

// Mock the context
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

describe('SelectAssignedOperatorPanel', () => {
  const mockOnSelect = jest.fn();
  const mockTeamList: ITeamDetail[] = [
    {
      id: '1',
      name: 'Team 1',
      members: [
        { id: '1', name: 'Operator 1', role: 'member' },
        { id: '2', name: 'Operator 2', role: 'member' },
      ] as ITeamMemberDetail[],
      org: 'org1',
      createAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'Team 2',
      members: [{ id: '3', name: 'Operator 3', role: 'member' }] as ITeamMemberDetail[],
      org: 'org1',
      createAt: new Date().toISOString(),
    },
  ];
  const mockOperatorList: IOperatorProfile[] = [
    {
      id: '1',
      name: 'Operator 1',
      status: Status.Available,
      email: '<EMAIL>',
      org: { id: 'org1', name: 'Org 1' },
      orgId: 'org1',
      picture: '',
      roles: [],
      teams: [],
    },
    {
      id: '2',
      name: 'Operator 2',
      status: Status.Available,
      email: '<EMAIL>',
      org: { id: 'org1', name: 'Org 1' },
      orgId: 'org1',
      picture: '',
      roles: [],
      teams: [],
    },
    {
      id: '3',
      name: 'Operator 3',
      status: Status.Available,
      email: '<EMAIL>',
      org: { id: 'org1', name: 'Org 1' },
      orgId: 'org1',
      picture: '',
      roles: [],
      teams: [],
    },
  ];

  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider
          theme={{
            colors: {
              navy: [
                '#1a1a1a',
                '#2a2a2a',
                '#3a3a3a',
                '#4a4a4a',
                '#5a5a5a',
                '#6a6a6a',
                '#7a7a7a',
                '#8a8a8a',
                '#9a9a9a',
                '#aaaaaa',
              ],
            },
          }}
        >
          {ui}
        </MantineProvider>
      </MantineEmotionProvider>
    );
  };

  it('renders without crashing', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId={null}
        selectedOperatorId={null}
      />
    );
    expect(screen.getByText('detail.action.panel_assign.title')).toBeInTheDocument();
  });

  it('displays team list correctly', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId={null}
        selectedOperatorId={null}
      />
    );
    expect(screen.getByText('Team 1')).toBeInTheDocument();
    expect(screen.getByText('Team 2')).toBeInTheDocument();
  });

  it('filters operators based on selected team', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId='1'
        selectedOperatorId={null}
      />
    );
    expect(screen.getByText('Operator 1')).toBeInTheDocument();
    expect(screen.getByText('Operator 2')).toBeInTheDocument();
    expect(screen.queryByText('Operator 3')).not.toBeInTheDocument();
  });

  it('handles search functionality', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId={null}
        selectedOperatorId={null}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      'detail.action.panel_assign.search_placeholder'
    );
    fireEvent.change(searchInput, { target: { value: 'Operator 1' } });
    expect(screen.getByText('Operator 1')).toBeInTheDocument();
    expect(screen.queryByText('Operator 2')).not.toBeInTheDocument();
  });

  it('shows no results message when search has no matches', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId={null}
        selectedOperatorId={null}
      />
    );

    const searchInput = screen.getByPlaceholderText(
      'detail.action.panel_assign.search_placeholder'
    );
    fireEvent.change(searchInput, { target: { value: 'NonExistentOperator' } });
    expect(screen.getByText('detail.action.no_result_search_operator')).toBeInTheDocument();
  });

  it('handles team selection', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId={null}
        selectedOperatorId={null}
      />
    );

    // Click a team
    const team1 = screen.getByText('Team 1');
    fireEvent.click(team1);
    // onSelect should NOT be called yet
    expect(mockOnSelect).not.toHaveBeenCalled();

    // Click an operator
    const operator1 = screen.getByText('Operator 1');
    fireEvent.click(operator1);
    // Now onSelect should be called
    expect(mockOnSelect).toHaveBeenCalled();
  });

  it('handles operator selection', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId='1'
        selectedOperatorId={null}
      />
    );

    const operator1 = screen.getByText('Operator 1');
    fireEvent.click(operator1);
    expect(mockOnSelect).toHaveBeenCalled();
  });

  it('shows unspecified assignee option', () => {
    renderWithProviders(
      <SelectAssignedOperatorPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        operatorList={mockOperatorList}
        selectedTeamId='1'
        selectedOperatorId={NO_SPECIFIED_OPERATOR_ID}
      />
    );

    expect(screen.getByText('detail.action.unspecified_assignee')).toBeInTheDocument();
  });
});
