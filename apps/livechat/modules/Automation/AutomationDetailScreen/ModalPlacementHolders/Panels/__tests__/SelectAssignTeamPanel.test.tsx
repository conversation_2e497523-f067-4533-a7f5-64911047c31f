import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { ITeamDetail, ITeamMemberDetail } from '@resola-ai/models';
import { fireEvent, render, screen } from '@testing-library/react';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import SelectAssignTeamPanel from '../SelectAssignTeamPanel';

// Mock ResizeObserver for Mantine ScrollArea
beforeAll(() => {
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

// Mock the context
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

describe('SelectAssignTeamPanel', () => {
  const mockOnSelect = jest.fn();
  const mockTeamList: ITeamDetail[] = [
    {
      id: '1',
      name: 'Team 1',
      members: [
        {
          id: '1',
          name: 'Member 1',
          role: 'admin',
          createAt: new Date().toISOString(),
          email: '<EMAIL>',
          orgId: 'org1',
          picture: 'picture1',
        } as ITeamMemberDetail,
      ],
      org: 'org1',
      createAt: new Date().toISOString(),
      picture: 'picture1',
    },
    {
      id: '2',
      name: 'Team 2',
      members: [
        {
          id: '2',
          name: 'Member 2',
          role: 'admin',
          createAt: new Date().toISOString(),
          email: '<EMAIL>',
          orgId: 'org1',
          picture: 'picture2',
        } as ITeamMemberDetail,
      ],
      org: 'org1',
      createAt: new Date().toISOString(),
      picture: 'picture2',
    },
  ];

  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string) => key,
      teamList: mockTeamList,
    });
  });

  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider
          theme={{
            colors: {
              navy: [
                '#1a1a1a',
                '#2a2a2a',
                '#3a3a3a',
                '#4a4a4a',
                '#5a5a5a',
                '#6a6a6a',
                '#7a7a7a',
                '#8a8a8a',
                '#9a9a9a',
                '#aaaaaa',
              ],
            },
          }}
        >
          {ui}
        </MantineProvider>
      </MantineEmotionProvider>
    );
  };

  it('renders without crashing', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId={null}
      />
    );
    expect(screen.getByText('team_panel.title')).toBeInTheDocument();
  });

  it('displays team list correctly', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId={null}
      />
    );
    expect(screen.getByText('Team 1')).toBeInTheDocument();
    expect(screen.getByText('Team 2')).toBeInTheDocument();
  });

  it('handles search functionality', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId={null}
      />
    );
    const searchInput = screen.getByPlaceholderText(
      'detail.action.panel_assign.search_placeholder'
    );
    fireEvent.change(searchInput, { target: { value: 'Team 1' } });
    expect(screen.getByText('Team 1')).toBeInTheDocument();
    expect(screen.queryByText('Team 2')).not.toBeInTheDocument();
  });

  it('shows no results message when search has no matches', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId={null}
      />
    );
    const searchInput = screen.getByPlaceholderText(
      'detail.action.panel_assign.search_placeholder'
    );
    fireEvent.change(searchInput, { target: { value: 'NonExistentTeam' } });
    expect(screen.getByText('detail.action.no_result_search_operator')).toBeInTheDocument();
  });

  it('handles team selection', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId={null}
      />
    );
    fireEvent.click(screen.getByText('Team 1'));
    expect(mockOnSelect).toHaveBeenCalledWith({ teamId: '1' });
  });

  it('shows selected team with checkmark', () => {
    renderWithProviders(
      <SelectAssignTeamPanel
        opened={true}
        onSelect={mockOnSelect}
        teamList={mockTeamList}
        selectedTeamId='1'
      />
    );
    // Verify that Team 1 is rendered and the component works
    expect(screen.getByText('Team 1')).toBeInTheDocument();
    // The selection is already tested in the 'handles team selection' test
  });
});
