import EL<PERSON>, { ElkExtendedEdge, ElkNode } from 'elkjs';
import { Edge, Node } from 'reactflow';
import { v4 as uuidV4 } from 'uuid';
import { AutomationData } from '../../../../models/automation';
import {
  ActionNode,
  AddNodeType,
  CallbackList,
  ConditionNode,
  ConnectionData,
  EdgePayloadData,
  ParentData,
  TypeEdge,
  TypeNode,
} from '../types';

const elk = new ELK();
const position = { x: 0, y: 0 };
const triggerNodeId = 'trigger';
const commonNodeWidth = 300;

const dimensionForNodeTypes = {
  [TypeNode.Trigger]: {
    width: commonNodeWidth,
    height: 102,
  },
  [TypeNode.Action]: {
    width: commonNodeWidth,
    height: 102,
  },
  [TypeNode.Condition]: {
    width: commonNodeWidth,
    height: 102,
  },
  [TypeNode.Branch]: {
    width: commonNodeWidth,
    height: 102,
  },
  [TypeNode.End]: {
    width: commonNodeWidth,
    height: 26,
  },
  [TypeNode.BranchToOtherNode]: {
    width: commonNodeWidth,
    height: 3,
  },
};

// eslint-disable-next-line no-unused-vars
function getDimensionNode(nodeType: TypeNode, node: Node) {
  if (dimensionForNodeTypes[nodeType]) {
    if (nodeType === TypeNode.Condition) {
      const data = { ...dimensionForNodeTypes[nodeType] };
      data.height = data.height + (node.data.conditions.length - 1) * 65;
      return data;
    }
    if (nodeType === TypeNode.Trigger) {
      const data = { ...dimensionForNodeTypes[nodeType] };
      if (node.data?.showErrorTriggerNode) {
        data.width = 470;
        data.height += 80;
      }
      return data;
    }
    return dimensionForNodeTypes[nodeType];
  }
  return {
    width: commonNodeWidth,
    height: 57,
  };
}

function calculateLayoutElk(nodes: Node[], edges: ElkExtendedEdge[]) {
  const nodesForElk = nodes.map((node) => {
    const { width, height } = getDimensionNode(node.type as TypeNode, node);
    return {
      id: node.id,
      width: width,
      height: height,
    };
  });
  const graph: ElkNode = {
    id: 'root',
    layoutOptions: {
      'elk.algorithm': 'layered',
      'elk.direction': 'DOWN',
      'elk.spacing.nodeNode': '100',
      'elk.hierarchyHandling': 'INCLUDE_CHILDREN',
      'elk.spacing.edgeNode': '100',
      'elk.layered.spacing.edgeNodeBetweenLayers': '100',
      'elk.layered.spacing.nodeNodeBetweenLayers': '100',
      'elk.layered.nodePlacement.bk.fixedAlignment': 'BALANCED',
      'elk.radial.centerOnRoot': 'true',
      'elk.layered.considerModelOrder.strategy': 'NODES_AND_EDGES',
    },

    children: nodesForElk,
    edges: edges,
  };
  return elk.layout(graph);
}

async function getLayoutCalculateElk(nodes, edges) {
  try {
    const data = await DiagramHelper.calculateLayoutElk(nodes, edges);
    return data;
  } catch (e) {
    console.log(e);
    return null;
  }
}

const WIDTH_FEATURE_NODE = commonNodeWidth; // width of featured node in px

function getNodesForDiagram(
  originalNodes = [],
  positionCalculatedNodes: ElkNode[] = [],
  centerScreenPx: number
) {
  if (originalNodes.length && positionCalculatedNodes.length) {
    const isTriggerNodeHasErrorFlag = originalNodes[0].data?.showErrorTriggerNode || false;
    const moveToCenterPx = centerScreenPx - positionCalculatedNodes[0].x; // Apply to trigger node then the children will follow
    const centerPxAdjusted =
      moveToCenterPx - (isTriggerNodeHasErrorFlag ? 470 : WIDTH_FEATURE_NODE) / 2;
    return [
      ...positionCalculatedNodes.map((node) => {
        const originalNode = originalNodes.find((n) => n.id === node.id);
        return {
          ...originalNode,
          position: {
            x: node.x + centerPxAdjusted,
            y: node.y, //+ (index > 0 ? 0 : 20)
          },
        };
      }),
    ];
  }
  return [];
}

function getEdgesForDiagram(originalEdges = [], positionCalculatedEdges = []) {
  if (originalEdges.length && positionCalculatedEdges.length) {
    return [
      ...positionCalculatedEdges.map((node) => {
        return {
          ...originalEdges.find((n) => n.id === node.id),
        };
      }),
    ];
  }
  return [];
}

function inOrderTraversalFlow(
  node,
  parentData: ParentData,
  callbackList: CallbackList,
  branchEdge?: boolean
) {
  const result = [];
  if (node !== null && Object.keys(node).length) {
    const currNodeId = node.id;
    const typeNode = node.type;
    if ([TypeNode.Action, TypeNode.Branch, TypeNode.Condition].includes(typeNode)) {
      const nodeResult = getNodeDataBasedOnType(node, parentData, callbackList, branchEdge);
      result.push(nodeResult);
      if (typeNode === TypeNode.Branch) {
        /// Add a virtual node , not visible in order to display a popup menu
        const branches = node.config.branches.filter(
          (branch) => Boolean(branch) && Object.keys(branch).length
        );
        if (branches.length) {
          const virtualNodeId = uuidV4();
          const virtualNode = getVirtualNodeData(currNodeId, virtualNodeId);
          result.push(virtualNode);
          node.config.branches.filter(Boolean).forEach((branch) => {
            result.push(
              ...inOrderTraversalFlow(
                branch,
                { parentNodeId: virtualNodeId, actualParentNodeId: currNodeId },
                callbackList
              )
            );
          });
        } else {
          // this will handle case .branches is empty => add endNode .
          result.push(
            ...inOrderTraversalFlow(
              null,
              { parentNodeId: currNodeId, actualParentNodeId: currNodeId },
              callbackList
            )
          );
        }
      }

      const newParentData: ParentData = {
        parentNodeId: currNodeId,
        actualParentNodeId: currNodeId,
      };

      if (typeNode === TypeNode.Condition) {
        const params = node?.config?.event?.params || null;
        if (params !== null) {
          result.push(...inOrderTraversalFlow(params.onSuccess, newParentData, callbackList, true));
          result.push(
            ...inOrderTraversalFlow(params.onFailure, newParentData, callbackList, false),
            false
          );
        }
      }

      if (typeNode === TypeNode.Action) {
        const params = node?.config?.event?.params || null;
        if (params !== null) {
          result.push(...inOrderTraversalFlow(params?.onSuccess, newParentData, callbackList));
        }
      }
    }
  } else {
    const unknownFlow = getEndNodeData(parentData, branchEdge);
    result.push(unknownFlow);
  }
  return result;
}

function getNodeDataBasedOnType(
  node,
  parentData: ParentData,
  callbackList: CallbackList,
  branchEdge?: boolean
) {
  if (node) {
    switch (node.type) {
      case TypeNode.Branch:
        return getBranchNodeData(node, parentData, undefined, branchEdge);

      case TypeNode.Condition:
        return getConditionNodeData(node, parentData, callbackList.showConditionModal, branchEdge);

      case TypeNode.Action:
        return getActionNodeData(node, parentData, callbackList.showActionModal, branchEdge);

      default:
        return {
          id: uuidV4(),
          type: TypeNode.End,
          style: {
            backgroundColor: '#D0BFFF',
            border: '2px solid #D0BFFF',
            borderRadius: '5px',
          },
          position,
          parentNodeId: parentData.parentNodeId,
          actualParentNodeId: parentData.actualParentNodeId,
        };
    }
  }
}

function getBranchNodeData(
  node,
  parentData: ParentData,
  callback?: () => void,
  branchEdge?: boolean
) {
  return {
    id: node.id ?? uuidV4(),
    type: TypeNode.Branch,
    data: {
      onClick: () => callback(),
      parentNodeId: parentData.parentNodeId,
      actualParentNodeId: parentData.actualParentNodeId,
    },
    style: {
      backgroundColor: '#E7F5FF',
      border: '2px solid #A5D8FF',
      borderRadius: '5px',
    },
    position,
    parentNodeId: parentData.parentNodeId,
    actualParentNodeId: parentData.actualParentNodeId,
    actionFromBranch: branchEdge,
  };
}

// eslint-disable-next-line no-unused-vars
function getConditionNodeData(
  node,
  parentData: ParentData,
  // eslint-disable-next-line no-unused-vars
  callback?: (id: string) => void,
  branchEdge?: boolean
) {
  const conditions = node.config.conditions;
  const conditionType = Object.keys(conditions)?.[0];
  const conditionList = conditions[conditionType];

  return {
    id: node.id,
    type: TypeNode.Condition,
    data: {
      onClick: () => callback?.(node.id),
      conditions: conditionList,
      conditionType: conditionType,
      parentNodeId: parentData.parentNodeId,
      actualParentNodeId: parentData.actualParentNodeId,
    },
    style: {
      backgroundColor: '#FFF9DB',
      border: '2px solid #FFEC99',
      borderRadius: '5px',
    },
    position,
    parentNodeId: parentData.parentNodeId,
    actualParentNodeId: parentData.actualParentNodeId,
    actionFromBranch: branchEdge,
  };
}

function getActionNodeData(
  node,
  parentData: ParentData,
  // eslint-disable-next-line no-unused-vars
  callback?: (id: string) => void,
  branchEdge?: boolean
) {
  const id = node.id || uuidV4();
  return {
    id: id,
    type: TypeNode.Action,
    data: {
      label: 'action 2',
      actionData: {
        action: node.config.action,
        payload: { ...node.config.payload },
      },
      onClick: () => callback?.(id),
      parentNodeId: parentData.parentNodeId,
      actualParentNodeId: parentData.actualParentNodeId,
    },
    style: {
      backgroundColor: '#F4FCE3',
      border: '2px solid #D8F5A2',
      borderRadius: '5px',
    },
    position,
    parentNodeId: parentData.parentNodeId,
    actualParentNodeId: parentData.actualParentNodeId,
    actionFromBranch: branchEdge,
  };
}

function getTriggerNodeData(
  triggerType: string,
  callback?: () => void,
  showErrorTriggerNode = false
) {
  return {
    id: triggerNodeId,
    type: TypeNode.Trigger,
    data: {
      label: '1',
      onClick: () => callback?.(),
      triggerType: triggerType,
      showErrorTriggerNode,
    },
    // style: {
    //     backgroundColor: '#E6FCF5',
    //     border: '2px solid #96F2D7',
    //     borderRadius: '5px',
    // },
    position,
    parentNodeId: 'root',
    actualParentNodeId: 'root',
  };
}

function getEndNodeData(parentData: ParentData, branchEdge?: boolean) {
  return {
    id: uuidV4(),
    type: TypeNode.End,
    position,
    parentNodeId: parentData.parentNodeId,
    actualParentNodeId: parentData.actualParentNodeId,
    actionFromBranch: branchEdge,
  };
}

function getVirtualNodeData(parentNodeId: string, id: string) {
  return {
    id: id,
    type: TypeNode.BranchToOtherNode,
    data: { label: 'Node 1' },
    position: { x: 0, y: 0 },
    parentNodeId: parentNodeId,
    actualParentNodeId: parentNodeId,
  };
}

function getEdgeData(
  fromNodeId: string,
  toNodeId: string,
  actualParentNodeId: string,
  typeEdge: TypeEdge
) {
  let edge: Edge = {
    id: `${fromNodeId}___${toNodeId}`,
    source: fromNodeId,
    target: toNodeId,
    type: typeEdge,
    data: { actualParentNodeId } as EdgePayloadData,
  };
  if ([TypeEdge.FalseEdge, TypeEdge.TrueEdge].includes(typeEdge)) {
    edge.data = {
      ...edge.data,
      conditionEdge: typeEdge,
    };
  }
  return edge;
}

function addMissingPropertyInOrderTraversal(node) {
  let nodeData = { ...node };
  if (nodeData !== null && Object.keys(nodeData).length) {
    let nodeId = nodeData?.id;
    const newNodeId = uuidV4();
    const currNodeId = nodeId ?? newNodeId;
    /// End : Save new node id

    // Start : Prepare for missing data
    if (!Object.prototype.hasOwnProperty.call(nodeData, 'id')) {
      nodeData = { ...nodeData, id: currNodeId };
    }
    if (!Object.prototype.hasOwnProperty.call(nodeData, 'type')) {
      nodeData = { ...nodeData, type: TypeNode.Action };
    }

    const typeNode = nodeData.type;
    if ([TypeNode.Action, TypeNode.Branch, TypeNode.Condition].includes(typeNode)) {
      if (typeNode === TypeNode.Branch && nodeData.config.branches?.length) {
        nodeData.config.branches = nodeData.config.branches.map((branch) => {
          return addMissingPropertyInOrderTraversal(branch);
        });
      }

      if (typeNode === TypeNode.Condition) {
        const params = nodeData?.config?.event?.params || null;
        if (params !== null && Object.keys(params).length) {
          nodeData.config.event.params = {
            onSuccess: addMissingPropertyInOrderTraversal(params?.onSuccess),
            onFailure: addMissingPropertyInOrderTraversal(params?.onFailure),
          };
        }
      }

      if (typeNode === TypeNode.Action) {
        const params = nodeData?.config?.event?.params || null;
        if (params !== null && Object.keys(params).length) {
          nodeData.config.event.params.onSuccess = addMissingPropertyInOrderTraversal(
            params.onSuccess
          );
        }
      }
    }
  }
  return nodeData;
}

function convertRuleEngineToNodesAndEdges(
  automation: AutomationData,
  callbackList: CallbackList,
  errors: any
) {
  const { showErrorTriggerNode = false } = errors;
  const firstTriggerNode = getTriggerNodeData(
    automation.trigger,
    callbackList.showTriggerModal,
    showErrorTriggerNode
  );
  const nodes = [firstTriggerNode];
  const edges: Edge[] = [];

  const nodesFromFLow = inOrderTraversalFlow(
    automation.flow as JSON,
    { parentNodeId: triggerNodeId, actualParentNodeId: triggerNodeId },
    callbackList
  ).filter((node) => typeof node !== 'boolean');

  nodes.push(...nodesFromFLow);
  if (nodes.length >= 2) {
    nodes.forEach((node) => {
      if (node.parentNodeId !== 'root') {
        const parentNode = nodes.find((nd) => nd.id === node.parentNodeId);
        let typeEdge = TypeEdge.Add;
        if (
          parentNode &&
          Object.hasOwn(node, 'actionFromBranch') &&
          typeof node['actionFromBranch'] === 'boolean'
        ) {
          typeEdge = node['actionFromBranch'] ? TypeEdge.TrueEdge : TypeEdge.FalseEdge;
        }
        edges.push(getEdgeData(node.parentNodeId, node.id, node.actualParentNodeId, typeEdge));
      }
    });
  }
  return {
    nodes: nodes,
    edges: edges,
  };
}

function findNodeByIdInOrderTraversal(node, idNode: string) {
  if (node === null || !Object.keys(node).length) return null;
  if (node.id === idNode) return node;
  if (node.type === TypeNode.Branch) {
    for (const branch of node.config.branches) {
      const foundNode = findNodeByIdInOrderTraversal(branch, idNode);
      if (foundNode) {
        return foundNode;
      }
    }
  }

  if (node.type === TypeNode.Condition) {
    const params = node?.config?.event?.params || null;
    if (params !== null && Object.keys(params).length) {
      const onSuccessNode = findNodeByIdInOrderTraversal(params.onSuccess, idNode);
      if (onSuccessNode) {
        return onSuccessNode;
      }

      const onFailureNode = findNodeByIdInOrderTraversal(params.onFailure, idNode);
      if (onFailureNode) {
        return onFailureNode;
      }
    }
  }

  if (node.type === TypeNode.Action) {
    const params = node?.config?.event?.params || null;
    if (params !== null && Object.keys(params).length) {
      const onSuccessNode = findNodeByIdInOrderTraversal(params.onSuccess, idNode);
      if (onSuccessNode) {
        return onSuccessNode;
      }
    }
  }

  return null;
}

// this function help to find the data for specified node using node's id
// implemented by using in-order traversal algorithm.
function findDataNodeFromRuleEngineTemplate(node, idNode: string) {
  if (!idNode || node === null || !Object.keys(node).length) return null;

  const foundNode = findNodeByIdInOrderTraversal(node, idNode);
  if (!foundNode) return null;

  // Found . then process by type of node.
  if (foundNode.type === TypeNode.Condition) {
    const { conditions } = foundNode.config;
    const type = Object.keys(conditions)[0];
    const conditionList = conditions[type];
    return {
      id: foundNode.id,
      type,
      conditions: conditionList,
    } as ConditionNode;
  }

  if (foundNode.type === TypeNode.Action) {
    const action = foundNode.config.action;
    const payload = foundNode.config.payload;
    return {
      id: foundNode.id,
      action,
      payload,
    } as ActionNode;
  }
}

function getNewBranchNodeData() {
  return {
    id: uuidV4(),
    type: TypeNode.Branch,
    config: {
      branches: [],
    },
  };
}

function getNewConditionNodeData() {
  return {
    id: uuidV4(),
    type: TypeNode.Condition,
    config: {
      conditions: {
        all: [
          // {
          //     id: uuidV4(),
          //     fact: 'enduser',
          //     path: '$.recurrent',
          //     value: '',
          //     operator: 'equal',
          // },
        ],
      },
      event: {
        type: 'rule',
        params: {
          onSuccess: {},
          onFailure: {},
        },
      },
    },
  };
}

function getNewActionNodeData() {
  return {
    id: uuidV4(),
    type: TypeNode.Action,
    config: {
      // action: ACTION_ASSIGN,
      // payload: {
      //     teamId: '',
      // },
      action: null,
      payload: {},
      event: {
        params: {
          onSuccess: {},
          onFailure: {},
        },
      },
    },
  };
}

function getNewNodeDataBasedOnType(type: TypeNode) {
  switch (type) {
    case TypeNode.Branch:
      return getNewBranchNodeData();
    case TypeNode.Condition:
      return getNewConditionNodeData();
    case TypeNode.Action:
      return getNewActionNodeData();
    default:
      return null;
  }
}

function addNodeToFlowCase1(sourceNode, targetNode, newNodeData) {
  const typeSourceNode = sourceNode.type;

  if (typeSourceNode === TypeNode.Branch) {
    const preBranchNodeIndex = sourceNode.config.branches.findIndex(
      (branch) => branch.id === targetNode.id
    );
    if (preBranchNodeIndex > -1) {
      const preOnData = sourceNode.config.branches[preBranchNodeIndex];
      if (newNodeData.type === TypeNode.Branch) {
        newNodeData.config.branches.push(preOnData);
      }
      if (newNodeData.type === TypeNode.Condition) {
        newNodeData.config.event.params.onSuccess = preOnData;
      }
      if (newNodeData.type === TypeNode.Action) {
        newNodeData.config.event.params.onSuccess = preOnData;
      }

      sourceNode.config.branches[preBranchNodeIndex] = newNodeData;
    }
  }

  if (typeSourceNode === TypeNode.Condition) {
    const params = sourceNode?.config?.event?.params || null;
    if (params !== null && Object.keys(params).length) {
      const branch = params?.onSuccess?.id === targetNode.id ? 'onSuccess' : 'onFailure';
      const preOnData = sourceNode.config.event.params[branch];
      if (newNodeData.type === TypeNode.Branch) {
        newNodeData.config.branches.push(preOnData);
      }
      if (newNodeData.type === TypeNode.Condition) {
        // Assign later node to onSuccess of new new node by default
        newNodeData.config.event.params.onSuccess = preOnData;
      }
      if (newNodeData.type === TypeNode.Action) {
        newNodeData.config.event.params.onSuccess = preOnData;
      }
      sourceNode.config.event.params[branch] = newNodeData;
    }
  }

  if (typeSourceNode === TypeNode.Action) {
    const params = sourceNode?.config?.event?.params || null;
    if (params !== null && Object.keys(params).length) {
      const preOnData = sourceNode.config.event.params.onSuccess;
      if (newNodeData.type === TypeNode.Branch) {
        newNodeData.config.branches.push(preOnData);
        sourceNode.config.event.params.onSuccess = newNodeData;
      }
      if (newNodeData.type === TypeNode.Condition) {
        // Assign later node to onSuccess of new new node by default
        newNodeData.config.event.params.onSuccess = preOnData;
        sourceNode.config.event.params.onSuccess = newNodeData;
      }
      if (newNodeData.type === TypeNode.Action) {
        newNodeData.config.event.params.onSuccess = preOnData;
        sourceNode.config.event.params.onSuccess = newNodeData;
      }
    }
  }
}
/// SourceNode --> TargetNode
///      ↑
///     Here
function addNodeToFlowCase2(sourceNode, newNodeData, boolEdge?: boolean | null) {
  if (sourceNode.type === TypeNode.Branch) {
    sourceNode.config.branches = (sourceNode.config.branches || []).filter(
      (branch) => branch !== null && Object.keys(branch).length
    );
    sourceNode.config.branches.push(newNodeData);
  }

  if (sourceNode.type === TypeNode.Condition && typeof boolEdge === 'boolean') {
    const branch = boolEdge ? 'onSuccess' : 'onFailure';
    sourceNode.config.event.params[branch] = newNodeData;
  }

  if (sourceNode.type === TypeNode.Action) {
    sourceNode.config.event.params.onSuccess = newNodeData;
  }
}

/// SourceNode --> TargetNode
///                 ↑
///                Here
/// Don't know source node, cuz it is virtual node(node between branch node and child branches)
/// Source Node should the branch node
function addNodeToFlowCase3(parentNode, targetId: string, newNodeData) {
  let branchIndex = -1;
  if (parentNode.type === TypeNode.Branch) {
    branchIndex = parentNode.config.branches.findIndex((branch) => branch.id === targetId);
  }
  if (branchIndex > -1) {
    const preOnData = parentNode.config.branches[branchIndex];
    if (newNodeData.type === TypeNode.Branch) {
      newNodeData.config.branches.push(preOnData);
    }
    if (newNodeData.type === TypeNode.Condition) {
      // Assign later node to onSuccess of new new node by default
      newNodeData.config.event.params.onSuccess = preOnData;
    }
    if (newNodeData.type === TypeNode.Action) {
      newNodeData.config.event.params.onSuccess = preOnData;
    }
    parentNode.config.branches[branchIndex] = newNodeData;
  }
}

function addNodeToBranchEmpty(parentNode, newNodeData) {
  if (parentNode.type === TypeNode.Branch) {
    parentNode.config.branches = [newNodeData];
  }
}

function addNodeAtRootFlow(node, newNodeData) {
  const preOnData = node.flow;
  if (newNodeData.type === TypeNode.Branch) {
    newNodeData.config.branches.push(preOnData);
    node.flow = newNodeData;
  }

  if (newNodeData.type === TypeNode.Condition) {
    // THis is current behavior to add to onSuccess property, may be changed later
    newNodeData.config.event.params.onSuccess = preOnData;
    node.flow = newNodeData;
  }

  if (newNodeData.type === TypeNode.Action) {
    newNodeData.config.event.params.onSuccess = preOnData;
    node.flow = newNodeData;
  }
}

// This function uses passing 'node' reference to change the property in place.
function addNodeByType(node, connection: ConnectionData, type: AddNodeType) {
  const { source, target, boolEdge = null, actualParentNodeId } = connection;
  const newNodeData = getNewNodeDataBasedOnType(type);

  if (!newNodeData) return node;

  if (connection.actualParentNodeId === triggerNodeId) {
    addNodeAtRootFlow(node, newNodeData);
    return node;
  }

  const foundSourceNode = findNodeByIdInOrderTraversal(node.flow, source);
  const foundTargetNode = findNodeByIdInOrderTraversal(node.flow, target);

  if (!foundSourceNode && !foundTargetNode) {
    // this case means source is virtual node,
    // so does target means end node, which id equivalent to virtual node
    // then find parent node by actualParentNodeId.
    const parentNode = findNodeByIdInOrderTraversal(node.flow, actualParentNodeId);
    if (parentNode) {
      // Parent node should be branch node and have empty branches property.
      addNodeToBranchEmpty(parentNode, newNodeData);
    }
  }

  if (foundSourceNode && foundTargetNode) {
    // This case means we know clearly about parent and children
    addNodeToFlowCase1(foundSourceNode, foundTargetNode, newNodeData);
  } else if (foundSourceNode && !foundTargetNode) {
    // This case means we don't have the real target node (just add-up end node maybe)
    // Simply push the new node to source node.
    addNodeToFlowCase2(foundSourceNode, newNodeData, boolEdge);
  } else if (!foundSourceNode && foundTargetNode) {
    // This case means we have a virtual node as source target, cannot find it in real flow.
    // Find the target to get parent node. then add node .
    const parentNode = findNodeByIdInOrderTraversal(node.flow, actualParentNodeId);
    // parentNode here should be branch node.
    if (parentNode) {
      addNodeToFlowCase3(parentNode, foundTargetNode.id, newNodeData);
    }
  }

  return node;
}

function removeNodeAndItsChildren(node, nodeIdToRemove: string) {
  if (node.type === TypeNode.Branch) {
    const newBranches = node.config.branches.filter((branch) => branch?.id !== nodeIdToRemove);
    node.config.branches = newBranches;
  }

  if (node.type === TypeNode.Condition) {
    // check boolean branch.
    const branch =
      node.config.event.params.onSuccess?.id === nodeIdToRemove ? 'onSuccess' : 'onFailure';
    node.config.event.params[branch] = {};
  }

  if (node.type === TypeNode.Action) {
    node.config.event.params.onSuccess = {};
  }
}

// This function uses passing 'node' reference to change the property in place.
function removeNodeById(node, parentNodeId: string, nodeIdToRemove: string) {
  if (node === null || !Object.keys(node).length || !parentNodeId || !nodeIdToRemove) return node;

  if (parentNodeId === triggerNodeId || nodeIdToRemove === triggerNodeId) {
    // Remove entire node in the diagram , only left the trigger node
    node.flow = {};
    return node;
  }

  // Here means node.flow has child node.
  const foundNodeParent = findNodeByIdInOrderTraversal(node.flow, parentNodeId);
  if (foundNodeParent) {
    removeNodeAndItsChildren(foundNodeParent, nodeIdToRemove);
  }
  return node;
}

function updateNodeData(node, nodeId: string, data: ActionNode | ConditionNode) {
  const foundNode = findNodeByIdInOrderTraversal(node?.flow, nodeId);
  if (foundNode) {
    const type = foundNode.type;
    if (type === TypeNode.Action) {
      foundNode.config.action = (data as ActionNode).action;
      foundNode.config.payload = (data as ActionNode).payload;
    }

    if (type === TypeNode.Condition) {
      const { type, conditions } = data as ConditionNode;
      foundNode.config.conditions = {
        [type]: conditions,
      };
    }
  }
  return node;
}

export const DiagramHelper = {
  calculateLayoutElk,
  getLayoutCalculateElk,
  getNodesForDiagram,
  getEdgesForDiagram,
  convertRuleEngineToNodesAndEdges,
  addMissingPropertyInOrderTraversal,
  findDataNodeFromRuleEngineTemplate,
  addNodeByType,
  removeNodeById,
  updateNodeData,
};
