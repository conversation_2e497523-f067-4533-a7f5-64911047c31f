import { Badge, Text, rem } from '@mantine/core';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { TypeNode } from '../../types';
import { BaseNode } from './_BaseNode';

export function EndNode() {
  const { t } = useAutomationDetailContext();
  return (
    <BaseNode>
      <Badge variant='filled' radius={'md'} size={'lg'} color='violet.2' pt={'md'} pb={'md'}>
        <Text size={rem(14)}>{t('detail.finish')}</Text>
      </Badge>
      <BaseNode.Handler type={TypeNode.End} />
    </BaseNode>
  );
}
