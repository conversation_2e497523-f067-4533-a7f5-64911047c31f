import { Center, Container, Space, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconAlertCircle } from '@tabler/icons-react';
import { useCallback, useMemo } from 'react';
import { NodeProps } from 'reactflow';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { TriggerNodeData, TypeNode } from '../../types';
import { BaseNode } from './_BaseNode';
import { IconBoldStyled } from './_Icons';

const useStyle = createStyles(() => ({
  triggerNode: {
    backgroundColor: '#E6FCF5',
    border: '2px solid #96F2D7',
    borderRadius: '5px',
  },
  errorContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    minWidth: 470,
    minHeight: 52,
  },
  errorText: {
    display: 'flex',
    flexFlow: 'row',
    alignItems: 'center',
    justifyItems: 'center',
    textAlign: 'center',
  },
}));

export function TriggerNode({ data }: NodeProps<TriggerNodeData>) {
  const { classes } = useStyle();
  const { t } = useAutomationDetailContext();

  const labelTriggers = useMemo(() => {
    return {
      'conversation.created': t('condition.whenNewMessageCome'),
      'conversation.message.received': t('condition.whenOperatorAssigned'),
      'conversation.closed': t('condition.whenChatIsClosed'),
    };
  }, [t]);

  const getLabel = useCallback(
    (triggerType) => {
      return labelTriggers?.[triggerType] || '';
    },
    [labelTriggers]
  );

  return (
    <>
      {data.showErrorTriggerNode && (
        <Center mb={'lg'}>
          <Container bg={'red.0'} className={classes.errorContainer} px={0}>
            <Text c={'red'} size={rem(12)} className={classes.errorText}>
              <span>
                <IconAlertCircle size='1.125rem' color='red' />
              </span>
              <Space w={10} />
              <Text>{t('trigger_not_set')}</Text>
            </Text>
          </Container>
        </Center>
      )}
      <BaseNode className={classes.triggerNode}>
        <BaseNode.LayoutNode
          titleNode={
            <BaseNode.Title
              icon={<IconBoldStyled size={'20px'} />}
              label={t('detail.trigger.label')}
            />
          }
          descriptionTitle={<BaseNode.DescriptionTitle description={t('')} />}
          bodyButton={
            <BaseNode.BodyButton
              label={
                <Text c='gray.9' fz={14} fw={700}>
                  {data.triggerType ? getLabel(data.triggerType) : t('detail.trigger.select.label')}
                </Text>
              }
              onClick={data.onClick}
            />
          }
        ></BaseNode.LayoutNode>
        <BaseNode.Handler type={TypeNode.Trigger} />
      </BaseNode>
    </>
  );
}
