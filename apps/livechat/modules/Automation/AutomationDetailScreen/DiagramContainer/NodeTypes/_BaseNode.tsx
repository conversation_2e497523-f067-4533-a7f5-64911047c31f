import { ActionIcon, Button, ButtonProps, Container, Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconTrash } from '@tabler/icons-react';
import { CSSProperties, ReactNode } from 'react';

import { TypeNode } from '../../types';
import { Handles } from './_Handle';

// eslint-disable-next-line no-unused-vars
const useStyle = createStyles((theme) => ({
  baseNode: {
    width: '300px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  layoutNode: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '10px',
    width: '290px',
    textAlign: 'center',
    padding: 0,
  },
  relative: {
    position: 'relative',
  },
  headFlex: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  iconTrashWrapper: {
    position: 'absolute',
    right: 0,
  },
  iconTrash: {
    cursor: 'pointer',
  },
}));

type ClassName = { className?: string };

type Children = { children: ReactNode };

type StyleProperties = { style?: CSSProperties };

export type BaseNodeProps = ClassName &
  Children & {
    isSelected?: boolean;
    onClick?: () => void;
    onMouseLeave?: () => void;
    onMouseEnter?: () => void;
  };

function Title({
  icon = null,
  label = '',
  style,
}: { icon?: ReactNode; label: string } & ClassName & StyleProperties) {
  return (
    <Flex direction={'row'} justify={'center'} align={'center'} style={style}>
      {icon}
      {label}
    </Flex>
  );
}

function RemoveIcon({
  className = '',
  onClick,
  disabled = false,
}: ClassName & { onClick?: () => void; disabled?: boolean }) {
  return (
    <ActionIcon
      onClick={onClick}
      color='navy.0'
      disabled={disabled}
      variant='transparent'
      className={className}
    >
      <IconTrash size={24} />
    </ActionIcon>
  );
}

function DescriptionTitle({
  description = '',
  className = '',
}: { description?: ReactNode } & ClassName) {
  return <Text className={className}>{description}</Text>;
}

function BodyButton({
  label = '',
  variant = 'outline',
  onClick,
  className,
}: { label?: ReactNode; variant?: ButtonProps['variant']; onClick?: () => void } & ClassName) {
  return (
    <Button
      variant={variant}
      onClick={onClick}
      color={'violet.0'}
      className={className}
      bg={'gray.0'}
      fullWidth
    >
      {label}
    </Button>
  );
}

function Head({
  titleNode,
  descriptionTitle = null,
  removeIcon,
}: {
  titleNode?: ReactNode;
  removeIcon?: ReactNode;
  descriptionTitle?: ReactNode;
}) {
  const { classes } = useStyle();
  if (titleNode && removeIcon) {
    return (
      <Flex direction={'row'} className={classes.headFlex}>
        <Container style={{ flexGrow: 1 }}>
          {descriptionTitle}
          {titleNode}
        </Container>
        <div className={classes.iconTrashWrapper}>{removeIcon}</div>
      </Flex>
    );
  }

  if (titleNode) {
    return (
      <Container fluid>
        {descriptionTitle}
        {titleNode}
      </Container>
    );
  }
}

function LayoutNode({
  titleNode,
  removeIcon,
  descriptionTitle,
  bodyButton,
  className = '',
}: ClassName & {
  titleNode?: ReactNode;
  removeIcon?: ReactNode;
  descriptionTitle?: ReactNode;
  bodyButton: ReactNode;
}) {
  const { classes } = useStyle();
  return (
    <Container fluid className={`${className} ${classes.layoutNode}`} pt='md' pb='md'>
      <Head titleNode={titleNode} removeIcon={removeIcon} descriptionTitle={descriptionTitle} />
      {bodyButton}
    </Container>
  );
}

function Handler({ type }: { type: TypeNode }) {
  return <Handles type={type} />;
}

export function BaseNode({
  children,
  className = '',
  onClick,
  onMouseEnter,
  onMouseLeave,
}: BaseNodeProps) {
  const { classes } = useStyle();

  return (
    <Container
      className={`${classes.baseNode} ${className} nodrag`}
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
      onMouseOver={onMouseEnter}
      onClick={onClick}
    >
      {children}
    </Container>
  );
}

BaseNode.displayName = 'BaseNode';
BaseNode.Title = Title;
BaseNode.RemoveIcon = RemoveIcon;
BaseNode.DescriptionTitle = DescriptionTitle;
BaseNode.BodyButton = BodyButton;
BaseNode.LayoutNode = LayoutNode;
BaseNode.Handler = Handler;
