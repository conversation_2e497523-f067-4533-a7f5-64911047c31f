import { NodeProps } from 'reactflow';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { BranchPayloadData, TypeNode } from '../../types';
import { BaseNode } from './_BaseNode';
import { IconHierarchy2Styled } from './_Icons';

export function BranchNode({ id, data }: NodeProps<BranchPayloadData>) {
  const { t, intendToRemoveNode, isManager } = useAutomationDetailContext();
  const { actualParentNodeId } = data;
  return (
    <BaseNode>
      <BaseNode.LayoutNode
        titleNode={
          <BaseNode.Title
            label={t('action.add.new.branch')}
            icon={<IconHierarchy2Styled size={'20px'} style={{ marginRight: '5px' }} />}
          />
        }
        removeIcon={
          <BaseNode.RemoveIcon
            onClick={() => intendToRemoveNode(actualParentNodeId, id)}
            disabled={!isManager}
          />
        }
        bodyButton={null}
      />
      <BaseNode.Handler type={TypeNode.Branch} />
    </BaseNode>
  );
}
