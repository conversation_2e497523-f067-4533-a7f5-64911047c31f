import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { IconAlertCircle } from '@tabler/icons-react';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { BaseNode } from '../_BaseNode';

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Create a test wrapper with Mantine providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Helper function to render components with the test wrapper
const renderWithWrapper = (ui: React.ReactNode) => {
  return render(ui, { wrapper: TestWrapper });
};

describe('BaseNode Components', () => {
  describe('Title', () => {
    it('renders with label', () => {
      renderWithWrapper(<BaseNode.Title label='Test Title' />);
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });

    it('renders with icon and label', () => {
      const { container } = renderWithWrapper(
        <BaseNode.Title label='Test Title' icon={<IconAlertCircle />} />
      );
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(container.querySelector('.tabler-icon-alert-circle')).toBeInTheDocument();
    });
  });

  describe('RemoveIcon', () => {
    it('renders remove icon', () => {
      renderWithWrapper(<BaseNode.RemoveIcon />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('handles click event', () => {
      const handleClick = jest.fn();
      renderWithWrapper(<BaseNode.RemoveIcon onClick={handleClick} />);
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalled();
    });

    it('disables when disabled prop is true', () => {
      renderWithWrapper(<BaseNode.RemoveIcon disabled />);
      expect(screen.getByRole('button')).toBeDisabled();
    });
  });

  describe('DescriptionTitle', () => {
    it('renders description text', () => {
      renderWithWrapper(<BaseNode.DescriptionTitle description='Test Description' />);
      expect(screen.getByText('Test Description')).toBeInTheDocument();
    });
  });

  describe('BodyButton', () => {
    it('renders button with label', () => {
      renderWithWrapper(<BaseNode.BodyButton label='Test Button' />);
      expect(screen.getByRole('button', { name: 'Test Button' })).toBeInTheDocument();
    });

    it('handles click event', () => {
      const handleClick = jest.fn();
      renderWithWrapper(<BaseNode.BodyButton label='Test Button' onClick={handleClick} />);
      fireEvent.click(screen.getByRole('button', { name: 'Test Button' }));
      expect(handleClick).toHaveBeenCalled();
    });
  });

  describe('Head', () => {
    it('renders with title and remove icon', () => {
      renderWithWrapper(
        <BaseNode.LayoutNode
          titleNode={<BaseNode.Title label='Test Title' />}
          removeIcon={<BaseNode.RemoveIcon />}
          bodyButton={<BaseNode.BodyButton label='Test Button' />}
        />
      );
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThanOrEqual(2); // RemoveIcon and BodyButton
    });

    it('renders with title only', () => {
      renderWithWrapper(
        <BaseNode.LayoutNode
          titleNode={<BaseNode.Title label='Test Title' />}
          bodyButton={<BaseNode.BodyButton label='Test Button' />}
        />
      );
      expect(screen.getByText('Test Title')).toBeInTheDocument();
    });
  });

  describe('LayoutNode', () => {
    it('renders complete layout', () => {
      renderWithWrapper(
        <BaseNode.LayoutNode
          titleNode={<BaseNode.Title label='Test Title' />}
          removeIcon={<BaseNode.RemoveIcon />}
          descriptionTitle={<BaseNode.DescriptionTitle description='Test Description' />}
          bodyButton={<BaseNode.BodyButton label='Test Button' />}
        />
      );
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.getByText('Test Description')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Test Button' })).toBeInTheDocument();
    });
  });

  describe('BaseNode', () => {
    it('renders children', () => {
      renderWithWrapper(
        <BaseNode>
          <div>Test Child</div>
        </BaseNode>
      );
      expect(screen.getByText('Test Child')).toBeInTheDocument();
    });

    it('handles mouse events', () => {
      const handleMouseEnter = jest.fn();
      const handleMouseLeave = jest.fn();
      const handleClick = jest.fn();

      renderWithWrapper(
        <BaseNode
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={handleClick}
        >
          <div>Test Child</div>
        </BaseNode>
      );

      const node = screen.getByText('Test Child').parentElement;
      fireEvent.mouseEnter(node!);
      expect(handleMouseEnter).toHaveBeenCalled();

      fireEvent.mouseLeave(node!);
      expect(handleMouseLeave).toHaveBeenCalled();

      fireEvent.click(node!);
      expect(handleClick).toHaveBeenCalled();
    });

    it('applies selected state', () => {
      renderWithWrapper(
        <BaseNode isSelected>
          <div>Test Child</div>
        </BaseNode>
      );
      const node = screen.getByText('Test Child').parentElement;
      expect(node).toHaveClass('nodrag');
    });
  });
});
