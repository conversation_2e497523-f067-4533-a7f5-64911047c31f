import { render, screen } from '@testing-library/react';
import { ReactFlowProvider } from 'reactflow';
import { TypeNode } from '../../../types';
import { Handles } from '../_Handle';

const renderWithProvider = (ui: React.ReactElement) => {
  return render(<ReactFlowProvider>{ui}</ReactFlowProvider>);
};

describe('Handles', () => {
  it('renders correct handles for Action node type', () => {
    renderWithProvider(<Handles type={TypeNode.Action} />);

    // Check for source handle at bottom
    expect(screen.getByTestId('handle-sb')).toBeInTheDocument();
    // Check for target handles at top and right
    expect(screen.getByTestId('handle-tt')).toBeInTheDocument();
    expect(screen.getByTestId('handle-tr')).toBeInTheDocument();
  });

  it('renders correct handles for Condition node type', () => {
    renderWithProvider(<Handles type={TypeNode.Condition} />);

    // Check for source handles
    expect(screen.getByTestId('handle-sb')).toBeInTheDocument();
    expect(screen.getByTestId('handle-sr')).toBeInTheDocument();
    expect(screen.getByTestId('handle-sl')).toBeInTheDocument();

    // Check for target handles
    expect(screen.getByTestId('handle-tt')).toBeInTheDocument();
    expect(screen.getByTestId('handle-tr')).toBeInTheDocument();
    expect(screen.getByTestId('handle-tl')).toBeInTheDocument();
  });

  it('renders correct handles for Trigger node type', () => {
    renderWithProvider(<Handles type={TypeNode.Trigger} />);

    // Check for single source handle at bottom
    expect(screen.getByTestId('handle-sb')).toBeInTheDocument();
  });

  it('renders correct handles for End node type', () => {
    renderWithProvider(<Handles type={TypeNode.End} />);

    // Check for single target handle at top
    expect(screen.getByTestId('handle-tt')).toBeInTheDocument();
  });

  it('renders correct handles for Branch node type', () => {
    renderWithProvider(<Handles type={TypeNode.Branch} />);

    // Check for source handle at bottom
    expect(screen.getByTestId('handle-sb')).toBeInTheDocument();
    // Check for target handle at top
    expect(screen.getByTestId('handle-tt')).toBeInTheDocument();
  });

  it('renders default handles for unknown node type', () => {
    renderWithProvider(<Handles type={'Unknown' as TypeNode} />);

    // Check for default handles
    expect(screen.getByTestId('handle-sb')).toBeInTheDocument();
    expect(screen.getByTestId('handle-tt')).toBeInTheDocument();
  });
});
