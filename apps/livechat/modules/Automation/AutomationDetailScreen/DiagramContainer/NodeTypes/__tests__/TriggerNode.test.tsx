import { render, screen } from '@testing-library/react';
import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import { MantineWrapper } from '../../../../../../utils/test';
import { TriggerType, TypeNode } from '../../../types';
import { TriggerNode } from '../TriggerNode';

jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => ({
    t: (key) => key,
  }),
}));

describe('TriggerNode', () => {
  const baseProps = {
    id: '1',
    data: {
      onClick: jest.fn(),
      parentNodeId: 'parent',
      actualParentNodeId: 'parent',
      triggerType: TriggerType.NewConversation,
    },
    type: TypeNode.Trigger,
    selected: false,
    dragging: false,
    position: { x: 0, y: 0 },
    width: 0,
    height: 0,
    isConnectable: false,
    targetPosition: undefined,
    sourcePosition: undefined,
    zIndex: 0,
    draggingHandle: null,
    events: {},
    xPos: 0,
    yPos: 0,
  };

  it('renders the trigger node with default title', () => {
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <TriggerNode {...baseProps} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('detail.trigger.label')).toBeInTheDocument();
  });

  it('renders the trigger node with a triggerType', () => {
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <TriggerNode {...baseProps} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('detail.trigger.label')).toBeInTheDocument();
  });

  it('renders error message if showErrorTriggerNode is true', () => {
    const props = {
      ...baseProps,
      data: {
        ...baseProps.data,
        showErrorTriggerNode: true,
      },
    };
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <TriggerNode {...props} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('trigger_not_set')).toBeInTheDocument();
  });
});
