import { render } from '@testing-library/react';
import {
  IconBoldStyled,
  IconChecklistStyled,
  IconHandFingerStyled,
  IconHierarchy2Styled,
  IconTrashStyled,
} from '../_Icons';

describe('Styled Icons', () => {
  it('renders IconBoldStyled', () => {
    const { container } = render(<IconBoldStyled />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders IconChecklistStyled', () => {
    const { container } = render(<IconChecklistStyled />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders IconHandFingerStyled', () => {
    const { container } = render(<IconHandFingerStyled />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders IconTrashStyled', () => {
    const { container } = render(<IconTrashStyled />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders IconHierarchy2Styled', () => {
    const { container } = render(<IconHierarchy2Styled />);
    expect(container.firstChild).toBeInTheDocument();
  });
});
