import { render, screen } from '@testing-library/react';
import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import { MantineWrapper } from '../../../../../../utils/test';
import { EndNode } from '../EndNode';

jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => ({
    t: (key) => key,
  }),
}));

describe('EndNode', () => {
  it('renders the end node with correct badge text', () => {
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <EndNode />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('detail.finish')).toBeInTheDocument();
  });
});
