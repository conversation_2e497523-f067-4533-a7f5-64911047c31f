import { render } from '@testing-library/react';
import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import { MantineWrapper } from '../../../../../../utils/test';
import { BranchToOtherNode } from '../BranchToOtherNode';

describe('BranchToOtherNode', () => {
  it('renders without crashing', () => {
    const { container } = render(
      <MantineWrapper>
        <ReactFlowProvider>
          <BranchToOtherNode />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(container.firstChild).toBeInTheDocument();
  });
});
