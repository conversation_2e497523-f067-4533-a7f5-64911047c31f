import { render, screen } from '@testing-library/react';
import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import { MantineWrapper } from '../../../../../../utils/test';
import { TypeNode } from '../../../types';
import { BranchNode } from '../BranchNode';

jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => ({
    t: (key) => key,
    intendToRemoveNode: jest.fn(),
  }),
}));

describe('BranchNode', () => {
  const baseProps = {
    id: '1',
    data: {
      onClick: jest.fn(),
      parentNodeId: 'parent',
      actualParentNodeId: 'parent',
    },
    type: TypeNode.Branch,
    selected: false,
    dragging: false,
    position: { x: 0, y: 0 },
    width: 0,
    height: 0,
    isConnectable: false,
    targetPosition: undefined,
    sourcePosition: undefined,
    zIndex: 0,
    draggingHandle: null,
    events: {},
    xPos: 0,
    yPos: 0,
  };

  it('renders the branch node with correct title', () => {
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <BranchNode {...baseProps} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('action.add.new.branch')).toBeInTheDocument();
  });
});
