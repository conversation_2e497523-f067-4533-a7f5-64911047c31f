import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import { Position, ReactFlowProvider } from 'reactflow';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { ActionNodeActionType, ScenarioAssignType } from '../../../types';
import { ActionNode } from '../ActionNode';

// Mock the useAutomationDetailContext hook
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

describe('ActionNode', () => {
  beforeEach(() => {
    // Mock the context values
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: (key: string, params?: Record<string, string>) => {
        const translations: Record<string, string> = {
          'detail.action.select.label': 'Select Action',
          'detail.action.title': 'Action',
          action_label_assign: 'Assign to {name}',
          action_label_send_message_only: 'Send Message',
          'action.webhook.option_label': 'Webhook',
        };
        if (params) {
          return translations[key]?.replace('{name}', params.name) || key;
        }
        return translations[key] || key;
      },
      teamList: [
        { id: 'team-1', name: 'Team 1' },
        { id: 'team-2', name: 'Team 2' },
      ],
      isManager: true,
      listOperators: [
        { id: 'op-1', name: 'Operator 1' },
        { id: 'op-2', name: 'Operator 2' },
      ],
      intendToRemoveNode: jest.fn(),
    });
  });

  const defaultProps = {
    id: 'node-1',
    type: 'action',
    data: {
      actionData: null,
      onClick: jest.fn(),
      parentNodeId: 'parent-1',
      actualParentNodeId: 'parent-1',
    },
    selected: false,
    zIndex: 1,
    isConnectable: true,
    xPos: 0,
    yPos: 0,
    dragHandle: '.drag-handle',
    targetPosition: Position.Left,
    sourcePosition: Position.Right,
    dragging: false,
  };

  const renderComponent = (props = {}) => {
    return render(
      <ReactFlowProvider>
        <MantineEmotionProvider>
          <MantineProvider>
            <ActionNode {...defaultProps} {...props} />
          </MantineProvider>
        </MantineEmotionProvider>
      </ReactFlowProvider>
    );
  };

  it('renders with default props', () => {
    renderComponent();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('renders with team assignment', () => {
    renderComponent({
      data: {
        ...defaultProps.data,
        actionData: {
          action: ActionNodeActionType.Assign,
          payload: {
            scenario: ScenarioAssignType.TEAM,
            data: {
              teamId: 'team-1',
            },
          },
        },
      },
    });
    expect(screen.getByText('action.node.operator_or_team.selected')).toBeInTheDocument();
  });

  it('renders with webhook action', () => {
    renderComponent({
      data: {
        ...defaultProps.data,
        actionData: {
          action: ActionNodeActionType.SendMessage,
          payload: {},
        },
      },
    });
    expect(screen.getByText('Send Message')).toBeInTheDocument();
  });
});
