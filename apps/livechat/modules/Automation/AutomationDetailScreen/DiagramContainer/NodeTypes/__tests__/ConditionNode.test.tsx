import { render, screen } from '@testing-library/react';
import React from 'react';
import { ReactFlowProvider } from 'reactflow';
import { MantineWrapper } from '../../../../../../utils/test';
import { ConditionType, TypeNode } from '../../../types';
import { ConditionNode } from '../ConditionNode';

jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: () => ({
    t: (key) => key,
    intendToRemoveNode: jest.fn(),
  }),
}));

describe('ConditionNode', () => {
  const baseProps = {
    id: '1',
    data: {
      onClick: jest.fn(),
      parentNodeId: 'parent',
      actualParentNodeId: 'parent',
      conditions: [],
      conditionType: 'all' as ConditionType,
    },
    type: TypeNode.Condition,
    selected: false,
    dragging: false,
    position: { x: 0, y: 0 },
    width: 0,
    height: 0,
    isConnectable: false,
    targetPosition: undefined,
    sourcePosition: undefined,
    zIndex: 0,
    draggingHandle: null,
    events: {},
    xPos: 0,
    yPos: 0,
  };

  it('renders the condition node with default title', () => {
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <ConditionNode {...baseProps} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('detail.condition.select.label')).toBeInTheDocument();
  });

  it('renders the condition node with condition', () => {
    const props = {
      ...baseProps,
      data: {
        ...baseProps.data,
        conditions: [{ fact: 'END_USER', path: 'CRM', value: true }],
      },
    };
    render(
      <MantineWrapper>
        <ReactFlowProvider>
          <ConditionNode {...props} />
        </ReactFlowProvider>
      </MantineWrapper>
    );
    expect(screen.getByText('detail.condition.select.label')).toBeInTheDocument();
  });
});
