import { useMemo } from 'react';
import { Handle, Position } from 'reactflow';
import { HandleConfig, TypeNode } from '../../types';

export function Handles({ type }: { type: TypeNode }) {
  const handlesConfig: HandleConfig[] = useMemo(() => {
    switch (type) {
      case TypeNode.Action:
        return [
          { id: 'sb', type: 'source', position: Position.Bottom },
          { id: 'tt', type: 'target', position: Position.Top },
          { id: 'tr', type: 'target', position: Position.Right },
        ];
      case TypeNode.Condition:
      case TypeNode.Anchor:
        return [
          { id: 'sb', type: 'source', position: Position.Bottom },
          { id: 'sr', type: 'source', position: Position.Right },
          { id: 'sl', type: 'source', position: Position.Left },
          { id: 'tt', type: 'target', position: Position.Top },
          { id: 'tr', type: 'target', position: Position.Right },
          { id: 'tl', type: 'target', position: Position.Left },
        ];
      case TypeNode.Trigger:
        return [{ id: 'sb', type: 'source', position: Position.Bottom }];
      case TypeNode.End:
        return [{ id: 'tt', type: 'target', position: Position.Top }];
      case TypeNode.Branch:
        return [
          { id: 'sb', type: 'source', position: Position.Bottom },
          { id: 'tt', type: 'target', position: Position.Top },
        ];
      default:
        return [
          { id: 'sb', type: 'source', position: Position.Bottom },
          { id: 'tt', type: 'target', position: Position.Top },
        ];
    }
  }, [type]);

  return (
    <>
      {handlesConfig.map((handle) => (
        <Handle
          key={handle.id}
          type={handle.type}
          position={handle.position}
          id={handle.id}
          data-testid={`handle-${handle.id}`}
        />
      ))}
    </>
  );
}
