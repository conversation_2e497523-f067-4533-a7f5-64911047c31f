import { createStyles } from '@mantine/emotion';
import { TypeNode } from '../../types';
import { BaseNode } from './_BaseNode';

const useStyle = createStyles((theme) => ({
  width300: {
    width: '302px',
    height: '1px',
    backgroundColor: theme.colors.gray[4],
    opacity: 0,
  },
}));
export function BranchToOtherNode() {
  const { classes } = useStyle();
  return (
    <BaseNode className={`${classes.width300}`}>
      <BaseNode.Handler type={TypeNode.BranchToOtherNode} />
    </BaseNode>
  );
}
