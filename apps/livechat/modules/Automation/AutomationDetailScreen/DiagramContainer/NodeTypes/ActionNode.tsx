import { Button, Text, rem } from '@mantine/core';
import { useCallback } from 'react';
import { NodeProps } from 'reactflow';
import {
  ACTION_ASSIGN,
  ACTION_SEND_MESSAGE,
  ACTION_WEBHOOK,
  NO_SPECIFIED_OPERATOR_ID,
} from '../../constant';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import {
  ActionAssignData,
  ActionNodePayloadData,
  AssignActionPayload,
  NewAssignTeamData,
  NewAssignTeamOperatorData,
  ScenarioAssignType,
  // SendMessageActionPayload,
  TypeNode,
} from '../../types';
import { BaseNode } from './_BaseNode';
import { IconHandFingerStyled } from './_Icons';

export function ActionNode({ id, data }: NodeProps<ActionNodePayloadData>) {
  const {
    t,
    teamList,
    isManager,
    listOperators,
    intendToRemoveNode,
    // ocsChannelsOptionsWithDefaultOption,
  } = useAutomationDetailContext();
  const { actionData, actualParentNodeId } = data;

  const renderActionLabel = useCallback(
    (actionData: ActionNodePayloadData['actionData']) => {
      if (actionData.action === ACTION_ASSIGN) {
        // Handle for old automation.
        const payload = actionData.payload as AssignActionPayload;

        if ((payload as ActionAssignData)?.scenario) {
          // new automation
          const scenario = (payload as ActionAssignData)?.scenario;

          if (scenario === ScenarioAssignType.TEAM) {
            const teamId = (payload as NewAssignTeamData)?.data.teamId;
            const teamName = teamList.find((team) => team.id === teamId)?.name;
            if (!teamName) return t('action_label_no_assign');

            return t('action.node.operator_or_team.selected', {
              name: teamName,
            });
          }

          if (scenario === ScenarioAssignType.TEAM_AND_OPERATOR) {
            const operatorId = (payload as NewAssignTeamOperatorData)?.data.operatorId;
            let name = '';
            if (operatorId === NO_SPECIFIED_OPERATOR_ID) {
              name = t('detail.action.unspecified_assignee');
            } else if (listOperators.find((operator) => operator.id === operatorId)) {
              name = listOperators.find((operator) => operator.id === operatorId)?.name;
            }
            return t('action.node.operator_or_team.selected', {
              name,
            });
          }

          if (scenario === ScenarioAssignType.PREVIOUS_ASSIGNEE) {
            return t('action.assign.select_box.option_3');
          }
        }

        const teamId = (actionData.payload as { teamId: string; operatorId: string }).teamId;
        const teamName = teamList.find((team) => team.id === teamId)?.name;
        if (!teamName) return t('action_label_no_assign');

        return t('action_label_assign', { name: teamName });
      }

      if (actionData.action === ACTION_SEND_MESSAGE) {
        // let channelName = '';
        // let atDays = '';
        // let atHour = '';
        // let atTime = null;
        // const payload = actionData.payload as SendMessageActionPayload;
        // const channelId = payload.ocsChannelId;
        // channelName =
        //     ocsChannelsOptionsWithDefaultOption.find((option) => option.value == channelId)
        //         ?.label || '';
        // if (payload.schedule) {
        //     atDays =
        //         payload.schedule.split('days').length === 2
        //             ? payload.schedule.split('days')?.[0].split('@next')[1].trim()
        //             : '';
        //     payload.schedule.split('at')?.[1] &&
        //         (atTime = payload.schedule.split('at')?.[1].trim());
        //     payload.schedule.split('hours').length === 2 &&
        //         (atHour = payload.schedule.split('hours')?.[0].split('@next')?.[1].trim());
        // }
        // return t('action_label_send_message', {
        //     time: `${atDays ? `${atDays}日` : ''}${atHour ? `${atHour}時` : ''}${
        //         atTime ? atTime : ''
        //     }`,
        //     channel: channelName,
        // });
        return t('action_label_send_message_only');
      }
      if (actionData.action === ACTION_WEBHOOK) {
        return t('action.webhook.option_label');
      }
      return t('detail.action.title');
    },
    [t, teamList, listOperators]
  );
  return (
    <BaseNode>
      <BaseNode.LayoutNode
        titleNode={
          <BaseNode.Title
            icon={<IconHandFingerStyled size={'20px'} style={{ marginRight: '5px' }} />}
            label={t('detail.action.select.label')}
          />
        }
        bodyButton={
          !actionData ? (
            <Button
              variant='outline'
              color='violet.0'
              w={142}
              onClick={data.onClick}
              radius={'md'}
              bg={'gray.0'}
            >
              {t('detail.action.title')}
            </Button>
          ) : (
            <Button
              variant='outline'
              color='violet.0'
              fullWidth
              onClick={data.onClick}
              radius={'md'}
              bg={'gray.0'}
            >
              <Text
                color='gray.9'
                size={rem(12)}
                fw={700}
                lineClamp={1}
                display={'inline'}
                title={renderActionLabel(actionData)}
              >
                {renderActionLabel(actionData)}
              </Text>
            </Button>
          )
        }
        removeIcon={
          <BaseNode.RemoveIcon
            onClick={() => intendToRemoveNode(actualParentNodeId, id)}
            disabled={!isManager}
          />
        }
      />
      <BaseNode.Handler type={TypeNode.Action} />
    </BaseNode>
  );
}
