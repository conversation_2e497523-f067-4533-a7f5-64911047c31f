import { Button, Flex, Text } from '@mantine/core';
import { useCallback } from 'react';
import { NodeProps } from 'reactflow';
import { NO_SPECIFIED_OPERATOR_ID } from '../../constant';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { ConditionParams, ConditionPayloadData, FACTS, Fact, PATHS, TypeNode } from '../../types';
import { BaseNode } from './_BaseNode';
import { IconChecklistStyled } from './_Icons';

export function ConditionNode({ id, data }: NodeProps<ConditionPayloadData>) {
  const {
    t,
    intendToRemoveNode,
    ocsChannelsOptionsWithDefaultOption,
    isManager,
    teamList,
    listOperators,
  } = useAutomationDetailContext();
  const { conditions = [] } = data;

  const renderCondition = useCallback(
    (condition: ConditionParams) => {
      const fact = condition.fact as Fact;
      const path = condition.path as PATHS;
      const value = condition.value;
      let text: any = '';
      let name = '';
      if (fact === FACTS.END_USER) {
        if (path.includes(PATHS.CRM)) {
          text = t('conditionNode.label.user.crm.label');
        } else {
          text = [true, 'true'].includes(condition.value)
            ? t('conditionNode.label.user.yes')
            : t('conditionNode.label.user.no');
        }
      }

      if (fact === FACTS.CONVERSATION) {
        switch (path) {
          case PATHS.OCS_CHANNEL:
            name =
              ocsChannelsOptionsWithDefaultOption.find((option) => option.value === condition.value)
                ?.label || '';
            return (
              <>
                <Text fz={14} fw={700} c='gray.9' ta='center'>
                  {t('conditionNode.label.conversation.first')}
                </Text>
                <Text
                  fz={14}
                  fw={700}
                  c='gray.9'
                  lineClamp={1}
                  ta='center'
                  style={{
                    display: 'inline',
                  }}
                  title={name}
                >
                  {name}
                </Text>
                <Text fz={14} fw={700} c='gray.9' ta='center'>
                  {t('conditionNode.label.conversation.last')}
                </Text>
              </>
            );
          case PATHS.TEAM_ID:
            const teamId = value as string;
            const teamName = teamList.find((team) => team.id === teamId)?.name || '';
            return (
              <>
                <Text
                  fz={14}
                  fw={700}
                  c='gray.9'
                  ta='center'
                  display={'block'}
                  sx={{ whiteSpace: 'break-spaces' }}
                >
                  {t('condition.node.team_selected.label.first')}
                </Text>
                <Text
                  fz={14}
                  fw={700}
                  c='gray.9'
                  lineClamp={1}
                  ta='center'
                  style={{
                    display: 'inline',
                  }}
                  title={teamName}
                >
                  {teamName}
                </Text>
                <Text fz={14} fw={700} c='gray.9' ta='center'>
                  {t('condition.node.team_selected.label.last')}
                </Text>
              </>
            );
          case PATHS.OPERATOR_ID_TEAM_ID:
            const teamIdAndOperatorId = value as string; // The format : team#id#operator#id
            if (!teamIdAndOperatorId) break;

            const operatorId = teamIdAndOperatorId?.substring(
              teamIdAndOperatorId?.lastIndexOf('#') + 1
            );

            name = '';
            if (operatorId === NO_SPECIFIED_OPERATOR_ID) {
              name = t('detail.action.unspecified_assignee');
            } else if (listOperators.find((operator) => operator.id === operatorId)) {
              name = listOperators.find((operator) => operator.id === operatorId)?.name;
            }
            return (
              <>
                <Text
                  fz={14}
                  fw={700}
                  c='gray.9'
                  ta='center'
                  display={'block'}
                  sx={{ whiteSpace: 'break-spaces' }}
                >
                  {t('condition.node.operator_selected.label.first')}
                </Text>
                <Text
                  fz={14}
                  fw={700}
                  c='gray.9'
                  lineClamp={1}
                  ta='center'
                  style={{
                    display: 'inline',
                  }}
                  title={name}
                >
                  {name}
                </Text>
                <Text fz={14} fw={700} c='gray.9' ta='center'>
                  {t('condition.node.operator_selected.label.last')}
                </Text>
              </>
            );
          case PATHS.PRESENCE_ASSIGNEE:
            if ([true, 'true'].includes(value))
              text = t('condition.node.presence_assignee_selected.label.true');
            else text = t('condition.node.presence_assignee_selected.label.false');
            break;
          case PATHS.AUTO_CLOSED_CONVERSATION:
            if ([true, 'true'].includes(value))
              text = t('condition.node.auto_closed_conversation.label.true');
            else text = t('condition.node.auto_closed_conversation.label.false');
            break;
        }
      }
      return (
        <Text
          fz={14}
          fw={700}
          c='gray.9'
          lineClamp={1}
          title={text}
          style={{
            display: 'inline',
          }}
        >
          {text}
        </Text>
      );
    },
    [t, listOperators, ocsChannelsOptionsWithDefaultOption, teamList]
  );
  const { actualParentNodeId } = data;
  return (
    <BaseNode>
      <BaseNode.LayoutNode
        titleNode={
          <BaseNode.Title
            icon={<IconChecklistStyled size={'20px'} style={{ marginRight: '5px' }} />}
            label={t('detail.condition.select.label')}
          />
        }
        descriptionTitle={<BaseNode.DescriptionTitle description={t('')} />}
        bodyButton={
          !conditions.length ? (
            <Button
              variant='outline'
              color='violet.0'
              fullWidth
              onClick={data.onClick}
              radius={'sm'}
              bg={'gray.0'}
            >
              <Text c='gray.9' fz={13} fw={700}>
                {t('detail.condition.title')}
              </Text>
            </Button>
          ) : (
            <Flex direction='column' pl={0} pr={0} w={290} gap={'sm'}>
              {conditions.map((condition, index) => {
                const conditionName = renderCondition(condition);
                return (
                  <Button
                    key={condition.fact + condition.path + index}
                    variant='outline'
                    color='violet.0'
                    onClick={data.onClick}
                    radius={'md'}
                    bg={'gray.0'}
                    fullWidth
                    p={10}
                    sx={{ height: 'auto' }}
                  >
                    <Text
                      fz={14}
                      fw={700}
                      c='gray.9'
                      lineClamp={1}
                      ta='center'
                      style={{
                        display: 'block',
                      }}
                    >
                      {conditionName}
                    </Text>
                  </Button>
                );
              })}
            </Flex>
          )
        }
        removeIcon={
          <BaseNode.RemoveIcon
            onClick={() => intendToRemoveNode(actualParentNodeId, id)}
            disabled={!isManager}
          />
        }
      />
      <BaseNode.Handler type={TypeNode.Condition} />
    </BaseNode>
  );
}
