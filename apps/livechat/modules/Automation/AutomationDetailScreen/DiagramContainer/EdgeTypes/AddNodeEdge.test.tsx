import { render, screen } from '@testing-library/react';
import { Position } from 'reactflow';
import { AddNodeEdge } from './AddNodeEdge';
import { CommonMenuAddPopup } from './CommonMenuAddPopup';

// Mock the CommonMenuAddPopup component
jest.mock('./CommonMenuAddPopup', () => ({
  CommonMenuAddPopup: jest.fn(() => <div data-testid='common-menu-add-popup'>Mock Menu</div>),
}));

// Mock reactflow's getSmoothStepPath
jest.mock('reactflow', () => ({
  ...jest.requireActual('reactflow'),
  getSmoothStepPath: jest.fn(() => ['mockPath', 100, 100]),
  BaseEdge: jest.fn(({ path, markerEnd, style }) => (
    <div data-testid='base-edge' data-path={path} data-marker-end={markerEnd} style={style}>
      <PERSON><PERSON>
    </div>
  )),
  EdgeLabelRenderer: jest.fn(({ children }) => (
    <div data-testid='edge-label-renderer'>{children}</div>
  )),
}));

describe('AddNodeEdge', () => {
  const defaultProps = {
    id: 'edge-1',
    sourceX: 0,
    sourceY: 0,
    targetX: 100,
    targetY: 100,
    sourcePosition: Position.Right,
    targetPosition: Position.Left,
    style: { stroke: 'black' },
    markerEnd: 'arrow',
    source: 'node-1',
    target: 'node-2',
    data: {
      actualParentNodeId: 'parent-1',
    },
    animated: false,
    selected: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the base edge with correct props', () => {
    render(<AddNodeEdge {...defaultProps} />);

    const baseEdge = screen.getByTestId('base-edge');
    expect(baseEdge).toBeInTheDocument();
    expect(baseEdge).toHaveAttribute('data-path', 'mockPath');
    expect(baseEdge).toHaveAttribute('data-marker-end', 'arrow');
    expect(baseEdge).toHaveStyle({ stroke: 'black' });
  });

  it('renders the edge label renderer with correct positioning', () => {
    render(<AddNodeEdge {...defaultProps} />);

    const edgeLabelRenderer = screen.getByTestId('edge-label-renderer');
    expect(edgeLabelRenderer).toBeInTheDocument();
  });

  it('renders the CommonMenuAddPopup with correct props', () => {
    render(<AddNodeEdge {...defaultProps} />);

    expect(CommonMenuAddPopup).toHaveBeenCalledWith(
      {
        source: 'node-1',
        target: 'node-2',
        actualParentNodeId: 'parent-1',
      },
      expect.any(Object)
    );
  });

  it('applies custom style to the edge', () => {
    const customStyle = { stroke: 'red', strokeWidth: 2 };
    render(<AddNodeEdge {...defaultProps} style={customStyle} />);

    const baseEdge = screen.getByTestId('base-edge');
    expect(baseEdge).toHaveStyle(customStyle);
  });

  it('handles missing style prop', () => {
    const { style, ...propsWithoutStyle } = defaultProps;
    render(<AddNodeEdge {...propsWithoutStyle} />);

    const baseEdge = screen.getByTestId('base-edge');
    expect(baseEdge).toBeInTheDocument();
  });

  it('handles missing markerEnd prop', () => {
    const { markerEnd, ...propsWithoutMarker } = defaultProps;
    render(<AddNodeEdge {...propsWithoutMarker} />);

    const baseEdge = screen.getByTestId('base-edge');
    expect(baseEdge).toBeInTheDocument();
  });
});
