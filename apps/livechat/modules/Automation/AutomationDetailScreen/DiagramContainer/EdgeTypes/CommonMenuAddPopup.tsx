import { Center, Menu, Modal, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlus } from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import { Buttons } from '../../../../Common';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { AddNodeType, ConnectionData, TypeNode } from '../../types';

const useStyle = createStyles((theme) => ({
  iconHolder: {
    display: 'flex',
    justifyContent: 'center',
    backgroundColor: 'white',
    alignItems: 'center',
    border: `3px solid ${theme.colors.navy[0]}`,
    borderRadius: theme.radius.lg,
    cursor: 'pointer',
    '&:hover': {
      boxShadow: `0 0 0 3px ${theme.colors.violet[1]}`,
    },
  },
  iconStyle: {
    color: theme.colors.navy[0],
  },
}));

export function CommonMenuAddPopup(data: ConnectionData) {
  const { classes } = useStyle();
  const [opened, setOpened] = useState(false);
  const { t, addNodeByType, exceedLimitNodes, isManager } = useAutomationDetailContext();
  const { visible: visibleExceedWarning, toggle: toggleExceedWarning } = useVisibilityControl();

  useEffect(() => {
    const callback = (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('react-flow__pane')) setOpened(false);
    };

    window.addEventListener('click', callback);
    return () => window.removeEventListener('click', callback);
  }, []);

  const onItemClick = (data: ConnectionData, type: AddNodeType) => {
    if (exceedLimitNodes) {
      toggleExceedWarning();
      return;
    }
    addNodeByType(data, type);
  };

  return (
    <>
      <Menu
        shadow='md'
        width={200}
        position='right-start'
        offset={20}
        opened={opened}
        onChange={setOpened}
      >
        <Menu.Target>
          <div className={classes.iconHolder} aria-label='Add Node'>
            <IconPlus className={classes.iconStyle} size={'20px'} />
          </div>
        </Menu.Target>
        <Menu.Dropdown style={{ zIndex: 1000 }}>
          <Menu.Item disabled={!isManager} onClick={() => onItemClick(data, TypeNode.Condition)}>
            {t('action.add.new.condition')}
          </Menu.Item>
          <Menu.Item disabled={!isManager} onClick={() => onItemClick(data, TypeNode.Action)}>
            {t('action.add.new.action')}
          </Menu.Item>
          {/* <Menu.Item onClick={() => onItemClick(data, TypeNode.Branch)}>
                        {t('action.add.new.branch')}
                    </Menu.Item> */}
        </Menu.Dropdown>
      </Menu>
      <Modal
        opened={visibleExceedWarning}
        centered
        onClose={toggleExceedWarning}
        withCloseButton={false}
        radius={8}
      >
        <Modal.CloseButton size={'lg'} />
        <Center>
          <Text style={{ whiteSpace: 'pre-line', lineHeight: 1.7 }} fw={600}>
            {t('exceed_limit_warning')}
          </Text>
        </Center>
        <Center m={40}>
          <Buttons.Cancel label={t('close')} onClick={toggleExceedWarning} />
        </Center>
      </Modal>
    </>
  );
}
