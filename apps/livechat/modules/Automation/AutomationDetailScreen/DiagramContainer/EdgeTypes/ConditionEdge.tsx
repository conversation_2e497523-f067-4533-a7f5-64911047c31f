import React, { CSSProperties, useMemo } from 'react';
import { BaseEdge, EdgeLabelRenderer, EdgeProps, getSmoothStepPath } from 'reactflow';
import { useAutomationDetailContext } from '../../contexts/AutomationDetailContext';
import { EdgePayloadData, TypeEdge } from '../../types';
import { CommonMenuAddPopup } from './CommonMenuAddPopup';

export function ConditionEdge({
  // eslint-disable-next-line no-unused-vars
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  source,
  target,
}: EdgeProps) {
  const { conditionEdge = TypeEdge.FalseEdge, actualParentNodeId } = data as EdgePayloadData;
  const { t } = useAutomationDetailContext();
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY: sourceY - 5,
    sourcePosition,
    targetX,
    targetY: targetY + 5,
    targetPosition,
  });
  const isFalseEdge = useMemo(() => conditionEdge === TypeEdge.FalseEdge, [conditionEdge]);
  const stylesForTypeEdge: CSSProperties = useMemo(() => {
    return {
      position: 'absolute',
      background: isFalseEdge ? '#FFF0F6' : '#D3F9D8',
      borderRadius: '5px',
      padding: `5px 10px`,
      color: isFalseEdge ? '#FF8787' : '#69DB7C',
      fontSize: 12,
      fontWeight: 700,
      transform: `translate(-50%, -100%) translate(${targetX}px,${targetY - 50}px)`,
    };
  }, [targetX, targetY, isFalseEdge]);
  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      <EdgeLabelRenderer>
        <div style={stylesForTypeEdge} className='nodrag nopan'>
          {t(isFalseEdge ? 'false_branch' : 'true_branch')}
        </div>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${targetX}px,${targetY - 20}px)`,
            fontSize: 12,
            // everything inside EdgeLabelRenderer has no pointer events by default
            // if you have an interactive element, set pointer-events: all
            pointerEvents: 'all',
          }}
          className='nodrag nopan'
        >
          <CommonMenuAddPopup
            source={source}
            target={target}
            boolEdge={!isFalseEdge}
            actualParentNodeId={actualParentNodeId}
          />
        </div>
      </EdgeLabelRenderer>
    </>
  );
}
