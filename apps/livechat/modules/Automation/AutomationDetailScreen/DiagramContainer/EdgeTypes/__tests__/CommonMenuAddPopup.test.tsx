import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import useVisibilityControl from '../../../../../../hooks/useVisibilityControl';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { TypeNode } from '../../../types';
import { CommonMenuAddPopup } from '../CommonMenuAddPopup';

// Mock the context and hooks
jest.mock('../../../contexts/AutomationDetailContext');
jest.mock('../../../../../../hooks/useVisibilityControl');

describe('CommonMenuAddPopup', () => {
  const mockAddNodeByType = jest.fn();
  const mockToggleExceedWarning = jest.fn();
  const mockT = jest.fn((key) => key);

  const defaultProps = {
    source: 'source-1',
    target: 'target-1',
    sourceHandle: 'handle-1',
    targetHandle: 'handle-2',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: mockT,
      addNodeByType: mockAddNodeByType,
      exceedLimitNodes: false,
      isManager: true,
    });
    (useVisibilityControl as jest.Mock).mockReturnValue({
      visible: false,
      toggle: mockToggleExceedWarning,
    });
  });

  function renderWithProvider(ui: React.ReactElement) {
    const theme = {
      colors: {
        navy: ['#001F3F', '#003366'],
        violet: ['#7F00FF', '#E0BBE4'],
      },
      radius: { lg: 8 },
    };
    return render(
      <MantineProvider theme={theme as any}>
        <MantineEmotionProvider>{ui}</MantineEmotionProvider>
      </MantineProvider>
    );
  }

  it('renders the add button', () => {
    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    expect(screen.getByLabelText('Add Node')).toBeInTheDocument();
  });

  it('opens menu when clicking the add button', async () => {
    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Add Node'));
    expect(await screen.findByText('action.add.new.condition')).toBeInTheDocument();
    expect(await screen.findByText('action.add.new.action')).toBeInTheDocument();
  });

  it('adds a condition node when clicking the condition option', async () => {
    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Add Node'));
    fireEvent.click(await screen.findByText('action.add.new.condition'));
    expect(mockAddNodeByType).toHaveBeenCalledWith(defaultProps, TypeNode.Condition);
  });

  it('adds an action node when clicking the action option', async () => {
    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Add Node'));
    fireEvent.click(await screen.findByText('action.add.new.action'));
    expect(mockAddNodeByType).toHaveBeenCalledWith(defaultProps, TypeNode.Action);
  });

  it('shows exceed limit warning when trying to add a node with exceedLimitNodes true', async () => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: mockT,
      addNodeByType: mockAddNodeByType,
      exceedLimitNodes: true,
      isManager: true,
    });

    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Add Node'));
    fireEvent.click(await screen.findByText('action.add.new.condition'));

    expect(mockToggleExceedWarning).toHaveBeenCalled();
    expect(mockAddNodeByType).not.toHaveBeenCalled();
  });

  it('disables menu items when user is not a manager', async () => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: mockT,
      addNodeByType: mockAddNodeByType,
      exceedLimitNodes: false,
      isManager: false,
    });

    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Add Node'));

    const conditionItem = await screen.findByText('action.add.new.condition');
    const actionItem = await screen.findByText('action.add.new.action');
    // Check for aria-disabled, disabled class, tabIndex, or focusability
    const isDisabled = (el: HTMLElement) => {
      const ancestor =
        el.closest('[aria-disabled="true"]') || el.closest('.mantine-Menu-itemDisabled');
      if (ancestor) return true;
      if (el.getAttribute('tabindex') === '-1') return true;
      // Try to focus the element
      el.focus();
      return document.activeElement !== el;
    };
    expect(isDisabled(conditionItem)).toBe(true);
    expect(isDisabled(actionItem)).toBe(true);
  });

  it('closes menu when clicking outside', async () => {
    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);

    // Open menu
    fireEvent.click(screen.getByLabelText('Add Node'));
    expect(await screen.findByText('action.add.new.condition')).toBeInTheDocument();

    // Simulate click outside on .react-flow__pane
    const pane = document.createElement('div');
    pane.className = 'react-flow__pane';
    document.body.appendChild(pane);
    fireEvent.click(pane);
    // Wait for the menu to close
    await waitFor(() => {
      expect(screen.queryByText('action.add.new.condition')).not.toBeInTheDocument();
    });
    document.body.removeChild(pane);
  });

  it('shows exceed limit warning modal when visible is true', () => {
    (useVisibilityControl as jest.Mock).mockReturnValue({
      visible: true,
      toggle: mockToggleExceedWarning,
    });

    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    expect(screen.getByText('exceed_limit_warning')).toBeInTheDocument();
    expect(screen.getByText('close')).toBeInTheDocument();
  });

  it('closes exceed limit warning modal when clicking close button', () => {
    (useVisibilityControl as jest.Mock).mockReturnValue({
      visible: true,
      toggle: mockToggleExceedWarning,
    });

    renderWithProvider(<CommonMenuAddPopup {...defaultProps} />);
    fireEvent.click(screen.getByText('close'));
    expect(mockToggleExceedWarning).toHaveBeenCalled();
  });
});
