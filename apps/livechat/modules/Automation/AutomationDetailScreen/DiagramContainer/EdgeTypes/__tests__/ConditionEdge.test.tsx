import { MantineProvider } from '@mantine/core';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import { render, screen } from '@testing-library/react';
import { Position, ReactFlowProvider } from 'reactflow';
import { useAutomationDetailContext } from '../../../contexts/AutomationDetailContext';
import { TypeEdge } from '../../../types';
import { ConditionEdge } from '../ConditionEdge';

// Mock the CommonMenuAddPopup component
jest.mock('../CommonMenuAddPopup', () => ({
  CommonMenuAddPopup: ({ boolEdge, source, target, actualParentNodeId }: any) => (
    <div
      data-testid='common-menu-add-popup'
      data-bool-edge={boolEdge}
      data-source={source}
      data-target={target}
      data-parent-id={actualParentNodeId}
    >
      Mocked CommonMenuAddPopup
    </div>
  ),
}));

// Mock the useAutomationDetailContext hook
jest.mock('../../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(),
}));

// Mock BaseEdge and EdgeLabelRenderer from reactflow
jest.mock('reactflow', () => {
  const actual = jest.requireActual('reactflow');
  return {
    ...actual,
    BaseEdge: ({ children }: any) => <div data-testid='base-edge'>{children}</div>,
    EdgeLabelRenderer: ({ children }: any) => <div>{children}</div>,
    Position: actual.Position,
    ReactFlowProvider: actual.ReactFlowProvider,
  };
});

const mockT = (key: string) => key;

const defaultProps = {
  id: 'edge-1',
  sourceX: 100,
  sourceY: 100,
  targetX: 200,
  targetY: 200,
  sourcePosition: Position.Right,
  targetPosition: Position.Left,
  style: {},
  data: {
    conditionEdge: TypeEdge.TrueEdge,
    actualParentNodeId: 'parent-1',
  },
  markerEnd: 'arrow',
  source: 'node-1',
  target: 'node-2',
};

const renderWithProvider = (component: React.ReactNode) => {
  return render(
    <ReactFlowProvider>
      <MantineProvider>
        <ModalsProvider>
          <Notifications />
          {component}
        </ModalsProvider>
      </MantineProvider>
    </ReactFlowProvider>
  );
};

describe('ConditionEdge', () => {
  beforeEach(() => {
    (useAutomationDetailContext as jest.Mock).mockReturnValue({
      t: mockT,
    });
  });

  it('renders true edge with correct label and color', () => {
    renderWithProvider(<ConditionEdge {...defaultProps} />);

    const label = screen.getByText('true_branch');
    expect(label).toBeInTheDocument();
    expect(label).toHaveStyle({
      background: '#D3F9D8',
      color: '#69DB7C',
    });
  });

  it('renders false edge with correct label and color', () => {
    const falseEdgeProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        conditionEdge: TypeEdge.FalseEdge,
      },
    };

    renderWithProvider(<ConditionEdge {...falseEdgeProps} />);

    const label = screen.getByText('false_branch');
    expect(label).toBeInTheDocument();
    expect(label).toHaveStyle({
      background: '#FFF0F6',
      color: '#FF8787',
    });
  });

  it('renders CommonMenuAddPopup with correct props', () => {
    renderWithProvider(<ConditionEdge {...defaultProps} />);

    const menuPopup = screen.getByTestId('common-menu-add-popup');
    expect(menuPopup).toBeInTheDocument();
    expect(menuPopup).toHaveAttribute('data-bool-edge', 'true');
    expect(menuPopup).toHaveAttribute('data-source', 'node-1');
    expect(menuPopup).toHaveAttribute('data-target', 'node-2');
    expect(menuPopup).toHaveAttribute('data-parent-id', 'parent-1');
  });

  it('renders CommonMenuAddPopup with false boolEdge for false edge', () => {
    const falseEdgeProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        conditionEdge: TypeEdge.FalseEdge,
      },
    };

    renderWithProvider(<ConditionEdge {...falseEdgeProps} />);

    const menuPopup = screen.getByTestId('common-menu-add-popup');
    expect(menuPopup).toHaveAttribute('data-bool-edge', 'false');
  });

  it('applies correct positioning styles to labels', () => {
    renderWithProvider(<ConditionEdge {...defaultProps} />);

    const label = screen.getByText('true_branch');
    expect(label).toHaveStyle({
      position: 'absolute',
      borderRadius: '5px',
      padding: '5px 10px',
      fontSize: '12px',
      fontWeight: '700',
    });
  });

  it('renders BaseEdge with correct props', () => {
    renderWithProvider(<ConditionEdge {...defaultProps} />);

    // BaseEdge is rendered by reactflow, so we can't directly test it
    // But we can verify that the component renders without errors
    expect(screen.getByText('true_branch')).toBeInTheDocument();
  });
});
