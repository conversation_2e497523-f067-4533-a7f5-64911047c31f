import { ElkExtendedEdge } from 'elkjs';
import { useEffect, useMemo } from 'react';
import { ReactFlowProvider, useEdgesState, useNodesState } from 'reactflow';
import { useAutomationLayoutContext } from '../AutomationLayoutContext';
import { useAutomationDetailContext } from '../contexts/AutomationDetailContext';
import Diagram from './Diagram';
import { DiagramHelper } from './DiagramHelpers';

export default function DiagramContainerHolder() {
  const { width, height, middleWidth } = useAutomationLayoutContext();
  const { renderedEdges, renderedNodes } = useAutomationDetailContext();

  const [nodes, setNodes] = useNodesState([]);
  const [edges, setEdges] = useEdgesState([]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (window) {
        const element = window.document.querySelector('.react-flow__attribution') as HTMLDivElement;
        element && (element.style.display = 'none');
      }
    }, 100);
    return () => clearTimeout(timeout);
  }, []);

  const edgeForCalculate: ElkExtendedEdge[] = useMemo(
    () =>
      renderedEdges.map((item) => {
        // eslint-disable-next-line no-unused-vars
        const { type, ...rest } = item;
        return { ...rest, sources: [item.source], targets: [item.target] };
      }),
    [renderedEdges]
  );

  useEffect(() => {
    // No need to watch change for middleWidth
    // or may be improved later.
    if (!middleWidth) return;
    const layoutCalculatedData = async () =>
      await DiagramHelper.getLayoutCalculateElk(renderedNodes, edgeForCalculate);
    layoutCalculatedData().then((data) => {
      if (data) {
        const nodesForDiagram = DiagramHelper.getNodesForDiagram(
          renderedNodes,
          data.children,
          middleWidth
        );
        const edgesForDiagram = DiagramHelper.getEdgesForDiagram(renderedEdges, data.edges);
        setNodes([...nodesForDiagram]);
        setEdges([...edgesForDiagram]);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [edgeForCalculate, setNodes, setEdges, renderedNodes, renderedEdges, middleWidth]);

  return (
    <div
      style={{ width: `${width}px`, height: `${height}px` }}
      data-testid='diagram-container-holder'
    >
      <ReactFlowProvider>
        <Diagram nodes={nodes} edges={edges} data-testid='diagram-component' />
      </ReactFlowProvider>
    </div>
  );
}
