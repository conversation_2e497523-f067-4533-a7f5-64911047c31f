import { render, screen } from '@testing-library/react';
import React from 'react';
import DiagramContainerHolder from '../DiagramContainer';

// Mock the context hooks
jest.mock('../../AutomationLayoutContext', () => ({
  useAutomationLayoutContext: jest.fn(() => ({
    width: 800,
    height: 600,
    middleWidth: 400,
    middleHeight: 300,
  })),
}));

jest.mock('../../contexts/AutomationDetailContext', () => ({
  useAutomationDetailContext: jest.fn(() => ({
    renderedNodes: [{ id: 'node1', type: 'default' }],
    renderedEdges: [{ id: 'edge1', source: 'node1', target: 'node2' }],
  })),
}));

// Mock DiagramHelper
jest.mock('../DiagramHelpers', () => ({
  DiagramHelper: {
    getLayoutCalculateElk: jest.fn().mockResolvedValue({
      children: [{ id: 'node1' }],
      edges: [{ id: 'edge1' }],
    }),
    getNodesForDiagram: jest.fn().mockReturnValue([{ id: 'node1', data: { label: 'Node 1' } }]),
    getEdgesForDiagram: jest
      .fn()
      .mockReturnValue([{ id: 'edge1', source: 'node1', target: 'node2' }]),
  },
}));

// Mock Diagram component
jest.mock('../Diagram', () => ({
  __esModule: true,
  default: ({ nodes, edges }: any) => (
    <div data-testid='diagram-component'>
      <div data-testid='diagram-nodes'>{JSON.stringify(nodes)}</div>
      <div data-testid='diagram-edges'>{JSON.stringify(edges)}</div>
    </div>
  ),
}));

// Mock ReactFlowProvider
jest.mock('reactflow', () => ({
  ReactFlowProvider: ({ children }: any) => <div data-testid='react-flow-provider'>{children}</div>,
  useNodesState: () => [[], jest.fn()],
  useEdgesState: () => [[], jest.fn()],
}));

describe('DiagramContainerHolder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with correct dimensions from context', async () => {
    render(<DiagramContainerHolder />);
    const container = screen.getByTestId('diagram-container-holder');
    expect(container).toHaveStyle({ width: '800px', height: '600px' });
  });

  it('renders Diagram component with ReactFlowProvider', async () => {
    render(<DiagramContainerHolder />);
    expect(screen.getByTestId('react-flow-provider')).toBeInTheDocument();
    expect(screen.getByTestId('diagram-component')).toBeInTheDocument();
  });

  it('hides react-flow attribution', () => {
    const attribution = document.createElement('div');
    attribution.className = 'react-flow__attribution';
    document.body.appendChild(attribution);

    jest.useFakeTimers();
    render(<DiagramContainerHolder />);
    jest.runAllTimers();

    expect(attribution.style.display).toBe('none');
    document.body.removeChild(attribution);
    jest.useRealTimers();
  });
});
