import { render, screen } from '@testing-library/react';
import React from 'react';
import Diagram from '../Diagram';

// Mock Flow component
jest.mock('../_Flow', () => ({
  Flow: ({ nodes, edges }: any) => (
    <div data-testid='mock-flow'>
      <div data-testid='nodes'>{JSON.stringify(nodes)}</div>
      <div data-testid='edges'>{JSON.stringify(edges)}</div>
    </div>
  ),
}));

describe('Diagram', () => {
  it('renders without crashing', () => {
    render(<Diagram nodes={[]} edges={[]} />);
    expect(screen.getByTestId('mock-flow')).toBeInTheDocument();
  });

  it('passes nodes and edges to Flow', () => {
    const nodes = [
      { id: '1', type: 'test', data: { label: 'A' }, position: { x: 0, y: 0 } },
      { id: '2', type: 'test', data: { label: 'B' }, position: { x: 10, y: 10 } },
    ];
    const edges = [{ id: 'e1-2', source: '1', target: '2', type: 'test' }];
    render(<Diagram nodes={nodes} edges={edges} />);
    expect(screen.getByTestId('nodes').textContent).toContain('A');
    expect(screen.getByTestId('nodes').textContent).toContain('B');
    expect(screen.getByTestId('edges').textContent).toContain('e1-2');
  });

  it('renders with default empty nodes and edges', () => {
    // @ts-expect-error: testing default props
    render(<Diagram />);
    expect(screen.getByTestId('nodes').textContent).toBe('[]');
    expect(screen.getByTestId('edges').textContent).toBe('[]');
  });
});
