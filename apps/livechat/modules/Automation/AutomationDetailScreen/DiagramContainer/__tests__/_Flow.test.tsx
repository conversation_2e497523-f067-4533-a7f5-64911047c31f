import { screen } from '@testing-library/react';
import { Edge, Node } from 'reactflow';
import { renderWithProvider } from '../../../../../utils/testing';
import { TypeEdge, TypeNode } from '../../types';
import { Flow } from '../_Flow';

// Mock node and edge components only
jest.mock('../NodeTypes/TriggerNode', () => ({
  TriggerNode: () => <div data-testid='trigger-node' />,
}));

jest.mock('../NodeTypes/ActionNode', () => ({
  ActionNode: () => <div data-testid='action-node' />,
}));

jest.mock('../EdgeTypes/AddNodeEdge', () => ({
  AddNodeEdge: () => <div data-testid='add-node-edge' />,
}));

describe('Flow Component', () => {
  const mockNodes: Node[] = [
    {
      id: '1',
      type: TypeNode.Trigger,
      position: { x: 0, y: 0 },
      data: { label: 'Trigger' },
    },
    {
      id: '2',
      type: TypeNode.Action,
      position: { x: 100, y: 100 },
      data: { label: 'Action' },
    },
  ];

  const mockEdges: Edge[] = [
    {
      id: 'e1-2',
      source: '1',
      target: '2',
      type: TypeEdge.Add,
    },
  ];

  it('renders the Flow component with nodes and edges', () => {
    renderWithProvider(<Flow nodes={mockNodes} edges={mockEdges} />);
    // Check for the presence of mocked node components by their test IDs
    expect(screen.getByTestId('trigger-node')).toBeInTheDocument();
    expect(screen.getByTestId('action-node')).toBeInTheDocument();
  });

  it('renders Background and Controls components', () => {
    renderWithProvider(<Flow nodes={mockNodes} edges={mockEdges} />);
    // Check for the presence of mocked node components as a proxy for successful render
    expect(screen.getByTestId('trigger-node')).toBeInTheDocument();
    expect(screen.getByTestId('action-node')).toBeInTheDocument();
  });

  it('applies the correct CSS class', () => {
    const { container } = renderWithProvider(<Flow nodes={mockNodes} edges={mockEdges} />);
    // Check that the .react-flow element exists
    const flowDiv = container.querySelector('.react-flow');
    expect(flowDiv).toBeInTheDocument();
  });
});
