import { v4 as uuidV4 } from 'uuid';
import { AutomationData } from '../../../../../models/automation';
import {
  ActionNode,
  ActionNodeActionType,
  ConditionNode,
  ConditionParams,
  ConditionType,
  NewAssignTeamData,
  RULES_SELECT_OPERATOR,
  ScenarioAssignType,
  TypeEdge,
  TypeNode,
} from '../../types';
import { DiagramHelper } from '../DiagramHelpers';

// Mock uuid to return predictable values
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid'),
}));

describe('DiagramHelper', () => {
  describe('getLayoutCalculateElk', () => {
    it('should calculate layout for nodes and edges', async () => {
      const nodes = [
        {
          id: 'node1',
          type: TypeNode.Action,
          data: {
            label: 'Action 1',
            actionData: {
              action: ActionNodeActionType.Assign,
              payload: {
                scenario: ScenarioAssignType.TEAM,
                data: {
                  teamId: 'team1',
                  rule: RULES_SELECT_OPERATOR.RANDOM,
                },
              },
            },
          },
        },
        {
          id: 'node2',
          type: TypeNode.Condition,
          data: {
            conditions: [
              {
                fact: 'test',
                path: '$.test',
                value: 'test-value',
              },
            ],
          },
        },
      ];
      const edges = [{ id: 'edge1', source: 'node1', target: 'node2' }];

      const result = await DiagramHelper.getLayoutCalculateElk(nodes, edges);
      expect(result).toBeDefined();
      expect(result.children).toHaveLength(2);
      expect(result.edges).toHaveLength(1);
    });

    it('should handle empty input', async () => {
      const result = await DiagramHelper.getLayoutCalculateElk([], []);
      expect(result).toBeDefined();
      expect(result.children).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });
  });

  describe('getNodesForDiagram', () => {
    it('should transform nodes with correct positions', () => {
      const originalNodes = [
        { id: 'node1', type: TypeNode.Action, data: { label: 'Action 1' } },
        { id: 'node2', type: TypeNode.Condition, data: { label: 'Condition 1' } },
      ];
      const positionCalculatedNodes = [
        { id: 'node1', x: 100, y: 100 },
        { id: 'node2', x: 200, y: 200 },
      ];
      const centerScreenPx = 500;

      const result = DiagramHelper.getNodesForDiagram(
        originalNodes,
        positionCalculatedNodes,
        centerScreenPx
      );
      expect(result).toHaveLength(2);
      expect(result[0].position).toBeDefined();
      expect(result[1].position).toBeDefined();
    });

    it('should handle empty input', () => {
      const result = DiagramHelper.getNodesForDiagram([], [], 500);
      expect(result).toHaveLength(0);
    });
  });

  describe('getEdgesForDiagram', () => {
    it('should transform edges correctly', () => {
      const originalEdges = [{ id: 'edge1', source: 'node1', target: 'node2', type: TypeEdge.Add }];
      const positionCalculatedEdges = [{ id: 'edge1', source: 'node1', target: 'node2' }];

      const result = DiagramHelper.getEdgesForDiagram(originalEdges, positionCalculatedEdges);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('edge1');
      expect(result[0].source).toBe('node1');
      expect(result[0].target).toBe('node2');
    });

    it('should handle empty input', () => {
      const result = DiagramHelper.getEdgesForDiagram([], []);
      expect(result).toHaveLength(0);
    });
  });

  describe('convertRuleEngineToNodesAndEdges', () => {
    it('should convert automation data to nodes and edges', () => {
      const automation: AutomationData = {
        name: 'Test Automation',
        status: 'active',
        desc: 'Test Description',
        owner: { id: 'test-owner', name: 'Test Owner' },
        trigger: 'test-trigger',
        flow: {
          id: 'flow1',
          type: TypeNode.Action,
          config: {
            action: ActionNodeActionType.Assign,
            payload: {
              scenario: ScenarioAssignType.TEAM,
              data: {
                teamId: 'team1',
                rule: RULES_SELECT_OPERATOR.RANDOM,
              },
            },
          },
        } as unknown as JSON,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
      };
      const callbackList = {
        showTriggerModal: jest.fn(),
        showActionModal: jest.fn(),
        showConditionModal: jest.fn(),
      };
      const errors = {};

      const result = DiagramHelper.convertRuleEngineToNodesAndEdges(
        automation,
        callbackList,
        errors
      );
      expect(result.nodes).toBeDefined();
      expect(result.edges).toBeDefined();
      expect(result.nodes.length).toBeGreaterThan(0);
    });
  });

  describe('addNodeByType', () => {
    it('should add a new node to the flow', () => {
      const node = {
        flow: {
          id: 'flow1',
          type: TypeNode.Action,
          config: {
            action: ActionNodeActionType.Assign,
            payload: {
              scenario: ScenarioAssignType.TEAM,
              data: {
                teamId: 'team1',
                rule: RULES_SELECT_OPERATOR.RANDOM,
              },
            },
            event: {
              params: {
                onSuccess: {},
                onFailure: {},
              },
            },
          },
        },
      };
      const connection = {
        source: 'flow1',
        target: 'node2',
        actualParentNodeId: 'flow1',
      };
      const type = TypeNode.Action;

      const result = DiagramHelper.addNodeByType(node, connection, type);
      expect(result.flow).toBeDefined();
      expect(result.flow.config.event.params.onSuccess).toBeDefined();
    });
  });

  describe('removeNodeById', () => {
    it('should remove a node from the flow', () => {
      const node = {
        flow: {
          id: 'flow1',
          type: TypeNode.Action,
          config: {
            action: 'test-action',
            payload: {},
            event: {
              params: {
                onSuccess: {
                  id: 'node2',
                  type: TypeNode.Action,
                },
              },
            },
          },
        },
      };
      const parentNodeId = 'flow1';
      const nodeIdToRemove = 'node2';

      const result = DiagramHelper.removeNodeById(node, parentNodeId, nodeIdToRemove);
      expect(result.flow.config.event.params.onSuccess).toEqual({});
    });
  });

  describe('updateNodeData', () => {
    it('should update action node data', () => {
      const node = {
        flow: {
          id: 'flow1',
          type: TypeNode.Action,
          config: {
            action: ActionNodeActionType.Assign,
            payload: {
              scenario: ScenarioAssignType.TEAM,
              data: {
                teamId: 'team1',
                rule: RULES_SELECT_OPERATOR.RANDOM,
              },
            },
          },
        },
      };
      const nodeId = 'flow1';
      const data: ActionNode = {
        id: 'flow1',
        action: ActionNodeActionType.Assign,
        payload: {
          scenario: ScenarioAssignType.TEAM,
          data: {
            teamId: 'team2',
            rule: RULES_SELECT_OPERATOR.ROUND_ROBIN,
          },
        },
      };

      const result = DiagramHelper.updateNodeData(node, nodeId, data);
      expect(result.flow.config.action).toBe(ActionNodeActionType.Assign);
      expect(result.flow.config.payload).toEqual(data.payload);
    });

    it('should update condition node data', () => {
      const node = {
        flow: {
          id: 'flow1',
          type: TypeNode.Condition,
          config: {
            conditions: {
              all: [],
            },
          },
        },
      };
      const nodeId = 'flow1';
      const data: ConditionNode = {
        id: 'flow1',
        type: 'all' as ConditionType,
        conditions: [
          {
            fact: 'test',
            path: '$.test',
            value: 'test-value',
          },
        ],
      };

      const result = DiagramHelper.updateNodeData(node, nodeId, data);
      expect(result.flow.config.conditions.all).toEqual(data.conditions);
    });
  });
});
