import { createStyles } from '@mantine/emotion';
import ReactFlow, { Background, ConnectionLineType, Controls, Edge, Node } from 'reactflow';
import 'reactflow/dist/style.css';
import { TypeEdge, TypeNode } from '../types';
import { AddNodeEdge, ConditionEdge } from './EdgeTypes';
import { EndNode, TriggerNode } from './NodeTypes';
import { ActionNode } from './NodeTypes/ActionNode';
import { BranchNode } from './NodeTypes/BranchNode';
import { BranchToOtherNode } from './NodeTypes/BranchToOtherNode';
import { ConditionNode } from './NodeTypes/ConditionNode';

const useStyle = createStyles(() => ({
  flowStyle: {
    '& .react-flow__handle': {
      opacity: 0,
    },
    '& .react-flow__edgelabel-renderer': {
      zIndex: 1,
    },
  },
}));

type FlowProps = {
  nodes: Node[];
  edges: Edge[];
};

const nodeTypes = {
  [TypeNode.Trigger]: TriggerNode,
  [TypeNode.Condition]: ConditionNode,
  [TypeNode.Action]: ActionNode,
  [TypeNode.End]: EndNode,
  [TypeNode.Branch]: BranchNode,
  [TypeNode.BranchToOtherNode]: BranchToOtherNode,
};

const edgeTypes = {
  [TypeEdge.Add]: AddNodeEdge,
  [TypeEdge.TrueEdge]: ConditionEdge,
  [TypeEdge.FalseEdge]: ConditionEdge,
};
export function Flow({ nodes, edges }: FlowProps) {
  const { classes } = useStyle();
  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      minZoom={0.75}
      maxZoom={0.95}
      className={classes.flowStyle}
      panOnScroll={true}
      edgeTypes={edgeTypes}
      nodeTypes={nodeTypes}
      connectionLineType={ConnectionLineType.Step}
    >
      <Background />
      <Controls position='bottom-right' />
    </ReactFlow>
  );
}
