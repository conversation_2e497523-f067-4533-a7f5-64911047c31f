import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Edge, Node } from 'reactflow';
import useVisibilityControl from '../../../../hooks/useVisibilityControl';
import { AutomationData, Status } from '../../../../models/automation';
import ApiService from '../../../../services/api';
import { delay } from '../../../../utils/common';
import { useTeamContext } from '../../../teamContext';
import { useUserContext } from '../../../userContext';
import { useWidgetContext } from '../../../widgetContext';
import { DiagramHelper } from '../DiagramContainer/DiagramHelpers';
import { MAX_NODE_IN_DIAGRAM } from '../constant';
import {
  ActionNode,
  AddNodeType,
  CallbackList,
  ConditionNode,
  ConnectionData,
  ModalActivityType,
  SelectConditionMappingType,
  TriggerType,
} from '../types';
import { useInitSelectConditionModel } from './initialAutomationData';
import { useAutomationDetailData } from './useAutomationDetailData';
import { useOcsChannelsData } from './useOcsChannelsData';
import { newObject, tryToParseJsonString } from './utils';

export function useAutomationDetail(automationId: string | null | undefined) {
  const router = useRouter();
  const { t } = useTranslate('workspace');
  const isEditAutomation = useMemo(() => !!automationId, [automationId]);
  const modalActivities = useVisibilityControl();
  const [currentModalType, setCurrModalType] = useState<ModalActivityType | null>(null);
  const { isManager, userProfile = null, listOperators, reloadListOperator } = useUserContext();
  const { crmFieldsByObject } = useWidgetContext();
  const conditionSelectModel = useInitSelectConditionModel();
  const { automationData, setAutomationData, isLoading, error, reload } = useAutomationDetailData(
    automationId,
    0
  );

  const {
    ocsChannelsOptions,
    ocsChannelsOptionsWithDefaultOption,
    allOscChannel,
    webhookOptions,
    isLoading: isLoadingOcsChannels,
  } = useOcsChannelsData();

  const { teamList, reloadTeamList } = useTeamContext();
  const [savingAutomation, setSavingAutomation] = useState(false);
  const [activeState, setActiveState] = useState<Status>('active');
  const [renderedNodes, setRenderedNodes] = useState<Node[]>([]);
  const [renderedEdges, setRenderedEdges] = useState<Edge[]>([]);

  const [currentEditDataCondition, setCurrentEditDataCondition] = useState<ConditionNode>(null);
  const [currentEditDataAction, setCurrentEditDataAction] = useState<ActionNode>(null);
  const [selectConditionModel, setSelectConditionModel] =
    useState<SelectConditionMappingType[]>(conditionSelectModel);
  const [showTriggerErrorMessage, setShowTriggerErrorMessage] = useState(false);

  // This function will modify the current automation data, add new node based on type,
  // then return new object automationData
  const closeActivityModal = useCallback(() => {
    modalActivities.close();
  }, [modalActivities]);

  const checkMissingRequiredInfo = useCallback((automationData: AutomationData) => {
    const isSetTrigger = !!automationData.trigger;
    const flow = automationData?.flow || {};
    if (!flow || !Object.keys(flow).length || !isSetTrigger) return true;
    return false;
  }, []);

  const addNodeByType = useCallback(
    (data: ConnectionData, type: AddNodeType) => {
      if (!automationData) return;

      const newAutomation = DiagramHelper.addNodeByType(newObject(automationData), data, type);
      closeActivityModal();
      newAutomation && setAutomationData({ ...newAutomation });
    },
    [automationData, closeActivityModal, setAutomationData]
  );

  const intendToRemoveNode = useCallback(
    (parentNodeId: string, nodeIdToRemove: string) => {
      // Current business logic is when removing nodeId , we also remove all descendants of this node.
      // That is straightforward, easy to do.
      // In the future , may be should have to confirm to proceed, but now, just do it.
      if (!parentNodeId && !nodeIdToRemove) return;

      const newAutomation = DiagramHelper.removeNodeById(
        newObject(automationData),
        parentNodeId,
        nodeIdToRemove
      );
      if (newAutomation) {
        setAutomationData({ ...newAutomation });
        closeActivityModal();
      }
    },
    [automationData, closeActivityModal, setAutomationData]
  );

  const handleChangeModalType = useCallback(
    (nextModalType: ModalActivityType, callback?: () => void) => {
      if (modalActivities.visible) {
        closeActivityModal();
        return setTimeout(() => {
          setCurrModalType(nextModalType);
          callback?.();
        }, 300);
      } else {
        setCurrModalType(nextModalType);
        callback?.();
      }
    },
    [closeActivityModal, modalActivities.visible]
  );

  const showTriggerActivityModal = useCallback(() => {
    handleChangeModalType(ModalActivityType.TriggerActivityModal, modalActivities.open);
  }, [handleChangeModalType, modalActivities]);

  const showConditionActivityModal = useCallback(
    (idNode: string) => {
      const conditionNode = DiagramHelper.findDataNodeFromRuleEngineTemplate(
        automationData.flow as JSON,
        idNode
      );
      if (!conditionNode) return;
      setCurrentEditDataCondition(() => ({ ...conditionNode }) as ConditionNode);
      handleChangeModalType(ModalActivityType.ConditionActivityModal, modalActivities.open);
    },
    [automationData, handleChangeModalType, modalActivities.open]
  );

  const showActionActivityModal = useCallback(
    (idNode: string) => {
      const actionNode = DiagramHelper.findDataNodeFromRuleEngineTemplate(
        automationData.flow,
        idNode
      );
      if (!actionNode) return;
      setCurrentEditDataAction({ ...actionNode } as ActionNode);
      handleChangeModalType(ModalActivityType.ActionActivityModal, modalActivities.open);
    },
    [automationData, handleChangeModalType, modalActivities.open]
  );

  const setTriggerNodeData = useCallback(
    (triggerType: TriggerType) => {
      if (!automationData) return;
      const newAutomation = { ...automationData, trigger: triggerType };
      setAutomationData(newAutomation);
    },
    [automationData, setAutomationData]
  );

  const setConditionNodeData = useCallback(
    (conditionNode: ConditionNode) => {
      const { id, ...data } = conditionNode;
      if (!id) return;
      const newAutomation = DiagramHelper.updateNodeData(
        newObject(automationData),
        id,
        data as ConditionNode
      );
      newAutomation && setAutomationData({ ...newAutomation });
    },
    [automationData, setAutomationData]
  );

  const setActionNodeData = useCallback(
    (actionNode: ActionNode) => {
      const { id, ...data } = actionNode;
      if (!id) return;
      const newAutomation = DiagramHelper.updateNodeData(
        newObject(automationData),
        id,
        data as ActionNode
      );
      newAutomation && setAutomationData({ ...newAutomation });
    },
    [automationData, setAutomationData]
  );

  const setNameAutomation = useCallback(
    (name: string) => {
      setAutomationData({ ...automationData, name: name });
    },
    [automationData, setAutomationData]
  );

  const saveDataAutomation = useCallback(async () => {
    if (!automationData) return;
    const automationTmp = newObject(automationData);
    const isMissingRequireInfo = checkMissingRequiredInfo(automationTmp);
    if (isMissingRequireInfo) {
      !showTriggerErrorMessage && setShowTriggerErrorMessage(true);
    } else {
      showTriggerErrorMessage && setShowTriggerErrorMessage(false);
    }
    if (isMissingRequireInfo) return;
    await delay(200);

    // Ready to save.
    automationTmp.status = activeState;
    automationTmp.flow = tryToParseJsonString(automationTmp.flow);
    setSavingAutomation(true);
    if (automationId) {
      await ApiService.updateAutomation(automationId, automationTmp);
    } else {
      const { name, desc, trigger, status, flow } = automationTmp;
      const res = await ApiService.createAutomation({
        name,
        desc,
        trigger,
        status,
        flow,
        owner: {
          id: userProfile?.id,
          name: userProfile?.name,
        },
      });
      res && router.push(`edit/${res.id}`);
    }
    setSavingAutomation(false);
  }, [
    router,
    activeState,
    automationId,
    automationData,
    userProfile?.id,
    userProfile?.name,
    showTriggerErrorMessage,
    checkMissingRequiredInfo,
  ]);

  /// All Memo Stuff Placed Here
  const triggerNodeData = useMemo(() => {
    return automationData ? (automationData.trigger as TriggerType) : null;
  }, [automationData]);

  const exceedLimitNodes = useMemo(() => {
    return renderedNodes.length >= MAX_NODE_IN_DIAGRAM;
  }, [renderedNodes]);

  const canSaveAutomation = useMemo(() => {
    if (!automationData) return false;
    return !exceedLimitNodes;
  }, [automationData, exceedLimitNodes]);

  /// All Memo Stuff Placed Here

  /// If automation , then convert it nodes and edges first, and only run when reloading.
  useEffect(() => {
    if (automationData) {
      const callbackList = {
        showTriggerModal: showTriggerActivityModal,
        showActionModal: showActionActivityModal,
        showConditionModal: showConditionActivityModal,
      } as CallbackList;
      const errors = { showErrorTriggerNode: showTriggerErrorMessage };
      const { nodes, edges } = DiagramHelper.convertRuleEngineToNodesAndEdges(
        automationData as unknown as AutomationData,
        callbackList,
        errors
      );

      setActiveState(automationData.status);
      setRenderedNodes(() => nodes);
      setRenderedEdges(() => edges);
    }
  }, [
    automationData,
    showTriggerErrorMessage,
    showActionActivityModal,
    showConditionActivityModal,
    showTriggerActivityModal,
  ]);

  return useMemo(
    () => ({
      t,
      error,
      teamList,
      isLoading,
      isManager,
      activeState,
      renderedNodes,
      allOscChannel,
      listOperators,
      renderedEdges,
      automationData,
      webhookOptions,
      modalActivities,
      triggerNodeData,
      savingAutomation,
      isEditAutomation,
      currentModalType,
      exceedLimitNodes,
      crmFieldsByObject,
      canSaveAutomation,
      ocsChannelsOptions,
      selectConditionModel,
      isLoadingOcsChannels,
      currentEditDataAction,
      currentEditDataCondition,
      ocsChannelsOptionsWithDefaultOption,
      reload,
      addNodeByType,
      setActiveState,
      reloadTeamList,
      setActionNodeData,
      setNameAutomation,
      intendToRemoveNode,
      reloadListOperator,
      setTriggerNodeData,
      closeActivityModal,
      saveDataAutomation,
      setSavingAutomation,
      setConditionNodeData,
      setSelectConditionModel,
      showActionActivityModal,
      setCurrentEditDataAction,
      showTriggerActivityModal,
      showConditionActivityModal,
      setCurrentEditDataCondition,
    }),
    [
      t,
      error,
      teamList,
      isLoading,
      isManager,
      activeState,
      listOperators,
      allOscChannel,
      renderedEdges,
      renderedNodes,
      automationData,
      webhookOptions,
      modalActivities,
      triggerNodeData,
      isEditAutomation,
      currentModalType,
      savingAutomation,
      exceedLimitNodes,
      crmFieldsByObject,
      canSaveAutomation,
      ocsChannelsOptions,
      isLoadingOcsChannels,
      selectConditionModel,
      currentEditDataAction,
      currentEditDataCondition,
      ocsChannelsOptionsWithDefaultOption,
      reload,
      addNodeByType,
      setActiveState,
      reloadTeamList,
      setActionNodeData,
      setNameAutomation,
      intendToRemoveNode,
      setTriggerNodeData,
      reloadListOperator,
      closeActivityModal,
      saveDataAutomation,
      setSavingAutomation,
      setConditionNodeData,
      setSelectConditionModel,
      showActionActivityModal,
      showTriggerActivityModal,
      setCurrentEditDataAction,
      showConditionActivityModal,
      setCurrentEditDataCondition,
    ]
  );
}
