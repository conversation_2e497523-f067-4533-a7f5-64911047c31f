import { useMemo } from 'react';
import useSWR from 'swr';
import ApiService from '../../../../services/api';
export function useAutomationConfig() {
  const {
    data = null,
    isLoading,
    error,
    mutate,
  } = useSWR('get-automation-configs', () => ApiService.getAutomationConfigs(), {
    revalidateOnMount: true,
  });

  return useMemo(
    () => ({
      data,
      isLoading,
      reload: mutate,
      error,
    }),
    [data, error, isLoading, mutate]
  );
}
