import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import { AutomationData } from '../../../../models/automation';
import { useWidgetContext } from '../../../widgetContext';
import { FACTS, PATHS, SelectConditionMappingType, TriggerType } from '../types';
export const initialAutomation: AutomationData = {
  name: '',
  trigger: null,
  desc: 'Automation1 description',
  status: 'active',
  service: 'livechat',
  owner: {
    name: '',
    id: '',
  },
  flow: null,
  created: '',
  updated: '',
};

export const setInitialAutomationData = (customData: Partial<AutomationData>) => {
  return { ...initialAutomation, ...customData };
};

export const useInitSelectConditionModel = (): SelectConditionMappingType[] => {
  const { t } = useTranslate('workspace');
  const { hasCorrectCrmWidgetInstalled } = useWidgetContext();

  return useMemo(
    () => [
      {
        triggerNames: [
          TriggerType.NewConversation,
          TriggerType.MessageReplyConversation,
          'conversation.updated',
        ],
        config: {
          enduser: {
            label: t('detail.condition.select.subject.option.1'),
            fact: FACTS.END_USER,
            attributes: [
              {
                label: t('detail.condition.select.condition.option.1'),
                type: 'Boolean',
                path: PATHS.CURRENT,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.6'),
                type: 'String',
                path: PATHS.CRM,
                disabled: !hasCorrectCrmWidgetInstalled,
                linkText: t('detail.condition.select.condition.option.2.sub.6.link_text'),
              },
            ],
          },
          conversation: {
            label: t('detail.condition.select.subject.option.2'),
            fact: FACTS.CONVERSATION,
            attributes: [
              {
                label: t('detail.condition.select.condition.option.2.sub.1'),
                type: 'String',
                path: PATHS.OCS_CHANNEL,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.2'),
                type: 'String',
                path: PATHS.TEAM_ID,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.3'),
                type: 'String',
                path: PATHS.OPERATOR_ID_TEAM_ID,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.4'),
                type: 'String',
                path: PATHS.PRESENCE_ASSIGNEE,
              },
            ],
          },
        },
      },
      {
        triggerNames: [TriggerType.CloseConversation],
        config: {
          enduser: {
            label: t('detail.condition.select.subject.option.1'),
            fact: FACTS.END_USER,
            attributes: [
              {
                label: t('detail.condition.select.condition.option.1'),
                type: 'Boolean',
                path: PATHS.CURRENT,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.6'),
                type: 'String',
                path: PATHS.CRM,
                disabled: !hasCorrectCrmWidgetInstalled,
                linkText: t('detail.condition.select.condition.option.2.sub.6.link_text'),
              },
            ],
          },
          conversation: {
            label: t('detail.condition.select.subject.option.2'),
            fact: FACTS.CONVERSATION,
            attributes: [
              {
                label: t('detail.condition.select.condition.option.2.sub.1'),
                type: 'String',
                path: PATHS.OCS_CHANNEL,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.2'),
                type: 'String',
                path: PATHS.TEAM_ID,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.3'),
                type: 'String',
                path: PATHS.OPERATOR_ID_TEAM_ID,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.4'),
                type: 'String',
                path: PATHS.PRESENCE_ASSIGNEE,
              },
              {
                label: t('detail.condition.select.condition.option.2.sub.5'),
                type: 'String',
                path: PATHS.AUTO_CLOSED_CONVERSATION,
              },
            ],
          },
        },
      },
    ],
    [t, hasCorrectCrmWidgetInstalled]
  );
};
