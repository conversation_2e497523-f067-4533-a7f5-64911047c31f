import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import { AutomationData } from '../../../../models/automation';
import ApiService from '../../../../services/api';
import { DiagramHelper } from '../DiagramContainer/DiagramHelpers';
import { setInitialAutomationData } from './initialAutomationData';
import { tryToParseJsonString } from './utils';

/// If automationId is not provided,
/// then automationData should be constructed with initial data.
export function useAutomationDetailData(
  automationId: string | null | undefined,
  numberCurrent: number
) {
  const { t } = useTranslate('workspace');
  const {
    data = null,
    isLoading,
    error,
    mutate,
  } = useSWR<AutomationData>(
    automationId ? `get-automation-detail-id=${automationId}` : null,
    () => ApiService.getAutomationDetail(automationId),
    {
      revalidateOnMount: true,
    }
  );

  /// This will be the most important part in this screen.
  /// Source data for all update, remove , edit ....
  const [automationData, setAutomationData] = useState<AutomationData>(null);

  // As of my idea of modifying directly on automation to generate diagram,
  // I need to clone the original data, then modify it
  useEffect(() => {
    // Set initial data for automationData when automationId is not around
    if (!automationId) {
      const newName = t('detail.defaultName', {
        number: (numberCurrent + 1).toString().padStart(2, '0'),
        ns: 'workspace',
      });
      const initialAutomation = setInitialAutomationData({ name: newName });
      setAutomationData(initialAutomation);
    }
    if (automationId && data) {
      const flow = tryToParseJsonString(data.flow);
      const newFlow = DiagramHelper.addMissingPropertyInOrderTraversal(flow);
      const newAutomation = {
        ...JSON.parse(JSON.stringify(data)),
        flow: newFlow,
      } as AutomationData;
      setAutomationData(newAutomation);
    }
  }, [automationId, data, numberCurrent, t]);

  return useMemo(() => {
    if (data) {
      data.flow = tryToParseJsonString(data.flow);
    }
    return {
      automation: data ?? null,
      automationData,
      setAutomationData,
      isLoading,
      error,
      reload: mutate,
    };
  }, [automationData, data, error, isLoading, mutate, setAutomationData]);
}
