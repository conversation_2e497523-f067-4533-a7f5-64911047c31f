import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo } from 'react';
import useSWR from 'swr';
import ApiService from '../../../../services/api';
import { IResponseExternalToolIntegration } from '../../../Settings/Integrations/models';
import { DEFAULT_OCS_CHANNEL_ID } from '../constant';
import { Option } from '../types';

export function useOcsChannelsData() {
  const { t } = useTranslate('workspace');
  const {
    data = null,
    isLoading,
    error,
    mutate,
  } = useSWR<IResponseExternalToolIntegration>(`get-automation-ocs-channels`, () =>
    ApiService.getToolsIntegration(undefined, 1000)
  );

  const {
    data: webhookIntegrations = null,
    isLoading: loadingWebhookIntegrations,
    mutate: reloadWebhookIntegrations,
  } = useSWR<IResponseExternalToolIntegration>(
    `get-automation-webhook-channels`,
    () => ApiService.getToolsIntegration('webhook'),
    {
      revalidateOnMount: true,
      revalidateIfStale: true,
    }
  );

  const ocsChannelsOptions = useMemo<Option[]>(() => {
    if (!data) return [];
    return (data?.data || [])
      .filter((item) => item.status === 'active')
      .map((item) => ({ label: item.name, value: item?.ocsChannelId || item.id }));
  }, [data]);

  const webhookOptions = useMemo<Option[]>(() => {
    if (!webhookIntegrations) return [];
    return webhookIntegrations.data
      .filter((item) => item.status === 'active')
      .map((item) => ({
        label: item.name,
        value: item.id,
      }));
  }, [webhookIntegrations]);

  useEffect(() => {
    mutate?.();
    reloadWebhookIntegrations?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return useMemo(() => {
    return {
      ocsChannelsOptionsWithDefaultOption: [
        {
          label: t('channel_default_ocs'),
          value: DEFAULT_OCS_CHANNEL_ID,
        },
        ...ocsChannelsOptions,
      ],
      allOscChannel: data ? data.data : [],
      ocsChannelsOptions,
      webhookOptions,
      isLoading,
      error,
      reload: mutate,
      loadingWebhookIntegrations,
      reloadWebhookIntegrations,
    };
  }, [
    t,
    webhookOptions,
    data,
    error,
    isLoading,
    mutate,
    ocsChannelsOptions,
    loadingWebhookIntegrations,
    reloadWebhookIntegrations,
  ]);
}
