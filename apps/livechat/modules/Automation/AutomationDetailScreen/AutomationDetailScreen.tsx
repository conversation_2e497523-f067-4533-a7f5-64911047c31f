import {
  ActionIcon,
  Button,
  Center,
  Container,
  Flex,
  FocusTrap,
  Group,
  Loader,
  SimpleGrid,
  Space,
  Switch,
  Text,
  TextInput,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useElementSize, useMediaQuery } from '@mantine/hooks';
import { BottomUpTween, LeftToRightTween } from '@resola-ai/ui';
import { IconArrowLeft, IconEdit } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { NAVBAR_HEIGHT } from '../../../constants';
import { AutomationLayoutContextProvider } from './AutomationLayoutContext';
import DiagramContainerHolder from './DiagramContainer/DiagramContainer';
import { ModalPlacementHolder } from './ModalPlacementHolders/ModalPlacementHolder';
import { useAutomationDetailContext } from './contexts/AutomationDetailContext';

const useStyle = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    maxWidth: '100%',
    paddingLeft: 0,
    paddingRight: 0,
    height: NAVBAR_HEIGHT,
    position: 'relative',
    '& .mantine-Switch-root input:not(:checked) + label.mantine-Switch-track': {
      backgroundColor: theme.colors.gray[5],
      borderColor: theme.colors.gray[5],
    },
  },
  violet: {
    color: theme.colors.navy[0],
  },
  actionBar: {
    backgroundColor: theme.colors.violet[0],
    padding: theme.spacing.md,
    paddingTop: theme.spacing.sm,
    paddingBottom: theme.spacing.sm,
  },
}));

export function AutomationDetailScreen() {
  const { classes } = useStyle();
  const { reload } = useAutomationDetailContext();
  useEffect(() => {
    reload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Container fluid className={classes.container}>
      <LoadingIndicator />
      <BackToAutomationListLink />
      <LeftToRightTween>
        <ActionBar />
      </LeftToRightTween>
      <BottomUpTween style={{ display: 'flex', flexGrow: 1 }}>
        <DiagramParentContainer />
      </BottomUpTween>
    </Container>
  );
}

function LoadingIndicator() {
  const { savingAutomation } = useAutomationDetailContext();
  return (
    <Flex
      style={{
        display: savingAutomation ? 'flex' : 'none',
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        zIndex: 100,
      }}
      align={'center'}
      justify={'center'}
      sx={(theme) => ({
        flexGrow: 1,
        backgroundColor: theme.colors.gray[3],
        opacity: 0.5,
      })}
    >
      <Center>
        <Loader type='dots' color='navy.0' />
      </Center>
    </Flex>
  );
}

function BackToAutomationListLink() {
  const { classes, theme } = useStyle();
  const { t } = useTranslate('workspace');
  return (
    <LeftToRightTween>
      <Group mb='md' ml={'lg'} pt={'md'}>
        <Link href={'/automation'} style={{ textDecoration: 'none' }}>
          <Group>
            <IconArrowLeft className={classes.violet} />
            <Text fw={700} sx={{ color: theme.colors.navy[0] }}>
              {t('backToAutomationList')}
            </Text>
          </Group>
        </Link>
      </Group>
    </LeftToRightTween>
  );
}

function ActionBar() {
  const { classes } = useStyle();
  const { isEditAutomation, t, isLoading } = useAutomationDetailContext();
  const isMobile = useMediaQuery('(max-width: 500px)');
  return (
    <SimpleGrid cols={{ xs: 1, md: 3 }} className={classes.actionBar}>
      <Group justify={isMobile ? 'center' : 'flex-start'} pl={isMobile ? 'md' : 0}>
        <Text size={'20px'} fw={700} color='dark.4'>
          {t(isEditAutomation ? 'action.edit' : 'action.create')}
        </Text>
      </Group>
      <Group justify='center' pl={isMobile ? 'md' : 0}>
        {isLoading ? <Loader type='dots' color='navy.0' /> : <AutomationNameInputOrLabel />}
      </Group>
      <Group justify={isMobile ? 'space-between' : 'flex-end'} pl={isMobile ? 'md' : 0}>
        <ActiveToggleAndSaveButton />
      </Group>
    </SimpleGrid>
  );
}

function AutomationNameInputOrLabel() {
  const { t, isEditAutomation, automationData, isManager, isLoading, setNameAutomation } =
    useAutomationDetailContext();
  const [edit, setEdit] = useState(false);
  const currentNameRef = useRef<string>(null);
  // eslint-disable-next-line no-unused-vars
  const [_, setUpdateChange] = useState(0);
  const automationNameInputRef = useRef<HTMLInputElement | null>(null);

  const handleClickEdit = useCallback(() => {
    setEdit(true);
    if (automationNameInputRef.current) {
      automationNameInputRef.current.value = currentNameRef.current;
    }
  }, []);

  useEffect(() => {
    currentNameRef.current = automationData?.name || '';
    if (!isLoading) setUpdateChange((pre) => pre + 1);
  }, [isEditAutomation, automationData, t, isLoading, setUpdateChange]);

  const handleKeyUp = useCallback(
    (e: KeyboardEvent) => {
      const value = (e.target as HTMLInputElement).value;
      if (e.key === 'Enter') {
        e.preventDefault();
        setEdit(false);
        if (value.trim().length) {
          setNameAutomation(value);
        } else {
          setNameAutomation(automationData?.name || '');
        }

        return;
      }
      automationNameInputRef.current && (automationNameInputRef.current.value = value);
      currentNameRef.current = value;
    },
    [automationData?.name, setNameAutomation]
  );

  const handleOnBlurInput = useCallback(
    (e) => {
      e.preventDefault();
      const value = (e.target as HTMLInputElement).value;
      setEdit(false);
      if (value.trim().length) {
        setNameAutomation(value);
      } else {
        setNameAutomation(automationData?.name || '');
      }
    },
    [automationData?.name, setNameAutomation]
  );

  return (
    <>
      <FocusTrap active={edit}>
        <TextInput
          ref={automationNameInputRef}
          data-autofocus
          onBlur={handleOnBlurInput}
          onKeyUp={(e) => handleKeyUp(e as unknown as KeyboardEvent)}
          style={{ display: edit ? 'block' : 'none' }}
        />
      </FocusTrap>

      <Group
        style={{
          display: edit ? 'none' : 'inline-flex',
          flexDirection: 'row',
          flexWrap: 'nowrap',
        }}
      >
        <Text size={'14px'} fw={700} c='dark.4' lineClamp={1} title={currentNameRef.current}>
          {currentNameRef.current}
        </Text>
        <ActionIcon
          disabled={!isManager}
          variant='transparent'
          color='navy.0'
          onClick={handleClickEdit}
        >
          <IconEdit />
        </ActionIcon>
      </Group>
    </>
  );
}

function ActiveToggleAndSaveButton() {
  const {
    t,
    isEditAutomation,
    saveDataAutomation,
    activeState,
    setActiveState,
    canSaveAutomation,
    isManager,
  } = useAutomationDetailContext();
  const [active, setActive] = useState(activeState);

  useEffect(() => {
    setActive(activeState);
  }, [activeState]);

  useEffect(() => {
    setActiveState(active);
  }, [active, setActiveState]);

  return (
    <>
      <Switch
        disabled={!isManager}
        label={t(active === 'active' ? 'active' : 'inactive')}
        labelPosition='left'
        color='decaGreen.6'
        checked={active === 'active'}
        onChange={() =>
          setActive((pre) => {
            return pre === 'active' ? 'inactive' : 'active';
          })
        }
      />
      <Space w={'md'} />
      <Button
        variant='filled'
        color='navy.0'
        size='xs'
        onClick={saveDataAutomation}
        disabled={!isManager || !canSaveAutomation}
      >
        {t(isEditAutomation ? 'detail.trigger.save' : 'detail.trigger.create')}
      </Button>
    </>
  );
}

function DiagramParentContainer() {
  const { ref, width, height } = useElementSize();
  const parentDimension = useMemo(() => ({ width, height }), [width, height]);
  return (
    <Container
      ref={ref}
      fluid
      style={{ flexGrow: 1 }}
      sx={(theme) => ({
        width: '100%',
        position: 'relative',
        backgroundColor: theme.colors.gray[0],
        paddingLeft: 0,
        paddingRight: 0,
      })}
    >
      <AutomationLayoutContextProvider width={width} height={height}>
        <DiagramContainerHolder />
      </AutomationLayoutContextProvider>
      <ModalPlacementHolder parentDimension={parentDimension} />
    </Container>
  );
}
