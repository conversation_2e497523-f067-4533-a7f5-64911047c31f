import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import { FeatureFlagName, IConversation, IOperatorProfile, Status } from '@resola-ai/models';
import ApiService from '../services/api';
import { UserStatus } from '../models/user';
import { LocalStorageUtils } from '@resola-ai/utils';
import { LAST_SENDER_TYPE } from '../constants/menu';
import useChangeLanguage from '../hooks/useChangeLanguage';
import useFeatureFlag from '../hooks/useFeatureFlag';
import { PERSONAL_SETTING_CHANNEL, UPDATE_SOUND_PERSONAL_SETTING_CHANNEL } from '../constants';

function getNamePriority(curr: IOperatorProfile, lang: string) {
  let name = '';
  if (curr?.displayName) {
    name = curr?.displayName;
  } else if (curr?.familyName && curr?.givenName) {
    name =
      lang === 'ja'
        ? `${curr?.familyName?.trim() || ''} ${curr?.givenName?.trim() || ''}`
        : `${curr?.givenName?.trim() || ''} ${curr?.familyName?.trim() || ''}`;
  } else {
    name = curr?.email || '';
  }
  return name;
}

const useUser = () => {
  const { lang } = useChangeLanguage();
  const { enabled: enabledWrapUpStatus } = useFeatureFlag(FeatureFlagName.wrapUpStatus);
  const [listOperatorsPrivate, setListOperatorsPrivate] = useState<IOperatorProfile[]>([]);
  const {
    data: listOperatorsUsual = [],
    isLoading: isLoadingListOperators,
    error: errorLoadingListOperator,
    mutate: reloadListOperator,
  } = useSWR('/get-list-operators-aka-users', () => ApiService.getListOperators());

  const {
    data: userProfile = null,
    mutate,
    isLoading: isLoadingProfile,
    isValidating: isValidatingProfile,
  } = useSWR<IOperatorProfile>('profile', ApiService.getOperatorProfile);

  const userId = userProfile?.id || '';
  const status: UserStatus = userProfile?.status || 'available';
  const organizationId = userProfile?.org?.id || userProfile?.orgId || '';
  const userTeams = useMemo(() => userProfile?.teams || [], [userProfile?.teams]);
  const sendMessageHotKey = userProfile?.preferences?.send_message_hot_key || 'enter';
  const userSoundSetting = useMemo(
    () => userProfile?.notificationSetting?.sound,
    [userProfile?.notificationSetting?.sound]
  );

  const operatorIdToFinalNameMap = useMemo<Record<string, string>>(() => {
    if (!listOperatorsPrivate?.length) return {};
    return listOperatorsPrivate.reduce((acc, curr) => {
      acc[curr.id] = getNamePriority(curr, lang);
      return acc;
    }, {});
  }, [lang, listOperatorsPrivate]);

  const operatorIdToImageMap = useMemo<Record<string, string>>(() => {
    if (!listOperatorsPrivate?.length) return {};
    return listOperatorsPrivate.reduce((acc, curr) => {
      acc[curr.id] = curr?.['picture'] || undefined;
      return acc;
    }, {});
  }, [listOperatorsPrivate]);

  const updateUserStatus = useCallback(
    async (onlineState: boolean) => {
      const status: UserStatus = onlineState ? 'available' : 'offline';
      if (!userId) return;
      await ApiService.updateUserProfile(userId, { status });
      mutate();
      // reloadListOperator();
    },
    [mutate, userId]
  );

  const listOperators = useMemo(() => {
    return listOperatorsPrivate?.map((item) => {
      return {
        ...item,
        name: operatorIdToFinalNameMap[item.id],
      };
    });
  }, [listOperatorsPrivate, operatorIdToFinalNameMap]);

  const getUserById = useCallback(
    (userId: string) => {
      return listOperators.find((o) => o.id === userId);
    },
    [listOperators]
  );

  const getUserInfoById = useCallback(
    (userId: string) => {
      const user = listOperators.find((o) => o.id === userId);
      if (user) {
        user.name = operatorIdToFinalNameMap?.[user.id] || user?.name || '';
      }
      return user;
    },
    [listOperators, operatorIdToFinalNameMap]
  );

  const updateLastSenderType = useCallback(
    async (type: string) => {
      const convSettings = { conversationSetting: { lastSenderType: type } };
      await ApiService.updateUserProfile(userId, { ...convSettings });
      LocalStorageUtils.set(LAST_SENDER_TYPE, type);
    },
    [userId]
  );

  const updatePreferences = useCallback(
    async (data) => {
      const preferences = { preferences: { ...userProfile.preferences, ...data } };
      await ApiService.updateUserProfile(userId, { ...preferences });
      mutate();
    },
    [userProfile?.preferences, userId, mutate]
  );

  const handleUpdateOperatorStatusFromSocket = useCallback(
    (operatorId: string, status: Status) => {
      if (!listOperatorsPrivate?.length) return;
      setListOperatorsPrivate((pre) => {
        const found = pre.find((item) => item.id === operatorId);
        if (found) {
          found.status = status;
        }
        return [...pre];
      });

      if (operatorId === userId) {
        mutate();
      }
    },
    [listOperatorsPrivate?.length, mutate, userId]
  );

  const isManager = useMemo(() => userProfile && userProfile.role === 'manager', [userProfile]);
  const userProfileMemo = useMemo<IOperatorProfile>(() => {
    if (!userProfile) return null;
    return {
      ...userProfile,
      name: getNamePriority(userProfile, lang),
    };
  }, [userProfile, lang]);

  const getAssigneeName = useCallback(
    (dataAssignee?: IConversation['assignee'], assigneeId?: string) => {
      if (!dataAssignee && !assigneeId) {
        return undefined;
      }
      // if it has assignee and that assignee exists in the list of operators
      const wannaFindAssigneeId = dataAssignee?.id || assigneeId;
      const found = listOperators.find((operator) => operator.id === wannaFindAssigneeId);
      if (found) {
        return operatorIdToFinalNameMap?.[found.id] || found?.name;
      }
      // if it has assignee but that assignee is not in the list of operators
      // then mean  that assignee is not in the current team,
      // add additional text into the name to express that this user is not belong to this team
      return `${dataAssignee?.name || ''} (?)`;
    },
    [listOperators, operatorIdToFinalNameMap]
  );

  // Local cache mutation only, no revalidation(no request called)
  const setUserProfile = useCallback(
    (profile: IOperatorProfile) => {
      mutate((prevProfile) => {
        if (typeof prevProfile !== 'object' || prevProfile === null) return profile;
        return { ...prevProfile, ...profile };
      }, false);
    },
    [mutate]
  );

  const updateSoundSettings = useCallback(
    (updatedSettings) => {
      if (updatedSettings.sound) {
        const { selectedSoundId, mode } = updatedSettings.sound;
        const updatedProfile = {
          ...userProfile,
          notificationSetting: {
            ...userProfile.notificationSetting,
            sound: { selectedSoundId, mode },
          },
        };
        setUserProfile(updatedProfile);
      }
    },
    [setUserProfile, userProfile]
  );

  useEffect(() => {
    const channel = new BroadcastChannel(PERSONAL_SETTING_CHANNEL);
    // Listen for messages
    channel.onmessage = (event) => {
      if (event.data.type === UPDATE_SOUND_PERSONAL_SETTING_CHANNEL) {
        const updatedSettings = event.data.payload;
        updateSoundSettings(updatedSettings);
      }
    };
    // Cleanup on unmount
    return () => {
      channel.close();
    };
  }, [updateSoundSettings]);

  useEffect(() => {
    if (listOperatorsUsual?.length) {
      setListOperatorsPrivate([...listOperatorsUsual]);
    }
  }, [listOperatorsUsual]);

  return useMemo(
    () => ({
      userId,
      status,
      userTeams,
      isManager,
      listOperators,
      organizationId,
      isLoadingProfile,
      userSoundSetting,
      sendMessageHotKey,
      enabledWrapUpStatus,
      isValidatingProfile,
      operatorIdToImageMap,
      isLoadingListOperators,
      operatorIdToFinalNameMap,
      errorLoadingListOperator,
      userProfile: userProfileMemo,
      isOnline: status === 'available',
      preferences: userProfileMemo?.preferences || {},
      mutate,
      getUserById,
      getAssigneeName,
      getUserInfoById,
      updateUserStatus,
      updatePreferences,
      reloadListOperator,
      updateLastSenderType,
      handleUpdateOperatorStatusFromSocket,
      setUserProfile,
    }),
    [
      status,
      userId,
      userTeams,
      isManager,
      listOperators,
      organizationId,
      userProfileMemo,
      userSoundSetting,
      isLoadingProfile,
      sendMessageHotKey,
      enabledWrapUpStatus,
      isValidatingProfile,
      operatorIdToImageMap,
      isLoadingListOperators,
      errorLoadingListOperator,
      operatorIdToFinalNameMap,
      mutate,
      getUserById,
      getAssigneeName,
      getUserInfoById,
      updateUserStatus,
      updatePreferences,
      reloadListOperator,
      updateLastSenderType,
      handleUpdateOperatorStatusFromSocket,
      setUserProfile,
    ]
  );
};

export type UserContextType = ReturnType<typeof useUser>;

const context = createContext<UserContextType | null>(null);

export const UserContextProvider: React.FC<any> = ({ children }) => {
  const value = useUser();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useUserContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useUserContext must be used inside UserContextProvider');
  }

  return value;
};
