import { Container, Grid, SimpleGrid, Skeleton, rem, useMantineTheme } from '@mantine/core';
import React from 'react';

const PRIMARY_COL_HEIGHT = rem(300);

export function MessageGrid() {
  const theme = useMantineTheme();
  const SECONDARY_COL_HEIGHT = `calc(${PRIMARY_COL_HEIGHT} / 2 - ${theme.spacing.md} / 2)`;

  return (
    <Container my='md'>
      <SimpleGrid spacing='md' cols={{ xs: 1, sm: 2 }}>
        <Skeleton height={PRIMARY_COL_HEIGHT} radius='md' animate={false} />
        <Grid gutter='md'>
          <Grid.Col>
            <Skeleton height={SECONDARY_COL_HEIGHT} radius='md' animate={false} />
          </Grid.Col>
          <Grid.Col span={6}>
            <Skeleton height={SECONDARY_COL_HEIGHT} radius='md' animate={false} />
          </Grid.Col>
          <Grid.Col span={6}>
            <Skeleton height={SECONDARY_COL_HEIGHT} radius='md' animate={false} />
          </Grid.Col>
        </Grid>
      </SimpleGrid>
    </Container>
  );
}
