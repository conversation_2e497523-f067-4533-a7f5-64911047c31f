import React, { useEffect } from 'react';
import { viewTransitionsStatus } from '../../../../utils/transition';
import { Flex } from '@mantine/core';
const ViewTransitionApiStatus: React.FC<any> = () => {
  useEffect(() => {
    console.log(viewTransitionsStatus());
  }, []);
  return (
    <Flex
      sx={{
        position: 'absolute',
        bottom: 0,
        left: 0,
      }}
    ></Flex>
  );
};

export default ViewTransitionApiStatus;
