import {
  ActionIcon,
  AppShell,
  defaultVariantColorsResolver,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconInbox, IconLayoutGridAdd, IconSearch, IconSettings } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react';
import AutomationListIconSvg from '../../../components/AutomationListIconSvg';
import {
  AUTOMATION_ROUTE,
  MAIN_ROUTE,
  NAVIGATOR_CONTAINER_ID_NAME,
  SETTINGS_TEAM_USER_LIST_ROUTE,
  WIDGET_ROUTE,
  WORKSPACE_CONTAINER_ID_NAME,
} from '../../../constants';
import { INBOX_NAVBAR_AND_MAIN_PERCENT } from '../../../constants/grid';
import { animatedNavigate } from '../../../utils/transition';
import SettingsMenu from '../../Menu/Settings';
import { useAppContext } from '../../appContext';
import SearchActionMenuItem from './SearchActionMenuItem';
import ViewTransitionApiStatus from './ViewTransitionApiStatus';

const useStyles = createStyles((theme) => ({
  wrapper: {
    display: 'flex',
    height: '100dvh',
  },

  aside: {
    flex: `0 0 ${INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_1}vw`,
    maxWidth: '61px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },

  asideResponsive: {
    flex: `0 0 ${INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_1}vw`,
  },

  main: {
    flex: 1,
    backgroundColor: theme.colors.gray[0],
  },

  mainIconActive: {
    '&, &:hover': {
      // TODO: fix the variant color resolver
      backgroundColor: defaultVariantColorsResolver({
        variant: 'filled',
        color: 'navy.0',
        theme,
      }).background,
      color: defaultVariantColorsResolver({
        variant: 'filled',
        color: theme.colors.navy[0],
        theme,
      }).color,
      borderRadius: '50%',
    },
  },

  IconButton: {
    marginTop: theme.spacing.md,
    width: rem(44),
    height: rem(44),
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: theme.colors.navy[0],
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: theme.colors.navy[3],
      color: theme.colors.navy[0],
    },
  },

  IconButtonResponsive: {
    margin: '20px 0 0 0',
    width: rem(20),
    height: rem(20),
    '& svg': {
      width: rem(18),
      height: rem(18),
    },
  },

  IconArrow: {
    marginTop: theme.spacing.md,
    width: rem(44),
    height: rem(44),
  },

  link: {
    boxSizing: 'border-box',
    display: 'block',
    textDecoration: 'none',
    borderTopRightRadius: theme.radius.md,
    borderBottomRightRadius: theme.radius.md,
    color: theme.colors.gray[7],
    padding: `0 ${theme.spacing.md}`,
    fontSize: theme.fontSizes.sm,
    marginRight: theme.spacing.md,
    fontWeight: 500,
    height: rem(44),
    lineHeight: rem(44),

    '&:hover': {
      backgroundColor: theme.colors.gray[1],
      color: theme.black,
    },
  },
}));

// eslint-disable-next-line no-unused-vars
const enum MainMenuType {
  // eslint-disable-next-line no-unused-vars
  INBOX = 'Inbox',
  // eslint-disable-next-line no-unused-vars
  SEARCH_BOX = `Search-Box`,
  // eslint-disable-next-line no-unused-vars
  AUTOMATION = 'Automation',
  // eslint-disable-next-line no-unused-vars
  WIDGET = 'Widget',
  // eslint-disable-next-line no-unused-vars
  SETTINGS = 'Settings',
}

interface MainIconLink {
  icon: React.FC<any>;
  label: MainMenuType;
  link?: string;
}

const mainIconLinksData: MainIconLink[] = [
  { icon: IconInbox, label: MainMenuType.INBOX, link: MAIN_ROUTE },
  { icon: IconSearch, label: MainMenuType.SEARCH_BOX },
  { icon: AutomationListIconSvg, label: MainMenuType.AUTOMATION, link: AUTOMATION_ROUTE },
  { icon: IconLayoutGridAdd, label: MainMenuType.WIDGET, link: WIDGET_ROUTE },
  { icon: IconSettings, label: MainMenuType.SETTINGS },
];

const findCorrectInitialMenuAction = (asPath: string): MainMenuType => {
  // console.log('debug: asPath', asPath);
  if (asPath === MAIN_ROUTE) return MainMenuType.INBOX;
  const path = asPath.split('/')?.[1]?.toLowerCase();
  return (
    mainIconLinksData.find((item) => {
      return path.startsWith(item.label.toLowerCase());
    })?.label || MainMenuType.INBOX
  );
};

export function AppVerticalNavbar() {
  const theme = useMantineTheme();
  const { responsive } = useAppContext();
  const { classes, cx } = useStyles();
  const router = useRouter();
  const { asPath } = router;
  const [active, setActive] = useState<MainMenuType>(findCorrectInitialMenuAction(asPath));
  const [currLocation, setCurrLocation] = useState<MainMenuType>(
    findCorrectInitialMenuAction(asPath)
  );

  useEffect(() => {
    const activeLink = findCorrectInitialMenuAction(asPath);
    setActive(activeLink);
    setCurrLocation(activeLink);
  }, [asPath]);

  const { responsiveScreen } = responsive;

  const handleClick = async (link?: MainIconLink) => {
    if (currLocation === link.label) return;
    if (!link?.link) {
      if (link.label === MainMenuType.SETTINGS) {
        animatedNavigate(SETTINGS_TEAM_USER_LIST_ROUTE, router);
      }
    } else {
      animatedNavigate(link.link, router);
    }
  };

  const handleOpenSearchBox = useCallback(
    (isOpen?: boolean) => {
      if (!isOpen) {
        // back to current link active
        setActive(findCorrectInitialMenuAction(asPath));
        return;
      }
      // set Search Box icon active
      setActive(MainMenuType.SEARCH_BOX);
    },
    [asPath]
  );

  const mainLinks = mainIconLinksData.map((link, index) => {
    if (link.label === MainMenuType.SEARCH_BOX && currLocation !== MainMenuType.SEARCH_BOX) {
      return (
        <SearchActionMenuItem
          onOpen={handleOpenSearchBox}
          key={index}
          className={cx(classes.IconButton, {
            [classes.mainIconActive]: link.label === active,
            [classes.IconButtonResponsive]: responsiveScreen,
          })}
          responsive={responsiveScreen}
        />
      );
    }
    return (
      <ActionIcon
        key={index}
        onClick={() => handleClick(link)}
        className={cx(classes.IconButton, {
          [classes.mainIconActive]: link.label === active,
          [classes.IconButtonResponsive]: responsiveScreen,
        })}
      >
        <link.icon
          size='1.4rem'
          stroke={responsiveScreen ? 2 : 1.5}
          color={link.label === active ? 'white' : theme.colors.navy[0]}
        />
      </ActionIcon>
    );
  });

  return (
    <AppShell
      sx={{
        borderRight: 'none',
        width: [active, currLocation].includes(MainMenuType.SETTINGS)
          ? `${INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_1 + INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_2}vw`
          : `${INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_1}vw`,
        maxWidth: [active, currLocation].includes(MainMenuType.SETTINGS) ? 'unset' : 61,
      }}
    >
      <AppShell.Section grow className={classes.wrapper}>
        <div
          className={cx(classes.aside, {
            [classes.asideResponsive]: responsiveScreen,
          })}
          id={NAVIGATOR_CONTAINER_ID_NAME}
        >
          {mainLinks}
          <ViewTransitionApiStatus />
        </div>
        {[active, currLocation].includes(MainMenuType.SETTINGS) && (
          <div id={WORKSPACE_CONTAINER_ID_NAME} className={classes.main}>
            <SettingsMenu />
          </div>
        )}
      </AppShell.Section>
    </AppShell>
  );
}
