import { ActionIcon, Box, Popover, TextInput } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconSearch } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { SEARCH_BOX_ROUTE } from '../../../constants';
import { animatedNavigate } from '../../../utils/transition';

type Props = {
  className?: string;
  responsive?: boolean;
  // eslint-disable-next-line no-unused-vars
  onOpen?: (isOpen?: boolean) => void;
};

const useStyle = createStyles((theme) => ({
  input: {
    '& input:focus': {
      border: `1px solid ${theme.colors.navy[0]}`,
      boxShadow: `0px 0px 1px 1px #1F84F4CC, 0 0 4px 0px #81dcffad`,
    },
  },
}));

export default function SearchActionMenuItem({ onOpen, className = '', responsive }: Props) {
  const { classes } = useStyle();
  const router = useRouter();
  const { t } = useTranslate('workspace');
  const [opened, setOpen] = useState(false);
  const textRef = useRef<HTMLInputElement>(null);

  const handleOpen = useCallback(() => {
    setOpen(true);
    setTimeout(() => textRef?.current.focus(), 100);
  }, []);

  const handlePressEnter = useCallback(
    (e: KeyboardEvent) => {
      if (e.key !== 'Enter') return;

      e.preventDefault();
      if (textRef?.current?.value) {
        console.log({
          searchText: textRef?.current?.value?.trim(),
          length: textRef?.current?.value?.length,
        });
        const searchText = encodeURIComponent(textRef?.current?.value.trim());
        animatedNavigate(`${SEARCH_BOX_ROUTE}?search_text=${searchText}`, router);
      }
    },
    [router]
  );

  useEffect(() => {
    onOpen?.(opened);
  }, [opened, onOpen]);

  return (
    <Popover
      trapFocus
      withArrow
      width={331}
      opened={opened}
      position='right'
      onChange={setOpen}
      shadow='0px 0px 4px 0px #aaa9a9;'
    >
      <Popover.Target>
        <ActionIcon className={className} onClick={handleOpen}>
          <IconSearch size='1.4rem' stroke={responsive ? 2 : 1.5} />
        </ActionIcon>
      </Popover.Target>

      <Popover.Dropdown>
        <Box p={5}>
          <TextInput
            ref={textRef}
            data-autofocus
            maxLength={350}
            placeholder={t('search_label')}
            onKeyDown={(e) => handlePressEnter(e as unknown as KeyboardEvent)}
            className={classes.input}
            leftSection={<IconSearch size='1.3rem' color='#5C5C5C' />}
          />
        </Box>
      </Popover.Dropdown>
    </Popover>
  );
}
