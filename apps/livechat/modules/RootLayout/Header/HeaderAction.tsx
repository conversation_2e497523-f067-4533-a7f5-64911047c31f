import { App<PERSON><PERSON>, Avatar, Container, Group, Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import Link from 'next/link';
import { useCallback, useMemo } from 'react';
import AppConfig from '../../../configs';
import { MAIN_ROUTE, NAVBAR_ID_NAME } from '../../../constants';
import { useAppContext } from '../../appContext';
import Hub from './Hub';
import Logo from './Logo';

const LOGO_HEIGHT = 59.47;

export const HEADER_HEIGHT = rem(LOGO_HEIGHT);

const useStyles = createStyles((theme, _, u) => ({
  inner: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  logoLink: {
    lineHeight: 1,
  },

  links: {
    [u.smallerThan('sm')]: {
      display: 'none',
    },
  },

  burger: {
    [u.largerThan('sm')]: {
      display: 'none',
    },
  },

  link: {
    display: 'block',
    lineHeight: 1,
    padding: `${rem(8)} ${rem(12)}`,
    borderRadius: theme.radius.sm,
    textDecoration: 'none',
    color: theme.colors.gray[7],
    fontSize: theme.fontSizes.sm,
    fontWeight: 500,

    '&:hover': {
      backgroundColor: theme.colors.gray[0],
    },
  },

  linkLabel: {
    marginRight: rem(5),
  },

  image: {
    padding: '12px 0',
    maxWidth: '225px',
    height: 'auto',
    width: '100%',
  },
}));

interface HeaderActionProps {
  links: {
    link: string;
    label: string;
    links: { link: string; label: string }[];
  }[];
}

// eslint-disable-next-line no-unused-vars
export function HeaderAction({ links }: HeaderActionProps) {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();
  const { responsive } = useAppContext();
  const { user, logout } = useAppContext();

  const goToProfile = useCallback(() => {
    if (AppConfig.IS_PRODUCTION) {
      // open in new page
      window.open(`${window.location.origin}/account`, '_blank');
    }
  }, []);

  const calcPaddingLeftLogo = useMemo(
    () => (responsive.responsiveScreen ? '5px' : '10px'),
    [responsive.responsiveScreen]
  );

  return (
    <Container className={classes.inner} fluid pl={0}>
      <Group
        align='center'
        justify='flex-start'
        sx={{
          paddingLeft: calcPaddingLeftLogo,
        }}
      >
        <div style={{ paddingLeft: '0.7rem' }}>
          <Hub />
        </div>
        <Link className={classes.logoLink} href={MAIN_ROUTE}>
          <Logo className={classes.image} />
        </Link>
      </Group>
      <Group gap={5}>
        <Menu shadow='md' width={228} position='bottom-end'>
          <Menu.Target>
            <Avatar
              radius='xl'
              src={user?.picture}
              alt={user?.name}
              title={user?.nickname}
              sx={{
                cursor: 'pointer',
              }}
            />
          </Menu.Target>

          <Menu.Dropdown
            sx={{
              borderRadius: '6px',
            }}
          >
            <Menu.Item
              sx={{
                textAlign: 'center',
              }}
              onClick={goToProfile}
            >
              {t('profile')}
            </Menu.Item>
            <Menu.Item
              onClick={logout}
              sx={{
                textAlign: 'center',
              }}
            >
              {t('logout')}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Group>
    </Container>
  );
}
