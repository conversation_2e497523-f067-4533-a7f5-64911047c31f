import React, { memo } from 'react';
import Image from 'next/image';
import { getPublicUrl } from '../../../../utils/public';
import { useAppContext } from '../../../../modules/appContext';
const Logo: React.FC<any> = ({ className }) => {
  const { lang } = useAppContext();

  const fileName = lang === 'en' ? '/images/logo-livechat-en.svg' : '/images/logo-livechat-jp.svg';

  return (
    <Image
      src={getPublicUrl(fileName)}
      className={className}
      width={256}
      height={28.36}
      alt='LOGO'
    />
  );
};

export default memo(Logo);
