import Image from 'next/image';
import React, { memo } from 'react';
import { useAppContext } from '../../../../modules/appContext';
import { getPublicUrl } from '../../../../utils/public';
const Logo: React.FC<any> = ({ className }) => {
  const { lang } = useAppContext();

  const fileName = lang === 'en' ? '/images/logo-livechat-en.svg' : '/images/logo-livechat-jp.svg';

  return (
    <Image
      src={getPublicUrl(fileName)}
      className={className}
      width={256}
      height={28.36}
      alt='LOGO'
    />
  );
};

export default memo(Logo);
