import { Box, Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCategory2 } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { Fragment, useMemo } from 'react';

type Hub = {
  label: string;
  icon?: React.ReactNode;
  link: string;
};

const useStyles = createStyles((_theme, _params, u) => ({
  hubIcon: {
    width: rem(36),
    height: rem(36),
    cursor: 'pointer',
    color: '#1D2088',
    backgroundColor: '#E4E5EC',
    borderRadius: rem(32),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    ['&:hover']: {
      backgroundColor: '#1D2088',
      color: '#E4E5EC',
    },
    ['&[data-expanded]']: {
      backgroundColor: '#1D2088',
      color: '#E4E5EC',
    },
  },
  hubGroup: {
    minWidth: rem(360),
    borderRadius: rem(16),
    padding: 0,
    marginTop: rem(14.5),
  },
  hubLink: {
    textDecoration: 'none',
    [`&:first-of-type .${u.ref('hubItem')}`]: {
      borderTopLeftRadius: rem(16),
      borderTopRightRadius: rem(16),
    },
    [`&:last-of-type .${u.ref('hubItem')}`]: {
      borderBottomLeftRadius: rem(16),
      borderBottomRightRadius: rem(16),
    },
  },
  hubItem: {
    ref: u.ref('hubItem'),
    fontSize: rem(18),
    fontWeight: 700,
    lineHeight: rem(27.9),
    padding: `${rem(12)} ${rem(24)}`,
    color: '#BDBDC5',
    ['&:hover']: {
      backgroundColor: '#F2F2F6',
    },
  },
  hubItemActive: {
    color: '#1D2088',
  },
}));

const decaHub = (t: any): Hub[] => [
  {
    label: t('decaChatbot'),
    link: '/chatbot',
  },
  {
    label: t('decaChatwindow'),
    link: '/chatbox',
  },
  {
    label: t('decaKnowledgeBase'),
    link: '/kb',
  },
  {
    label: t('decaLiveChat'),
    link: '/livechat',
  },
  {
    label: t('decaCRM'),
    link: '/crm',
  },
  {
    label: t('decaForms'),
    link: '/forms',
  },
  {
    label: t('decaTables'),
    link: '/tables',
  },
  {
    label: t('decaAIWidgets'),
    link: '/ai-widgets',
  },
  {
    label: t('decaPages'),
    link: '/pages',
  },
  {
    label: t('decaAiStudio'),
    link: '/studio',
  },
];

const isLocal = () => {
  if (typeof window === 'undefined') return false;
  return window.location.origin.includes('localhost');
};

const Hub = () => {
  const { t } = useTranslate('workspace');
  const { classes, cx } = useStyles();
  const hubs = useMemo(() => decaHub(t), [t]);
  const currentPath = window.location.pathname;
  const isLocalhost = isLocal();

  return (
    <Menu position='bottom-start'>
      <Menu.Target>
        <Box className={classes.hubIcon}>
          <IconCategory2 style={{ width: rem(18), height: rem(18) }} />
        </Box>
      </Menu.Target>
      <Menu.Dropdown className={classes.hubGroup}>
        {hubs.map((hub, index) => (
          <Fragment key={index}>
            <a
              className={classes.hubLink}
              href={`${window.location.origin}${hub.link}`}
              rel='noreferrer'
            >
              <Menu.Item
                leftSection={hub.icon}
                className={cx(classes.hubItem, {
                  [classes.hubItemActive]:
                    currentPath === hub.link ||
                    currentPath.includes(hub.link) ||
                    (hub.link === '/livechat' && isLocalhost),
                })}
              >
                {hub.label}
              </Menu.Item>
            </a>
            {index !== hubs.length - 1 && <Menu.Divider m={0} />}
          </Fragment>
        ))}
      </Menu.Dropdown>
    </Menu>
  );
};

export default Hub;
