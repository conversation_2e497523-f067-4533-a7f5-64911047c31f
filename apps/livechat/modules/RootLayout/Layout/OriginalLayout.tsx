import { AppShell, rem } from '@mantine/core';
import { ReactNode, useMemo } from 'react';
import { withTranslation } from 'react-i18next';
import { NAVBAR_ID_NAME } from '../../../constants';
import { INBOX_NAVBAR_AND_MAIN_PERCENT } from '../../../constants/grid';
import useFocusEventManager from '../../../hooks/useFocusEventManager';
import useOrgRedirect from '../../../hooks/useOrgRedirect';
import { useRealtimeApp } from '../../../hooks/useRealtimeApp';
import useRouteCorrection from '../../../hooks/useRouteCorrection';
import { useSoundNotification } from '../../../hooks/useSoundNotification';
import useTypingEventManager from '../../../hooks/useTypingEventManager';
import useUiCorrection from '../../../hooks/useUiCorrection';
import { isAllowedDomains } from '../../../utils/location';
import Warning from '../../Warning';
import { useAppContext } from '../../appContext';
import { AppVerticalNavbar } from '../AppVerticalNavbar/AppVerticalNavbar';
import { HEADER_HEIGHT, HeaderAction } from '../Header/HeaderAction';

const links = [
  {
    link: 'resola.ai',
    label: 'RESOLA',
    links: [{ link: 'resola.ai', label: 'resola' }],
  },
];

const OriginLayout = ({ children }: { children: ReactNode }) => {
  const {
    responsive: { responsiveScreen },
    isOperationSettingPage,
  } = useAppContext();

  const modifyPaddingLeft = useMemo(() => isOperationSettingPage, [isOperationSettingPage]);

  useOrgRedirect();
  useRouteCorrection();
  useUiCorrection();
  useFocusEventManager();
  useTypingEventManager();
  useRealtimeApp();
  useSoundNotification();

  if (isAllowedDomains()) {
    return null;
  }
  return (
    <>
      <Warning />
      <AppShell
        styles={(theme) => ({
          main: {
            backgroundColor: theme.colors.gray[0],
          },
        })}
        header={{ height: HEADER_HEIGHT }}
        // eslint-disable-next-line no-unused-vars
        sx={(theme) => ({
          '.mantine-AppShell-main': {
            paddingTop: `calc(var(--app-shell-header-height, 0px) + ${
              responsiveScreen ? `${rem(-0.2)}` : '0rem'
            })`,
            paddingLeft: !modifyPaddingLeft
              ? `calc(var(--app-shell-navbar-width, 0px))`
              : `${
                  INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_1 + INBOX_NAVBAR_AND_MAIN_PERCENT.COLUMN_2
                }vw`,
            paddingRight: modifyPaddingLeft ? '0' : 'calc(var(--app-shell-aside-width, 0px))',
            backgroundColor: 'white',
            paddingBottom: 0,
          },
        })}
      >
        <AppShell.Header
          id={NAVBAR_ID_NAME}
          sx={{
            borderBottom: '0.0625rem solid #dee2e6',
            zIndex: 200,
          }}
        >
          <HeaderAction links={links} />
        </AppShell.Header>
        <AppShell.Navbar
          sx={(theme) => ({
            backgroundColor: theme.colors.gray[1],
            borderRight: `${rem(1)} solid ${theme.colors.gray[3]}`,
          })}
        >
          <AppVerticalNavbar />
        </AppShell.Navbar>
        <AppShell.Main>{children}</AppShell.Main>
      </AppShell>
    </>
  );
};

const OriginalLayout = withTranslation()(OriginLayout);
export default OriginalLayout;
