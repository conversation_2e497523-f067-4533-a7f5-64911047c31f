import { I18nextProvider } from 'react-i18next';
import { AppContextProvider, useAppContext } from '../appContext';
import AuthenticateLayer from '../../components/authenticate';
import i18n from '../../services/i18n';
import { UserContextProvider } from '../userContext';
import { TeamContextProvider } from '../teamContext';
import OriginalLayout from './Layout/OriginalLayout';
import SwrCustomConfig from './SwrCustomConfig';
import { WidgetContextProvider } from '../widgetContext';
import { OperationSettingContextProvider } from '../operationSettingContext';
import React, { useEffect } from 'react';
import ApiService from '../../services/api';
import { WarningContextProvider } from '../warningContext';
import { TeamUserListContextProvider } from '../teamUserListContext';

const CustomI18nextProvider = I18nextProvider as any; // TODO prevent build failed, should improve this later

const AuthenticatedLayout = ({ children }: any) => {
  const [hasPermission, setHasPermission] = React.useState(false);
  const { logout } = useAppContext();
  useEffect(() => {
    ApiService.getOperatorProfile(true)
      .then((res) => {
        setHasPermission(!!res);
      })
      // eslint-disable-next-line no-unused-vars
      .catch((_err) => {
        setHasPermission(false);
        window?.stop();
        logout();
      });
  }, [logout]);

  if (!hasPermission) {
    return null;
  }
  return (
    <SwrCustomConfig>
      <WarningContextProvider>
        <UserContextProvider>
          <TeamContextProvider>
            <TeamUserListContextProvider>
              <WidgetContextProvider>
                <OperationSettingContextProvider>
                  <OriginalLayout>{children}</OriginalLayout>
                </OperationSettingContextProvider>
              </WidgetContextProvider>
            </TeamUserListContextProvider>
          </TeamContextProvider>
        </UserContextProvider>
      </WarningContextProvider>
    </SwrCustomConfig>
  );
};

function RootLayout({ children }: any) {
  return (
    <CustomI18nextProvider i18n={i18n}>
      <AppContextProvider>
        <AuthenticateLayer>
          <AuthenticatedLayout>{children}</AuthenticatedLayout>
        </AuthenticateLayer>
      </AppContextProvider>
    </CustomI18nextProvider>
  );
}

export default RootLayout;
