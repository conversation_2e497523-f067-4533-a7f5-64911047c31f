import React, { useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';
import AuthenticateLayer from '../../components/authenticate';
import ApiService from '../../services/api';
import i18n from '../../services/i18n';
import { AppContextProvider, useAppContext } from '../appContext';
import { OperationSettingContextProvider } from '../operationSettingContext';
import { TeamContextProvider } from '../teamContext';
import { TeamUserListContextProvider } from '../teamUserListContext';
import { UserContextProvider } from '../userContext';
import { WarningContextProvider } from '../warningContext';
import { WidgetContextProvider } from '../widgetContext';
import OriginalLayout from './Layout/OriginalLayout';
import SwrCustomConfig from './SwrCustomConfig';

const CustomI18nextProvider = I18nextProvider as any; // TODO prevent build failed, should improve this later

const AuthenticatedLayout = ({ children }: any) => {
  const [hasPermission, setHasPermission] = React.useState(false);
  const { logout } = useAppContext();
  useEffect(() => {
    ApiService.getOperatorProfile(true)
      .then((res) => {
        setHasPermission(!!res);
      })
      // eslint-disable-next-line no-unused-vars
      .catch((_err) => {
        setHasPermission(false);
        window?.stop();
        logout();
      });
  }, [logout]);

  if (!hasPermission) {
    return null;
  }
  return (
    <SwrCustomConfig>
      <WarningContextProvider>
        <UserContextProvider>
          <TeamContextProvider>
            <TeamUserListContextProvider>
              <WidgetContextProvider>
                <OperationSettingContextProvider>
                  <OriginalLayout>{children}</OriginalLayout>
                </OperationSettingContextProvider>
              </WidgetContextProvider>
            </TeamUserListContextProvider>
          </TeamContextProvider>
        </UserContextProvider>
      </WarningContextProvider>
    </SwrCustomConfig>
  );
};

function RootLayout({ children }: any) {
  return (
    <CustomI18nextProvider i18n={i18n}>
      <AppContextProvider>
        <AuthenticateLayer>
          <AuthenticatedLayout>{children}</AuthenticatedLayout>
        </AuthenticateLayer>
      </AppContextProvider>
    </CustomI18nextProvider>
  );
}

export default RootLayout;
