import styled from '@emotion/styled';
import { Button, Center, Container, Flex, Group, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IConversation } from '@resola-ai/models';
import { BottomUpTween } from '@resola-ai/ui';
import { createCustomEventListener } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React, { useEffect, useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import {
  CHATROOM_MODULE_HEIGHT,
  COMMON_CHILD_MODULE_HEIGHT,
  CONVERSATION_ASSIGNED,
  CONVERSATION_ASSIGNED_BY_ANOTHER,
  CONVERSATION_LIST_MODULE_HEIGHT,
  DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR,
  MAIN_MODULE_ROOT_HEIGHT,
  TOOLS_MODULE_HEIGHT,
} from '../../constants';
import { INBOX_MAIN_PERCENT } from '../../constants/grid';
import useVisibilityControl from '../../hooks/useVisibilityControl';
import ChatRoom from '../Chatbox/ChatRoom';
import ConversationsListContainer from '../Conversations';
import WorkspaceMenuComponent from '../Menu/Workspace/WorkspaceMenu';
import NoContent from '../NoContent';
import Tools from '../Tools/Tools';
import { AiSummaryContextProvider } from '../aiSummaryContext';
import { useAppContext } from '../appContext';
import {
  ConversationActionContextProvider,
  useConversationActionContext,
} from '../conversationActionContext';
import { ConversationContextProvider } from '../conversationContext';
import { InitContextProvider } from '../initContext';
import { MessageContextProvider } from '../messageContext';
import { OpenAiContextProvider } from '../openaiContext';
import { UploadAssetContextProvider } from '../uploadAssetContext';
import { WorkspaceContextProvider } from '../workspaceContext';

const BORDER_LEFT = '0.0625rem solid #e9ecef';
const animationContainerHeight = COMMON_CHILD_MODULE_HEIGHT;

const ResponsiveScreenCol = styled.div`
    position: relative;
    border-left: ${BORDER_LEFT};
    height: ${MAIN_MODULE_ROOT_HEIGHT};
`;

const ChatRoomResponsiveScreenCol = styled(ResponsiveScreenCol)`
    height: ${CHATROOM_MODULE_HEIGHT};
`;

const ResponsiveScreenColConversation = styled(ResponsiveScreenCol)`
    height: ${CONVERSATION_LIST_MODULE_HEIGHT};
`;

const customStyles = createStyles((theme) => ({
  col: {
    position: 'relative',
    borderLeft: BORDER_LEFT,
    height: animationContainerHeight,
  },
  chatRoom: {
    position: 'relative',
    borderLeft: BORDER_LEFT,
    height: CHATROOM_MODULE_HEIGHT,
  },
  toolCol: {
    position: 'relative',
    borderLeft: BORDER_LEFT,
    height: TOOLS_MODULE_HEIGHT,
    paddingRight: 0,
  },
  main: {
    flex: 1,
    paddingTop: '20px',
    height: '100%',
    backgroundColor: theme.colors.gray[0],
  },
  panelResize: {
    flex: '0 0 0rem',
    display: 'flex',
    justifyContent: 'stretch',
    alignItems: 'stretch',
    outline: 'none',
    transition: 'background-color 0.2s linear, flex-basis 0.2s linear',

    '&[data-resize-handle-active="keyboard"]': {
      backgroundColor: '#8485C7',
      flexBasis: '0.25rem',
    },
    '&[data-resize-handle-active="pointer"]': {
      backgroundColor: '#8485C7',
      flexBasis: '0.25rem',
    },
  },
}));

const ResponsiveScreenColTools = styled(ResponsiveScreenCol)`
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: ${rem(15)};
    padding-right: 15px;
    height: ${TOOLS_MODULE_HEIGHT};
    overflow-y: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

interface AnimationProps {
  isTools?: boolean;
  isChatRoom?: boolean;
  children: React.ReactNode;
}

function AnimationContainer({ children, isChatRoom, isTools }: AnimationProps) {
  const { classes } = customStyles();

  const getClassName = () => {
    if (isChatRoom) return classes.chatRoom;
    if (isTools) return classes.toolCol;
    return classes.col;
  };

  const className = getClassName();
  return <BottomUpTween className={className}>{children}</BottomUpTween>;
}

const ToolColumn = () => {
  const { shouldDisplay } = useConversationActionContext();
  if (!shouldDisplay) return null;
  return (
    <>
      <ResponsiveScreenColTools>
        <Tools />
      </ResponsiveScreenColTools>
    </>
  );
};

function Main() {
  const { classes } = customStyles();
  const { lang } = useAppContext();
  return (
    <>
      <Flex style={{ height: `${MAIN_MODULE_ROOT_HEIGHT}` }} direction='row'>
        <PanelGroup direction='horizontal' autoSaveId='inbox_screen_layout_data'>
          <Panel
            defaultSize={INBOX_MAIN_PERCENT.COLUMN_NAVBAR}
            minSize={INBOX_MAIN_PERCENT.COLUMN_NAVBAR - 5}
            maxSize={INBOX_MAIN_PERCENT.COLUMN_NAVBAR + 10}
            order={1}
          >
            <div className={classes.main}>
              <WorkspaceContextProvider>
                <WorkspaceMenuComponent lang={lang} />
              </WorkspaceContextProvider>
            </div>
          </Panel>
          <PanelResizeHandle className={classes.panelResize} />
          <Panel
            defaultSize={INBOX_MAIN_PERCENT.COLUMN_CONVERSATION_LIST}
            minSize={INBOX_MAIN_PERCENT.COLUMN_CONVERSATION_LIST - 5}
            maxSize={INBOX_MAIN_PERCENT.COLUMN_CONVERSATION_LIST + 5}
            order={2}
          >
            <ConversationContextProvider>
              <InitContextProvider>
                <ResponsiveScreenColConversation>
                  <ConversationsListContainer />
                </ResponsiveScreenColConversation>
              </InitContextProvider>
            </ConversationContextProvider>
          </Panel>
          <PanelResizeHandle className={classes.panelResize} />
          <Panel
            defaultSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGE_LIST_AND_TOOLS}
            minSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGE_LIST_AND_TOOLS - 10}
            maxSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGE_LIST_AND_TOOLS + 10}
            order={3}
          >
            <PanelGroup
              direction='horizontal'
              autoSaveId='inbox_chatbox_and_tool_layout'
              id='inbox_chatbox_and_tool_layout_panel_group'
            >
              <ChatBoxAndToolColumn />
            </PanelGroup>
          </Panel>
        </PanelGroup>
      </Flex>
      <ModalShowSelfAssignConversationButAlreadyAssigned />
    </>
  );
}

const ChatBoxAndToolColumn = () => {
  const { classes } = customStyles();
  const { lang } = useAppContext();
  const [hasSelectedConversation, setHasSelectConversation] = useState(false);

  useEffect(() => {
    const handleReceiveConversation = (
      event: CustomEvent<{ conversation: IConversation | undefined }>
    ) => {
      const { conversation } = event.detail;
      setHasSelectConversation(!!conversation);
    };
    const cleanUp = createCustomEventListener(
      'deca-livechat-conversation-context-has-loaded-conversation',
      handleReceiveConversation
    );
    return () => {
      cleanUp();
    };
  }, []);
  return (
    <>
      {!hasSelectedConversation && (
        <Panel order={3.1} id='place_holder_for_chat_box_and_tools_panel'>
          <Flex
            sx={{
              display: 'inherit',
            }}
          >
            <AnimationContainer>
              <NoContent />
            </AnimationContainer>
          </Flex>
        </Panel>
      )}
      <ConversationActionContextProvider>
        <OpenAiContextProvider>
          <UploadAssetContextProvider>
            <MessageContextProvider>
              <AiSummaryContextProvider>
                {hasSelectedConversation && (
                  <Panel
                    order={3.2}
                    id='chat_box_panel'
                    defaultSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGES_LIST}
                    maxSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGES_LIST + 20}
                    minSize={INBOX_MAIN_PERCENT.COLUMN_MESSAGES_LIST - 10}
                  >
                    <ChatRoomResponsiveScreenCol>
                      <ChatRoom lang={lang} />
                    </ChatRoomResponsiveScreenCol>
                  </Panel>
                )}
              </AiSummaryContextProvider>
            </MessageContextProvider>
          </UploadAssetContextProvider>
        </OpenAiContextProvider>
      </ConversationActionContextProvider>

      <ConversationActionContextProvider>
        {hasSelectedConversation && (
          <>
            <PanelResizeHandle className={classes.panelResize} />
            <Panel
              order={3.3}
              id='tools_panel'
              defaultSize={INBOX_MAIN_PERCENT.COLUMN_TOOL_LIST}
              maxSize={INBOX_MAIN_PERCENT.COLUMN_TOOL_LIST + 20}
              minSize={INBOX_MAIN_PERCENT.COLUMN_TOOL_LIST - 5}
            >
              <ToolColumn />
            </Panel>
          </>
        )}
      </ConversationActionContextProvider>
    </>
  );
};

const ModalShowSelfAssignConversationButAlreadyAssigned = () => {
  const { visible, open, close } = useVisibilityControl(false);
  const [message, setMessage] = useState('');
  const { t } = useTranslate('workspace');

  useEffect(() => {
    const handleEvent = (e) => {
      if (![CONVERSATION_ASSIGNED_BY_ANOTHER, CONVERSATION_ASSIGNED].includes(e?.detail?.message)) {
        return;
      }

      if (e?.detail?.message === CONVERSATION_ASSIGNED) {
        setMessage(t('errorMessageSelfAssignAlreadyTaken'));
      } else {
        setMessage(t('errorMessageSelfAssignAlreadyTakenCaseAnother'));
      }
      open();
    };
    const cleanUp = createCustomEventListener(
      DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR,
      handleEvent
    );
    return () => {
      cleanUp();
    };
  }, [open, t]);

  return (
    <Modal
      centered
      size={'500px'}
      onClose={close}
      opened={visible}
      withCloseButton={false}
      style={{ position: 'relative' }}
    >
      <Modal.CloseButton className='' size={'lg'} style={{ position: 'absolute', right: '4%' }} />
      <Container mt={50}>
        <Center mb={'lg'}>
          <Text fw={700} ta='center' c={'dark.4'} size={rem(17)} sx={{ whiteSpace: 'normal' }}>
            {message}
          </Text>
        </Center>
        <Group mb={20} mt={40} justify='flex-end'>
          <Button variant='outline' color='gray.6' onClick={close} fz={rem(16)}>
            {t('close')}
          </Button>
        </Group>
      </Container>
    </Modal>
  );
};

export default Main;
