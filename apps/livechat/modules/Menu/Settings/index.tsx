import { UrlObject } from 'url';
import { Box, Container, Space, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { useTranslate } from '@tolgee/react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useMemo } from 'react';

const useStyle = createStyles((theme, _, u) => ({
  main: {
    marginTop: theme.spacing.xl,
    color: COLOR_DEFAULT_BLACK,
    overflowY: 'auto',
    maxHeight: 'calc(100vh - 100px)',
    height: '100%',
  },
  titleMain: {
    padding: '0 12px',
    fontWeight: 700,
    fontSize: '15px',
  },
  linkComponent: {
    textDecoration: 'none',
    color: 'black',
  },
  linkStyle: {
    fontWeight: 400,
    fontSize: theme.fontSizes.sm,
    marginBottom: '0.75rem',
    borderRadius: theme.radius.md,
    paddingLeft: theme.spacing.xs,
    paddingRight: theme.spacing.xs,
    paddingBottom: '5px',
    paddingTop: '5px',
    cursor: 'pointer',
    transition: 'all',
    '&:hover': {
      backgroundColor: '#e5dbff',
    },
  },
  linkActive: {
    backgroundColor: '#e5dbff',
  },
}));

type SettingsLinksType = {
  link: UrlObject | string;
  title: string;
};

export default function SettingsMenu() {
  const { t } = useTranslate('workspace');
  const { classes, cx } = useStyle();
  const router = useRouter();

  const settingsLinks = useMemo<SettingsLinksType[]>(() => {
    return [
      {
        link: '/settings/team-user-list',
        title: t('settings.team_user_menu_label'),
      },
      {
        link: '/settings/operations',
        title: t('settings.business_status_menu_label'),
      },
      {
        link: '/settings/autoreply',
        title: t('settings.auto_reply_settings_label'),
      },
      {
        link: '/settings/integrations',
        title: t('settings.external_tool_integration_label'),
      },
      {
        link: '/settings/functions',
        title: t('settings.function_settings'),
      },
      {
        link: '/settings/notifications',
        title: t('settings.personal_settings'),
      },
    ];
  }, [t]);

  return (
    <Container className={classes.main}>
      <Title order={6} className={classes.titleMain}>
        {t('settings.title_menu')}
      </Title>
      <Space h={'xs'} />
      {settingsLinks.map((settings) => {
        return (
          <Link key={settings.title} href={settings.link} className={classes.linkComponent}>
            <Box
              key={settings.title}
              className={cx(classes.linkStyle, {
                [classes.linkActive]: router.asPath.includes(settings.link as string),
              })}
            >
              {settings.title}
            </Box>
          </Link>
        );
      })}
    </Container>
  );
}
