import { createCustomEventListener } from '@resola-ai/utils';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { GLOBAL_REALTIME_EVENT_NAME } from '../../../constants';
import { IWebsocketResponse } from '../../../models/websocket';
import { IWorkspacesConversationStatistic } from '../../../models/workspace';
import ApiService from '../../../services/api';
import logger from '../../../services/logger';

export function useWorkspaceData() {
  const [unreadAssignedConversationNumber, setAssignedUnreadConversationNumber] =
    useState<number>(0);

  const getUnreadConversationStatus = useCallback(async () => {
    try {
      const data = await ApiService.getUnreadConversationStatus();
      const unreadData = data.unreadConversations;
      setAssignedUnreadConversationNumber(unreadData?.quantity?.length || 0);
    } catch (error) {
      logger.error(
        'Error when get unread conversation status',
        error?.response?.data?.message || ''
      );
    }
  }, []);

  useEffect(() => {
    const handleSetupNewData = (
      event: CustomEvent<{ data: IWebsocketResponse<IWorkspacesConversationStatistic> }>
    ) => {
      const { data } = event.detail;
      if (data.type !== 'user.unread.conversation.updated') return;

      const unreadConversation = data.data;
      setAssignedUnreadConversationNumber(unreadConversation?.quantity?.length || 0);
    };

    const unregisterEvent = createCustomEventListener(
      GLOBAL_REALTIME_EVENT_NAME,
      handleSetupNewData
    );

    // const unregisterEvent = createCustomEventListener(
    //     'deca-livechat-unread-conversation-statistic-notify',
    //     handleSetupNewData,
    // );

    return () => {
      unregisterEvent();
    };
  }, []);

  return useMemo(
    () => ({
      unreadAssignedConversationNumber,
      getUnreadConversationStatus,
    }),
    [getUnreadConversationStatus, unreadAssignedConversationNumber]
  );
}
