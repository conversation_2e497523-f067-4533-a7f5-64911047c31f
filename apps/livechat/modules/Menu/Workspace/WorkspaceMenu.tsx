import {
  ActionIcon,
  <PERSON><PERSON>,
  <PERSON>lapse,
  Container,
  Flex,
  Group,
  Menu,
  Modal,
  Stack,
  Text,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { BottomUpTween, If, NameWithPopover } from '@resola-ai/ui';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { IconChevronDown, IconChevronRight } from '@tabler/icons-react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ANIMATION_DELAY_SECOND } from '../../../constants';
import {
  MENU_BOOKMARK,
  MENU_MY_ASSIGNED,
  MENU_SUBTYPE_TEAM_ALL,
  MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR,
  MENU_UNASSIGNED,
} from '../../../constants/menu';
import { MenuIdType } from '../../../models/menu';
import ApiService from '../../../services/api';
import { ParamsTypeGenerator } from '../../../utils/systemParamsGenerator';
import { useAppContext } from '../../appContext';
import { useOperationSettingContext } from '../../operationSettingContext';
import { useUserContext } from '../../userContext';
import { useWorkspaceContext } from '../../workspaceContext';
import { WorkspaceType } from '../constants';
import SkeletonLoading from './Skeleton';
import { useWorkspaceData } from './useWorkspaceData';

const useStyles = createStyles((theme) => ({
  main: {
    color: COLOR_DEFAULT_BLACK,
    overflowY: 'auto',
    maxHeight: 'calc(100vh - 100px)',
    height: '100%',
  },

  mainResponsive: {
    width: '100%',
    marginLeft: rem(0),
    marginRight: rem(0),
    paddingLeft: rem(10),
    paddingRight: rem(10),
    marginTop: rem(16),
    fontSize: rem(12),
  },

  title: {
    textAlign: 'left',
    marginBottom: '0.875rem',
    padding: '0 8px',
    fontWeight: 700,
    fontSize: '14px',
  },

  titleResponsive: {
    fontSize: rem(12),
  },

  linkWrapper: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '0.5rem',
    borderRadius: '6px',
    padding: '0px 8px',
    fontSize: theme.fontSizes.sm,
  },

  linkWrapperResponsive: {
    fontSize: rem(12),
    // h6
    '& h6': {
      fontSize: rem(12),
    },
  },

  linkMenu: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#e5dbff',
    },
  },

  linkActive: {
    backgroundColor: '#e5dbff',
  },

  titleWrapper: {
    width: '100%',
    minHeight: '28px',
  },
  titleWrapperResponsive: {
    paddingInline: 0,
  },

  unreadNumber: {
    marginLeft: theme.spacing.md,
    color: theme.white,
    borderRadius: '50%',
    padding: '2px 0',
    backgroundColor: theme.colors.red[5],
    fontSize: theme.fontSizes.xs,
    minWidth: '22px',
    minHeight: '22px',
    textAlign: 'center',
  },

  unreadNumberTwoDigits: {
    marginLeft: theme.spacing.md,
    color: theme.white,
    borderRadius: '50%',
    padding: '4px 0',
    backgroundColor: theme.colors.red[5],
    fontSize: theme.fontSizes.xs,
    minWidth: '28px',
    minHeight: '28px',
    textAlign: 'center',
  },

  numbOfMessages: {
    fontWeight: 700,
  },

  subTitleIcon: {
    padding: '0 8px',
    cursor: 'initial',
  },

  collapseWrapper: {
    padding: '0px 12px 0px 0px',
    cursor: 'pointer',
    justifyContent: 'flex-start',
  },

  collapseContent: {
    paddingLeft: '28px',
  },

  stateDot: {
    width: '8px',
    height: '8px',
    borderRadius: '2px',
    marginRight: '4px',
  },

  onOffStateResponsive: {
    fontSize: rem(10),
    whiteSpace: 'nowrap',
  },

  onlineState: {
    backgroundColor: theme.colors.green[6],
    borderRadius: '50%',
  },

  offlineState: {
    backgroundColor: theme.colors.gray[6],
    borderRadius: '50%',
  },

  alignAssign: {
    padding: '0px 8px',
  },

  customButton: {
    color: '#5c5c5c',
    backgroundColor: '#ffffff',
    borderColor: '#5c5c5c',
    fontSize: '0.75rem',
    padding: '0 0.5rem',
    width: '100%',
    height: '2rem',
    '&:hover': {
      backgroundColor: '#ffffff',
      color: '#5c5c5c',
    },
  },
  customCloseButton: {
    color: '#5c5c5c',
    backgroundColor: '#ffffff',
    borderColor: '#5c5c5c',
    fontSize: '0.75rem',
    padding: '0 1.5rem',
    height: '2rem',
    display: 'block',
    '&:hover': {
      backgroundColor: '#ffffff',
      color: '#5c5c5c',
    },
  },
}));

export default function WorkspaceMenuComponent({ lang }: { lang: string }) {
  const { updateLang } = useWorkspaceContext();
  useEffect(() => {
    updateLang(lang);
  }, [lang, updateLang]);
  return <WorkspaceMenu />;
}

const WorkspaceMapTypes = [
  MENU_MY_ASSIGNED,
  MENU_UNASSIGNED,
  MENU_SUBTYPE_TEAM_ALL,
  MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR,
  MENU_BOOKMARK,
];

const WorkspaceMenu = () => {
  const { classes, cx } = useStyles();
  const { t: tRebotWidget } = useTranslation('rebot_widget');
  const { accessToken, responsive, endUserEditedRef } = useAppContext();
  const { updateUserStatus, isOnline } = useUserContext();
  const {
    t,
    isLoading,
    activeMenuId,
    workSpaceMenu,
    toggleCollapse,
    setActiveMenuId,
    getMyWorkspaces,
  } = useWorkspaceContext();
  const [onlineState, setOnlineState] = useState<boolean>(isOnline);
  const { responsiveScreen } = responsive;
  const { getUnreadConversationStatus, unreadAssignedConversationNumber } = useWorkspaceData();

  const [collapsedItems, setCollapsedItems] = useState({});

  const { preferences } = useOperationSettingContext();
  const showAutoBtnIfOff = preferences?.showAutoBtnIfOff || false;
  const [opened, setOpened] = useState(false);

  const selectWorkspaceAndSendEventSetupRelatedConversations = useCallback(
    (workspaceType: string, teamId: string) => {
      if (!WorkspaceMapTypes.includes(workspaceType)) return;
      const dataSendEvents = ParamsTypeGenerator.getParamsByType(workspaceType, teamId);
      sendCustomEvent('deca-livechat-workspace-setup-related-conversations', dataSendEvents);
    },
    []
  );

  const selectWorkspace = useCallback(
    (activeLinkId: MenuIdType, workspaceType: string, teamId: string | null) => {
      if (endUserEditedRef.current && !window.confirm(tRebotWidget('change_warning'))) {
        return false;
      } else {
        setActiveMenuId(activeLinkId);
        endUserEditedRef.current = false;
        selectWorkspaceAndSendEventSetupRelatedConversations(workspaceType, teamId);
      }
    },
    [
      tRebotWidget,
      setActiveMenuId,
      endUserEditedRef,
      selectWorkspaceAndSendEventSetupRelatedConversations,
    ]
  );

  const renderTitle = useCallback(
    (menuItem, index) => {
      if (menuItem.type === WorkspaceType.MY_ASSIGNED) {
        return (
          <div
            className={cx(classes.linkWrapper, {
              [classes.linkWrapperResponsive]: responsiveScreen,
            })}
          >
            <Flex
              gap='8'
              align='center'
              direction='row'
              justify='flex-start'
              className={cx(classes.titleWrapper, {
                [classes.titleWrapperResponsive]: responsiveScreen,
              })}
            >
              <Text
                fw={700}
                fz={14}
                sx={{
                  ...(responsiveScreen
                    ? {
                        whiteSpace: 'nowrap',
                        padding: '0 0',
                      }
                    : {}),
                }}
              >
                {menuItem.title}
              </Text>
              <Menu shadow='md' trigger='click' width={200} openDelay={100} closeDelay={400}>
                <Menu.Target>
                  <Flex
                    align='center'
                    direction='row'
                    justify='flex-start'
                    sx={{
                      ':hover': {
                        cursor: 'pointer',
                        fontWeight: 600,
                      },
                    }}
                  >
                    <div
                      className={cx(classes.stateDot, {
                        [classes.onlineState]: onlineState,
                        [classes.offlineState]: !onlineState,
                      })}
                    ></div>
                    <Text
                      fz={13}
                      c='gray.6'
                      className={cx({
                        [classes.onOffStateResponsive]: responsiveScreen,
                      })}
                    >
                      {onlineState
                        ? `${t('inbox.myassigned.online.title')}`
                        : `${t('inbox.myassigned.offline.title')}`}
                    </Text>
                    <ActionIcon
                      variant='transparent'
                      color='gray.7'
                      style={{ width: '1rem', minWidth: '1rem' }}
                    >
                      <IconChevronDown size='1rem' stroke={1.5} />
                    </ActionIcon>
                  </Flex>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item
                    onClick={() => {
                      setOnlineState(true);
                      updateUserStatus(true);
                    }}
                  >
                    <Flex gap='xs' align='center' direction='row' justify='flex-start'>
                      <div className={cx(classes.stateDot, classes.onlineState)}></div>
                      <Text fz={14}>{`${t('inbox.myassigned.online.title')}`}</Text>
                    </Flex>
                  </Menu.Item>
                  <Menu.Item
                    onClick={() => {
                      setOnlineState(false);
                      updateUserStatus(false);
                    }}
                  >
                    <Flex gap='xs' align='center' direction='row' justify='flex-start'>
                      <div className={cx(classes.stateDot, classes.offlineState)}></div>
                      <Text fz={14}>{`${t('inbox.myassigned.offline.title')}`}</Text>
                    </Flex>
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Flex>
          </div>
        );
      }

      if (menuItem.type === WorkspaceType.UN_ASSIGNED_TEAM) {
        return (
          <div className={cx(classes.linkWrapper, classes.subTitleIcon)} key={index}>
            <Text
              mt={4}
              fz={14}
              fw={700}
              sx={{
                ...(responsiveScreen
                  ? {
                      whiteSpace: 'nowrap',
                      padding: '0 0',
                      fontSize: rem(12),
                    }
                  : {}),
              }}
            >
              {menuItem.title}
            </Text>
          </div>
        );
      }

      if (menuItem.type === WorkspaceType.LIST_OF_TEAMS) {
        return (
          <div className={cx(classes.linkWrapper, classes.collapseWrapper)}>
            <Flex
              justify='flex-start'
              align='center'
              direction='row'
              className={classes.titleWrapper}
              onClick={() => toggleCollapse(index)}
            >
              {collapsedItems[menuItem.id] ? (
                <ActionIcon variant='transparent' color='gray.7'>
                  <IconChevronDown size='0.875rem' stroke={2} />
                </ActionIcon>
              ) : (
                <ActionIcon variant='transparent' color='gray.7'>
                  <IconChevronRight size='0.875rem' stroke={2} />
                </ActionIcon>
              )}
              <NameWithPopover
                maw={responsive.belowMediumScreen ? undefined : 'auto'}
                name={menuItem.title}
                responsiveScreen={responsiveScreen}
              />
            </Flex>
          </div>
        );
      }
    },
    [
      t,
      onlineState,
      collapsedItems,
      responsiveScreen,
      classes.stateDot,
      classes.linkWrapper,
      classes.onlineState,
      classes.subTitleIcon,
      classes.offlineState,
      classes.titleWrapper,
      classes.collapseWrapper,
      responsive.belowMediumScreen,
      classes.onOffStateResponsive,
      classes.linkWrapperResponsive,
      classes.titleWrapperResponsive,
      cx,
      toggleCollapse,
      updateUserStatus,
    ]
  );

  const renderWorkspaceLink = useCallback(
    (subItem, index: number, workspace: string) => {
      return (
        <div
          key={index}
          onClick={() => selectWorkspace(subItem.id, subItem.type, subItem.teamId)}
          className={cx(classes.linkWrapper, classes.linkMenu, {
            [classes.linkActive]: activeMenuId === subItem.id,
            [classes.alignAssign]: workspace === WorkspaceType.MY_ASSIGNED,
          })}
        >
          <Flex
            justify='space-between'
            align='center'
            direction='row'
            className={classes.titleWrapper}
          >
            <Flex
              sx={{
                ...(responsiveScreen
                  ? {
                      fontSize: rem(12),
                    }
                  : {}),
              }}
              align='center'
            >
              {subItem.icon}
              {subItem.label}
            </Flex>

            <If
              condition={
                unreadAssignedConversationNumber > 0 &&
                workspace === WorkspaceType.MY_ASSIGNED &&
                subItem.type !== 'bookmark'
              }
            >
              <If condition={unreadAssignedConversationNumber > 99}>
                <Text
                  className={classes.unreadNumberTwoDigits}
                  sx={{
                    ...(responsiveScreen
                      ? {
                          fontSize: rem(10),
                          minWidth: '25px',
                          minHeight: '25px',
                        }
                      : {}),
                  }}
                >{`99+`}</Text>
              </If>
              <If
                condition={
                  unreadAssignedConversationNumber > 0 && unreadAssignedConversationNumber <= 99
                }
              >
                <Text
                  className={classes.unreadNumberTwoDigits}
                  sx={{
                    ...(responsiveScreen
                      ? {
                          fontSize: rem(10),
                          minWidth: '25px',
                          minHeight: '25px',
                        }
                      : {}),
                  }}
                >
                  {unreadAssignedConversationNumber}
                </Text>
              </If>
            </If>
            <If
              condition={
                workspace === WorkspaceType.UN_ASSIGNED_TEAM && subItem.conversationNumbers > 0
              }
            >
              <Text className={classes.numbOfMessages} fz={12}>
                {subItem.conversationNumbers}
              </Text>
            </If>
            <If
              condition={
                workspace === WorkspaceType.LIST_OF_TEAMS && subItem.conversationNumbers > 0
              }
            >
              <Text className={classes.numbOfMessages} fz={12}>
                {subItem.conversationNumbers}
              </Text>
            </If>
          </Flex>
        </div>
      );
    },
    [
      activeMenuId,
      responsiveScreen,
      classes.linkMenu,
      classes.linkActive,
      classes.alignAssign,
      classes.linkWrapper,
      classes.titleWrapper,
      classes.numbOfMessages,
      classes.unreadNumberTwoDigits,
      unreadAssignedConversationNumber,
      selectWorkspace,
      cx,
    ]
  );

  const handleTeamClick = (teamId) => {
    setCollapsedItems((prevState) => ({
      ...prevState,
      [teamId]: !prevState[teamId],
    }));
  };

  const handleOptionClick = (event) => {
    event.stopPropagation();
  };

  const autoAssignOperatorToConversation = async () => {
    try {
      const selfAssignConversation = await ApiService.selfAssignConversationAmongAll();

      if (selfAssignConversation === 'open') {
        setOpened(true);
      } else {
        selectWorkspace('myAssigned', 'myAssigned', '');
        setTimeout(() => {
          sendCustomEvent('deca-livechat-refetch-conversations-for-new-list-change', {});
        }, 500);
        setTimeout(() => {
          sendCustomEvent('deca-livechat-change-selected-conversation', {
            conversationId: selfAssignConversation.id,
          });
        }, 1000);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (accessToken) {
      getMyWorkspaces();
      getUnreadConversationStatus();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessToken]);

  useEffect(() => {
    setOnlineState(isOnline);
  }, [isOnline]);

  useEffect(() => {
    const handleSelectActiveMenu = (e: CustomEvent<{ activeMenuId: MenuIdType }>) => {
      const { activeMenuId } = e.detail;
      setActiveMenuId(activeMenuId);
    };
    const unregisterEvent2 = createCustomEventListener(
      'deca-livechat-select-active-menu-id-workspace-menu',
      handleSelectActiveMenu
    );
    return unregisterEvent2;
  }, [setActiveMenuId]);

  if (isLoading) {
    return (
      <Container className={classes.main}>
        <Text
          className={cx(classes.title, {
            [classes.titleResponsive]: responsiveScreen,
          })}
        >
          {t('inbox.title')}
        </Text>
        <SkeletonLoading />
      </Container>
    );
  }

  return (
    <Container
      className={cx(classes.main, {
        [classes.mainResponsive]: responsiveScreen,
      })}
    >
      <Text
        className={cx(classes.title, {
          [classes.titleResponsive]: responsiveScreen,
        })}
      >
        {t('inbox.title')}
      </Text>
      {showAutoBtnIfOff === true && (
        <>
          <Container px={0} mb={5}>
            <Button className={classes.customButton} onClick={autoAssignOperatorToConversation}>
              {t('settings.auto_assignment')}
            </Button>
          </Container>

          <Modal opened={opened} onClose={() => setOpened(false)} centered>
            <Stack gap={rem(12)} mb={10}>
              <Text ta={'left'} fw={700} fz={14} style={{ whiteSpace: 'pre-line' }}>
                {t('settings.auto_assignment_modal')}
              </Text>
            </Stack>

            <Group gap={rem(10)} mt={rem(20)} mb={0} justify='flex-end'>
              <Button className={classes.customCloseButton} onClick={() => setOpened(false)}>
                {t('close')}
              </Button>
            </Group>
          </Modal>
        </>
      )}
      {workSpaceMenu.length > 0 &&
        workSpaceMenu.map((menuItem, index) =>
          menuItem.type === WorkspaceType.LIST_OF_TEAMS ? (
            <div key={index} onClick={() => handleTeamClick(menuItem.id)}>
              <BottomUpTween delay={ANIMATION_DELAY_SECOND * index}>
                {renderTitle(menuItem, index)}
                <Collapse in={collapsedItems[menuItem.id]} className={classes.collapseContent}>
                  {menuItem.subMenu.map((subItem, index) => (
                    <div key={index} onClick={handleOptionClick}>
                      {renderWorkspaceLink(subItem, index, menuItem.type)}
                    </div>
                  ))}
                </Collapse>
              </BottomUpTween>
            </div>
          ) : (
            <div key={index}>
              <BottomUpTween delay={ANIMATION_DELAY_SECOND * index}>
                {renderTitle(menuItem, index)}
                {menuItem.subMenu.map((subItem, index) =>
                  renderWorkspaceLink(subItem, index, menuItem.type)
                )}
              </BottomUpTween>
            </div>
          )
        )}
    </Container>
  );
};
