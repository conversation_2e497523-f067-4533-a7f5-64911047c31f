import axios from 'axios';
import { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { createCustomEventListener, LocalStorageUtils } from '@resola-ai/utils';
import AppConfig from '../configs';
import { scrollToMessageListBottom } from '../utils/scroll';
import { useAppContext } from './appContext';
import { useConversationActionContext } from './conversationActionContext';
import { IWebsocketResponse } from '../models/websocket';
import { GLOBAL_REALTIME_EVENT_NAME } from '../constants';

const AI_SUMMARY_KEY = 'aiSummary';

const useAiSummary = () => {
  const { accessToken } = useAppContext();
  const { currentConversationId } = useConversationActionContext();

  const [aiSummaryLoadings, setAiSummaryLoadings] = useState<Record<string, boolean>>({});
  const [aiSummaryTexts, setAiSummaryTexts] = useState<Record<string, string>>({});

  const resetAiSummaryText = useCallback(() => {
    setAiSummaryTexts((prev) => ({
      ...prev,
      [currentConversationId]: '',
    }));
  }, [currentConversationId]);

  const aiSummary = useCallback(async (): Promise<string> => {
    const response = await axios.get(
      `${AppConfig.BASE_URL_API}/conversations/${currentConversationId}/summary`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    return response.data.data;
  }, [accessToken, currentConversationId]);

  const aiSummaryWrapper = useCallback(async () => {
    try {
      setAiSummaryLoadings((prev) => ({
        ...prev,
        [currentConversationId]: true,
      }));
      resetAiSummaryText();
      await aiSummary();
      setTimeout(() => {
        setAiSummaryLoadings((prev) => ({
          ...prev,
          [currentConversationId]: false,
        }));
        scrollToMessageListBottom();
      }, 2000);
    } catch (error) {
      console.error(error);
    }
  }, [resetAiSummaryText, aiSummary, currentConversationId]);

  const aiSummaryTimeoutId = useRef<any>(null);

  useEffect(() => {
    const handleReceiveSummaryContent = (event: CustomEvent<{ data: IWebsocketResponse<any> }>) => {
      const { data } = event.detail;
      if (data.type !== 'openai.conversation.summary') return;

      const { content: letter, conversationId: convId } = data;
      setAiSummaryLoadings((prev) => ({
        ...prev,
        [convId]: true,
      }));
      scrollToMessageListBottom();

      if (!letter) {
        // console.log('Do not have letter');
        return;
      }
      const concatText = (prev: string, current: string) => {
        return prev + current;
      };
      setAiSummaryTexts((prev) => ({
        ...prev,
        [convId]: concatText(prev[convId] || '', letter),
      }));
      if (aiSummaryTimeoutId.current) {
        clearTimeout(aiSummaryTimeoutId.current);
      }
      aiSummaryTimeoutId.current = setTimeout(() => {
        setAiSummaryLoadings((prev) => ({
          ...prev,
          [convId]: false,
        }));
        scrollToMessageListBottom();
      }, 1000);
    };

    const unregisterEvent = createCustomEventListener(
      GLOBAL_REALTIME_EVENT_NAME,
      handleReceiveSummaryContent
    );

    // const unregisterEvent = createCustomEventListener(
    //     'deca-livechat-ai-summary-content-update',
    //     handleReceiveSummaryContent,
    // );
    return () => unregisterEvent();
  }, [currentConversationId]);

  useEffect(() => {
    setAiSummaryTexts(LocalStorageUtils.get(AI_SUMMARY_KEY) ?? {});
  }, []);

  useEffect(() => {
    const currentAiSummaryValues = LocalStorageUtils.get(AI_SUMMARY_KEY) || {};
    LocalStorageUtils.set(AI_SUMMARY_KEY, {
      ...currentAiSummaryValues,
      ...aiSummaryTexts,
    });
  }, [aiSummaryTexts]);

  const aiSummaryText = aiSummaryTexts[currentConversationId];

  return {
    aiSummary: aiSummaryWrapper,
    aiSummaryText,
    resetAiSummaryText: resetAiSummaryText,
    aiSummaryLoadings: aiSummaryLoadings,
  };
};

export type AiSummaryContextType = ReturnType<typeof useAiSummary>;

const context = createContext<AiSummaryContextType | null>(null);

export const AiSummaryContextProvider: React.FC<any> = ({ children }) => {
  const value = useAiSummary();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useAiSummaryContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAiSummaryContext must be used inside AiSummaryContextProvider');
  }

  return value;
};
