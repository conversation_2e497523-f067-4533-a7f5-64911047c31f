import { IMessage, MetaData, Sender } from '@resola-ai/models';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import dayjs from 'dayjs';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { GLOBAL_REALTIME_EVENT_NAME, USER_READ_MESSAGE_IN_CONVERSATION } from '../constants';
import useAttachmentUpload from '../hooks/useAttachmentUpload';
import useMessageTimelinePaging from '../hooks/useMessageTimelinePaging';
import { IResource } from '../models/resource';
import ApiService from '../services/api';
import { delay } from '../utils/common';
import { useConversationActionContext } from './conversationActionContext';
import { useDraftContent } from './TextEditor/Editor/hooks/useDraftContent';

const useStoreEndUserReadAt = () => {
  const funcs = useDraftContent('end_user_last_read_at');
  return useMemo(
    () => ({
      getEndUserLastReadAt: funcs.getDraftContent,
      updateEndUserLastReadAt: funcs.updateDraftContent,
      clearEndUserLastReadAt: funcs.clearDraftContent,
    }),
    [funcs]
  );
};

const isFromHumanMessage = (message: IMessage) => {
  return ['enduser', 'operator'].includes(message?.sender?.type);
};

const useMessage = () => {
  const {
    endUserReadAt,
    currentConversation,
    currentConversationId,
    handleAddMessageToConversationsContext,
    updateLastMessageAccumulateByTime,
  } = useConversationActionContext();

  const {
    loading,
    messages,
    noMessage,
    isReachedEnd,
    addMessagesToRepo,
    initialSwitchConver,
    lastTimeStampCursor,
    prefetchMessagesAtCursor,
    temporaryOffLoadingIndicator,
    handleLoadConversationAtCursor,
  } = useMessageTimelinePaging(currentConversationId);

  const {
    resources,
    removeResources,
    addAttachedFiles,
    uploadResourceLoading,
    resetResourcesHandler,
    setUploadResourceLoading,
  } = useAttachmentUpload(currentConversationId);

  const [sender, setSender] = useState<Sender>({ name: '', iconUrl: '', type: '' });

  const [lastUserReadAt, setLastUserReadAt] = useState<string>(undefined);
  const { getEndUserLastReadAt, updateEndUserLastReadAt } = useStoreEndUserReadAt();

  // realtime handler
  const handleReceiveMessageEvent = useCallback(
    async (message: IMessage) => {
      if (currentConversationId === message.conversationId) {
        addMessagesToRepo([message]);
        isFromHumanMessage(message) &&
          updateLastMessageAccumulateByTime({
            id: message.id,
            created: message.created,
            data: message.data,
            sender: {
              id: message?.sender?.id,
              type: message?.sender?.type,
            },
          });
      }
    },
    [addMessagesToRepo, currentConversationId, updateLastMessageAccumulateByTime]
  );

  // send message functions
  const sendTextMessage = useCallback(
    async (conversationId: string, message: string, type: string = 'text', metadata: MetaData) => {
      if (message) {
        try {
          const response = await ApiService.sendConversationMessage(
            conversationId,
            message,
            type,
            metadata
          );

          // append message to the list of messages
          handleReceiveMessageEvent(response[0]);
          handleAddMessageToConversationsContext(response[0]);

          return true;
        } catch (error) {
          // send a event
          sendCustomEvent('deca-livechat-editor-message-update', { message: message });
          return false;
        }
      }
    },
    [handleReceiveMessageEvent, handleAddMessageToConversationsContext]
  );

  const sendMediaAsMessage = useCallback(
    async (conversationId: string, resource: IResource, metadata: MetaData) => {
      if (resource && resource.url) {
        // Determine message type based on resource type
        let messageType = 'image'; // default

        if (resource.type === 'document') {
          messageType = 'document';
        } else if (resource.type === 'video') {
          messageType = 'video';
        } else if (resource.type === 'image') {
          messageType = 'image';
        }

        // Include filename in metadata for documents
        const messageMetadata =
          resource.type === 'document' ? { ...metadata, filename: resource.name } : metadata;

        // Send media with appropriate type
        await ApiService.sendConversationMedia(
          conversationId,
          resource.url,
          resource.id,
          messageType,
          messageMetadata
        );
        return true;
      }
      return false;
    },
    []
  );

  const sendTextMessageAndImages = useCallback(
    async (
      conversationId: string,
      message: string,
      type: string,
      resources: IResource[] = [],
      metadata: MetaData
    ) => {
      setUploadResourceLoading(true);
      try {
        await sendTextMessage(conversationId, message, type, metadata);
        await delay(500);
        const promises = resources.map((resource, index) => {
          return delay(index * 700).then(() =>
            sendMediaAsMessage(conversationId, resource, metadata)
          );
        });
        await Promise.all(promises);
        resetResourcesHandler();
        return true;
      } catch (error) {
        return false;
      } finally {
        setUploadResourceLoading(false);
      }
    },
    [resetResourcesHandler, sendMediaAsMessage, sendTextMessage, setUploadResourceLoading]
  );

  const sendMessage = useCallback(
    async (conversationId: string, message: string, metadata: MetaData, type: string = 'text') => {
      if (uploadResourceLoading) {
        // do not send message if the upload resource is loading
        return false;
      }
      if (resources.length > 0) {
        await sendTextMessageAndImages(
          conversationId,
          message,
          type,
          resources as unknown as IResource[],
          metadata
        );
      } else {
        await sendTextMessage(conversationId, message, type, metadata);
      }
    },
    [resources, uploadResourceLoading, sendTextMessage, sendTextMessageAndImages]
  );

  useEffect(() => {
    const controller = new AbortController();
    const handlePrefetchFirstPartMessages = (e: CustomEvent<{ conversationId: string }>) => {
      const { conversationId } = e.detail;
      if (!conversationId) return;
      prefetchMessagesAtCursor({ conversationId });
    };

    createCustomEventListener(
      'prefetch-first-message-list-to-message-context',
      handlePrefetchFirstPartMessages,
      controller.signal
    );

    return () => {
      controller.abort();
    };
  }, [prefetchMessagesAtCursor]);

  useEffect(() => {
    const handleReceiveGlobalEvent = (event: CustomEvent) => {
      const { data } = event.detail;
      if (data.type === 'message.new') {
        const message = data.data as IMessage;
        handleReceiveMessageEvent(message);
        handleAddMessageToConversationsContext(message);
      }

      if (data.type === USER_READ_MESSAGE_IN_CONVERSATION) {
        if (!data?.conversationId) return; // Unknown data, cannot handle
        // Update to session storage, update for any conversation that belongs to operator
        updateEndUserLastReadAt(data?.conversationId, data?.readAt || '');
        // Only update state when event's conversationId matched with current conversation id
        // This helps trigger the message list rerendering only.
        if (data?.conversationId === currentConversationId) {
          setLastUserReadAt(data?.readAt || undefined);
        }
      }
    };

    const cleanUp = createCustomEventListener(GLOBAL_REALTIME_EVENT_NAME, handleReceiveGlobalEvent);

    return () => {
      cleanUp();
    };
  }, [
    handleReceiveMessageEvent,
    currentConversationId,
    updateEndUserLastReadAt,
    handleAddMessageToConversationsContext,
  ]);

  // When navigate between conversations,
  // need to recheck the last user read at
  useEffect(() => {
    if (!currentConversation) return;
    const storedLastReadAt = getEndUserLastReadAt(currentConversation?.id || '') || undefined;

    if (!endUserReadAt || !storedLastReadAt) {
      setLastUserReadAt(endUserReadAt || storedLastReadAt || undefined);
      return;
    }

    setLastUserReadAt(
      dayjs(endUserReadAt).isAfter(dayjs(storedLastReadAt)) ? endUserReadAt : storedLastReadAt
    );
  }, [currentConversation, endUserReadAt, getEndUserLastReadAt]);

  return useMemo(
    () => ({
      sender,
      loading,
      messages,
      resources,
      isReachedEnd,
      lastUserReadAt,
      isEmpty: noMessage,
      initialSwitchConver,
      lastTimeStampCursor,
      uploadResourceLoading,
      temporaryOffLoadingIndicator,
      setSender,
      sendMessage,
      handleReceiveMessageEvent,
      handleLoadConversationAtCursor,
      addAttachmentFiles: addAttachedFiles,
      removeAttachmentFiles: removeResources,
    }),
    [
      sender,
      loading,
      messages,
      resources,
      noMessage,
      isReachedEnd,
      lastUserReadAt,
      initialSwitchConver,
      lastTimeStampCursor,
      uploadResourceLoading,
      temporaryOffLoadingIndicator,
      sendMessage,
      removeResources,
      addAttachedFiles,
      handleReceiveMessageEvent,
      handleLoadConversationAtCursor,
    ]
  );
};

export type MessageContextType = ReturnType<typeof useMessage>;

const context = createContext<MessageContextType | null>(null);

export const MessageContextProvider: React.FC<any> = ({ children }) => {
  const value = useMessage();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useMessageContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useMessageContext must be used inside MessageContextProvider');
  }

  return value;
};

export { isFromHumanMessage };
