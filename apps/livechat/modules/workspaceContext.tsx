import { ConversationListFilterType } from '@resola-ai/models';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { isEmptyObject } from '@tiptap/react';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { GLOBAL_REALTIME_EVENT_NAME } from '../constants';
import {
  DEFAULT_CONVERSATION_ASSIGNED,
  DEFAULT_CONVERSATION_STATUS,
} from '../constants/conversation';
import {
  MENU_BOOKMARK,
  MENU_MY_ASSIGNED,
  MENU_SUBTYPE_TEAM_ALL,
  MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR,
  MENU_UNASSIGNED,
} from '../constants/menu';
import { fallbackLng } from '../i18n/settings';
import { MenuIdType } from '../models/menu';
import { IWebsocketResponse, RealtimeEventType } from '../models/websocket';
import {
  IWorkspaceTeam,
  IWorkspaceUnAssignTeamConversation,
  IWorkspaces,
} from '../models/workspace';
import ApiService from '../services/api';
import { createTeamMenuAllId, createTeamMenuId, createTeamMenuUnassignedId } from '../utils/menu';
import { WorkspaceType } from './Menu/constants';
import { initWorkspaceResp } from './Menu/data';
import { IWorkspaceMenu } from './Menu/models';

const useWorkspace = () => {
  const [lang, setLang] = useState<string>(fallbackLng);
  const { t } = useTranslate('workspace');

  const [workSpaceMenu, setWorkSpaceMenu] = useState<IWorkspaceMenu[]>([]);
  const [activeMenuId, setActiveMenuId] = useState<MenuIdType>(MENU_MY_ASSIGNED);
  const [collapsedItems, setCollapsedItems] = useState([]);
  const router = useRouter();

  const [workspaces, setWorkspaces] = useState<IWorkspaces>(initWorkspaceResp);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const workspaceMenuRef = useRef(workSpaceMenu);

  const sendLoadConversationsEventIfDetectChange = useCallback(
    (dataIncoming: IWorkspaceUnAssignTeamConversation | IWorkspaceTeam, type: string) => {
      let shouldSentEvent = false;
      if (type === MENU_UNASSIGNED) {
        const workspaceMenu = [...workspaceMenuRef.current];
        const unAssignedTeamMenu = workspaceMenu.find(
          (item) => item.type === WorkspaceType.UN_ASSIGNED_TEAM
        );
        if (
          unAssignedTeamMenu?.subMenu[0].conversationNumbers !==
          (dataIncoming as IWorkspaceUnAssignTeamConversation)?.unAssignedTeamConversationNo
        ) {
          shouldSentEvent = true;
        }
      }

      if (type === MENU_SUBTYPE_TEAM_ALL || type === MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR) {
        const { teams } = dataIncoming as IWorkspaceTeam;
        const workspaceMenu = [...workspaceMenuRef.current];
        if (teams.length > 0) {
          teams.forEach((team) => {
            const teamMenu = workspaceMenu.find((item) => item.id === createTeamMenuId(team.id));
            if (
              teamMenu?.subMenu?.[0].conversationNumbers !== team.allConversationNo ||
              teamMenu?.subMenu?.[1].conversationNumbers !== team.unAssignedOperatorConversationNo
            ) {
              if (!shouldSentEvent) shouldSentEvent = true;
            }
          });
        }
      }

      if (shouldSentEvent) {
        sendCustomEvent('deca-livechat-refetch-conversations-for-new-list-change', {
          fetchNewList: true,
        });
      }
    },
    []
  );

  const mappingWorkspaceUnAssignTeamConversationToWorkspacesMenu = useCallback(
    (data: IWorkspaceUnAssignTeamConversation) => {
      const { unAssignedTeamConversationNo } = data;
      sendLoadConversationsEventIfDetectChange(data, MENU_UNASSIGNED);
      setWorkSpaceMenu((prevMenu) => {
        return prevMenu.map((item) => {
          if (item.type === WorkspaceType.UN_ASSIGNED_TEAM) {
            return {
              ...item,
              subMenu: item.subMenu.map((subItem) => {
                if (subItem.type === MENU_UNASSIGNED) {
                  return {
                    ...subItem,
                    conversationNumbers: unAssignedTeamConversationNo,
                  };
                }
                return subItem;
              }),
            };
          }
          return item;
        });
      });
    },
    [sendLoadConversationsEventIfDetectChange]
  );

  const mappingWorkspaceTeamToWorkspacesMenu = useCallback(
    (data: IWorkspaceTeam) => {
      const { teams } = data;
      sendLoadConversationsEventIfDetectChange(data, MENU_SUBTYPE_TEAM_ALL);
      setWorkSpaceMenu((prevMenu) => {
        return prevMenu.map((item) => {
          if (item.type === WorkspaceType.LIST_OF_TEAMS) {
            const updatedSubMenu = item.subMenu.map((subItem) => {
              const teamAll = teams.find((team) => createTeamMenuAllId(team.id) === subItem.id);
              const teamUnassigned = teams.find(
                (team) => createTeamMenuUnassignedId(team.id) === subItem.id
              );
              if (teamAll) {
                return {
                  ...subItem,
                  conversationNumbers: teamAll.allConversationNo,
                };
              }
              if (teamUnassigned) {
                return {
                  ...subItem,
                  conversationNumbers: teamUnassigned.unAssignedOperatorConversationNo,
                };
              }
              return subItem;
            });
            return {
              ...item,
              subMenu: updatedSubMenu,
            };
          }
          return item;
        });
      });
    },
    [sendLoadConversationsEventIfDetectChange]
  );

  const mappingDataToWorkspacesMenu = useCallback(
    (data: IWorkspaces) => {
      let initWorkspaceMenu = [
        {
          id: 'my-assigned',
          title: `${t('inbox.myassigned.title')}`,
          type: WorkspaceType.MY_ASSIGNED,
          subMenu: [
            {
              id: MENU_MY_ASSIGNED,
              label: `${t('inbox.myassigned.assigned.title')}`,
              teamId: '',
              type: MENU_MY_ASSIGNED,
              conversationNumbers: 0,
            },
            {
              id: MENU_BOOKMARK,
              label: `${t('inbox.myassigned.bookmark.title')}`,
              teamId: '',
              type: MENU_BOOKMARK,
              conversationNumbers: 0,
            },
          ],
        },
        {
          id: 'unassigned',
          title: `${t('inbox.team.title')}`,
          type: WorkspaceType.UN_ASSIGNED_TEAM,
          subMenu: [
            {
              id: MENU_UNASSIGNED,
              label: `${t('inbox.team.unassigned.title')}`,
              teamId: '',
              type: MENU_UNASSIGNED,
              conversationNumbers: 0,
            },
          ],
        },
      ];

      initWorkspaceMenu.forEach((item) => {
        if (item.type === WorkspaceType.MY_ASSIGNED) {
          item.subMenu[0].conversationNumbers = data.myAssignedConversationNo;
        }
        if (item.type === WorkspaceType.UN_ASSIGNED_TEAM) {
          item.subMenu[0].conversationNumbers = data.unAssignedTeamConversationNo;
        }
      });

      if (data.teams.length > 0) {
        data.teams.forEach((team) => {
          const menu = {
            id: createTeamMenuId(team.id),
            title: team.name,
            type: WorkspaceType.LIST_OF_TEAMS,
            subMenu: [
              {
                id: createTeamMenuAllId(team.id),
                label: `${t('inbox.team.all.title')}`,
                teamId: team.id,
                type: MENU_SUBTYPE_TEAM_ALL,
                conversationNumbers: team.allConversationNo,
              },
              {
                id: createTeamMenuUnassignedId(team.id),
                label: `${t('inbox.team.unassigned.title')}`,
                teamId: team.id,
                type: MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR,
                conversationNumbers: team.unAssignedOperatorConversationNo,
              },
            ],
          };

          initWorkspaceMenu.push(menu);
        });
      }

      setWorkSpaceMenu(initWorkspaceMenu);
    },
    [t]
  );

  const getMyWorkspaces = useCallback(async () => {
    setIsLoading(true);
    const data = await ApiService.getOperatorWorkspace();
    mappingDataToWorkspacesMenu(data);
    setWorkspaces(data);
    setIsLoading(false);
  }, [mappingDataToWorkspacesMenu]);

  const toggleCollapse = useCallback((index) => {
    setCollapsedItems((prevCollapsedItems) => {
      const updatedCollapsedItems = [...prevCollapsedItems];
      updatedCollapsedItems[index] = !updatedCollapsedItems[index];
      return updatedCollapsedItems;
    });
  }, []);

  const expandAllItemsInMenu = useCallback(() => {
    setCollapsedItems((prevCollapsedItems) => {
      const updatedCollapsedItems = [...prevCollapsedItems];
      const totalItems = 10;
      for (let i = 0; i < totalItems; i++) {
        updatedCollapsedItems[i] = true;
      }
      return updatedCollapsedItems;
    });
  }, []);

  const setUpWorkSpaceMenu = useCallback(
    (inputStatus?: string, inputAssigned?: string, inputTeam?: string, bookmark?: string) => {
      let status = inputStatus ?? DEFAULT_CONVERSATION_STATUS;
      let assigned = inputAssigned ?? DEFAULT_CONVERSATION_ASSIGNED;
      let team = inputTeam as string;
      if (team === 'undefined') {
        team = undefined;
      }

      if (typeof assigned === 'boolean') {
        assigned = assigned as boolean;
      } else if (typeof assigned === 'string') {
        assigned = assigned === 'true';
      }

      if (status !== undefined) {
        status = status as ConversationListFilterType;
      }

      if (!team && assigned === true) {
        setActiveMenuId(MENU_MY_ASSIGNED);
      }
      if (!team && assigned === false) {
        setActiveMenuId(MENU_UNASSIGNED);
      }
      if ((team && inputAssigned === undefined) || inputAssigned === 'undefined') {
        assigned = undefined;
        setActiveMenuId(createTeamMenuAllId(team) as any);
        expandAllItemsInMenu();
      }
      if (team && assigned === false) {
        assigned = false;
        setActiveMenuId(createTeamMenuUnassignedId(team) as any);
        expandAllItemsInMenu();
      }
      if (bookmark) {
        setActiveMenuId(MENU_BOOKMARK);
      }
    },
    [expandAllItemsInMenu, setActiveMenuId]
  );
  useEffect(() => {
    workspaceMenuRef.current = workSpaceMenu;
  }, [workSpaceMenu]);

  useEffect(() => {
    if (!isEmptyObject(router.query)) {
      const { status, assigned, team, bookmark } = router.query;
      setUpWorkSpaceMenu(status as string, assigned as string, team as string, bookmark as string);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const handleUpdateParams = (e) => {
      setUpWorkSpaceMenu(e.detail.status, e.detail.assigned, e.detail.team, e.detail.bookmark);
    };

    const unregisterEvent1 = createCustomEventListener(
      'deca-livechat-init-context-send-event-setup-workspace',
      handleUpdateParams
    );
    const unregisterEvent = createCustomEventListener(
      'deca-livechat-conversation-list-param-update-workspace-menu',
      handleUpdateParams
    );
    return () => {
      unregisterEvent1();
      unregisterEvent();
    };
  }, [setUpWorkSpaceMenu]);

  useEffect(() => {
    const handleUpdateWorkspaceFromRealtime = (
      event: CustomEvent<{ data: IWebsocketResponse<any> }>
    ) => {
      const { data } = event.detail;
      const eventTypesAccepted: RealtimeEventType[] = [
        'user.workspace.unassigned_team.updated',
        'user.workspace.teams.updated',
        'user.conversation.state.updated',
        'user.workspace.updated',
        'conversation.team.assigned',
        'team.member.added',
      ];

      if (!eventTypesAccepted.includes(data.type)) return;

      if (data.type === 'user.workspace.unassigned_team.updated') {
        const unassignedTeam = data.data;
        mappingWorkspaceUnAssignTeamConversationToWorkspacesMenu(unassignedTeam);
      }

      if (data.type === 'user.workspace.teams.updated') {
        const workspaceTeam = (data as unknown as IWebsocketResponse<IWorkspaceTeam>).data;
        mappingWorkspaceTeamToWorkspacesMenu(workspaceTeam);
      }

      if (
        [
          'user.conversation.state.updated',
          'conversation.team.assigned',
          'team.member.added',
        ].includes(data.type)
      ) {
        getMyWorkspaces();
      }

      if (data.type === 'user.workspace.updated') {
        const workspaces = (data as IWebsocketResponse<IWorkspaces>).data;
        mappingDataToWorkspacesMenu(workspaces);
      }
    };

    const cleanup = createCustomEventListener(
      GLOBAL_REALTIME_EVENT_NAME,
      handleUpdateWorkspaceFromRealtime
    );
    return () => {
      cleanup();
    };
  }, [
    getMyWorkspaces,
    mappingDataToWorkspacesMenu,
    mappingWorkspaceTeamToWorkspacesMenu,
    mappingWorkspaceUnAssignTeamConversationToWorkspacesMenu,
  ]);

  return useMemo(
    () => ({
      lang,
      isLoading,
      workspaces,
      t,
      activeMenuId,
      workSpaceMenu,
      collapsedItems,
      originLang: lang,
      setWorkspaces,
      toggleCollapse,
      getMyWorkspaces,
      setActiveMenuId,
      setWorkSpaceMenu,
      setCollapsedItems,
      expandAllItemsInMenu,
      updateLang: (lang: string) => setLang(lang),
      handleWorkspaceChangeEvent: mappingDataToWorkspacesMenu,
      handleWorkspaceUnassignConversationUpdateEvent:
        mappingWorkspaceUnAssignTeamConversationToWorkspacesMenu,
      handleWorkspaceTeamUpdateEvent: mappingWorkspaceTeamToWorkspacesMenu,
    }),
    [
      lang,
      isLoading,
      workspaces,
      activeMenuId,
      workSpaceMenu,
      collapsedItems,
      t,
      toggleCollapse,
      getMyWorkspaces,
      expandAllItemsInMenu,
      mappingDataToWorkspacesMenu,
      mappingWorkspaceTeamToWorkspacesMenu,
      mappingWorkspaceUnAssignTeamConversationToWorkspacesMenu,
    ]
  );
};

export type WorkspaceContextType = ReturnType<typeof useWorkspace>;

const context = createContext<WorkspaceContextType | null>(null);

export const WorkspaceContextProvider: React.FC<any> = ({ children }) => {
  const value = useWorkspace();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWorkspaceContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWorkspaceContext must be used inside WorkspaceContextProvider');
  }

  return value;
};
