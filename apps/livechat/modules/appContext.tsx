import { useAuth0 } from '@auth0/auth0-react';
import { useMediaQuery } from '@mantine/hooks';
import { useRouter } from 'next/router';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createCustomEventListener, LocalStorageUtils, windowUtil } from '@resola-ai/utils';
import { LANGUAGE_PREFERENCE, SUPPORTED_LANGUAGES } from '../constants';
import { LAST_SENDER_TYPE } from '../constants/menu';
import { fallbackLng } from '../i18n/settings';
import axiosService from '../services/axios';
import { setDayjsLocale } from '../utils/dayjs-locales';
import { getRedirectUri } from '../utils/redirect';
import { tolgee } from '../tolgee';
import React from 'react';
import { TolgeeProvider } from '@tolgee/react';
import { datadogService } from '@resola-ai/services-shared';

const SETTING_PAGES = [
  '/settings/team-user-list',
  '/settings/operations',
  '/settings/autoreply',
  '/settings/integrations',
  '/settings/notifications',
  '/settings/functions',
];

const useApp = () => {
  const { user, isLoading, isAuthenticated, logout: _logout, getAccessTokenSilently } = useAuth0();
  // manage current language here
  const { changeLanguage } = tolgee;
  const [lang, setLang] = useState<string>(fallbackLng);
  const [accessToken, setAccessToken] = useState<string>('');

  const router = useRouter();

  const tabletScreen = useMediaQuery('(max-width: 1024px)');
  const smallScreen = useMediaQuery('(max-width: 1368px)');
  const macBookScreen = useMediaQuery('(max-width: 1440px)');
  const belowMediumScreen = useMediaQuery('(max-width: 1600px)');
  const mediumScreen = useMediaQuery('(max-width: 1920px)');
  const responsiveScreen = useMediaQuery('(max-width: 1133px)');

  const isInboxPage = useMemo(() => router.pathname === '/', [router.pathname]);
  const isAutomationPage = useMemo(() => router.asPath.includes('/automation'), [router.asPath]);
  const isWidgetPage = useMemo(() => router.asPath.includes('/widget'), [router.asPath]);
  const isOperationSettingPage = useMemo(
    () =>
      SETTING_PAGES.includes(router.asPath) ||
      SETTING_PAGES.some((item) => router.asPath.includes(item)),
    [router.asPath]
  );

  const endUserEditedRef = useRef<boolean>(false);

  console.log('REDIRECT URI', getRedirectUri());

  const logout = useCallback(() => {
    _logout({ logoutParams: { returnTo: getRedirectUri() } });
  }, [_logout]);

  useEffect(() => {
    windowUtil.set('changeLanguage', changeLanguage);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const handleFormChange = (event: CustomEvent) => {
      endUserEditedRef.current = event.detail?.dirty;
    };
    const unregisterEvent = createCustomEventListener(
      'form-editing-notify-change-to-app-context',
      handleFormChange
    );
    return () => unregisterEvent();
  }, []);

  useEffect(() => {
    axiosService.setAccessToken(accessToken);
  }, [accessToken]);

  useEffect(() => {
    // sometimes , query.lang is not a string, may be array
    let langRaw = router.query?.lang;
    if (Array.isArray(langRaw)) {
      langRaw = langRaw.find((item) => SUPPORTED_LANGUAGES.includes(item?.toLowerCase())) || '';
    } else if (typeof langRaw === 'string') {
      langRaw = langRaw.toLowerCase();
    }

    let lang = SUPPORTED_LANGUAGES.includes(langRaw) ? langRaw : '';
    const langPreference = LocalStorageUtils.get(LANGUAGE_PREFERENCE);

    if (!lang && SUPPORTED_LANGUAGES.includes(langPreference)) {
      lang = langPreference;
    }

    if (lang && (lang !== tolgee.getLanguage() || lang !== langPreference)) {
      setLang(lang);
      changeLanguage(lang);
      LocalStorageUtils.set(LANGUAGE_PREFERENCE, lang);
    }
  }, [changeLanguage, router?.query?.lang]);

  useEffect(() => {
    setDayjsLocale(lang);
  }, [lang]);

  useEffect(() => {
    if (isAuthenticated) {
      getAccessTokenSilently()
        .then((token) => {
          setAccessToken(token);
        })
        .catch(() => {
          // TODO: will be improved later, this is just for debug in the development stage, show noti when have error and show detail in console log
          // console.log('[Get Access Token Error]', err);
          // NotificationService.sendNotification(
          //     'Error',
          //     'Failed to get access token',
          //     'error',
          // );
        });
    }
  }, [isAuthenticated, getAccessTokenSilently]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      LocalStorageUtils.remove('aiSummary');
      LocalStorageUtils.remove(LAST_SENDER_TYPE);
    }
  }, [isLoading, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated && user) {
      try {
        datadogService.setUser({
          id: user[`${user.namespace}/metadata`].user_id,
          email: user.email,
          name: user.nickname ?? user.name ?? '',
        });
        datadogService.setOrganization(user[`${user.namespace}/organization`] ?? {});
      } catch (error) {
        console.error(`Failed to set user in datadog: `, user, error);
      }
    }
  }, [user, isAuthenticated]);

  return {
    lang,
    updateLang: (lang: string) => setLang(lang),
    responsive: {
      smallScreen,
      mediumScreen,
      tabletScreen,
      macBookScreen,
      responsiveScreen,
      belowMediumScreen,
    },
    accessToken,
    responsiveScreen,
    isAuthenticated: isAuthenticated && accessToken.length > 0,
    logout,
    user: {
      ...user,
      organizationName: user?.org_name,
    },
    isInboxPage,
    isWidgetPage,
    isAutomationPage,
    endUserEditedRef,
    isOperationSettingPage,
  };
};

export type AppContextType = ReturnType<typeof useApp>;

const context = createContext<AppContextType | null>(null);

export const AppContextProvider: React.FC<any> = ({ children }) => {
  const value = useApp();

  return (
    <TolgeeProvider tolgee={tolgee} fallback='Loading...'>
      <context.Provider value={value}>{children}</context.Provider>
    </TolgeeProvider>
  );
};

export const useAppContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAppContext must be used inside AppContextProvider');
  }

  return value;
};
