import { useSetState } from '@mantine/hooks';
import { ConversationListFilterType, IConversation, IMessage } from '@resola-ai/models';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { useRouter } from 'next/router';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import useSWR, { preload } from 'swr';
import {
  DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR,
  GLOBAL_REALTIME_EVENT_NAME,
} from '../constants';
import useConversationPaging from '../hooks/useConversationPaging';
import useVisibilityControl from '../hooks/useVisibilityControl';
import { ConversationListParams } from '../models/conversationParams';
import { MenuIdType } from '../models/menu';
import { IWebsocketResponse, RealtimeEventType } from '../models/websocket';
import {
  IWorkspacesConversationReadMessage,
  IWorkspacesConversationStatistic,
} from '../models/workspace';
import ApiService from '../services/api';
import logger from '../services/logger';
import { getDefaultAssigneeId } from '../utils/assignee';
import { deepCopy } from '../utils/common';
import { createLastMessageFromMessage } from '../utils/message';
import { addParamsToCurrentUrl } from '../utils/queryParam';
import { useTeamContext } from './teamContext';
import { useUserContext } from './userContext';

const useConversation = () => {
  const router = useRouter();
  const initRef = useRef<boolean>(false);
  const newConversationRef = useRef<any>(null);
  const { userProfile, userId } = useUserContext();
  const { teamList } = useTeamContext();
  const [conversationId, setConversationId] = useState<string>('');
  const { visible: visibleConfirmModal, open, close: closeConfirmModal } = useVisibilityControl();
  const [currentConversationHolder, setCurrentConversationHolder] = useState<IConversation>(null);

  const {
    next,
    conversations,
    conversationsParams,
    isEmpty: isListEmpty,
    loading: isLoadingList,
    isReachingEnd: isReachingEndForListConversation,
    showLoadingMore,
    setNext,
    refetchConversations,
    refetchConversListByNewParams,
    updateStatusConversationInAll,
    updateOrCreateConvInAllConvers,
    updateExistingConversationInAllConversationsHandler,
  } = useConversationPaging();

  const [conversationStatistic, setConversationStatistic] = useSetState({
    totalUnread: 0,
    hasNewUnread: false,
    hasInProgressUnread: false,
    hasCompletedUnread: false,
  });

  const lastReadTimeConversationsRef = useRef<IWorkspacesConversationReadMessage>({});

  const {
    data: current,
    isLoading: isLoadingCurrent,
    mutate: reloadCurrConver,
  } = useSWR(conversationId, ApiService.getConversationById, { revalidateIfStale: true });

  const refetchCurrentConversation = useCallback(
    (configData?: Partial<IConversation>, opts?: boolean) => {
      if (configData) {
        reloadCurrConver({ ...current, ...configData }, opts);
        return;
      }
      reloadCurrConver();
    },
    [current, reloadCurrConver]
  );

  const prefetchConversation = useCallback((conversationId: string) => {
    preload(conversationId, () => ApiService.getConversationById(conversationId));
  }, []);

  // This is a way we can have a copy of loaded conversation,
  // AIM TO :
  // easy to change.
  // easy to update.
  // Free to modify.
  useEffect(() => {
    if (current) {
      try {
        const copied = deepCopy(current);
        setCurrentConversationHolder(copied);
      } catch (e) {}
    }
    sendCustomEvent('deca-livechat-conversation-context-has-loaded-conversation', {
      conversation: current,
    });
    return () => {
      setCurrentConversationHolder(null);
      newConversationRef.current = null;
    };
  }, [current]);

  const handleConversationUnreadStatusChangeEvent = useCallback(
    (eventData: IWorkspacesConversationStatistic) => {
      setConversationStatistic({
        totalUnread: eventData?.quantity?.length || 0,
        hasNewUnread: eventData?.new?.length > 0,
        hasInProgressUnread: eventData?.inProgress?.length > 0,
        hasCompletedUnread: eventData?.completed?.length > 0,
      });
    },
    [setConversationStatistic]
  );

  const getUnreadConversationStatus = useCallback(async () => {
    try {
      const data = await ApiService.getUnreadConversationStatus();
      const unreadData = data.unreadConversations;
      const readLastTime = data.readLastMessageAt;
      lastReadTimeConversationsRef.current = readLastTime;
      handleConversationUnreadStatusChangeEvent(unreadData);
    } catch (error) {
      logger.error(
        'Error when get unread conversation status',
        error?.response?.data?.message || ''
      );
    }
  }, [handleConversationUnreadStatusChangeEvent]);

  const assignConversationToOperator = useCallback(
    async (conversationId: string, operatorId: string, assignToMe: boolean = false) => {
      try {
        const result = await ApiService.assignOperatorToConversation({
          operatorId: operatorId,
          conversationId: conversationId,
          teamId: currentConversationHolder?.team?.id,
          assignToMe: assignToMe,
        });

        if ((result as { message: string })?.message) {
          sendCustomEvent(DECA_LIVECHAT_EVENT_MODAL_SHOW_SOME_MESSAGE_FOR_ERROR, {
            message: (result as { message: string }).message,
          });
        }

        setTimeout(() => {
          reloadCurrConver();
        }, 1000);

        if (operatorId && currentConversationHolder?.team?.id) {
          refetchConversListByNewParams({
            assigned: undefined,
            team: currentConversationHolder?.team?.id,
          });

          return true;
        }
        if (operatorId === '') {
          refetchConversListByNewParams({
            assigned: false,
            team: undefined,
          });
          return true;
        }
        refetchConversListByNewParams({
          assigned: true,
        });
        return true;
      } catch (error) {
        return false;
      }
    },
    [currentConversationHolder?.team?.id, reloadCurrConver, refetchConversListByNewParams]
  );

  const assignConversationToTeam = useCallback(
    async (conversationId: string, userTeamId: string) => {
      try {
        await ApiService.assignTeamToConversation({
          teamId: userTeamId,
          conversationId: conversationId,
        });

        setTimeout(() => {
          reloadCurrConver();
        }, 1000);
        const newParams: ConversationListParams = {
          assigned: false,
          team: userTeamId,
          status: 'new',
        };
        refetchConversListByNewParams(newParams);
        sendCustomEvent('deca-livechat-conversation-list-param-update-workspace-menu', {
          assigned: false,
          team: userTeamId,
          status: 'new',
        });

        return true;
      } catch (error) {
        return false;
      }
    },
    [reloadCurrConver, refetchConversListByNewParams]
  );

  const handleNewAssignedEntity = useCallback(
    (operatorId: string, teamId: string = undefined, ctx: 'from-no-assignee' | 'else' = 'else') => {
      setTimeout(() => {
        reloadCurrConver();
      }, 800);

      if (operatorId === '') {
        const newParams: ConversationListParams = {
          assigned: false,
          team: teamId,
        };
        refetchConversListByNewParams(newParams);
        return true;
      } else if (ctx === 'from-no-assignee' && operatorId) {
        const newParams: ConversationListParams = {
          assigned: true,
          team: undefined,
          cId: currentConversationHolder?.id,
          status: 'new',
        };
        setNext(undefined);
        refetchConversListByNewParams(newParams);
        setTimeout(() => {
          sendCustomEvent('deca-livechat-select-active-menu-id-workspace-menu', {
            activeMenuId: 'myAssigned' as MenuIdType,
          });
        }, 300);
        setTimeout(() => {
          sendCustomEvent('deca-livechat-change-selected-conversation', {
            conversationId: currentConversationHolder?.id,
          });
        }, 2000);
        return true;
      } else {
        setTimeout(() => {
          refetchConversations();
        }, 1000);
      }
    },
    [
      reloadCurrConver,
      refetchConversListByNewParams,
      currentConversationHolder?.id,
      setNext,
      refetchConversations,
    ]
  );

  // due to this function now is only used in a place separated from tool widgets list
  // so any change in conversation need to notify to tool column
  const assignOperatorToConversation = useCallback(
    async (conversationId: string, operatorId: string, teamId: string = undefined) => {
      try {
        await ApiService.assignOperatorToConversation({
          operatorId,
          conversationId,
          teamId,
        });
        handleNewAssignedEntity(operatorId, teamId, 'from-no-assignee');
        sendCustomEvent('deca-livechat-reload-conversation-for-tool-column', {});
      } catch (error) {
        logger.error(
          'Error when assign operator to conversation',
          error?.response?.data?.message || ''
        );
      }
    },
    [handleNewAssignedEntity]
  );

  const receiveCloseConversation = useCallback(
    (conversationId: string) => {
      if (conversationId !== current?.id) return;
      const newParams: ConversationListParams = {
        status: 'completed',
      };
      refetchConversListByNewParams(newParams);
      setTimeout(() => {
        reloadCurrConver();
      }, 600);
    },
    [current?.id, reloadCurrConver, refetchConversListByNewParams]
  );

  const receiveWrapUpConversation = useCallback(
    async (conversationId: string) => {
      try {
        await reloadCurrConver();
        setTimeout(() => {
          sendCustomEvent('deca-livechat-change-selected-conversation', {
            conversationId: conversationId,
            status: 'inWrapUp',
          });
        }, 1500);
        setTimeout(() => {
          sendCustomEvent('deca-livechat-change-selected-conversation', {
            conversationId: conversationId,
            status: 'inWrapUp',
          });
        }, 3000);
        return true;
      } catch (e) {
        return false;
      }
    },
    [reloadCurrConver]
  );

  const changeAutoCompleteParams = useCallback(
    async (conversationId: string, enable: boolean, interval: number) => {
      try {
        await ApiService.convoSetAutoComplete({ conversationId, enable, interval });
        return true;
      } catch (error) {
        return false;
      }
    },
    []
  );

  const syncStatusParamsToCurrConverOrInProgress = useCallback(() => {
    if (!currentConversationHolder) return;

    if (conversationsParams.status !== currentConversationHolder?.status) {
      setTimeout(() => {
        const newParams: ConversationListParams = {
          status: currentConversationHolder?.status,
        };
        refetchConversListByNewParams(newParams);
      }, 800);
    }
    if (currentConversationHolder?.status === 'new') {
      setTimeout(() => {
        const newParams: ConversationListParams = {
          status: 'inProgress',
        };
        refetchConversListByNewParams(newParams);
      }, 800);
    }
  }, [conversationsParams.status, currentConversationHolder, refetchConversListByNewParams]);

  const changeConversationStatus = useCallback(
    async (conversationId: string, status: ConversationListFilterType) => {
      try {
        await ApiService.changeConversationStatus(conversationId, status);
        reloadCurrConver();
        const newParams: ConversationListParams = {
          status: status,
        };
        refetchConversListByNewParams(newParams);
        return true;
      } catch (error) {
        return false;
      }
    },
    [reloadCurrConver, refetchConversListByNewParams]
  );

  const receivedBookmarkChange = useCallback(
    (id: string, action: 'add' | 'remove') => {
      try {
        let _params = { ...conversationsParams };
        // shouldReloadConvo = true means we're viewing the bookmark screen,
        // but this conversation being viewed was unbookmarked.
        // so we need to reload the list, and remove the selection of the current conversation.
        const shouldReloadConvo = action === 'remove' && !!_params.bookmark;
        if (shouldReloadConvo) {
          _params = { ..._params, cId: undefined };
        }

        // Update current data displaying
        const conversationDetail = conversations?.find((c) => c.id === id);
        !!conversationDetail &&
          updateExistingConversationInAllConversationsHandler({
            ...deepCopy(conversationDetail),
            isBookmark: action === 'add',
          });

        // Request to get list data base on shouldReloadConvo
        if (!shouldReloadConvo) {
          setTimeout(() => refetchConversListByNewParams({ ..._params }), 1000);
        } else {
          refetchConversListByNewParams({ ..._params });
          setNext(undefined);
          sendCustomEvent('deca-livechat-conversation-detail-reset', {});
        }

        setTimeout(() => {
          reloadCurrConver();
        }, 1000);
      } catch (error) {}
    },
    [
      setNext,
      reloadCurrConver,
      conversations,
      conversationsParams,
      refetchConversListByNewParams,
      updateExistingConversationInAllConversationsHandler,
    ]
  );

  const selectConversationHandler = useCallback(
    (conversation: IConversation) => {
      setConversationId(conversation.id);
      updateExistingConversationInAllConversationsHandler({
        ...conversation,
        unread: false,
      });
    },
    [updateExistingConversationInAllConversationsHandler]
  );

  const selectConversationIdHandler = useCallback((conversationId: string) => {
    setConversationId(conversationId);
  }, []);

  const selectConversation = useCallback(
    (conversationId: string, status?: ConversationListFilterType) => {
      setConversationId(conversationId);
      const conversationDetail = conversations.find((c) => c.id === conversationId);
      if (!conversationDetail || (!status && !conversationDetail?.unread)) return;
      let markUpdate = false;
      const updatedData: IConversation = deepCopy(conversationDetail);
      if (status) {
        markUpdate = true;
        updatedData.status = status;
      }
      if (!!updatedData?.unread) {
        markUpdate = true;
        updatedData.unread = false;
      }
      if (markUpdate) {
        updateExistingConversationInAllConversationsHandler(updatedData);
      }
    },
    [conversations, updateExistingConversationInAllConversationsHandler]
  );

  const handleUpdateNewMessageToConversationEvent = useCallback(
    (message: IMessage) => {
      const incomingConversationMessageId = message.conversationId;
      const conversation = conversations?.find((c) => c.id === incomingConversationMessageId);

      const sameConversation = currentConversationHolder?.id === incomingConversationMessageId;
      const lastMessage = createLastMessageFromMessage(message);
      if (sameConversation) {
        // Means setting for current conversation.
        // Update lastMessage , this help message list to show AISummary button correctly
        const newConversationWithLastMessageUpdated = {
          ...currentConversationHolder,
          lastMessage: lastMessage,
          unread: false,
        };
        setCurrentConversationHolder(newConversationWithLastMessageUpdated);
      }
      if (conversation) {
        const dataType = message.data.type;
        const senderType = message.sender.type;
        const isTheCurrentConversation = conversationId === conversation.id;

        if (
          senderType === 'system' &&
          dataType === 'activity' &&
          message.data.text !== 'conversation.operator.assigned'
        ) {
          return null;
        }

        let newConversation = {
          ...conversation,
          unread: !isTheCurrentConversation,
        };
        if (senderType !== 'system') {
          newConversation = {
            ...newConversation,
            lastMessage: lastMessage,
          };
          const statusConversation = conversation?.status;
          setConversationStatistic((current) => ({
            totalUnread: current.totalUnread + 1,
            hasNewUnread: !isTheCurrentConversation && statusConversation === 'new',
            hasInProgressUnread: !isTheCurrentConversation && statusConversation === 'inProgress',
            hasCompletedUnread: !isTheCurrentConversation && statusConversation === 'completed',
          }));
        }
        updateExistingConversationInAllConversationsHandler(newConversation);
      }
    },
    [
      conversations,
      conversationId,
      currentConversationHolder,
      setConversationStatistic,
      updateExistingConversationInAllConversationsHandler,
    ]
  );

  const receiveAddMessageToConversations = useCallback(
    // eslint-disable-next-line no-unused-vars
    (message: IMessage) => {
      syncStatusParamsToCurrConverOrInProgress();
      if (currentConversationHolder?.status === 'new') {
        setTimeout(
          () =>
            refetchCurrentConversation({
              ...currentConversationHolder,
              status: 'inProgress',
            }),
          500
        );
      }
    },
    [
      refetchCurrentConversation,
      currentConversationHolder,
      syncStatusParamsToCurrConverOrInProgress,
    ]
  );

  const handleSetUnreadForAConversation = useCallback(
    (incomingConversationId: string, unread = true) => {
      // Viewing same conversation. No need to handle.
      if (!conversationId || conversationId !== incomingConversationId) return;
      const conversation = conversations?.find((c) => c.id === incomingConversationId);
      if (conversation) {
        updateExistingConversationInAllConversationsHandler({
          ...conversation,
          unread: unread,
        });
      }
    },
    [conversationId, conversations, updateExistingConversationInAllConversationsHandler]
  );

  const handleConversationChangeEvent = useCallback(
    (conversation: IConversation) => {
      updateOrCreateConvInAllConvers(conversation);
      const isCurrentConversation = conversationId === conversation.id;
      if (isCurrentConversation) {
        reloadCurrConver(conversation, false);
      }
    },
    [conversationId, updateOrCreateConvInAllConvers, reloadCurrConver]
  );

  const handleUpdateStatusConverInAllConver = useCallback(
    (conversation: IConversation) => {
      updateStatusConversationInAll(conversation);
      const isCurrentConversation = conversationId === conversation.id;
      if (isCurrentConversation) {
        refetchCurrentConversation(conversation, false);
      }
    },
    [conversationId, refetchCurrentConversation, updateStatusConversationInAll]
  );

  const handleCreateConversationFromEndOne = useCallback(async () => {
    if (!current?.id) return;
    const res = await ApiService.createNewConversationFromEndedOne(current?.id);

    if (!res) return;

    if (!res?.isNewConversation) {
      open();
      newConversationRef.current = res;
    } else {
      const origin = location?.origin;
      const path = location?.pathname;
      let link = `${origin}${path}?status=${res?.status}&per_page=20&cId=${res?.id}&team=${res?.teamId}`;
      if (res?.assigneeId?.includes('UNKNOWN')) {
        link += '&assigned=false';
      }
      location.href = link;
    }
  }, [current?.id, open]);

  const confirmMoveToOtherConversation = useCallback(() => {
    if (!newConversationRef.current) return;
    const origin = location?.origin;
    const path = location?.pathname;

    let link = `${origin}${path}?status=${newConversationRef.current?.status}&per_page=20&cId=${newConversationRef.current?.id}&team=${newConversationRef.current?.teamId}`;
    if (newConversationRef?.current?.assigneeId?.includes('UNKNOWN')) {
      link += '&assigned=false';
    }
    location.href = link;
  }, []);

  useEffect(() => {
    if (initRef.current === true) return;
    if (conversations && conversations.length > 0) {
      if (router.query.cId) {
        setConversationId(router.query.cId as string);
      } else {
        setConversationId(conversations[0].id);
      }
      initRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversations]);

  useEffect(() => {
    addParamsToCurrentUrl(
      {
        ...conversationsParams,
        next: undefined,
        cId: conversationId,
      },
      router
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationsParams, conversationId]);

  useEffect(() => {
    sendCustomEvent('deca-livechat-setup-new-conversation-id-for-tool-column', {
      conversationId,
    });
    if (conversationId) {
      setTimeout(() => {
        getUnreadConversationStatus().then();
      }, 1000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId]);

  useEffect(() => {
    const controller = new AbortController();
    const handleSelectedConversationChange = (event: CustomEvent) => {
      const { conversationId, status = undefined } = event.detail;
      selectConversation(conversationId, status);
    };

    const handleReceiveNewAssignedEntity = (event: CustomEvent) => {
      const { operatorId, teamId } = event.detail;
      handleNewAssignedEntity(operatorId, teamId, 'from-no-assignee');
    };

    const handleReceiveNewSelfAssignedEntity = (
      event: CustomEvent<{ conversationId: string; operatorId: string; assignToMe: boolean }>
    ) => {
      const { conversationId, operatorId, assignToMe } = event.detail;
      assignConversationToOperator(conversationId, operatorId, !!assignToMe);
    };

    const handleChangeBookmarkFlagConversation = (
      event: CustomEvent<{ id: string; action: 'add' | 'remove' }>
    ) => {
      const { action, id } = event.detail;
      receivedBookmarkChange(id, action);
    };

    const handleCloseConversation = (event: CustomEvent<{ conversationId: string }>) => {
      const { conversationId } = event.detail;
      receiveCloseConversation(conversationId);
    };

    const handleWrapUpConversation = (event: CustomEvent<{ conversationId: string }>) => {
      const { conversationId } = event.detail;
      receiveWrapUpConversation(conversationId);
    };

    const handleReceiveAddNewMessage = (event: CustomEvent<{ message: IMessage }>) => {
      const { message } = event.detail;
      receiveAddMessageToConversations(message);
    };

    const handleReceiveChangeFilterParamCurrConver = () => {
      syncStatusParamsToCurrConverOrInProgress();
    };

    const handlePrefetchConversation = (event: CustomEvent<{ conversationId: string }>) => {
      const { conversationId } = event.detail;
      if (!!conversationId) prefetchConversation(conversationId);
    };

    createCustomEventListener(
      'deca-livechat-change-selected-conversation',
      handleSelectedConversationChange,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-update-entity-in-charge-to-conversation-from-tool-column',
      handleReceiveNewAssignedEntity,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-update-self-assign-entity-in-charge-to-conversation-from-tool-column',
      handleReceiveNewSelfAssignedEntity,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-update-bookmark-flag-to-conversation-context',
      handleChangeBookmarkFlagConversation,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-close-conversation-to-conversation-context',
      handleCloseConversation,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-wrapup-conversation-to-conversation-context',
      handleWrapUpConversation,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      handleReceiveAddNewMessage,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-update-status-conversation-to-conversation-context',
      handleReceiveChangeFilterParamCurrConver,
      controller.signal
    );

    createCustomEventListener(
      'prefetch-conversation-id-to-conversation-context',
      handlePrefetchConversation,
      controller.signal
    );

    return () => {
      controller.abort();
    };
  }, [
    selectConversation,
    prefetchConversation,
    receivedBookmarkChange,
    handleNewAssignedEntity,
    receiveCloseConversation,
    receiveWrapUpConversation,
    refetchCurrentConversation,
    assignConversationToOperator,
    receiveAddMessageToConversations,
    syncStatusParamsToCurrConverOrInProgress,
  ]);

  useEffect(() => {
    const handleReceiveRealtimeEvent = (event: CustomEvent<{ data: IWebsocketResponse<any> }>) => {
      const { data } = event.detail;
      const eventTypesAccepted: RealtimeEventType[] = [
        'message.new',
        'team.member.added',
        'conversation.team.assigned',
        'conversation.status.updated',
        'conversation.operator.assigned',
        'user.conversation.state.updated',
        'user.unread.conversation.updated',
      ];

      if (!eventTypesAccepted.includes(data.type)) return;

      if (data.type === 'message.new') {
        const message = (data as IWebsocketResponse<IMessage>).data;
        sendCustomEvent('deca-livechat-widgets-event-message-new', {
          conversationId: message.conversationId,
          isEndUser: message.sender.type === 'enduser',
          isSameConversation: currentConversationHolder?.id === message.conversationId,
          message: createLastMessageFromMessage(message),
        });

        handleUpdateNewMessageToConversationEvent(message);
        getUnreadConversationStatus();
      }

      if (data.type === 'conversation.operator.assigned') {
        const conversation = (data as IWebsocketResponse<IConversation>).data;

        // check if in assigned list and change operator => remove cId from params
        if (
          currentConversationHolder?.id === conversation.id &&
          !conversationsParams?.team &&
          conversation.status !== 'completed'
        ) {
          refetchConversListByNewParams({
            cId: undefined,
          });
          setTimeout(() => sendCustomEvent('deca-livechat-conversation-detail-reset', {}), 500);
        }
        // reload current conversation
        if (currentConversationHolder?.id === conversation.id) {
          setTimeout(() => {
            refetchCurrentConversation();
          }, 700);
        }
        // reload list
        if (conversationsParams?.assigned) {
          setTimeout(() => {
            refetchConversations();
          }, 800);
        }

        if (conversation?.assigneeId === userId) {
          setTimeout(() => {
            handleSetUnreadForAConversation(conversation?.id);
          }, 2000);
        }
      }

      if (data.type === 'conversation.status.updated') {
        const conversation = (data as IWebsocketResponse<IConversation>).data;
        if (conversation.status == 'completed') {
          refetchConversations();
          if (currentConversationHolder?.id === conversation.id) {
            refetchConversListByNewParams({ status: 'completed' });
            setTimeout(() => {
              refetchCurrentConversation();
            }, 600);
          }
          return;
        }
        handleUpdateStatusConverInAllConver(conversation);
      }
      if (
        [
          'user.conversation.state.updated',
          'conversation.team.assigned',
          'team.member.added',
        ].includes(data.type)
      ) {
        getUnreadConversationStatus();
      }

      if (data.type === 'user.unread.conversation.updated') {
        try {
          const unreadConversation = (data as IWebsocketResponse<IWorkspacesConversationStatistic>)
            .data;
          handleConversationUnreadStatusChangeEvent(unreadConversation);
        } catch (error) {
          console.log('Fail to update unread status:', error);
        }
      }
    };
    const cleanUp = createCustomEventListener(
      GLOBAL_REALTIME_EVENT_NAME,
      handleReceiveRealtimeEvent
    );
    return () => {
      cleanUp();
    };
  }, [
    userId,
    conversationsParams?.team,
    conversationsParams?.assigned,
    currentConversationHolder?.id,
    refetchConversations,
    refetchCurrentConversation,
    getUnreadConversationStatus,
    refetchConversListByNewParams,
    handleSetUnreadForAConversation,
    handleUpdateStatusConverInAllConver,
    handleUpdateNewMessageToConversationEvent,
    handleConversationUnreadStatusChangeEvent,
  ]);

  useEffect(() => {
    // when conversations change, send the change as an event to other components
    sendCustomEvent('deca-livechat-conversation-list-update', { conversations });
  }, [conversations]);

  useEffect(() => {
    const controller = new AbortController();
    const resetConversationDetailHandler = () => {
      setConversationId('');
    };
    const renewConversationList = (e: CustomEvent<{ fetchNewList?: boolean }>) => {
      const { fetchNewList } = e.detail;
      refetchConversations(fetchNewList);
    };

    const setupConversationsRelatedWorkspace = (event) => {
      resetConversationDetailHandler();
      const params = event.detail?.params || {};
      const next = event.detail?.next || undefined;
      setNext(next);
      refetchConversListByNewParams(params);
    };

    createCustomEventListener(
      'deca-livechat-conversation-detail-reset',
      resetConversationDetailHandler,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-workspace-setup-related-conversations',
      setupConversationsRelatedWorkspace,
      controller.signal
    );

    createCustomEventListener(
      'deca-livechat-refetch-conversations-for-new-list-change',
      renewConversationList,
      controller.signal
    );

    return () => {
      controller.abort();
    };
  }, [refetchConversations, setNext, refetchConversListByNewParams]);

  const assignee = useMemo(
    () => currentConversationHolder?.assignee,
    [currentConversationHolder?.assignee]
  );

  const dataAssign = useMemo(
    () => ({
      isAssigned: !!assignee,
      customer: currentConversationHolder?.enduser,
      isNew: currentConversationHolder?.status === 'new',
      lastMessage: currentConversationHolder?.lastMessage,
      isCompleted: currentConversationHolder?.status === 'completed',
      isInProgress: currentConversationHolder?.status === 'inProgress',
      isWrapUp: currentConversationHolder?.status === 'inWrapUp',
      isTheAssignee: !!assignee && !!userProfile && assignee.id === userProfile?.id,
      hasTeamAssign: !!currentConversationHolder?.teamId,
      haveAssignee:
        currentConversationHolder?.assigneeId &&
        userProfile?.orgId &&
        currentConversationHolder?.assigneeId !== getDefaultAssigneeId(userProfile.orgId),
      platformWeb: currentConversationHolder?.enduser?.platform === 'web',
      platformLine: currentConversationHolder?.enduser?.platform === 'line',
    }),
    [
      assignee,
      userProfile,
      currentConversationHolder?.teamId,
      currentConversationHolder?.status,
      currentConversationHolder?.enduser,
      currentConversationHolder?.assigneeId,
      currentConversationHolder?.lastMessage,
    ]
  );

  return useMemo(
    () => ({
      next,
      visibleConfirmModal,
      isNew: dataAssign.isNew,
      isWrapUp: dataAssign.isWrapUp,
      listConversations: conversations,
      isAssigned: dataAssign.isAssigned,
      lastMessage: dataAssign.lastMessage,
      isCompleted: dataAssign.isCompleted,
      platformWeb: dataAssign.platformWeb,
      platformLine: dataAssign.platformLine,
      isInProgress: dataAssign.isInProgress,
      haveAssignee: dataAssign.haveAssignee,
      currentConversationId: conversationId,
      isTheAssignee: dataAssign.isTheAssignee,
      hasTeamAssign: dataAssign.hasTeamAssign,
      paramsConversation: conversationsParams,
      isLoadingGetConversations: isLoadingList,
      currentConversation: currentConversationHolder,
      isLoadingCurrentConversation: isLoadingCurrent,
      currentCustomer: {
        ...dataAssign.customer,
        lastTimeChat: currentConversationHolder?.lastMessage?.created,
      },
      conversationListInfinite: {
        isEmpty: isListEmpty,
        isReachingEnd: isReachingEndForListConversation,
      },
      currentTeam:
        typeof currentConversationHolder?.team === 'string'
          ? teamList.find((t) => t.id === (currentConversationHolder?.team as unknown as string))
          : currentConversationHolder?.team,
      hasUnreadNewConversation: conversationStatistic?.hasNewUnread,
      hasUnreadCompletedConversation: conversationStatistic?.hasCompletedUnread,
      hasUnreadInProgressConversation: conversationStatistic?.hasInProgressUnread,
      unreadAssignedConversationNumber: conversationStatistic?.totalUnread,
      setNext,
      showLoadingMore,
      closeConfirmModal,
      assignConversationToTeam,
      changeAutoCompleteParams,
      changeConversationStatus,
      selectConversationHandler,
      refetchCurrentConversation,
      selectConversationIdHandler,
      getUnreadConversationStatus,
      assignOperatorToConversation,
      refetchConversListByNewParams,
      handleConversationChangeEvent,
      confirmMoveToOtherConversation,
      handleSetUnreadForAConversation,
      handleCreateConversationFromEndOne,
      handleUpdateNewMessageToConversationEvent,
      handleConversationUnreadStatusChangeEvent,
      refetchCurrentConversationList: refetchConversations,
      changeFilterParamsToInProgress: syncStatusParamsToCurrConverOrInProgress,
      setCurrentConversation: (conversation: IConversation) => {
        setConversationId(conversation.id);
      },
    }),
    [
      next,
      teamList,
      isListEmpty,
      isLoadingList,
      conversations,
      conversationId,
      showLoadingMore,
      isLoadingCurrent,
      visibleConfirmModal,
      conversationsParams,
      conversationStatistic,
      dataAssign.isNew,
      dataAssign.isWrapUp,
      dataAssign.customer,
      dataAssign.isAssigned,
      dataAssign.isCompleted,
      dataAssign.haveAssignee,
      dataAssign.isInProgress,
      dataAssign.hasTeamAssign,
      dataAssign.isTheAssignee,
      dataAssign.lastMessage,
      dataAssign.platformWeb,
      dataAssign.platformLine,
      currentConversationHolder,
      isReachingEndForListConversation,
      setNext,
      closeConfirmModal,
      refetchConversations,
      changeAutoCompleteParams,
      changeConversationStatus,
      assignConversationToTeam,
      selectConversationHandler,
      refetchCurrentConversation,
      getUnreadConversationStatus,
      selectConversationIdHandler,
      assignOperatorToConversation,
      refetchConversListByNewParams,
      handleConversationChangeEvent,
      confirmMoveToOtherConversation,
      handleSetUnreadForAConversation,
      handleCreateConversationFromEndOne,
      syncStatusParamsToCurrConverOrInProgress,
      handleUpdateNewMessageToConversationEvent,
      handleConversationUnreadStatusChangeEvent,
    ]
  );
};

export type ConversationContextType = ReturnType<typeof useConversation>;

const context = createContext<ConversationContextType | null>(null);

export const ConversationContextProvider: React.FC<any> = ({ children }) => {
  const value = useConversation();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useConversationContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useConversationContext must be used inside ConversationContextProvider');
  }

  return value;
};
