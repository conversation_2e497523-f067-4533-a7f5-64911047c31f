import { EngineProps } from '@resola-ai/widget-engine';

export interface IWidget {
  id?: number;
  description: string;
  installed?: boolean;
  version: string;
  imageLink: string;
  widgetSettingId?: string;
  widgetId?: string;
  data?: {
    [key: string]: any;
  };
  component?: string;
  engine?: EngineProps;
  // config data
  name: string;
  prompt?: string;
  submitOn?: string;
  configure?: string;
}

export interface IWidgetSetting {
  component?: string;
  id: string;
  orgId: string;
  status: string;
  userId: string;
  widgetId: string;
  created: string;
  data: Record<string, any>;
  widgetSettingId: string;
  engine: EngineProps;
  name?: string;
  description?: string;
}

export type WidgetTabType = 'all' | 'installed' | null;

export type CRMConfigDataType = {
  id: string;
  name: string;
  workspaceId: string;
  fields: {
    id: string;
    name: string;
    type: string;
  }[];
  views: {
    id: string;
    name: string;
  }[];
};
