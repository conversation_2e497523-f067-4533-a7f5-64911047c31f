import React from 'react';
import { Button, Group } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

const useStyles = createStyles((theme) => ({
    commonButton: {
        fontSize: '0.875rem',
    },
    buttonConfirm: {
        color: '#FFFFFF',
        '&:hover': {
            backgroundColor: theme.colors.navy[1],
        },
    },
    buttonCancel: {
        color: theme.colors.gray[9],
        '&:hover': {
            backgroundColor: theme.colors.gray[2],
        },
    },
    buttonRemove: {
        color: '#FFFFFF',
        '&:hover': {
            backgroundColor: theme.colors.red[7],
        },
    },
}));

interface ButtonGroupProps {
    cancelText: string;
    saveText: string;
    position: 'flex-start' | 'flex-end' | 'center' | 'space-between';
    onCancel: () => void;
    onSave: () => void;
    isRemove?: boolean;
    reverse?: boolean;
    disabled?: boolean;
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({
    cancelText,
    saveText,
    position,
    onCancel,
    onSave,
    isRemove = false,
    reverse = false,
    disabled,
}) => {
    const { classes, cx } = useStyles();

    return (
        <Group justify={position} style={reverse ? { flexDirection: 'row-reverse' } : {}}>
            <Button
                size="xs"
                radius="sm"
                color="gray.9"
                variant="outline"
                onClick={onCancel}
                className={cx(classes.commonButton, classes.buttonCancel)}
            >
                {cancelText}
            </Button>
            <Button
                size="xs"
                radius="sm"
                variant="filled"
                onClick={onSave}
                disabled={disabled}
                color={isRemove ? 'red.5' : 'navy.0'}
                className={cx(
                    classes.commonButton,
                    isRemove ? classes.buttonRemove : classes.buttonConfirm,
                )}
                styles={{
                    root: {
                        '&:hover:disabled': {
                            backgroundColor: 'var(--mantine-color-gray-1)',
                        },
                    },
                }}
            >
                {saveText}
            </Button>
        </Group>
    );
};

export default ButtonGroup;
