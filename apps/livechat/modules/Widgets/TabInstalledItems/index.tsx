import { Center, Container, Loader } from '@mantine/core';
import { BottomUpTween } from '@resola-ai/ui';
import React, { useEffect } from 'react';
import { useCallback } from 'react';
import { useWidgetContext } from '../../widgetContext';
import NoIntegratedWidget from '../NoIntegratedWidget';
import { IWidget } from '../models';
import InstalledItem from './Item';

type Props = {
  // eslint-disable-next-line no-unused-vars
  onNavigateToTabAllWidgets: (value: string) => void;
};

const TabInstalledItems: React.FC<Props> = ({ onNavigateToTabAllWidgets }) => {
  const { installedWidgetList, loadingInstallWidget, fetchInstalledWidgetList } =
    useWidgetContext();

  const handleWidgetDeleted = useCallback(async () => {
    try {
      await fetchInstalledWidgetList();
    } catch (error) {
      console.error('Error fetching installed widgets after deletion:', error);
      // Handle error appropriately
    }
  }, [fetchInstalledWidgetList]);

  useEffect(() => {
    fetchInstalledWidgetList();
    /**
     * Call the above function only once to keep api from calling loop
     * And the dependency of this useEffect should be [] to check only once when mounting.
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loadingInstallWidget) {
    return (
      <Center mt={'lg'}>
        <Loader variant='dots' color='navy.0' />
      </Center>
    );
  }

  if (installedWidgetList.length === 0) {
    return <NoIntegratedWidget onNavigateToTabAllWidgets={onNavigateToTabAllWidgets} />;
  }

  return (
    <Container fluid px={0}>
      {installedWidgetList.map((item: IWidget, index) => (
        <BottomUpTween delay={index * 0.1} key={index}>
          <InstalledItem item={item} onWidgetDeleted={handleWidgetDeleted} />
        </BottomUpTween>
      ))}
    </Container>
  );
};

export default TabInstalledItems;
