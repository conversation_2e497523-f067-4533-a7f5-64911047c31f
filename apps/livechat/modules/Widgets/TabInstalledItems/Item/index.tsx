import { ActionIcon, Box, Flex, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconSettings, IconTrash } from '@tabler/icons-react';
import React, { useCallback } from 'react';
import { useUserContext } from '../../../userContext';
import { useWidgetContext } from '../../../widgetContext';
import { IWidget } from '../../models';

const useStyles = createStyles((theme) => ({
  boxItem: {
    border: '1px solid #C1C2C5',
    borderColor: theme.colors.gray[3],
    padding: '20px',
    borderRadius: '6px',
    boxShadow:
      '0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05),rgba(0, 0, 0, 0.05) 0 0.625rem 0.9375rem -0.3125rem,rgba(0, 0, 0, 0.04) 0 0.4375rem 0.4375rem -0.3125rem',
    minHeight: '105px',
    marginBottom: '16px',
    cursor: 'pointer',
    '&:hover': {
      borderColor: theme.colors.navy[0],
    },
  },
  flexItem: {
    minHeight: '65px',
  },
  titleItem: {
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 1,
    WebkitBoxOrient: 'vertical',
    color: theme.colors.dark[4],
  },
  textItem: {
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    color: theme.colors.gray[7],
  },
  pointerCursor: {
    cursor: 'pointer',
  },
  noneCursor: {
    pointerEvents: 'none',
  },
}));

interface InstalledItemProps {
  item: IWidget;
  onWidgetDeleted: () => void;
}

const InstalledItem: React.FC<InstalledItemProps> = ({ item, onWidgetDeleted }) => {
  const { isManager } = useUserContext();
  const { setOpenWidgetFormModal, setWidgetSelected, setOpenDeleteConfirmationModal } =
    useWidgetContext();
  const { classes } = useStyles();

  const handleSelectWidget = useCallback(
    (item: IWidget) => {
      if (!isManager) return;
      setWidgetSelected(item);
      setTimeout(() => setOpenWidgetFormModal(true));
    },
    [isManager, setOpenWidgetFormModal, setWidgetSelected]
  );

  const handleRemoveWidget = useCallback(
    async (e: any, item: IWidget) => {
      e.stopPropagation();
      setWidgetSelected(item);
      setOpenDeleteConfirmationModal(true);

      try {
        // Call the onWidgetDeleted function to refresh the list
        onWidgetDeleted();
      } catch (error) {
        console.error('Error deleting widget:', error);
        // Handle error appropriately
      }
    },
    [onWidgetDeleted, setOpenDeleteConfirmationModal, setWidgetSelected]
  );

  return (
    <Box
      className={classes.boxItem}
      onClick={() => handleSelectWidget(item)}
      id={item?.widgetSettingId?.toString()}
    >
      <Flex
        gap='md'
        direction='row'
        align='center'
        justify='space-between'
        className={classes.flexItem}
      >
        <Flex gap='md' direction='row' align='flex-start' justify='flex-start' w='90%'>
          <Box>
            <Title
              order={4}
              // mb={12}
              mb={16} // this is to make it the same with all widget spacing
              className={classes.titleItem}
            >
              {item.name}
            </Title>
            <Text fz='sm' className={classes.textItem}>
              {item.description}
            </Text>
          </Box>
        </Flex>

        <Flex gap='xs' direction='row' align='center' justify='flex-start'>
          <ActionIcon
            variant='transparent'
            color={isManager ? 'navy.0' : 'navy.2'}
            onClick={() => handleSelectWidget(item)}
            className={isManager ? classes.pointerCursor : classes.noneCursor}
          >
            <IconSettings size={24} strokeWidth={2} />
          </ActionIcon>
          <ActionIcon
            variant='transparent'
            color={isManager ? 'navy.0' : 'navy.2'}
            onClick={(e) => handleRemoveWidget(e, item)}
            className={isManager ? classes.pointerCursor : classes.noneCursor}
          >
            <IconTrash size={24} strokeWidth={2} />
          </ActionIcon>
        </Flex>
      </Flex>
    </Box>
  );
};

export default InstalledItem;
