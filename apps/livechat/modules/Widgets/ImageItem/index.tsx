import { Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
const useStyles = createStyles(() => ({
  widgetFrame: {
    borderRadius: '10px',
    border: '1px solid #C1C2C5',
  },
  img: {
    height: '100%',
    width: '100%',
    borderRadius: '10px',
  },
}));

interface ImageItemProps {
  imageLink: string;
  width: string;
  height: string;
}

const ImageItem: React.FC<ImageItemProps> = ({ imageLink, width, height }) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.widgetFrame} w={width} h={height}>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img className={classes.img} src={imageLink} alt={imageLink} />
    </Box>
  );
};

export default ImageItem;
