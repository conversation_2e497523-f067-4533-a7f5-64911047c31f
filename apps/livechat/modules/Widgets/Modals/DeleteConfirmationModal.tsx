import React from 'react';
import { Box, Flex, Group, Modal, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import ButtonGroup from '../ButtonGroup';
import { useWidgetContext } from '../../widgetContext';
import { IconX } from '@tabler/icons-react';
import ApiService from '../../../services/api';

const useStyles = createStyles((theme) => ({
    widgetFrame: {
        height: '120px',
        width: '120px',
        borderRadius: '10px',
        border: '1px solid #C1C2C5',
    },
    title: {
        fontSize: '20px',
        fontWeight: 'bold',
    },
    closeButton: {
        cursor: 'pointer',
        '&:hover': {
            backgroundColor: theme.colors.gray[2],
        },
    },
}));

const DeleteConfirmationModal = () => {
    const {
        t,
        widgetSelected,
        installedWidgetList,
        setInstalledWidgetList,
        openDeleteConfirmationModal,
        setOpenDeleteConfirmationModal,
    } = useWidgetContext();
    const { classes } = useStyles();

    const handleClose = () => {
        setOpenDeleteConfirmationModal(false);
    };

    const handleCancel = () => {
        handleClose();
    };

    const handleRemove = () => {
        ApiService.removeWidgetSetting(widgetSelected.widgetSettingId)
            .then()
            .catch((err) => console.log(err));
        const updatedInstalledWidgetList = installedWidgetList.filter(
            (item) => item.widgetSettingId !== widgetSelected.widgetSettingId,
        );
        setInstalledWidgetList(updatedInstalledWidgetList);
        handleClose();
    };

    return (
        <Modal
            classNames={{
                title: classes.title,
                close: classes.closeButton,
            }}
            radius="md"
            title=""
            size="md"
            centered
            onClose={handleClose}
            withCloseButton={false}
            opened={openDeleteConfirmationModal}
            closeOnClickOutside={false}
            closeOnEscape={false}
        >
            <Group justify="flex-end" onClick={handleClose}>
                <IconX
                    className={classes.closeButton}
                    size={24}
                    strokeWidth={2}
                    color={'#5C5F66'}
                />
            </Group>
            <Flex justify="flex-start" align="flex-start" direction="column" mb={20}>
                <Title order={5} ta="left" c="dark.4">
                    {t('widget.uninstall.title', { name: widgetSelected.name || '' })}
                </Title>
                <Title order={5} ta="left" c="dark.4">
                    {t('widget.uninstall.confirm')}
                </Title>
            </Flex>
            <Box mb={0}>
                <ButtonGroup
                    reverse
                    isRemove={true}
                    position="flex-start"
                    cancelText={`${t('widget.cancel')}`}
                    saveText={`${t('widget.uninstall')}`}
                    onCancel={handleCancel}
                    onSave={handleRemove}
                />
            </Box>
        </Modal>
    );
};

export default DeleteConfirmationModal;
