import { Grid, Input, Modal, Radio, Stack, Text, Textarea, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ACTIVITY_WIDGET_PROMPT,
  CHATBOT_WIDGET_PROMPT,
  CRM_WIDGET_PROMPT,
  OPENAI_WIDGET_PROMPT,
} from '../../../constants/index';
import ApiService from '../../../services/api';
import { useWidgetContext } from '../../widgetContext';
import ButtonGroup from '../ButtonGroup';
import { SearchableDropdownMultiSelectOption } from '../Components/SearchableDropdownMultiSelect';
import SearchableDropdownSelect, {
  SearchableDropdownOption,
} from '../Components/SearchableDropdownSelect';
import { IWidget } from '../models';

// const CUSTOM_YELLOW_COLOR = '#996F00';
const useStyles = createStyles((theme) => ({
  customTextarea: {
    height: '200px',
    fontSize: '0.875rem',
  },
  title: {
    fontSize: '20px',
    fontWeight: 'bold',
    color: theme.colors.dark[4],
  },
  closeButton: {
    svg: {
      width: '24px !important',
      height: '24px !important',
    },
  },
  textInput: {
    '& .mantine-Input-input': {
      fontSize: '0.875rem',
      '&:focus': {
        borderColor: theme.colors.navy[0],
      },
    },
  },
}));

const WidgetFormModal = () => {
  const {
    t,
    crmConfigData,
    widgetSelected,
    setWidgetSelected,
    openWidgetFormModal,
    isInstalledFromAllTab,
    setInstalledWidgetList,
    setOpenWidgetFormModal,
    fetchInstalledWidgetList,
    setIsInstalledFromAllTab,
    closeWidgetFormModalFromAllTabAndNavigateItem,
  } = useWidgetContext();
  const { classes } = useStyles();
  const [name, setName] = useState<string>('');
  const [prompt, setPrompt] = useState<string>('');
  const [submitOn, setSubmitOn] = useState<string>('');
  const [description, setDescription] = useState<string>('');

  const [crmObject, setCrmObject] = useState<string | null>('');
  const [crmIdentifyFieldId, setCrmIdentifyFieldId] = useState<string>('');
  const [crmViewId, setCrmViewId] = useState<string>('');

  const [errorCrmObject, setErrorCrmObject] = useState<boolean>(false);
  const [errorCrmFields, setErrorCrmFields] = useState<string | boolean>(false);
  const [isSaving, setIsSaving] = useState(false);

  const configurationList = useMemo(
    () => [
      {
        value: 'auto',
        label: `${t('widget.form.option.auto.executed')}`,
      },
      {
        value: 'manual',
        label: `${t('widget.form.option.manual.executed')}`,
      },
    ],
    [t]
  );
  const chatbotConfiguration = useMemo(
    () => [
      {
        value: 'display',
        label: `${t('widget.form.option.display.executed')}`,
      },
      {
        value: 'update',
        label: `${t('widget.form.option.update.executed')}`,
      },
    ],
    [t]
  );

  const crmObjOptions = useMemo<SearchableDropdownOption[]>(() => {
    if (!crmConfigData?.length) return [];
    return (
      crmConfigData?.map((item) => {
        return {
          value: item?.id,
          label: item?.name,
        };
      }) || []
    );
  }, [crmConfigData]);

  const crmFieldsByObject = useMemo<SearchableDropdownMultiSelectOption[]>(() => {
    if (!crmConfigData?.length || !crmObject) return [];
    return (
      (crmConfigData?.find((item) => item?.id === crmObject)?.fields || [])?.map((item) => {
        const isInvalidOption = false;
        return {
          label: item?.name,
          value: item?.id,
          disabled: isInvalidOption,
          warning: isInvalidOption,
        };
      }) || []
    );
  }, [crmConfigData, crmObject]);

  const crmViewsByObject = useMemo<SearchableDropdownOption[]>(() => {
    if (!crmConfigData?.length || !crmObject) return [];
    return (
      (crmConfigData?.find((item) => item?.id === crmObject)?.views || [])?.map((item) => {
        return {
          value: item?.id,
          label: item?.name,
        };
      }) || []
    );
  }, [crmConfigData, crmObject]);

  const initialValidateCrm = useCallback(
    (widgetSelected) => {
      const crmObj = widgetSelected?.engine?.data?.objectId || '';
      const originCrmObj = crmConfigData?.find((item) => item?.id === crmObj);
      const crmIdentifyFieldId = widgetSelected?.engine?.data?.identifyFieldId || '';

      if (!crmObj || !originCrmObj) {
        setErrorCrmObject(true);
      } else {
        setErrorCrmObject(false);
      }

      if (!crmIdentifyFieldId.length) {
        setErrorCrmFields(true);
      } else if (
        !originCrmObj ||
        (originCrmObj && !originCrmObj?.fields?.find((field) => field?.id === crmIdentifyFieldId))
      ) {
        setErrorCrmFields(t('crm_widget.removed_column_error'));
      } else {
        setErrorCrmFields(false);
      }
    },
    [crmConfigData, t]
  );

  const validateCrmData = useCallback(() => {
    let isValid = true;
    if (!crmObject) {
      isValid = false;
      setErrorCrmObject(true);
    } else {
      setErrorCrmObject(false);
    }
    if (!crmIdentifyFieldId?.length) {
      isValid = false;
      setErrorCrmFields(true);
    } else {
      if (crmObject) {
        const originCrmObj = crmConfigData?.find((item) => item?.id === crmObject);
        if (
          !originCrmObj ||
          (originCrmObj && !originCrmObj?.fields?.find((field) => field?.id === crmIdentifyFieldId))
        ) {
          setErrorCrmFields(t('crm_widget.removed_column_error'));
          isValid = false;
        }
      } else {
        setErrorCrmFields(false);
      }
    }
    return isValid;
  }, [crmConfigData, crmIdentifyFieldId, crmObject, t]);

  const setupData = useCallback(
    (widgetSelected: IWidget, action: 'newInstalled' | 'openAgain' | 'cancel' = 'newInstalled') => {
      setName(widgetSelected?.name);
      setPrompt(widgetSelected?.prompt);
      setSubmitOn(widgetSelected?.configure);
      setDescription(widgetSelected?.description);

      if (widgetSelected?.engine?.component === 'crm') {
        setCrmObject(widgetSelected?.engine?.data?.objectId || '');
        setCrmIdentifyFieldId(widgetSelected?.engine?.data?.identifyFieldId || '');
        setCrmViewId(widgetSelected?.engine?.data?.viewId || '');

        action === 'openAgain' && initialValidateCrm(widgetSelected);
        if (action === 'cancel') {
          setErrorCrmObject(false);
          setErrorCrmFields(false);
        }
      } else if (widgetSelected?.component === 'crm') {
        if (action === 'cancel') {
          setErrorCrmObject(false);
          setErrorCrmFields(false);
        }
        if (action === 'newInstalled') {
          setCrmObject('');
          setCrmIdentifyFieldId('');
          setCrmViewId('');
        }
      } else {
        setCrmObject('');
        setCrmIdentifyFieldId('');
        setCrmViewId('');
      }
    },
    [initialValidateCrm]
  );

  const handleSelectCrmObject = useCallback(
    (val: string) => {
      if (!val) return;
      if (val === crmObject) return;
      setCrmObject(val);
      setErrorCrmFields(false);
      setErrorCrmObject(false);
    },
    [crmObject]
  );

  useEffect(() => {
    setupData(widgetSelected, 'newInstalled');
    switch (true) {
      case widgetSelected.configure !== '':
        setSubmitOn(widgetSelected.configure);
        break;
      case widgetSelected.component === CHATBOT_WIDGET_PROMPT:
        setSubmitOn('display');
        break;
      default:
        setSubmitOn('auto');
    }
  }, [widgetSelected, setupData]);

  useEffect(() => {
    !isInstalledFromAllTab && openWidgetFormModal && setupData(widgetSelected, 'openAgain');
  }, [isInstalledFromAllTab, openWidgetFormModal, setupData, widgetSelected]);

  const handleClose = useCallback(
    (widgetSettingId?: string) => {
      setIsSaving(false);
      setOpenWidgetFormModal(false);
      setupData(widgetSelected, 'cancel');
      if (widgetSettingId && isInstalledFromAllTab) {
        closeWidgetFormModalFromAllTabAndNavigateItem(widgetSettingId);
      }
      isInstalledFromAllTab && setIsInstalledFromAllTab(false);
    },
    [
      setupData,
      widgetSelected,
      isInstalledFromAllTab,
      setOpenWidgetFormModal,
      setIsInstalledFromAllTab,
      closeWidgetFormModalFromAllTabAndNavigateItem,
    ]
  );

  const isWidget = useCallback(
    (widget: string) => {
      const currentWidget = widgetSelected?.component || widgetSelected?.engine?.component;
      return currentWidget === widget;
    },
    [widgetSelected?.component, widgetSelected?.engine?.component]
  );

  const checkTitle = (widgetSelected) => {
    switch (widgetSelected?.component || widgetSelected?.engine?.component) {
      case CHATBOT_WIDGET_PROMPT:
        return `${t('widget.chatbottitle')}`;
      case ACTIVITY_WIDGET_PROMPT:
        return `${t('widget.activitytitle')}`;
      case CRM_WIDGET_PROMPT:
        return `${t('widget.crm.title')}`;
      default:
        return `${t('widget.aiautoreplytitle')}`;
    }
  };

  const saveWidget = useCallback<
    () => Promise<{ isSaved: boolean; widgetSettingId?: string }>
  >(async () => {
    let data: Record<string, any> = {
      prompt: prompt,
      configure: submitOn,
    };

    if (isWidget(CRM_WIDGET_PROMPT)) {
      data = {
        ...data,
        objectId: crmObject,
        displayFields: [],
        workspaceId: crmConfigData?.find((item) => item?.id === crmObject)?.workspaceId,
        identifyFieldId: crmIdentifyFieldId,
        viewId: crmViewId,
      };
    }

    try {
      let widgetSettingIdFromCreateApi: string;
      if (widgetSelected?.widgetSettingId) {
        await ApiService.updateWidgetSetting(widgetSelected?.widgetSettingId, {
          name: name,
          description: description,
          data: data,
        });
        setInstalledWidgetList((prev) => {
          const index = prev.findIndex(
            (item) => item?.widgetSettingId === widgetSelected?.widgetSettingId
          );
          if (index === -1) return prev;
          prev[index] = {
            ...prev?.[index],
            name: name,
            data: data,
            prompt: prompt,
            configure: submitOn,
            description: description,
          };
          return [...prev];
        });
      } else {
        // Create new widget settings.
        const response = await ApiService.addWidgetSetting(
          widgetSelected.widgetId,
          name,
          description,
          data
        );
        if (!response) {
          return {
            isSaved: false,
            widgetSettingId: undefined,
          };
        }

        widgetSettingIdFromCreateApi = response?.data?.id;
        const item = {
          ...widgetSelected,
          name: name,
          data: data,
          prompt: prompt,
          installed: true,
          configure: submitOn,
          description: description,
          widgetSettingId: widgetSettingIdFromCreateApi,
        };
        setInstalledWidgetList((prev) => [...prev, item]);
        setWidgetSelected((prev) => ({
          ...prev,
          widgetSettingId: widgetSettingIdFromCreateApi,
        }));
      }
      return {
        isSaved: true,
        widgetSettingId: widgetSelected?.widgetSettingId || widgetSettingIdFromCreateApi,
      };
    } catch (e) {
      console.error('Error create/updating widget:', e);
      return {
        isSaved: false,
        widgetSettingId: undefined,
      };
    }
  }, [
    name,
    prompt,
    submitOn,
    isWidget,
    crmObject,
    crmViewId,
    description,
    crmConfigData,
    widgetSelected,
    crmIdentifyFieldId,
    setWidgetSelected,
    setInstalledWidgetList,
  ]);

  const handleSaveWidget = useCallback(async () => {
    if (isSaving) return;
    if (isWidget(CRM_WIDGET_PROMPT) && !validateCrmData()) {
      return;
    }
    setIsSaving(true);
    const saveCheck = await saveWidget();
    if (!saveCheck.isSaved) {
      setIsSaving(false);
      return;
    }
    setIsSaving(false);
    handleClose(saveCheck.widgetSettingId);
    setIsInstalledFromAllTab(false);
    await fetchInstalledWidgetList();
  }, [
    isWidget,
    isSaving,
    saveWidget,
    handleClose,
    validateCrmData,
    fetchInstalledWidgetList,
    setIsInstalledFromAllTab,
  ]);

  return (
    <Modal
      size={640}
      centered
      radius='md'
      padding='xl'
      closeOnEscape={false}
      closeOnClickOutside={false}
      classNames={{
        title: classes.title,
        close: classes.closeButton,
      }}
      opened={openWidgetFormModal}
      title={checkTitle(widgetSelected)}
      onClose={() => handleClose()}
    >
      <Grid>
        <Grid.Col span={3}>
          <Title order={6}>
            {`${t('widget.form.name')}`}
            <span style={{ color: 'red' }}>*</span>
          </Title>
        </Grid.Col>
        <Grid.Col span={9}>
          <Input
            size='xs'
            value={name}
            className={classes.textInput}
            placeholder={`${t('widget.form.name.placeholder')}`}
            onChange={(event) => setName(event.currentTarget.value)}
            error={!(name || '').trim().length}
          />
        </Grid.Col>
        <Grid.Col span={3}>
          <Title order={6}>{`${t('widget.form.description')}`}</Title>
        </Grid.Col>
        <Grid.Col span={9}>
          <Textarea
            autosize
            maxRows={5}
            maxLength={100}
            value={description}
            classNames={{
              input: classes.customTextarea,
            }}
            sx={(theme) => ({
              '.mantine-Input-input:focus': {
                borderColor: theme.colors.navy[0],
              },
            })}
            placeholder={`${t('widget.form.description.placeholder')}`}
            onChange={(event) => setDescription(event.currentTarget.value)}
          />
        </Grid.Col>
        {isWidget(OPENAI_WIDGET_PROMPT) && (
          <>
            <Grid.Col span={3}>
              <Title order={6}>{`${t('widget.form.prompt')}`}</Title>
            </Grid.Col>
            <Grid.Col span={9}>
              <Textarea
                value={prompt}
                classNames={{
                  input: classes.customTextarea,
                }}
                sx={(theme) => ({
                  '.mantine-Input-input:focus': {
                    borderColor: theme.colors.navy[0],
                  },
                })}
                placeholder={`${t('widget.form.prompt.placeholder')}`}
                onChange={(event) => setPrompt(event.currentTarget.value)}
              />
            </Grid.Col>
          </>
        )}
        {!isWidget(ACTIVITY_WIDGET_PROMPT) && !isWidget(CRM_WIDGET_PROMPT) && (
          <>
            <Grid.Col span={3}>
              <Title order={6}>{`${t('widget.form.configuration')}`}</Title>
            </Grid.Col>
            <Grid.Col span={9} mb={24} pt={0}>
              <Stack mt='xs'>
                {isWidget(OPENAI_WIDGET_PROMPT) &&
                  configurationList.map((item, index) => (
                    <Radio
                      key={index}
                      color='navy.0'
                      value={item.value}
                      label={item.label}
                      checked={submitOn === item.value}
                      onChange={(event) => setSubmitOn(event.target.value)}
                    />
                  ))}
                {isWidget(CHATBOT_WIDGET_PROMPT) &&
                  chatbotConfiguration.map((item, index) => (
                    <Radio
                      color='navy.0'
                      key={index}
                      value={item.value}
                      label={item.label}
                      checked={submitOn === item.value}
                      onChange={(event) => setSubmitOn(event.target.value)}
                    />
                  ))}
              </Stack>
            </Grid.Col>
          </>
        )}
        {isWidget(CRM_WIDGET_PROMPT) && (
          <>
            <Grid.Col span={3}>
              <Title order={6}>
                {`${t('crm_widget.display_content.title')}`}
                <span style={{ color: 'red' }}>*</span>
              </Title>
            </Grid.Col>
            <Grid.Col span={9} mb={24} pt={0}>
              <Stack mt='xs' gap={5}>
                <Text c='dimmed' fz={12} fw={400} truncate>
                  {t('crm_widget.display_content.crm_object.title')}
                </Text>
                <SearchableDropdownSelect
                  value={crmObject}
                  error={errorCrmObject}
                  options={crmObjOptions}
                  onSelected={handleSelectCrmObject}
                  placeholder={t('crm_widget.input.placeholder')}
                />
                <Text c='dimmed' fz={12} fw={400} mt='md' truncate>
                  {t('crm_widget.display_content.crm_identified_field.title')}
                </Text>
                <SearchableDropdownSelect
                  value={crmIdentifyFieldId}
                  error={errorCrmFields}
                  options={crmFieldsByObject}
                  onSelected={(val) => setCrmIdentifyFieldId(val)}
                  placeholder={t('crm_widget.display_content.crm_identified_field.placeholder')}
                  disabled={!crmObject}
                />
                <Text c='dimmed' fz={12} fw={400} mt='md' truncate>
                  {t('crm_widget.display_content.crm_view.title')}
                </Text>
                <SearchableDropdownSelect
                  value={crmViewId}
                  error={errorCrmFields}
                  options={crmViewsByObject}
                  onSelected={(val) => setCrmViewId(val)}
                  placeholder={t('crm_widget.display_content.crm_view.placeholder')}
                  disabled={!crmObject}
                />
                <Text c='dimmed' fz={12} fw={400}>
                  {t('crm_widget.display_content.crm_view.description')}
                </Text>
              </Stack>
            </Grid.Col>
          </>
        )}
        <Grid.Col span={12}>
          <ButtonGroup
            position='flex-end'
            saveText={`${t('widget.save')}`}
            cancelText={`${t('widget.cancel')}`}
            disabled={!(name || '')?.trim().length}
            onCancel={!isSaving ? () => handleClose() : () => {}}
            onSave={!isSaving ? handleSaveWidget : () => {}}
          />
        </Grid.Col>
      </Grid>
    </Modal>
  );
};

export default WidgetFormModal;
