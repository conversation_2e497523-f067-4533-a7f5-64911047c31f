import { Box, Container, Flex, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
import ViewOnly from '../../components/ViewOnly';
import { COMMON_PAGE_MAX_WIDTH } from '../../constants';
import { useUserContext } from '../userContext';
import { useWidgetContext } from '../widgetContext';
import { DeleteConfirmationModal, WidgetFormModal, WidgetInstalledModal } from './Modals';
import TabsControl from './TabsControl';

const useStyle = createStyles((theme) => ({
  container: {
    maxWidth: COMMON_PAGE_MAX_WIDTH,
    '& :target': {
      scrollBehavior: 'smooth',
      scrollMarginTop: '100px',
      border: `1px solid ${theme.colors.violet[5]}`,
    },
  },
  pageTitle: {
    color: theme.colors.dark[4],
  },
}));

export default function WidgetModule() {
  const { classes } = useStyle();
  const { t } = useWidgetContext();
  const { isManager, isLoadingProfile } = useUserContext();

  return (
    <Container fluid px={0} className={classes.container}>
      <Box>
        <Flex align={'center'} mt={'lg'} gap='20px'>
          <Title className={classes.pageTitle} order={3} fw={700}>
            {t('widget.title')}
          </Title>
          {!isLoadingProfile && !isManager && <ViewOnly />}
        </Flex>

        <Box mt={30}>
          <TabsControl />
        </Box>
      </Box>
      <WidgetFormModal />
      <WidgetInstalledModal />
      <DeleteConfirmationModal />
    </Container>
  );
}
