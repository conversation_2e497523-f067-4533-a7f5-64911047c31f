import {
  Button,
  Checkbox,
  Combobox,
  Flex,
  Group,
  Input,
  Pill,
  PillsInput,
  Text,
  useCombobox,
  useMantineTheme,
} from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';

export type SearchableDropdownMultiSelectOption = {
  label: string;
  value: string;
  disabled?: boolean;
  warning?: boolean;
};

type Props = {
  disabled?: boolean;
  options: SearchableDropdownMultiSelectOption[];
  placeholder?: string;
  value: string[];
  // eslint-disable-next-line no-unused-vars
  onSelected: (value: string[]) => void;
  error?: ReactNode;
};

function SearchableDropdownMultiSelect({
  disabled,
  options = [],
  placeholder = '',
  value,
  onSelected,
  error,
}: Props) {
  const theme = useMantineTheme();
  const { t } = useTranslate('workspace');
  const [innerValue, setValue] = useState<string[]>([]);
  const [search, setSearch] = useState('');

  const combobox = useCombobox({
    onDropdownClose: () => {
      if (disabled) {
        return;
      }
      combobox.resetSelectedOption();
      try {
        combobox?.focusTarget();
      } catch (e) {}
      setSearch('');
    },
    onDropdownOpen: () => {
      if (disabled) return;
      combobox.updateSelectedOptionIndex('active');
      combobox.focusSearchInput();
    },
  });

  const handleValueSelect = useCallback(
    (val: string) => {
      setValue((current) => {
        const res = current.includes(val) ? current.filter((v) => v !== val) : [...current, val];
        onSelected(res);
        return res;
      });
    },
    [onSelected]
  );

  const handleValueRemove = useCallback(
    (val: string) => {
      if (disabled) return;
      setValue((current) => {
        const res = current.filter((v) => v !== val);
        onSelected(res);
        return res;
      });
    },
    [disabled, onSelected]
  );

  const values = useMemo(() => {
    const firstThreeItem = innerValue.slice(0, 3).map((item) => (
      <Pill
        key={item}
        withRemoveButton
        disabled={disabled}
        onRemove={() => handleValueRemove(item)}
        sx={{ backgroundColor: theme.colors.navy[6], color: theme.colors.gray[6] }}
      >
        <Text fw={700} sx={{ color: theme.colors.navy[4], lineHeight: 'inherit' }} fz={12}>
          {options.find((option) => option.value === item)?.label}
        </Text>
      </Pill>
    ));

    return innerValue.length > 3
      ? [
          ...firstThreeItem,
          <Pill
            fw={700}
            key={'rest_item'}
            withRemoveButton={false}
            sx={{ backgroundColor: theme.colors.navy[6], color: theme.colors.navy[4] }}
          >
            + {innerValue.length - 3}
          </Pill>,
        ]
      : firstThreeItem;
  }, [disabled, handleValueRemove, innerValue, options, theme.colors.gray, theme.colors.navy]);

  const innerOptions = useMemo(() => {
    return options
      .filter((item) => item.label.toLowerCase().includes(search.toLowerCase().trim()))
      .map((item) => (
        <>
          <Combobox.Option
            key={item.value}
            value={item.value}
            disabled={item?.disabled}
            active={value.includes(item.value)}
            sx={{ marginBottom: 5 }}
          >
            <Group
              gap='sm'
              py={5}
              sx={{
                overflow: 'hidden',
                flexWrap: 'nowrap',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
              }}
            >
              <Checkbox
                aria-hidden
                tabIndex={-1}
                onChange={() => {}}
                style={{ pointerEvents: 'none' }}
                checked={innerValue.includes(item.value)}
                color={item?.disabled ? 'gray' : theme.colors.navy[0]}
              />
              <span>{item.label}</span>
            </Group>
          </Combobox.Option>
          {item?.warning ? (
            <Group
              px={15}
              ml={10}
              mr={15}
              mt={-10}
              justify='space-between'
              sx={{ backgroundColor: theme.colors.red[0], borderRadius: 5 }}
            >
              <Flex justify={'center'} align={'center'}>
                <IconInfoCircle color={theme.colors.red[9]} size={20} stroke={2} />
                <Text ml={5} fz={14} fw={400} c={theme.colors.red[9]}>
                  {t('crm_widget.warning_removed_column_error')}
                </Text>
              </Flex>
              <Button variant='transparent' color='navy.4'>
                {t('crm_widget.sync_button')}
              </Button>
            </Group>
          ) : null}
        </>
      ));
  }, [innerValue, options, search, t, theme.colors.navy, theme.colors.red, value]);

  useEffect(() => {
    if (Array.isArray(value)) setValue(value);
  }, [value]);

  return (
    <Combobox
      store={combobox}
      position='bottom'
      disabled={disabled}
      withinPortal={true}
      onOptionSubmit={handleValueSelect}
      middlewares={{ flip: false, shift: false }}
    >
      <Combobox.Target>
        <PillsInput
          pointer
          error={error}
          rightSection={<Combobox.Chevron />}
          onClick={() => combobox.toggleDropdown()}
        >
          <Pill.Group>
            {values.length > 0 ? values : <Input.Placeholder>{placeholder}</Input.Placeholder>}

            <Combobox.EventsTarget>
              <PillsInput.Field
                type='hidden'
                onKeyDown={(event) => {
                  if (event.key === 'Backspace') {
                    event.preventDefault();
                    handleValueRemove(value[value.length - 1]);
                  }
                }}
              />
            </Combobox.EventsTarget>
          </Pill.Group>
        </PillsInput>
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Search
          value={search}
          sx={{
            '.mantine-Combobox-section[data-position="left"]': {
              width: '20px',
              paddingTop: '5px',
            },
            '.mantine-Combobox-input': {
              fontSize: '14px',
              paddingTop: '5px',
              borderBottom: 'none',
              paddingInlineStart: '25px',
            },
          }}
          leftSection={
            <div
              style={{
                width: '2px',
                height: '70%',
                backgroundColor: theme.colors.navy[1],
              }}
            ></div>
          }
          leftSectionPointerEvents='none'
          onChange={(event) => setSearch(event.currentTarget.value)}
          placeholder={t('modal_operator.input_placeholder')}
        />
        <Combobox.Options
          sx={{
            maxHeight: 200,
            overflowY: 'auto',
            marginTop: '-4px',
            overflowX: 'hidden',
            marginRight: '-5px',
          }}
        >
          {innerOptions.length > 0 ? (
            innerOptions
          ) : (
            <Combobox.Empty>{t('crm_widget.search_no_match')}</Combobox.Empty>
          )}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}

export default SearchableDropdownMultiSelect;
