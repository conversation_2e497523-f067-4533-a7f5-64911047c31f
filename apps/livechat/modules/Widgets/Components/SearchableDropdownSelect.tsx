import { Combobox, Input, InputBase, useCombobox, useMantineTheme } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { ReactNode, useEffect, useMemo, useState } from 'react';

export type SearchableDropdownOption = {
  label: string;
  value: string;
};

type Props = {
  value: string | null;
  // eslint-disable-next-line no-unused-vars
  onSelected: (value: string | null) => void;
  options: SearchableDropdownOption[];
  placeholder?: string;
  disabled?: boolean;
  error?: ReactNode;
};

const SearchableDropdownSelect = ({
  value,
  onSelected,
  options = [],
  placeholder = '',
  disabled = false,
  error,
}: Props) => {
  const theme = useMantineTheme();
  const { t } = useTranslate('workspace');
  const [search, setSearch] = useState('');
  const [innerValue, setInnerValue] = useState<string | null>(value);
  const combobox = useCombobox({
    onDropdownClose: () => {
      if (disabled) {
        return;
      }
      combobox?.resetSelectedOption();
      combobox?.focusTarget();
      setSearch('');
    },

    onDropdownOpen: () => {
      if (disabled) {
        return;
      }
      combobox.focusSearchInput();
    },
  });

  const innerOptions = useMemo(() => {
    return options
      .filter((item) => item.label.toLowerCase().includes(search.toLowerCase().trim()))
      .map((item) => (
        <Combobox.Option
          value={item.value}
          key={item.value}
          sx={{
            backgroundColor: item.value === value ? theme.colors.gray[0] : undefined,
            paddingTop: '7px',
            paddingBottom: '7px',
          }}
        >
          {item.label}
        </Combobox.Option>
      ));
  }, [options, search, theme.colors.gray, value]);

  useEffect(() => {
    if (value) setInnerValue(value);
  }, [value]);

  return (
    <Combobox
      store={combobox}
      withinPortal={true}
      position='bottom'
      disabled={disabled}
      middlewares={{ flip: false, shift: false }}
      onOptionSubmit={(val) => {
        setInnerValue(val);
        onSelected(val);
        combobox.closeDropdown();
      }}
    >
      <Combobox.Target>
        <InputBase
          pointer
          type='button'
          error={error}
          component='button'
          disabled={disabled}
          sx={{ overflow: 'hidden' }}
          rightSectionPointerEvents='none'
          rightSection={<Combobox.Chevron />}
          onClick={() => combobox.toggleDropdown()}
        >
          {options.find((item) => item.value === innerValue)?.label || (
            <Input.Placeholder color='gray.4'>{placeholder}</Input.Placeholder>
          )}
        </InputBase>
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Search
          value={search}
          sx={{
            '.mantine-Combobox-section[data-position="left"]': {
              width: '20px',
              paddingTop: '5px',
            },
            '.mantine-Combobox-input': {
              paddingInlineStart: '25px',
              borderBottom: 'none',
              paddingTop: '5px',
              fontSize: '14px',
            },
          }}
          leftSection={
            <div
              style={{
                width: '2px',
                height: '70%',
                backgroundColor: theme.colors.navy[1],
              }}
            ></div>
          }
          leftSectionPointerEvents='none'
          onChange={(event) => setSearch(event.currentTarget.value)}
          placeholder={t('modal_operator.input_placeholder')}
        />
        <Combobox.Options
          sx={{
            maxHeight: 200,
            overflowY: 'auto',
            overflowX: 'hidden',
            marginTop: '-4px',
            marginRight: '-5px',
          }}
        >
          {innerOptions.length > 0 ? (
            innerOptions
          ) : (
            <Combobox.Empty>{t('crm_widget.search_no_match')}</Combobox.Empty>
          )}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default SearchableDropdownSelect;
