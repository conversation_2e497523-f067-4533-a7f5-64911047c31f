import { Box, Button, Flex, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
import { useUserContext } from '../../../userContext';
import { useWidgetContext } from '../../../widgetContext';
import { IWidget } from '../../models';

const useStyles = createStyles((theme) => ({
  boxItem: {
    border: '1px solid #C1C2C5',
    borderColor: theme.colors.gray[3],
    padding: '20px',
    borderRadius: '6px',
    boxShadow:
      '0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05),rgba(0, 0, 0, 0.05) 0 0.625rem 0.9375rem -0.3125rem,rgba(0, 0, 0, 0.04) 0 0.4375rem 0.4375rem -0.3125rem',
    minHeight: '140px',
    cursor: 'pointer',
    '&:hover': {
      borderColor: theme.colors.navy[0],
    },
  },
  nameItem: {
    color: theme.colors.dark[4],
    maxWidth: '340px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 1,
    WebkitBoxOrient: 'vertical',
  },
  installedNameText: {
    maxWidth: '202px',
  },
  textItem: {
    color: theme.colors.gray[7],
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
  },
  pointerCursor: {
    cursor: 'pointer',
  },
  flexChildItem: {
    flex: 1,
  },
}));

interface WidgetItemProps {
  item: IWidget;
}

const WidgetItem: React.FC<WidgetItemProps> = ({ item }) => {
  const { t } = useWidgetContext();
  const { classes } = useStyles();
  const { isManager } = useUserContext();

  return (
    <Box className={classes.boxItem}>
      <Flex gap='md' justify='flex-start' align='flex-start' direction='row'>
        <Box className={classes.flexChildItem}>
          <Flex direction='row' align='center' justify='space-between' mb={12}>
            <Title
              order={4}
              className={`${classes.nameItem} ${item.installed && classes.installedNameText}`}
            >
              {item.name}
            </Title>
            <Button
              variant='filled'
              color='navy.0'
              radius='md'
              bg={isManager ? 'navy.0' : 'navy.2'}
              size='xs'
            >
              {t('widget.install')}
            </Button>
          </Flex>
          <Box mt={16}>
            <Text fz='sm' className={classes.textItem}>
              {item.description}
            </Text>
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

export default WidgetItem;
