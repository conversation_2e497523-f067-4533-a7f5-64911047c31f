import { ActionIcon, Flex, Group, ScrollArea, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight, IconWindowMaximize, IconX } from '@tabler/icons-react';
import { useEffect, useMemo } from 'react';
import BarLoader from '../../components/barLoader';
import { createMessageId } from '../../utils/message';
import MessageItem from '../Chatbox/MessageHolders/MessageItem';
import { useSearchPageContext } from './SearchPageContext';
import { usePreviewMessages } from './usePreviewMessages';

type Props = {
  height: number;
};

const HIGHLIGHT_ITEM_ID = 'message_to_highlight_or_scroll_into_view';

const useStyle = createStyles(() => ({
  scrollStyle: {
    overflow: 'auto',
    paddingRight: '20px',
    paddingLeft: '0px',
    paddingTop: '10px',
    overscrollBehavior: 'contain',
  },
}));
export const PreviewMessages = ({ height }: Props) => {
  const {
    setSelectPreviewItem,
    selectedPreViewItem,
    selectedTeamName,
    gotoInboxPageToViewConversation,
  } = useSearchPageContext();

  if (!selectedPreViewItem) return null;

  return (
    <Flex
      maw={547}
      miw={547}
      bg='white'
      p={'1rem'}
      direction='column'
      h={`calc(${height}px - 1rem)`}
      sx={{ borderRadius: '10px' }}
    >
      <Group pb={10} justify='space-between'>
        <Group gap={5}>
          <Text truncate c='#7A7A7A' size={rem(12)}>
            {selectedTeamName}
          </Text>
          <IconChevronRight size={15} color='#7A7A7A' />
          <Text color='#7A7A7A' size={rem(12)}>
            All
          </Text>
        </Group>

        <Group gap={10}>
          <ActionIcon
            color='navy.0'
            variant='transparent'
            onClick={gotoInboxPageToViewConversation}
          >
            <IconWindowMaximize />
          </ActionIcon>
          <ActionIcon
            color='navy.0'
            variant='transparent'
            onClick={() => setSelectPreviewItem(undefined)}
          >
            <IconX />
          </ActionIcon>
        </Group>
      </Group>
      <MessagesPreviewList />
    </Flex>
  );
};

const MessagesPreviewList = () => {
  const { classes } = useStyle();
  const { selectedPreViewItem } = useSearchPageContext();
  const { isValidating, messages, lang } = usePreviewMessages();

  const highlightId = useMemo(() => {
    return (selectedPreViewItem || '')?.split('##')?.[0];
  }, [selectedPreViewItem]);

  useEffect(() => {
    if (!isValidating) {
      setTimeout(() => {
        const element = document.getElementById(HIGHLIGHT_ITEM_ID);
        element?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        });
      }, 200);
    }
  }, [isValidating, selectedPreViewItem]);

  return (
    <Flex sx={{ flexGrow: 1, overflowY: 'auto' }}>
      <Flex
        sx={{ flexGrow: 1, display: isValidating ? 'flex' : 'none' }}
        justify={'center'}
        align={'center'}
      >
        <BarLoader />
      </Flex>
      <ScrollArea
        h={'100%'}
        type='never'
        className={classes.scrollStyle}
        sx={{
          display: isValidating ? 'none' : 'flex',
          paddingInline: 10,
          flexGrow: 1,
        }}
      >
        <Stack className='Message History List' gap={'md'}>
          {messages.map((message, idx) => {
            if (message.id === highlightId) {
              return (
                <div id={HIGHLIGHT_ITEM_ID} key={createMessageId(message)}>
                  <MessageItem
                    idx={idx}
                    message={message}
                    lang={lang}
                    typeDisplay='searchBox'
                    isTheLastMessage={false}
                    key={createMessageId(message)}
                    highlight={message.id === highlightId}
                  />
                </div>
              );
            }
            return (
              <MessageItem
                idx={idx}
                message={message}
                lang={lang}
                typeDisplay='searchBox'
                isTheLastMessage={false}
                key={createMessageId(message)}
                highlight={message.id === highlightId}
              />
            );
          })}
        </Stack>
      </ScrollArea>
    </Flex>
  );
};
