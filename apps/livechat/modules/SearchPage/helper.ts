import { historyPushState } from '../../utils/queryParam';
import { SearchResultItemType } from './type';

export function getSignatureString(item: SearchResultItemType) {
  return `${item.id}##${item.conversationId}##${item.created}##${item.team?.id || 'no_data'}##${
    item?.conversation?.status || 'new'
  }`;
}

export function compareSignatureString(item: SearchResultItemType, strCompare: string) {
  const tmpStr = getSignatureString(item);
  return tmpStr === strCompare;
}

export function reviseDataFromString(signatureString: string) {
  const [id, conversationId, created, teamId, conversationStatus] = signatureString.split('##');
  return {
    id,
    created,
    cId: conversationId,
    status: conversationStatus,
    team: teamId !== 'no_data' ? teamId : undefined,
    per_page: 20,
  };
}

export function getQueryStringFromObj(data: Object) {
  return Object.entries(data)
    .filter((entry) => Boolean(entry[1]))
    .map(([key, value]) => {
      return `${key}=${encodeURIComponent(value)}`;
    })
    .join('&');
}

export const syncQueryParamsToCurrUri = (queries: Record<string, string | number>) => {
  const queryString = getQueryStringFromObj(queries);

  historyPushState(queryString);
};
