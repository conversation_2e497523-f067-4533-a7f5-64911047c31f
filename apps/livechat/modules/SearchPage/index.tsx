import {
  ActionIcon,
  Center,
  Container,
  Flex,
  Group,
  Loader,
  Text,
  TextInput,
  Tooltip,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useElementSize } from '@mantine/hooks';
import { IconInfoCircle, IconSearch, IconX } from '@tabler/icons-react';
import { useMemo } from 'react';
import ConversationSvg from '../Conversations/ConversationList/NoConversation/ConversationSvg';
import { PreviewMessages } from './PreviewMessages';
import { ResultList } from './ResultList';
import { SearchPageContextProvider, useSearchPageContext } from './SearchPageContext';

const useStyle = createStyles((theme) => ({
  input: {
    '& input:focus': {
      border: `1px solid ${theme.colors.navy[0]}`,
      boxShadow: `0px 0px 1px 1px #1F84F4CC, 0 0 4px 0px #81dcffad`,
    },
  },

  customToolTip: {
    '& .mantine-Tooltip-tooltip': {
      boxShadow: '0 0 2px #a6a6a6',
    },
    '& .mantine-Tooltip-tooltip .mantine-Tooltip-arrow': {
      boxShadow: '-1px -1px 1px #a6a6a6',
    },
  },
}));

const SearchPage = () => {
  const { classes } = useStyle();
  const { ref: containerRef, height: containerHeight, width: containerWidth } = useElementSize();
  const {
    t,
    data,
    totalResult,
    isNewlySearch,
    currSearchInput,
    isPagingOrReload,
    handleSearch,
    setCurrSearchInput,
  } = useSearchPageContext();

  const showResult = useMemo(() => {
    return !isNewlySearch && !!totalResult;
  }, [isNewlySearch, totalResult]);

  return (
    <Flex
      p={'0'}
      w={'100%'}
      direction='column'
      sx={{
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#F9F9FA',
        height: `calc(100dvh - var(--app-shell-header-height, 0px) + 0rem)`,
      }}
    >
      <Center mt={30} mb='lg'>
        <Flex direction='column'>
          <TextInput
            w={400}
            value={currSearchInput}
            className={classes.input}
            placeholder={t('search_label')}
            rightSection={
              isPagingOrReload ? (
                <Loader size='xs' color='#1F84F4' />
              ) : (
                !!currSearchInput && (
                  <ActionIcon
                    color='gray.6'
                    variant='transparent'
                    onClick={() => setCurrSearchInput('')}
                  >
                    <IconX size={16} />
                  </ActionIcon>
                )
              )
            }
            leftSection={<IconSearch size={16} />}
            onKeyDown={(e) => handleSearch(e as unknown as KeyboardEvent)}
            onChange={(e) => setCurrSearchInput(e.target.value)}
          />
          <Group gap={0} mt='0.5rem' justify='flex-start' className={classes.customToolTip}>
            <Text
              c='#8F8F8F'
              size={rem(12)}
              sx={{ visibility: totalResult > 0 ? 'visible' : 'hidden' }}
            >
              {t('search_result_count', { count: totalResult })}
            </Text>
            <Tooltip
              withArrow
              color='white'
              arrowSize={6}
              position='bottom'
              label={
                <Text c='#525252' size={rem(14)} fw={400}>
                  {t('warningResult')}
                </Text>
              }
              styles={{
                tooltip: {
                  boxShadow: '0 0 2px #a6a6a6',
                },
                arrow: {
                  boxShadow: '-1px -1px 1px #a6a6a6',
                },
              }}
            >
              <ActionIcon
                ml={5}
                color='gray.6'
                variant='transparent'
                style={{ visibility: totalResult > 0 ? 'visible' : 'hidden' }}
                sx={(theme) => ({
                  '&:hover': {
                    color: theme.colors.navy[0],
                  },
                })}
              >
                <IconInfoCircle size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </Flex>
      </Center>
      <LoadingAndNoData show={!showResult} isSearching={isNewlySearch} />
      <Container
        fluid
        ref={containerRef}
        style={{ flexGrow: 1, display: showResult ? 'flex' : 'none', flexDirection: 'row' }}
        sx={(theme) => ({
          width: '100%',
          position: 'relative',
          backgroundColor: theme.colors.gray[0],
          paddingLeft: 0,
          paddingRight: 0,
        })}
      >
        <div
          style={{
            width: `${containerWidth}px`,
            height: `${containerHeight}px`,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'flex-start',
            gap: '20px',
          }}
        >
          <ResultList searchData={data} height={containerHeight} />
          <PreviewMessages height={containerHeight} />
        </div>
      </Container>
    </Flex>
  );
};

const LoadingAndNoData = ({ show, isSearching }: { show: boolean; isSearching: boolean }) => {
  const { t } = useSearchPageContext();

  return (
    <Flex
      gap={21}
      align={'center'}
      justify={'center'}
      direction={'column'}
      sx={{ flexGrow: 1 }}
      style={{ display: show ? 'flex' : 'none' }}
    >
      {isSearching ? (
        <Loader type='bars' color='navy.0' />
      ) : (
        <>
          <ConversationSvg />
          <Text c={'#909296'} size={'14px'} fw={400}>
            {t('no_search_result')}
          </Text>
        </>
      )}
    </Flex>
  );
};

export const SearchPageScreen = () => {
  return (
    <SearchPageContextProvider>
      <SearchPage />
    </SearchPageContextProvider>
  );
};
