import { useCallback, useEffect, useMemo, useState } from 'react';
import useS<PERSON>, { preload } from 'swr';
import { SearchBoxAPI } from '../../services/api/SearchBoxApi';
import { NO_DATA_SET, SearchResultDataType } from './type';

const perPage = 6;
// eslint-disable-next-line no-unused-vars
const enum MoveDirection {
  // eslint-disable-next-line no-unused-vars
  PREV = 'prev',
  // eslint-disable-next-line no-unused-vars
  NEXT = 'next',
}
export const useSearchMessages = (searchStr: string) => {
  const [loadingFlag, setLoadingFlag] = useState<MoveDirection>(MoveDirection.PREV);
  const [idLoadingFlag, setIdLoadingFlag] = useState<string>();
  const {
    data,
    isLoading,
    isValidating,
    mutate: reload,
  } = useSWR<SearchResultDataType | null>(
    searchStr ? ['searchMessageInSearchBox', searchStr, perPage, loadingFlag, idLoadingFlag] : null,
    () => SearchBoxAPI.searchMessagesForSearchBox(searchStr, perPage, loadingFlag, idLoadingFlag),
    {
      revalidateOnMount: true,
    }
  );

  const dataPagination = useMemo(() => {
    return {
      data: data?.data || [],
      pagination: {
        next: data?.pagination?.next || null,
        prev: data?.pagination?.prev || null,
        perPage: data?.pagination?.perPage || 0,
        totalResult: data?.pagination?.total || data?.data?.length || 0,
      },
      actualTextSearch: data?.pagination?.searchParams?.text || NO_DATA_SET,
      filter: data?.pagination?.searchParams?.filter || null,
      duration: data?.pagination?.searchParams?.duration || 0,
    };
  }, [data]);

  const goBack = useCallback(() => {
    // console.log('move back', dataPagination.pagination.prev);
    if (!!dataPagination.pagination.prev) {
      setLoadingFlag(MoveDirection.PREV);
      setIdLoadingFlag(dataPagination.pagination.prev);
      setTimeout(() => reload(), 100);
    }
  }, [dataPagination.pagination.prev, reload]);

  const moveNext = useCallback(() => {
    // console.log('move next', dataPagination.pagination.next);

    if (!!dataPagination.pagination.next) {
      setLoadingFlag(MoveDirection.NEXT);
      setIdLoadingFlag(dataPagination.pagination.next);
      setTimeout(() => reload(), 100);
    }
  }, [dataPagination.pagination.next, reload]);

  const clearMoveIdLoading = useCallback(() => setIdLoadingFlag(undefined), []);

  const handlePrefetchNextPage = useCallback(() => {
    const key = [
      'searchMessageInSearchBox',
      searchStr,
      perPage,
      MoveDirection.NEXT,
      dataPagination.pagination.next,
    ];
    preload(key, () =>
      SearchBoxAPI.searchMessagesForSearchBox(
        searchStr,
        perPage,
        MoveDirection.NEXT,
        dataPagination.pagination.next
      )
    );
  }, [dataPagination.pagination.next, searchStr]);

  const handlePrefetchBackPage = useCallback(() => {
    const key = [
      'searchMessageInSearchBox',
      searchStr,
      perPage,
      MoveDirection.PREV,
      dataPagination.pagination.prev,
    ];
    preload(key, () =>
      SearchBoxAPI.searchMessagesForSearchBox(
        searchStr,
        perPage,
        MoveDirection.PREV,
        dataPagination.pagination.prev
      )
    );
  }, [dataPagination.pagination.prev, searchStr]);

  useEffect(() => {
    if (!!dataPagination.pagination.prev) {
      handlePrefetchBackPage();
    }
  }, [dataPagination.pagination.prev, handlePrefetchBackPage]);

  useEffect(() => {
    if (!!dataPagination.pagination.next) {
      handlePrefetchNextPage();
    }
  }, [dataPagination.pagination.next, handlePrefetchNextPage]);

  return useMemo(
    () => ({
      isLoading,
      isValidating,
      ...dataPagination,
      noResult: dataPagination.data.length,
      reload,
      goBack,
      moveNext,
      clearMoveIdLoading,
    }),
    [dataPagination, goBack, moveNext, isLoading, isValidating, reload, clearMoveIdLoading]
  );
};
