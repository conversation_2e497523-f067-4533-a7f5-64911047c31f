import {
  Avatar,
  Container,
  Flex,
  Group,
  Highlight,
  Menu,
  Text,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { displayFullDateTime, formatDate } from '@resola-ai/utils';
import { useMemo } from 'react';
import { NO_ASSIGNEE_KEY_CHECK } from '../../constants';
import Bookmarked from '../Conversations/ConversationList/Conversation/Bookmarked';
import { useSearchPageContext } from './SearchPageContext';
import { compareSignatureString, getSignatureString } from './helper';
import { SearchResultItemType } from './type';

export const ResultItem = ({ resultItem }: { resultItem: SearchResultItemType }) => {
  const theme = useMantineTheme();
  const { t, getAssigneeName, highlightedText, selectedPreViewItem, setSelectPreviewItem } =
    useSearchPageContext();

  const name = useMemo(() => {
    const tmpName =
      getAssigneeName(resultItem?.conversation?.assignee, resultItem?.conversation?.assigneeId) ||
      NO_ASSIGNEE_KEY_CHECK;

    return tmpName === NO_ASSIGNEE_KEY_CHECK
      ? t('noAssignee')
      : t('hasAssigneeName', { name: tmpName });
  }, [
    getAssigneeName,
    resultItem?.conversation?.assignee,
    resultItem?.conversation?.assigneeId,
    t,
  ]);

  const isSelected = useMemo(() => {
    if (!selectedPreViewItem) return false;
    return compareSignatureString(resultItem, selectedPreViewItem);
  }, [resultItem, selectedPreViewItem]);

  return (
    <Container
      py={10}
      w={'100%'}
      mb={'10px'}
      maw={'628px'}
      bg={isSelected ? theme.colors.gray[1] : 'white'}
      style={{ border: `1px solid ${theme.colors.gray[3]}`, borderRadius: '10px' }}
      sx={() => ({
        cursor: 'pointer',
        ':hover': {
          backgroundColor: theme.colors.gray[1],
        },
      })}
      onClick={() => {
        setSelectPreviewItem(getSignatureString(resultItem));
      }}
    >
      <Group justify='space-between'>
        <Text size={rem(12)} c='dark.3'>
          {resultItem?.team?.name || ''}
        </Text>
        <Group justify='flex-end' gap={0}>
          <Text
            truncate
            style={{
              lineClamp: 1,
              fontWeight: 400,
              fontSize: '11px',
              lineHeight: '16px',
              color: theme.colors.gray[6],
            }}
            title={name}
          >
            {name}
          </Text>
          <Bookmarked show={resultItem?.conversation?.isBookmark} />
        </Group>
      </Group>
      <Flex
        mt={10}
        mb={2}
        wrap={'nowrap'}
        align={'center'}
        direction={'row'}
        justify={'space-between'}
      >
        <Group gap={0} justify='flex-start' style={{ flexWrap: 'nowrap' }}>
          <Avatar
            mr={'sm'}
            radius='xl'
            alt={'Avatar'}
            color={'cyan'}
            src={resultItem.sender?.picture}
            style={{ height: '35px', width: '35px' }}
          ></Avatar>
          <Text
            mr={'sm'}
            ta={'left'}
            fw={'bold'}
            size={rem(16)}
            truncate='end'
            c={COLOR_DEFAULT_BLACK}
            style={{ maxWidth: '140px' }}
          >
            {resultItem?.sender.name || resultItem?.sender?.id}
          </Text>
          <Menu trigger={'hover'} width={'auto'} position={'top'} withArrow shadow='md'>
            <Menu.Target>
              <Text
                ta='left'
                c='gray.6'
                size={rem(11)}
                truncate
              >{`ID ${resultItem?.sender?.id?.slice(0, 5)}`}</Text>
            </Menu.Target>
            <Menu.Dropdown maw={'500px'}>
              <Text ta='left' size={'xs'} truncate p={5}>
                {resultItem?.sender.id}
              </Text>
            </Menu.Dropdown>
          </Menu>
        </Group>
        <Text
          truncate
          c='gray.6'
          size={'xs'}
          sx={{
            fontSize: '11px',
          }}
          title={displayFullDateTime(resultItem?.created)}
        >
          {formatDate(new Date(resultItem?.created || ''))}
        </Text>
      </Flex>
      <Flex align={'center'} justify={'space-between'} mih={35}>
        <Highlight
          fw={400}
          lineClamp={2}
          size={rem(14)}
          highlight={highlightedText}
          styles={(theme) => ({
            root: {
              lineHeight: '1.22rem',
              color: theme.colors.gray[7],
              mark: {
                color: theme.colors.gray[7],
              },
            },
          })}
        >
          {resultItem?.data.text}
        </Highlight>
      </Flex>
    </Container>
  );
};
