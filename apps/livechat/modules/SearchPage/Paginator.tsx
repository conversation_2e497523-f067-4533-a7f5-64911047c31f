import { ActionIcon, Button, Center, Space, Text } from '@mantine/core';
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react';
import { useSearchPageContext } from './SearchPageContext';

export const Paginator = () => {
  const { pagination, t, goBack, moveNext } = useSearchPageContext();
  const { next, prev } = pagination;
  // if totalResult is smaller than perPage then no need to show
  // if (totalResult <= perPage) return null;
  return (
    <Center pb={'1rem'}>
      <Button.Group>
        <Button w={120} radius={20} variant='default' disabled={!prev} onClick={goBack}>
          <ActionIcon color={!!prev ? 'navy.0' : 'gray.4'} variant='transparent'>
            <IconChevronLeft size={30} />
          </ActionIcon>
          <Space w={10} />
          <Text color={!!prev ? 'navy.0' : undefined} fz={14} fw={700}>
            {t('preBtn')}
          </Text>
        </Button>
        <Button w={120} radius={20} variant='default' disabled={!next} onClick={moveNext}>
          <Text color={!!next ? 'navy.0' : undefined} fz={14} fw={700}>
            {t('nextBtn')}
          </Text>
          <Space w={10} />
          <ActionIcon color={!!next ? 'navy.0' : 'gray.4'} variant='transparent'>
            <IconChevronRight size={30} />
          </ActionIcon>
        </Button>
      </Button.Group>
    </Center>
  );
};
