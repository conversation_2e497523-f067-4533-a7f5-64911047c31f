import { useCounter, useDebouncedState } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { MAIN_ROUTE } from '../../constants';
import { throttle } from '../../utils/throttle';
import { useTeamContext } from '../teamContext';
import { useUserContext } from '../userContext';
import { getQueryStringFromObj, reviseDataFromString, syncQueryParamsToCurrUri } from './helper';
import { NO_DATA_SET } from './type';
import { useSearchMessages } from './useSearchMessages';

const MAX_RETRY_TIMES = 5;

export const useSearchPage = () => {
  const { t } = useTranslate('workspace');
  const router = useRouter();
  const query = router.query;
  const { getAssigneeName } = useUserContext();
  const { teamList } = useTeamContext();
  const [currSearchInput, setCurrSearchInput] = useState('');
  const [wannaSearchString, setWannaSearchString] = useDebouncedState('', 200, { leading: true });
  const [count, counter] = useCounter(0, { min: 0, max: MAX_RETRY_TIMES });

  // read the format at apps/livechat/modules/SearchPage/helper.ts -> getSignatureString()
  const [selectedPreViewItem, setSelectPreviewItem] = useState<string>();

  const {
    data,
    isLoading,
    pagination,
    isValidating: isPagingOrReload,
    actualTextSearch,
    reload,
    goBack,
    moveNext,
    clearMoveIdLoading,
  } = useSearchMessages(wannaSearchString);

  const isNewlySearch = useMemo(() => isLoading && isPagingOrReload, [isLoading, isPagingOrReload]);

  const queryParam = useMemo(() => {
    let searchText = query?.search_text;
    if (Array.isArray(searchText)) {
      searchText = searchText?.[0] || '';
    } else {
      searchText = `${searchText}`;
    }
    searchText = decodeURIComponent(searchText);
    return decodeURIComponent(searchText);
  }, [query?.search_text]);

  const selectedTeamName = useMemo(() => {
    let teamName = '';
    if (!!selectedPreViewItem) {
      const { team } = reviseDataFromString(selectedPreViewItem);
      teamName = teamList.find((item) => item.id === team)?.name;
    }
    return teamName;
  }, [selectedPreViewItem, teamList]);

  const highlightedText = useMemo(() => {
    if (!wannaSearchString) return '';
    if (!!actualTextSearch && actualTextSearch !== NO_DATA_SET) {
      return actualTextSearch;
    }
    return wannaSearchString;
  }, [actualTextSearch, wannaSearchString]);

  const reloadThrottle = throttle(() => reload());

  const handleSearch = useCallback(
    (e: KeyboardEvent) => {
      const searchInput = currSearchInput?.trim() || '';
      if (e.key !== 'Enter' || !searchInput.length) return;
      syncQueryParamsToCurrUri({ search_text: searchInput });
      setSelectPreviewItem(undefined);
      clearMoveIdLoading();
      if (searchInput !== wannaSearchString) {
        counter.reset();
        setWannaSearchString(searchInput);
      }
      if (searchInput === wannaSearchString && !isPagingOrReload && count < MAX_RETRY_TIMES) {
        counter.increment();
        reloadThrottle();
      }
    },
    [
      count,
      counter,
      currSearchInput,
      isPagingOrReload,
      wannaSearchString,
      reloadThrottle,
      clearMoveIdLoading,
      setWannaSearchString,
    ]
  );

  const gotoInboxPageToViewConversation = useCallback(() => {
    if (selectedPreViewItem) {
      const data = reviseDataFromString(selectedPreViewItem);
      // eslint-disable-next-line no-unused-vars
      const { id, created, ...rest } = data;
      const queryString = getQueryStringFromObj(rest);
      router.push(`${MAIN_ROUTE}?${queryString}`);
    }
  }, [router, selectedPreViewItem]);

  useEffect(() => {
    if (!queryParam) return;
    setCurrSearchInput(queryParam);
    setWannaSearchString(queryParam);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParam]);

  return useMemo(
    () => ({
      t,
      data,
      pagination,
      queryParam,
      isNewlySearch,
      highlightedText,
      currSearchInput,
      isPagingOrReload,
      actualTextSearch,
      selectedTeamName,
      wannaSearchString,
      selectedPreViewItem,
      totalResult: pagination?.totalResult,
      goBack,
      moveNext,
      handleSearch,
      getAssigneeName,
      setCurrSearchInput,
      clearMoveIdLoading,
      setSelectPreviewItem,
      syncQueryParamsToCurrUri,
      gotoInboxPageToViewConversation,
    }),
    [
      t,
      data,
      pagination,
      queryParam,
      isNewlySearch,
      highlightedText,
      currSearchInput,
      actualTextSearch,
      isPagingOrReload,
      selectedTeamName,
      wannaSearchString,
      selectedPreViewItem,
      goBack,
      moveNext,
      handleSearch,
      getAssigneeName,
      clearMoveIdLoading,
      setSelectPreviewItem,
      gotoInboxPageToViewConversation,
    ]
  );
};
