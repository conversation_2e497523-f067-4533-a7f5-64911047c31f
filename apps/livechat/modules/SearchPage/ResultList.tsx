import { Flex, ScrollArea } from '@mantine/core';
import { Paginator } from './Paginator';
import { ResultItem } from './ResultItem';
import { useSearchPageContext } from './SearchPageContext';
import { SearchResultItemType } from './type';

export const ResultList = ({
  searchData,
  height,
}: {
  searchData: SearchResultItemType[];
  height: number;
}) => {
  const { selectedPreViewItem } = useSearchPageContext();

  return (
    <Flex
      direction={'column'}
      h={`calc(${height}px - 1rem)`}
      maw={660}
      miw={660}
      sx={
        !!selectedPreViewItem && {
          paddingTop: '1rem',
          background: 'white',
          borderRadius: '10px',
        }
      }
    >
      <Flex direction={'column'} w={'100%'} mb={15} sx={{ flexGrow: 1, overflowY: 'auto' }}>
        <ScrollArea
          h={'100%'}
          type='never'
          sx={{
            paddingInline: '1rem',
            flexGrow: 1,
          }}
        >
          {searchData.map((item) => {
            return <ResultItem key={item.id} resultItem={item} />;
          })}
        </ScrollArea>
      </Flex>
      <Paginator />
    </Flex>
  );
};
