import useSWR from 'swr';
import { SearchBoxAPI } from '../../services/api/SearchBoxApi';
import { useSearchPageContext } from './SearchPageContext';
import { useMemo } from 'react';
import { PreviewMessagesType } from './type';
import { useAppContext } from '../appContext';

export const usePreviewMessages = () => {
  const { responsive } = useAppContext();
  const { selectedPreViewItem } = useSearchPageContext();
  const { lang } = useAppContext();
  const [, conversationId, timeStamp] = (selectedPreViewItem || '').split('##');
  const {
    data,
    isLoading,
    isValidating,
    mutate: reload,
  } = useSWR<PreviewMessagesType | null>(
    !!conversationId && !!timeStamp ? ['previewMessage', conversationId, timeStamp] : null,
    () => SearchBoxAPI.getMessagesForPreview(conversationId, timeStamp)
  );

  return useMemo(
    () => ({
      lang,
      isLoading,
      isValidating,
      noResult: !!data?.data?.length,
      messages: data?.data || [],
      responsive: !!responsive?.responsiveScreen || false,
      reload,
    }),
    [data?.data, isLoading, isValidating, lang, reload, responsive?.responsiveScreen]
  );
};
