import { useMemo } from 'react';
import useSWR from 'swr';
import { SearchBoxAPI } from '../../services/api/SearchBoxApi';
import { useAppContext } from '../appContext';
import { useSearchPageContext } from './SearchPageContext';
import { PreviewMessagesType } from './type';

export const usePreviewMessages = () => {
  const { responsive } = useAppContext();
  const { selectedPreViewItem } = useSearchPageContext();
  const { lang } = useAppContext();
  const [, conversationId, timeStamp] = (selectedPreViewItem || '').split('##');
  const {
    data,
    isLoading,
    isValidating,
    mutate: reload,
  } = useSWR<PreviewMessagesType | null>(
    !!conversationId && !!timeStamp ? ['previewMessage', conversationId, timeStamp] : null,
    () => SearchBoxAPI.getMessagesForPreview(conversationId, timeStamp)
  );

  return useMemo(
    () => ({
      lang,
      isLoading,
      isValidating,
      noResult: !!data?.data?.length,
      messages: data?.data || [],
      responsive: !!responsive?.responsiveScreen || false,
      reload,
    }),
    [data?.data, isLoading, isValidating, lang, reload, responsive?.responsiveScreen]
  );
};
