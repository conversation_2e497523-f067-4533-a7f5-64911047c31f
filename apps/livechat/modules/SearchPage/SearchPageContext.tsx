import { createContext, PropsWithChildren, useContext } from 'react';
import { useSearchPage } from './useSearchPage';

export type SearchPageType = ReturnType<typeof useSearchPage>;

export const SearchPageContext = createContext<SearchPageType | null>(null);

export const SearchPageContextProvider = ({ children }: PropsWithChildren) => {
  const data = useSearchPage();
  return <SearchPageContext.Provider value={data}>{children}</SearchPageContext.Provider>;
};

export const useSearchPageContext = () => {
  const data = useContext(SearchPageContext);
  if (!data) {
    throw new Error("useSearchPageContext should be used in SearchPageContextProvider'");
  }

  return data;
};
