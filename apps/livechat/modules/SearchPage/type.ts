import { IMessage, IMessageData } from '@resola-ai/models';
export type SearchResultItemType = {
  id: string;
  conversationId: string;
  sender: {
    type: string;
    id: string;
    name?: string;
    picture?: string;
  };
  data: IMessageData;
  type: string;
  created: string;
  updated: string;
  team: {
    id: string;
    name: string;
  };
  conversation: {
    id: string;
    status: string;
    isBookmark?: boolean;
    assigneeId?: string;
    assignee?: {
      id: string;
      name: string;
    };
  };
};

export type ResultPaginatorType = {
  perPage: number;
  next?: string | null;
  prev?: string | null;
  total?: number;
  searchParams?: {
    text?: string;
    filter: {
      teamId?: string[];
    };
    duration?: number;
  };
};

export type SearchResultDataType = {
  data: SearchResultItemType[];
  pagination: ResultPaginatorType;
};

export type PreviewMessagesType = {
  data: IMessage[];
  pagination: ResultPaginatorType;
};

export const EVENT_PREFETCH_DATA_NEXT = 'deca-livechat-prefetch-search-box-next-page';
export const EVENT_PREFETCH_DATA_BACK = 'deca-livechat-prefetch-search-box-back-page';
export const NO_DATA_SET = 'NO_DATA';
