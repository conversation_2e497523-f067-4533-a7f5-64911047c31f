import axios from 'axios';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createCustomEventListener } from '@resola-ai/utils';
import AppConfig from '../configs';
import { IConversationAIAdjustmentList } from '../models/ai';
import { delay } from '../utils/common';
import { scrollToAIAdjustmentMessageListBottom } from '../utils/scroll';
import { useAppContext } from './appContext';
import { useConversationActionContext } from './conversationActionContext';
import { IWebsocketResponse } from '../models/websocket';
import { GLOBAL_REALTIME_EVENT_NAME } from '../constants';

const DELAY_TIME = 2000;
type AIAdjustmentProgressType = 'init' | 'start' | 'inprogress' | 'end' | 'exit';

const useOpenAi = () => {
  const [, setAiAutoAdjustmentTexts] = useState<Record<string, string>>({});
  const [conversationAIAdjustmentList, setConversationAIAdjustmentList] = useState<
    IConversationAIAdjustmentList[]
  >([]);
  const [isLoadingAIAdjustment, setIsLoadingAIAdjustment] = useState(false);
  const aiAdjustmentRefIndex = useRef<number>(0);
  const progressAIAdjustmentRef = useRef<AIAdjustmentProgressType>('init');

  const { accessToken } = useAppContext();
  const { currentConversation, currentConversationId } = useConversationActionContext();

  const aiSuggestion = useCallback(
    async (conversationId: string, widgetSettingId: string) => {
      // console.log({ link: `${AppConfig.BASE_URL_API}/ai/suggestions` });

      if (!widgetSettingId || !conversationId) {
        // console.log(
        //     'Do not provide widgetSettingId or conversationId, can not call api for ai suggestion',
        // );
        return {
          flag: 'NO_INFO',
        };
      }
      if (currentConversation?.status === 'completed') {
        // console.log('Completed Conversation does not need to call api for ai suggestion');
        return {
          flag: 'NO_CALLING',
        };
      }
      // if not assignee then do not call api for ai suggestion
      // if (currentConversation?.assigneeId !== userId) {
      //     console.log('Not assignee, do not call api for ai suggestion');
      //     return;
      // }
      try {
        const response = await axios.post(
          `${AppConfig.BASE_URL_API}/ai/suggestions`,
          {
            widgetId: widgetSettingId,
            conversationId: conversationId,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        // console.log('get ai suggestion response data', response.data.data);
        try {
          const receivedSuggestionText = response.data.data;
          return receivedSuggestionText;
        } catch (error) {
          console.log('get ai suggestion response handle have error', error);
        }
      } catch (error) {
        console.log('get ai suggestion response error', error);
        // return error?.response?.data?.message;
        // setWarningText(error?.response?.data?.message);
      }
    },
    [accessToken, currentConversation?.status]
  );

  const aiSuggestionWrapper = useCallback(
    async (conversationId: string, widgetSettingId: string) => {
      await delay(DELAY_TIME);
      return await aiSuggestion(conversationId, widgetSettingId);
    },
    [aiSuggestion]
  );

  /*-------------------------Handle AI Auto Adjustment--------------------------------*/
  const aiAutoAdjustmentTimeoutId = useRef<any>(null);

  const resetAiAdjustmentText = useCallback(() => {
    setAiAutoAdjustmentTexts((prev) => ({
      ...prev,
      [currentConversationId]: '',
    }));
  }, [currentConversationId]);

  const aiAutoAdjustment = useCallback(
    async (message: string, conversationId: string, type?: string) => {
      if (!conversationId) return;

      try {
        const response = await axios.post(
          `${AppConfig.BASE_URL_API}/ai/adjustments`,
          {
            message,
            conversationId,
            type,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        try {
          return response.data;
        } catch (error) {
          console.log('AI Adjustment Error:', error);
        }
      } catch (error) {
        console.log('Get AI Adjustment Error:', error);
      }
    },
    [accessToken]
  );

  const aiAutoAdjustmentWrapper = useCallback(
    async (message: string, conversationId: string, type?: string) => {
      try {
        resetAiAdjustmentText();
        setIsLoadingAIAdjustment(true);
        progressAIAdjustmentRef.current = 'init';
        const res = await aiAutoAdjustment(message, conversationId, type);
        !res && setIsLoadingAIAdjustment(false);
      } catch (error) {
        const errorMessage = error?.response?.data?.message;
        console.log(errorMessage);
      }
    },
    [resetAiAdjustmentText, aiAutoAdjustment]
  );

  useEffect(() => {
    const handleReceiveAIAdjustmentText = (
      event: CustomEvent<{ data: IWebsocketResponse<any> }>
    ) => {
      if (progressAIAdjustmentRef.current === 'exit') return;
      const { data } = event.detail;
      if (data.type !== 'openai.message.adjustment') return;

      scrollToAIAdjustmentMessageListBottom();
      const { content, start, end } = data;

      if (start) {
        progressAIAdjustmentRef.current = 'start';
        setIsLoadingAIAdjustment(true);
      } else if (end) {
        progressAIAdjustmentRef.current = 'end';
        setIsLoadingAIAdjustment(false);
      } else {
        progressAIAdjustmentRef.current = 'inprogress';
      }

      const concatText = (prev: string, current: string) => {
        return prev + current;
      };
      setConversationAIAdjustmentList((prev) => {
        const updatedConversationAIAdjustmentList = [...prev];
        const lastItem = updatedConversationAIAdjustmentList[aiAdjustmentRefIndex.current];
        if (!lastItem) {
          return updatedConversationAIAdjustmentList;
        }
        lastItem.message = concatText(lastItem.message, content);
        lastItem.start = start;
        lastItem.end = end;
        return updatedConversationAIAdjustmentList;
      });
      if (aiAutoAdjustmentTimeoutId.current) {
        clearTimeout(aiAutoAdjustmentTimeoutId.current);
      }
      aiAutoAdjustmentTimeoutId.current = setTimeout(() => {
        scrollToAIAdjustmentMessageListBottom();
      }, 500);
    };

    const unregisterEvent = createCustomEventListener(
      GLOBAL_REALTIME_EVENT_NAME,
      handleReceiveAIAdjustmentText
    );

    // const unregisterEvent = createCustomEventListener(
    //     'deca-livechat-ai-auto-adjustment-content-update',
    //     handleReceiveAIAdjustmentText,
    // );
    return () => unregisterEvent();
  }, [currentConversationId]);

  const updateConversationAIAdjustmentList = useCallback(
    (item: IConversationAIAdjustmentList) => {
      aiAdjustmentRefIndex.current = conversationAIAdjustmentList.length;
      setConversationAIAdjustmentList([...conversationAIAdjustmentList, item]);
    },
    [conversationAIAdjustmentList]
  );

  const resetConversationAIAdjustmentList = useCallback(() => {
    setConversationAIAdjustmentList([]);
  }, []);

  const resetIsLoadingAIAdjustment = useCallback(() => {
    setIsLoadingAIAdjustment(false);
  }, []);

  const exitProgressAIAdjustmentRef = useCallback(() => {
    progressAIAdjustmentRef.current = 'exit';
  }, []);

  return useMemo(
    () => ({
      isLoadingAIAdjustment,
      conversationAIAdjustmentList,
      resetIsLoadingAIAdjustment,
      exitProgressAIAdjustmentRef,
      updateConversationAIAdjustmentList,
      resetConversationAIAdjustmentList,
      aiSuggestion: aiSuggestionWrapper,
      aiSuggestionManualTrigger: aiSuggestion,
      aiAutoAdjustment: aiAutoAdjustmentWrapper,
      resetAiAdjustmentText: resetAiAdjustmentText,
    }),
    [
      isLoadingAIAdjustment,
      conversationAIAdjustmentList,
      aiSuggestion,
      aiSuggestionWrapper,
      resetAiAdjustmentText,
      aiAutoAdjustmentWrapper,
      resetIsLoadingAIAdjustment,
      exitProgressAIAdjustmentRef,
      resetConversationAIAdjustmentList,
      updateConversationAIAdjustmentList,
    ]
  );
};

export type OpenAiContextType = ReturnType<typeof useOpenAi>;

const context = createContext<OpenAiContextType | null>(null);

export const OpenAiContextProvider: React.FC<any> = ({ children }) => {
  const value = useOpenAi();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useOpenAiContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useOpenAiContext must be used inside OpenAiContextProvider');
  }

  return value;
};
