import { Autocomplete, Flex, rem, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useMemo } from 'react';
import { useUserContext } from '../../../userContext';
interface Props {
  // eslint-disable-next-line no-unused-vars
  onChange: (team: any) => void;
}
const TeamSection: React.FC<Props> = ({ onChange }) => {
  const { userTeams } = useUserContext();
  const { t } = useTranslate('workspace');

  const teamUserNames = useMemo(() => {
    if (!userTeams || !userTeams.length) return [];
    return userTeams.map((team: any) => team.name);
  }, [userTeams]);

  const handleSelectTeam = useCallback(
    (teamName: string) => {
      if (!userTeams || !userTeams.length) return;
      onChange(userTeams.find((team: any) => team.name === teamName));
    },
    [onChange, userTeams]
  );

  if (userTeams.length === 0) {
    return (
      <Flex justify={'center'} mt={20} mb={20}>
        <Text>{t('popup.selfAssignTeam.noTeam')}</Text>
      </Flex>
    );
  }

  return (
    <Flex
      direction={'column'}
      align={'flex-start'}
      sx={{
        '.mantine-Autocomplete-dropdown': {
          zIndex: 999,
        },
      }}
      gap={10}
    >
      <Text
        c={'#495057'}
        fw={400}
        size={rem(14)}
        sx={{ lineHeight: '21.7px', whiteSpace: 'pre-line' }}
      >
        {t('popup.selfAssignTeam.description1')}
      </Text>

      <Flex align={'flex-start'} gap={15} w='100%'>
        <Flex style={{ justifyContent: 'center', alignItems: 'center' }} h='30px'>
          <Text fw={500} size={rem(14)} c={'#212529'}>
            {t('popup.selfAssignTeam.label')}
          </Text>
          <Text fw={500} size={rem(14)} c={'#FF6B6B'}>
            *
          </Text>
        </Flex>

        <Autocomplete
          data={teamUserNames}
          onChange={handleSelectTeam}
          placeholder={t('popup.selfAssignTeam.placeholder')}
          sx={(theme) => ({
            flex: 1,
            input: {
              fontFamily: 'Open Sans',
              fontWeight: 400,
              fontSize: '14px',
              lineHeight: '18.6px',
              // on focus, change the border color
              ':focus': {
                borderColor: theme.colors.navy[1],
              },
            },
            option: {
              fontSize: '14px',
            },
          })}
        />
      </Flex>
    </Flex>
  );
};

export default TeamSection;
