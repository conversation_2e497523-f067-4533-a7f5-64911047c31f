import { Button, Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React, { useCallback } from 'react';
import { delay } from '../../../utils/common';
import { useConversationActionContext } from '../../conversationActionContext';
import { useUserContext } from '../../userContext';
import TeamSection from './TeamSection';

interface Props {
  close: () => void;
}

const useStyle = createStyles((theme) => ({
  buttonConfirm: {
    minWidth: '84px',
    backgroundColor: theme.colors.navy[0],
    borderRadius: '8px',
    fontWeight: 600,
    fontSize: '14px',
    '&:hover': {
      backgroundColor: theme.colors.navy[1],
    },
  },
  buttonCancel: {
    minWidth: '86px',
    backgroundColor: 'white',
    borderWidth: '1.5px',
    borderRadius: '8px',
    fontWeight: 600,
    fontSize: '14px',
    color: '#5C5F66',
    borderColor: '#5C5F66',
    '&:hover': {
      backgroundColor: '#f0f0f0',
    },
  },
}));

const PopupDetail: React.FC<Props> = ({ close }) => {
  const { classes } = useStyle();
  const { userProfile, userTeams } = useUserContext();
  const [selectedTeam, setSelectedTeam] = React.useState<any>(null);
  const { t } = useTranslate('workspace');
  const {
    currentConversation,
    handleSelfAssignEntityInCharge,
    handleAssignEntityInCharge,
    isLoadingConversation,
  } = useConversationActionContext();

  const submitSelfAssign = useCallback(async () => {
    // assign this operator to this conversation
    console.log('Self Assign');
    await handleSelfAssignEntityInCharge();
    close();
  }, [close, handleSelfAssignEntityInCharge]);

  const submitTeamAssign = useCallback(
    async (teamId: string) => {
      // assign this operator to this conversation
      handleAssignEntityInCharge(userProfile.id, teamId);
      await delay(1000);
      close();
    },
    [close, handleAssignEntityInCharge, userProfile.id]
  );

  if (currentConversation && !currentConversation.teamId) {
    // show ui to assign this conversation to a team
    return (
      <>
        <Flex p={0} direction={'column'} align={'flex-start'} gap={10}>
          <Text
            ta='left'
            mb={10}
            sx={{
              fontWeight: 500,
              fontSize: '16px',
              lineHeight: '24.8px',
            }}
          >
            {t('popup.selfAssign.title')}
          </Text>
          <TeamSection onChange={setSelectedTeam} />
          <Text
            ta={'left'}
            c={'#495057'}
            sx={{
              fontWeight: 400,
              fontSize: '14px',
              lineHeight: '21.7px',
              whiteSpace: 'pre-line',
            }}
          >
            {t('popup.selfAssign.description')}
          </Text>
        </Flex>
        <Flex
          justify='flex-end'
          gap={20}
          m={30}
          mr={0}
          mb={0}
          sx={{
            display: userTeams.length > 0 ? 'flex' : 'none',
          }}
        >
          <Button disabled={isLoadingConversation} className={classes.buttonCancel} onClick={close}>
            {t('popup.selfAssign.cancel')}
          </Button>
          <Button
            disabled={isLoadingConversation}
            loading={isLoadingConversation}
            className={classes.buttonConfirm}
            onClick={async () => {
              // console.log('selectedTeam', selectedTeam);
              if (selectedTeam && selectedTeam.id) {
                await submitTeamAssign(selectedTeam.id);
              }
            }}
          >
            {t('popup.selfAssign.submit')}
          </Button>
        </Flex>
      </>
    );
  }

  return (
    <>
      <Flex p={0} direction={'column'} align={'flex-start'} gap={10}>
        <Text
          ta='center'
          sx={{
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '24.8px',
          }}
        >
          {t('popup.selfAssign.title')}
        </Text>
        <Text
          ta={'left'}
          c={'#495057'}
          sx={{
            fontWeight: 400,
            fontSize: '14px',
            lineHeight: '21.7px',
            whiteSpace: 'pre-line',
          }}
        >
          {t('popup.selfAssign.description')}
        </Text>
      </Flex>
      <Flex
        justify={'flex-end'}
        gap={20}
        m={30}
        mr={0}
        mb={0}
        sx={{
          display: userTeams.length > 0 ? 'flex' : 'none',
        }}
      >
        <Button disabled={isLoadingConversation} className={classes.buttonCancel} onClick={close}>
          {t('popup.selfAssign.cancel')}
        </Button>
        <Button
          disabled={isLoadingConversation}
          loading={isLoadingConversation}
          className={classes.buttonConfirm}
          onClick={submitSelfAssign}
        >
          {t('popup.selfAssign.submit')}
        </Button>
      </Flex>
    </>
  );
};

export default PopupDetail;
