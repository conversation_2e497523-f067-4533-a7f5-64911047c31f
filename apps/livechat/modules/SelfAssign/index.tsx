import React from 'react';
import SelfAssignWrapper from './SelfAssignWrapper';
import { ThemeContextProvider } from './themeContext';
import ErrorBoundary from '../../components/errorBoundary';
import { useConversationActionContext } from '../conversationActionContext';

const SelfAssign = ({ lang }: { lang: string }) => {
  const { currentConversation } = useConversationActionContext();
  if (!currentConversation) return null;
  return (
    <ErrorBoundary fallback={<div></div>}>
      <ThemeContextProvider>
        <SelfAssignWrapper lang={lang} />
      </ThemeContextProvider>
    </ErrorBoundary>
  );
};

export default SelfAssign;
