import { createContext, useContext } from 'react';

const useTheme = () => {
  // TODO: will use mantine theme here after everything is clear and settle by designer and manager - will use useMantineTheme hook, that is why I put all the colors in this context for easy to manage and easy to update with mantine theme later.
  const mainViolet = '#845EF7';
  const hoverBackgroundViolet = '#7450d6';
  const hoverBgVioletInRgb = '116, 80, 214';

  const mainBgGray = '#F8F9FA';

  return {
    mainViolet,
    hoverBackgroundViolet,
    hoverBgVioletInRgb,
    mainBgGray,
  };
};

export type ThemeContextType = ReturnType<typeof useTheme>;

const context = createContext<ThemeContextType | null>(null);

export const ThemeContextProvider: React.FC<any> = ({ children }) => {
  const value = useTheme();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useThemeContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useThemeContext must be used inside ThemeContextProvider');
  }

  return value;
};
