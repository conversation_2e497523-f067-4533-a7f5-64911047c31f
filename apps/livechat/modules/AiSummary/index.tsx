import { Flex, Loader, Tooltip } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import useCustomAnimation from '@resola-ai/ui/hooks/useCustomAnimation';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useEffect } from 'react';
import { useAiSummaryContext } from '../aiSummaryContext';
import { useConversationActionContext } from '../conversationActionContext';
import IconSvg from './IconSvg';

const MAIN_CLASS_NAME = 'ai-summary-container';
// eslint-disable-next-line no-unused-vars
const AiSummary: React.FC = () => {
  const [show] = useDisclosure(true);
  const { t } = useTranslate('workspace');
  const { currentConversation } = useConversationActionContext();
  const { aiSummary, aiSummaryLoadings } = useAiSummaryContext();
  const currentAILoading = aiSummaryLoadings[currentConversation?.id];
  const isLoadingRef = React.useRef(currentAILoading);

  const clickHandler = useCallback(async () => {
    if (isLoadingRef.current) {
      // console.log('is summarizing');
      return;
    }
    await aiSummary();
    // close();
  }, [aiSummary]);

  useCustomAnimation(
    show,
    MAIN_CLASS_NAME,
    { opacity: 1, scale: 1, rotate: 0, duration: 0.38 },
    { opacity: 1, scale: 1, rotate: 0, duration: 0.38 }
  );

  useEffect(() => {
    isLoadingRef.current = currentAILoading;
  }, [currentAILoading]);

  if (!currentConversation) {
    return null;
  }

  return (
    <>
      <Flex
        className={MAIN_CLASS_NAME}
        align={'end'}
        h={'100%'}
        mx={5}
        sx={{
          position: 'relative',
        }}
      >
        <Flex
          sx={{
            position: 'absolute',
            // in the middle
            bottom: '3px',
            left: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          {currentAILoading && (
            <Flex
              sx={{
                position: 'absolute',
                // in the middle
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 99,
              }}
            >
              <Loader size={25} color={'navy.0'} type='dots' />
            </Flex>
          )}
          <Tooltip
            label={t('ai.summary.tooltip')}
            withArrow
            position={'top'}
            arrowSize={6}
            offset={10}
            transitionProps={{ transition: 'pop', duration: 300 }}
            radius={6}
          >
            <Flex
              h={'100%'}
              onClick={clickHandler}
              align={'center'}
              justify={'center'}
              sx={{
                cursor: 'pointer',
                // opacity: aiSummaryLoading ? 0.9 : 1,
                visibility: currentAILoading || !show ? 'hidden' : 'visible',
              }}
            >
              <IconSvg />
            </Flex>
          </Tooltip>
        </Flex>
      </Flex>
    </>
  );
};

export default AiSummary;
