import { Flex, Text, rem } from '@mantine/core';
import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import useCustomAnimation from '@resola-ai/ui/hooks/useCustomAnimation';
import { useTranslate } from '@tolgee/react';
import React, { memo, useEffect, useMemo } from 'react';
import { useAiSummaryContext } from '../../aiSummaryContext';
import { useConversationActionContext } from '../../conversationActionContext';

const CRM_SAVE_BUTTON_CONTAINER_CLASS_NAME = 'livechat_crm_save_button_container';

const AiSummaryContent: React.FC<any> = () => {
  const { t } = useTranslate('workspace');
  const [theLastText, setTheLastText] = React.useState('');
  const { currentConversationId } = useConversationActionContext();
  const { aiSummaryText, aiSummaryLoadings } = useAiSummaryContext();
  const text = useMemo(() => theLastText, [theLastText]);
  const show = !!text;

  const showCrmSaveButton = show && !aiSummaryLoadings[currentConversationId];

  useCustomAnimation(
    showCrmSaveButton,
    CRM_SAVE_BUTTON_CONTAINER_CLASS_NAME,
    { opacity: 0, y: 20, duration: 0.38 },
    { opacity: 1, y: 0, duration: 0.38 }
  );

  useEffect(() => {
    if (aiSummaryText !== '') setTheLastText(aiSummaryText);
  }, [aiSummaryText]);

  return (
    <Flex
      data-testid='ai-summary-content'
      direction={'column'}
      sx={{
        borderRadius: rem(10),
        backgroundColor: '#F3F0FF',
        height: show ? 'auto' : '5px',
        visibility: show ? 'visible' : 'hidden',
        padding: show ? `${rem(15)} ${rem(18)} ${rem(16)}` : '5px',
      }}
    >
      <Text
        fw={700}
        size={rem(14)}
        c={COLOR_DEFAULT_BLACK}
        sx={{
          lineHeight: '21.7px',
        }}
      >
        {t('ai.summary.content.title')}
      </Text>
      <Text
        fw={400}
        size={rem(14)}
        c={COLOR_DEFAULT_BLACK}
        sx={{
          lineHeight: '21.7px',
        }}
      >
        {text}
      </Text>
    </Flex>
  );
};

export default memo(AiSummaryContent);
