import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import {
  IPayloadCreateToolIntegration,
  ILineIntegration,
  IntegrationTabType,
  IResponseExternalToolIntegration,
  AllTypeIntegration,
  WEBHOOK_INTEGRATION,
  LINE_INTEGRATION,
  WEB_INTEGRATION,
  IntegrationType,
  WEBSOCKET_INTEGRATION,
} from './Settings/Integrations/models';
import { initIntegrationToolForm } from './Settings/Integrations/mockData';
import ApiService from '../services/api';
import useVisibilityControl from '../hooks/useVisibilityControl';
import { useDebouncedValue } from '@mantine/hooks';
import { useUserContext } from './userContext';
import useSWR, { BareFetcher } from 'swr';
import { useRouter } from 'next/router';
import useSWRInfinite from 'swr/infinite';
import { RestApi } from '../services/axios';
import { useTranslate } from '@tolgee/react';

const LIMIT = 50;

function useLoadingTools<T = any>(
  key: string,
  funcLoading: BareFetcher<any>,
  // eslint-disable-next-line no-unused-vars
  resolveItemCallback: (item: any) => any
) {
  const [dataCopied, setDataCopied] = useState<T[]>([]);
  const {
    data = null,
    isLoading,
    mutate,
  } = useSWR<any[]>(key, funcLoading, {
    revalidateOnMount: true,
    revalidateIfStale: true,
  });
  const [isLoadingDebounce] = useDebouncedValue(isLoading, 100);

  useEffect(() => {
    if (data) {
      setDataCopied(resolveItemCallback(data));
    }
  }, [data, resolveItemCallback]);

  return useMemo(
    () => ({
      isLoading: isLoadingDebounce,
      data: dataCopied,
      reload: mutate,
      setData: setDataCopied,
    }),
    [dataCopied, isLoadingDebounce, mutate]
  );
}
const loadIntegratedTools = () => ApiService.getToolsIntegration();
const loadAllTools = () => ApiService.getAllToolsOnPlatform();
const resolveItemCallback = (data) => data.data;

const useExternalTools = () => {
  const router = useRouter();
  const { asPath } = router;
  const { t } = useTranslate('workspace');
  const isIntegrationPage = useMemo(() => asPath.includes('integration'), [asPath]);
  const [filterType, setFilterType] = useState<IntegrationType>();
  const { isManager, isLoadingProfile } = useUserContext();
  const [toolSelected, setToolSelected] = useState<ILineIntegration>(
    initIntegrationToolForm as unknown as ILineIntegration
  );
  const { visible: visibleConfirmModal, toggle: toggleConfirmModal } = useVisibilityControl();
  const [integrationActiveTab, setIntegrationActiveTab] = useState<IntegrationTabType>(null);

  const {
    data: integratedToolsList = [],
    isLoading: loadingIntegratedList,
    reload: handleGetToolsIntegration,
    setData: setIntegratedToolsList,
  } = useLoadingTools('get-integrated-tools-list', loadIntegratedTools, resolveItemCallback);

  const {
    data: allToolsList = [],
    isLoading: loadingAllList,
    reload: handleGetAllToolsOnPlatform,
    setData: setAllToolsList,
  } = useLoadingTools<AllTypeIntegration>('get-all-tools-list', loadAllTools, resolveItemCallback);

  const getKey = useCallback(
    (pageIndex, previousPageData) => {
      let integrationType = '';
      if (filterType) {
        integrationType = `&integration_type=${filterType}`;
      }
      if (previousPageData && !previousPageData?.pagination?.next) return null;
      if (pageIndex === 0) return `/settings/integrations?limit=${LIMIT}${integrationType}`;
      return `/settings/integrations?next=${previousPageData?.pagination?.next}&limit=${LIMIT}${integrationType}`;
    },
    [filterType]
  );

  const {
    data = [],
    size,
    setSize,
    isLoading,
    isValidating,
    mutate: reload,
  } = useSWRInfinite(
    getKey,
    async (url) => {
      const response = await RestApi.get<IResponseExternalToolIntegration>(url);
      return response.data;
    },
    { revalidateFirstPage: false }
  );

  const loadMore = useCallback(() => {
    setSize((size) => size + 1);
  }, [setSize]);

  const handleCreateToolsIntegration = useCallback(
    async (payload: IPayloadCreateToolIntegration) => {
      return await ApiService.createToolsIntegration(payload);
    },
    []
  );

  const updateToolIntegration = useCallback(
    async (integrationId: string, payload: IPayloadCreateToolIntegration) => {
      return await ApiService.updateToolsIntegration(integrationId, payload);
    },
    []
  );

  const deleteToolIntegration = useCallback(async (integrationId: string) => {
    await ApiService.deleteToolsIntegration(integrationId);
  }, []);

  const fullReloadData = useCallback(() => {
    reload();
    handleGetToolsIntegration();
  }, [handleGetToolsIntegration, reload]);

  const allToolsListDescription = useMemo(() => {
    const descriptionTypeMap: Record<string, string> = {};
    allToolsList?.forEach((item) => {
      let description = '';
      if (item?.config?.integrationType === LINE_INTEGRATION)
        description = t('lineIntegrationDescription');
      if (item?.config?.integrationType === WEBHOOK_INTEGRATION)
        description = t('webhookIntegrationDescription');
      descriptionTypeMap[item.config?.integrationType] = description;
    });
    return descriptionTypeMap;
  }, [allToolsList, t]);

  const integratedList = useMemo(() => {
    const dataIntegrated = data?.flatMap((d) => d.data) ?? [];
    return dataIntegrated.map((item) => ({
      ...item,
      description: allToolsListDescription?.[item.integrationType] || '',
    }));
  }, [allToolsListDescription, data]);

  const isNext = useMemo(() => {
    return !!data?.[size - 1]?.pagination?.next;
  }, [data, size]);

  const integratedTypeOptions = useMemo(() => {
    return [
      { label: WEB_INTEGRATION.toLocaleUpperCase(), value: WEB_INTEGRATION },
      { label: WEBHOOK_INTEGRATION.toLocaleUpperCase(), value: WEBHOOK_INTEGRATION },
      { label: t('chatwindow'), value: WEBSOCKET_INTEGRATION },
      { label: LINE_INTEGRATION.toLocaleUpperCase(), value: LINE_INTEGRATION },
    ];
  }, []);

  useEffect(() => {
    /* set default param for the Integration page */
    if (router.query && isIntegrationPage) {
      const activeTab = router.query?.tab ?? 'integrated';
      setIntegrationActiveTab(activeTab as IntegrationTabType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isIntegrationPage]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      reload();
    }, 500);
    return () => clearTimeout(timeout);
  }, [filterType, reload]);

  return useMemo(
    () => ({
      isNext,
      isManager,
      filterType,
      isValidating,
      toolSelected,
      allToolsList,
      integratedList,
      integratedToolsList,
      visibleConfirmModal,
      integrationActiveTab,
      integratedTypeOptions,
      isLoadingListPage: isLoading,
      isLoading: loadingIntegratedList || loadingAllList || isLoadingProfile,
      loadMore,
      setFilterType,
      setAllToolsList,
      setToolSelected,
      toggleConfirmModal,
      reload: fullReloadData,
      updateToolIntegration,
      deleteToolIntegration,
      setIntegratedToolsList,
      setIntegrationActiveTab,
      handleGetToolsIntegration,
      handleGetAllToolsOnPlatform,
      handleCreateToolsIntegration,
    }),
    [
      isNext,
      isLoading,
      isManager,
      isValidating,
      toolSelected,
      allToolsList,
      integratedList,
      loadingAllList,
      isLoadingProfile,
      integratedToolsList,
      visibleConfirmModal,
      integrationActiveTab,
      loadingIntegratedList,
      integratedTypeOptions,
      filterType,
      loadMore,
      setFilterType,
      fullReloadData,
      setAllToolsList,
      toggleConfirmModal,
      updateToolIntegration,
      deleteToolIntegration,
      setIntegratedToolsList,
      handleGetToolsIntegration,
      handleGetAllToolsOnPlatform,
      handleCreateToolsIntegration,
    ]
  );
};

export type ExternalToolType = ReturnType<typeof useExternalTools>;

const context = createContext<ExternalToolType | null>(null);

export const ExternalToolsContextProvider: React.FC<any> = ({ children }) => {
  const value = useExternalTools();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useExternalToolsContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useExternalTools must be used inside ExternalToolsContextProvider');
  }

  return value;
};
