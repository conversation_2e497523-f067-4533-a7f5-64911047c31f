import { createContext, useContext, useState } from 'react';

const useWarning = () => {
  const [warningText, setWarningText] = useState<string>('');
  return {
    warningText,
    setWarningText,
  };
};

export type WarningContextType = ReturnType<typeof useWarning>;

const context = createContext<WarningContextType | null>(null);

export const WarningContextProvider: React.FC<any> = ({ children }) => {
  const value = useWarning();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWarningContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWarningContext must be used inside WarningContextProvider');
  }

  return value;
};
