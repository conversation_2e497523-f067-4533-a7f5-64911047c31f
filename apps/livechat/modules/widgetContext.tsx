import { useDebouncedValue } from '@mantine/hooks';
import { FeatureFlagName } from '@resola-ai/models';
import { WIDGET_ENGINE_OPEN_AI_AI_SUGGESTION_DEFAULT_PROMPT } from '@resola-ai/shared-constants';
import { TFnType, useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import useSWR, { BareFetcher } from 'swr';
import { CRM_WIDGET_PROMPT, OPENAI_WIDGET_PROMPT } from '../constants';
import useFeatureFlag from '../hooks/useFeatureFlag';
import ApiService from '../services/api';
import { ITemplate, TemplateKaoAPI } from '../services/api/templateKao';
import { initWidgetItem } from './Widgets/mockData';
import { CRMConfigDataType, IWidget, IWidgetSetting, WidgetTabType } from './Widgets/models';
import { useAppContext } from './appContext';

const DEFAULT_PROMPT = WIDGET_ENGINE_OPEN_AI_AI_SUGGESTION_DEFAULT_PROMPT;

const mapComponentToName = (component: string, t: TFnType) => {
  const mapping = {
    openAi: t('widget.aiautoreplytitle'),
    chatbot: t('widget.chatbottitle'),
    activity: t('activity_widget_title'),
    crm: t('crm_widget_title'),
  };
  return mapping[component] || component;
};

const mapComponentToDescription = (component: string, t: TFnType) => {
  const mapping = {
    openAi: t('ai_widget_description'),
    chatbot: t('rebot_widget_description'),
    activity: t('activity_widget_description'),
    crm: t('crm_widget_description'),
  };
  return mapping[component] || component;
};

const mapComponentToPrompt = (component: string) => {
  const mapping = {
    openAi: DEFAULT_PROMPT,
  };
  return mapping[component] || component;
};

const DEFAULT_VERSION = 'Beta 1.0';

const mappingWidget = (item: IWidgetSetting, t: TFnType) => {
  let description = item?.description;
  if (description === undefined) {
    description = mapComponentToDescription(item.component, t);
  }
  return {
    name: item.name || item.data.name,
    description: description,
    installed: true,
    version: DEFAULT_VERSION,
    widgetSettingId: item.widgetSettingId,
    widgetId: item.widgetId,
    prompt: item.data?.prompt,
    configure: item.data?.configure,
    engine: item.engine,
  } as IWidget;
};

function useLoadingWidget(
  key: string,
  funcLoading: BareFetcher<any[]>,
  // eslint-disable-next-line no-unused-vars
  resolveItemCallback: (item: any) => any
) {
  const [dataCopied, setDataCopied] = useState([]);
  const {
    data = null,
    isLoading,
    mutate,
  } = useSWR<any[]>(key, funcLoading, {
    revalidateOnMount: true,
    revalidateIfStale: true,
  });
  const [isLoadingDebounce] = useDebouncedValue(isLoading, 100);
  const { enabled: aiFeatures } = useFeatureFlag(FeatureFlagName.aiFeatures);

  useEffect(() => {
    if (data?.length) {
      const newData = data
        .filter((widget) => {
          return (
            (widget?.component === OPENAI_WIDGET_PROMPT && aiFeatures) ||
            widget?.component !== OPENAI_WIDGET_PROMPT
          );
        })
        .map(resolveItemCallback);

      setDataCopied(newData);
    }
  }, [data, resolveItemCallback, aiFeatures]);

  return useMemo(
    () => ({
      isLoading: isLoadingDebounce,
      data: dataCopied,
      reload: mutate,
      setData: setDataCopied,
    }),
    [dataCopied, isLoadingDebounce, mutate]
  );
}
const loadInstallWidget = () => ApiService.getWidgetSettings();
const loadAllWidget = () => ApiService.getWidgets();
const callbackResolveAllWidgetItem = (item, t) => {
  return {
    name: mapComponentToName(item.component, t),
    description: mapComponentToDescription(item.component, t),
    component: item.component,
    installed: false,
    version: DEFAULT_VERSION,
    widgetSettingId: item.widgetSettingId,
    widgetId: item.widgetId,
    data: item.data,
    prompt: mapComponentToPrompt(item.component),
  };
};

export function useWidget() {
  const router = useRouter();
  const { t } = useTranslate('workspace');
  const { lang } = useAppContext();
  const {
    data: installedWidgetList = [],
    isLoading: isLoadingInstalledWidget,
    reload: fetchInstalledWidgetList,
    setData: setInstalledWidgetList,
  } = useLoadingWidget(
    'get-installed-widget-list',
    loadInstallWidget,
    useCallback((item) => mappingWidget(item, t), [t])
  );

  const {
    data: allWidgetList = [],
    isLoading: isLoadingAllWidget,
    reload: fetchAllWidgetList,
  } = useLoadingWidget(
    'get-all-widget-list',
    loadAllWidget,
    useCallback((item) => callbackResolveAllWidgetItem(item, t), [t])
  );

  const { enabled: enabledTemplateWidget } = useFeatureFlag(FeatureFlagName.templateWidget);

  const { data: templateData } = useSWR<ITemplate>(
    enabledTemplateWidget ? '/template/kao' : null,
    async () => {
      return await TemplateKaoAPI.getList();
    }
  );

  const { data: crmConfigData = [] } = useSWR<CRMConfigDataType[]>(
    'crm-widget-setting-configured-data',
    async () => {
      return await ApiService.getCRMConfigData();
    }
  );

  const [openWidgetFormModal, setOpenWidgetFormModal] = useState<boolean>(false);
  const [openInstalledWidgetModal, setOpenInstalledWidgetModal] = useState<boolean>(false);
  const [openDeleteConfirmationModal, setOpenDeleteConfirmationModal] = useState<boolean>(false);
  const [widgetSelected, setWidgetSelected] = useState<IWidget>(initWidgetItem);
  const [widgetActiveTab, setWidgetActiveTab] = useState<WidgetTabType>(null);
  const [isInstalledFromAllTab, setIsInstalledFromAllTab] = useState(false);

  const { asPath } = router;
  const isWidgetPage = useMemo(() => asPath.includes('widget'), [asPath]);

  const openWidgetFormModalFromAllTab = useCallback((item: IWidget) => {
    setWidgetSelected(item);
    setOpenWidgetFormModal(true);
    setIsInstalledFromAllTab(true);
  }, []);

  const closeWidgetFormModalFromAllTabAndNavigateItem = useCallback((widgetSettingId?: string) => {
    setIsInstalledFromAllTab(false);
    setWidgetActiveTab('installed');
    if (!widgetSettingId) return;

    setTimeout(() => {
      if (document) {
        const a = document.createElement('a');
        a.href = `#${widgetSettingId}`;
        a.style.width = '0px';
        a.style.height = '0px';
        a.click();
      }
    }, 100);
  }, []);

  const finalAllWidgetList = useMemo(
    () =>
      allWidgetList?.map((item) => {
        const installedItem = installedWidgetList.find(
          (installedItem) => installedItem.component === item.component
        );
        if (installedItem) {
          return {
            ...item,
            installed: true,
          };
        }
        return item;
      }),
    [allWidgetList, installedWidgetList]
  );

  const hasCorrectCrmWidgetInstalled = useMemo(() => {
    if (!installedWidgetList?.length) return false;

    const crmWidget = installedWidgetList?.find((widget) => widget?.prompt === CRM_WIDGET_PROMPT);

    if (!crmWidget) return false;
    return true;
  }, [installedWidgetList]);

  const crmFieldsByObject = useMemo(() => {
    try {
      if (!hasCorrectCrmWidgetInstalled) return [];
      const crmWidget = installedWidgetList?.find((widget) => widget?.prompt === CRM_WIDGET_PROMPT);
      const crmObject = crmWidget?.engine?.data?.objectId || '';
      const crmIdentifyFieldId = crmWidget.engine?.data?.identifyFieldId;

      if (!crmConfigData?.length || !crmObject || !crmIdentifyFieldId?.length) return [];

      return (
        (crmConfigData?.find((item) => item?.id === crmObject)?.fields || [])
          ?.filter((item) => item?.id === crmIdentifyFieldId)
          ?.map((item) => {
            const isInvalidOption = false;
            return {
              label: item?.name,
              value: item?.id,
              disabled: isInvalidOption,
              warning: isInvalidOption,
            };
          }) || []
      );
    } catch (e) {
      return [];
    }
  }, [crmConfigData, hasCorrectCrmWidgetInstalled, installedWidgetList]);

  useEffect(() => {
    if (router.query && isWidgetPage) {
      const activeTab = router.query?.tab ?? 'installed';
      setWidgetActiveTab(activeTab as WidgetTabType);
    }
  }, [router.query, isWidgetPage]);

  return useMemo(
    () => ({
      t,
      lang,
      templateData,
      crmConfigData,
      widgetSelected,
      widgetActiveTab,
      crmFieldsByObject,
      openWidgetFormModal,
      installedWidgetList,
      enabledTemplateWidget,
      isInstalledFromAllTab,
      openInstalledWidgetModal,
      openDeleteConfirmationModal,
      hasCorrectCrmWidgetInstalled,
      allWidgetList: finalAllWidgetList,
      loadingAllWidget: isLoadingAllWidget,
      loadingInstallWidget: isLoadingInstalledWidget,
      setWidgetSelected,
      fetchAllWidgetList,
      setWidgetActiveTab,
      setOpenWidgetFormModal,
      setInstalledWidgetList,
      setIsInstalledFromAllTab,
      fetchInstalledWidgetList,
      setOpenInstalledWidgetModal,
      openWidgetFormModalFromAllTab,
      setOpenDeleteConfirmationModal,
      closeWidgetFormModalFromAllTabAndNavigateItem,
    }),
    [
      lang,
      templateData,
      crmConfigData,
      widgetSelected,
      widgetActiveTab,
      crmFieldsByObject,
      isLoadingAllWidget,
      finalAllWidgetList,
      openWidgetFormModal,
      installedWidgetList,
      enabledTemplateWidget,
      isInstalledFromAllTab,
      isLoadingInstalledWidget,
      openInstalledWidgetModal,
      openDeleteConfirmationModal,
      hasCorrectCrmWidgetInstalled,
      t,
      fetchAllWidgetList,
      setInstalledWidgetList,
      fetchInstalledWidgetList,
      openWidgetFormModalFromAllTab,
      closeWidgetFormModalFromAllTabAndNavigateItem,
    ]
  );
}

export type WidgetContextType = ReturnType<typeof useWidget>;

const context = createContext<WidgetContextType | null>(null);

export const WidgetContextProvider: React.FC<any> = ({ children }) => {
  const value = useWidget();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWidgetContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWidgetContext must be used inside WidgetContextProvider');
  }

  return value;
};
