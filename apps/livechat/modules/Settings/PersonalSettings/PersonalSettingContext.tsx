import { useTranslate } from '@tolgee/react';
import { ReactNode, createContext, useContext, useMemo } from 'react';

export const usePersonalSetting = () => {
  const { t } = useTranslate('workspace');
  return useMemo(
    () => ({
      t,
    }),
    [t]
  );
};

type NotificationSettingType = ReturnType<typeof usePersonalSetting>;

const PersonalSettingContext = createContext<NotificationSettingType | null>(null);

export const PersonalSettingContextProvider = ({ children }: { children: ReactNode }) => {
  const data = usePersonalSetting();
  return <PersonalSettingContext.Provider value={data}>{children}</PersonalSettingContext.Provider>;
};

export const usePersonalSettingContext = () => {
  const data = useContext(PersonalSettingContext);

  if (!data) {
    throw new Error(
      'NotificationSettingContext should be used in NotificationSettingContextProvider'
    );
  }

  return data;
};
