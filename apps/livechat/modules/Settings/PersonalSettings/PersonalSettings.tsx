import {
  Box,
  Center,
  Checkbox,
  Container,
  Flex,
  Grid,
  Group,
  Loader,
  Radio,
  SegmentedControl,
  Text,
  Title,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  NotificationItemSettings,
  NotificationSettingTypes,
  NotificationSettings,
  NotificationSoundMode,
} from '@resola-ai/models/collection/settings';
import { BottomUpTween, RightToLeftTween } from '@resola-ai/ui';
import AudioPlayer from '@resola-ai/ui/components/AudioPlayer';
import { AudioPlayerProvider } from '@resola-ai/ui/components/AudioPlayer/AudioPlayerContext';
import Dropdown from '@resola-ai/ui/components/Dropdown';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  PERSONAL_SETTING_CHANNEL,
  UPDATE_SOUND_PERSONAL_SETTING_CHANNEL,
} from '../../../constants';
import ApiService from '../../../services/api';
import { SettingsAPI } from '../../../services/api/settings';
import { useOperationSettingContext } from '../../operationSettingContext';
import { useUserContext } from '../../userContext';
import { usePersonalSettingContext } from './PersonalSettingContext';

const useStyle = createStyles((theme) => ({
  notifySettings: {
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
    padding: '10px 0 32px 32px',
  },
  title: {
    color: theme.colors.dark[4],
    fontSize: '20px',
    marginBottom: '20px',
  },
  pageTitle: {
    color: theme.colors.dark[4],
    fontSize: '16px',
  },
  labelCheckbox: {
    color: theme.colors.dark[4],
    fontWeight: 600,
  },
  activeControlWrapper: {
    '.mantine-SegmentedControl-label': {
      color: '#FFFFFF',
      '&:hover': {
        color: '#FFFFFF',
      },
    },
  },
  controlWrapper: {
    '.mantine-SegmentedControl-label': {
      fontWeight: 700,
      '&:hover': {},
    },
  },
  segmentControlRoot: {
    backgroundColor: theme.colors.gray[2],
    borderRadius: '6px',
  },
  checkboxArea: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
}));

const defaultOptions = {
  inapp: false,
  email: true,
};

const defaultConditions = {
  allMessage: true,
  onlyAtStart: false,
};

const defaultSettings = {
  [NotificationSettingTypes.AssignToMe]: {
    ...defaultOptions,
    ...defaultConditions,
  },
  [NotificationSettingTypes.AssignToMyTeam]: {
    ...defaultOptions,
    ...defaultConditions,
  },
  [NotificationSettingTypes.NoAssignedTeam]: {
    ...defaultOptions,
    ...defaultConditions,
  },
  [NotificationSettingTypes.ReassignedToMe]: {
    ...defaultOptions,
  },
  sound: {
    selectedSoundId: '',
    mode: NotificationSoundMode.AllMessage,
  },
};

const Types = [
  NotificationSettingTypes.AssignToMe,
  NotificationSettingTypes.AssignToMyTeam,
  NotificationSettingTypes.NoAssignedTeam,
  NotificationSettingTypes.ReassignedToMe,
];

const Labels = {
  [NotificationSettingTypes.AssignToMe]: 'notification.ticket_assign_to_me_label',
  [NotificationSettingTypes.AssignToMyTeam]: 'notification.ticket_assign_to_team_label',
  [NotificationSettingTypes.NoAssignedTeam]: 'notification.ticket_assign_to_unknown_label',
  [NotificationSettingTypes.ReassignedToMe]: 'notification.ticket_pic_change_or_assign_to_me_label',
};

// eslint-disable-next-line no-unused-vars
const enum METHOD_SEND_MESSAGE {
  // eslint-disable-next-line no-unused-vars
  ENTER = 'enter',
  // eslint-disable-next-line no-unused-vars
  SHIFT_ENTER = 'enter_shift',
}

export function PersonalSettings() {
  const { classes } = useStyle();
  const { t } = usePersonalSettingContext();

  const { userProfile, mutate, sendMessageHotKey, preferences, isValidatingProfile } =
    useUserContext();
  const { notificationSounds } = useOperationSettingContext();

  const [methodSendMessage, setMethodSendMessage] = useState<METHOD_SEND_MESSAGE>(
    METHOD_SEND_MESSAGE.ENTER
  );
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>();
  const [selectedSound, setSelectedSound] = useState<string | null>(null);
  const [selectedSoundMode, setSelectedSoundMode] = useState<NotificationSoundMode | null>(null);

  const personalSettingChannel = useMemo(() => {
    return new BroadcastChannel(PERSONAL_SETTING_CHANNEL);
  }, []);

  const shouldDisabled = useMemo(() => {
    return !userProfile;
  }, [userProfile]);

  const handleChange = useCallback(
    async (type: NotificationSettingTypes, value: string) => {
      let newSettings = {
        ...notificationSettings,
      };

      if (['inapp', 'email'].includes(value)) {
        newSettings = {
          ...newSettings,
          [type]: {
            ...notificationSettings[type],
            [value]: !notificationSettings[type][value],
          },
        };
      } else {
        newSettings = {
          ...newSettings,
          [type]: {
            ...notificationSettings[type],
            conditions: {
              ...notificationSettings[type].conditions,
              allMessage: value === 'all',
              onlyAtStart: value !== 'all',
            },
          },
        };
      }

      setNotificationSettings(newSettings);
      await SettingsAPI.updateNotification(userProfile?.id, newSettings);
    },
    [notificationSettings, userProfile?.id]
  );

  const handleSaveSendMessageHotKeyPreferences = useCallback(
    async (type: METHOD_SEND_MESSAGE) => {
      setMethodSendMessage(type);
      await SettingsAPI.updatePreferences(userProfile?.id, {
        ...preferences,
        send_message_hot_key: type,
      });
    },
    [preferences, userProfile?.id]
  );

  useEffect(() => {
    if (userProfile?.notificationSetting) {
      setNotificationSettings(userProfile.notificationSetting);
      const soundSettings = userProfile.notificationSetting.sound;
      const selectedSoundId = soundSettings?.selectedSoundId ?? null;
      const soundMode = soundSettings?.mode ?? NotificationSoundMode.AllMessage;
      setSelectedSound(selectedSoundId);
      setSelectedSoundMode(soundMode);
    } else if (!isValidatingProfile) {
      setNotificationSettings(defaultSettings);
    }
  }, [userProfile, isValidatingProfile]);

  useEffect(() => {
    if (sendMessageHotKey) {
      setMethodSendMessage(sendMessageHotKey as METHOD_SEND_MESSAGE);
    }
  }, [sendMessageHotKey]);

  useEffect(() => {
    return () => personalSettingChannel.close();
  }, [personalSettingChannel]);

  useEffect(() => {
    return () => {
      mutate('/api/userProfile');
    };
  }, [mutate]);

  const updateSoundSettingsHandler = useCallback(
    async (soundId?: string, mode?: NotificationSoundMode) => {
      try {
        const userId = userProfile?.id;
        if (!userId) {
          console.error('User ID is not available');
          return;
        }

        const currentSettings = userProfile?.notificationSetting ?? {};

        const updatedSettings = {
          ...currentSettings,
          sound: {
            selectedSoundId: soundId || selectedSound,
            mode: mode || selectedSoundMode,
          },
        };

        if (mode) {
          setSelectedSoundMode(mode);
        }
        if (soundId) {
          setSelectedSound(soundId);
        }
        await ApiService.updateUserProfile(userId, {
          notificationSetting: updatedSettings,
        });

        personalSettingChannel.postMessage({
          type: UPDATE_SOUND_PERSONAL_SETTING_CHANNEL,
          payload: updatedSettings,
        });
      } catch (error) {
        console.error('Failed to update sound settings:', error);
      }
    },
    [
      selectedSound,
      userProfile?.id,
      selectedSoundMode,
      personalSettingChannel,
      userProfile?.notificationSetting,
    ]
  );

  if (isValidatingProfile || !notificationSettings) {
    return (
      <Flex
        w={'100%'}
        align={'center'}
        justify={'center'}
        style={{ height: `calc(100dvh - var(--app-shell-header-height, 0px) + 0rem)` }}
      >
        <Loader color={'navy.5'} />
      </Flex>
    );
  }

  return (
    <AudioPlayerProvider>
      <Container size={'lg'} mb={'lg'} p={'1rem'}>
        <Title className={classes.title} mt={'lg'}>
          {t('personal.setting.title')}
        </Title>
        <Container className={classes.notifySettings} fluid mb={'lg'}>
          <Title className={classes.pageTitle} mt={'lg'}>
            {t('conversation.setting.title')}
          </Title>
          <Group mt={13}>
            <Text lineClamp={1} fw={700} size={rem(14)} c='gray.7' mr={80} h={'1.2rem'}>
              {t('method.send_message.title')}
            </Text>
            <Radio
              mr={'lg'}
              color='navy.0'
              disabled={shouldDisabled}
              sx={{ cursor: 'pointer' }}
              value={METHOD_SEND_MESSAGE.ENTER}
              label={t('method.send_message.enter')}
              checked={methodSendMessage === METHOD_SEND_MESSAGE.ENTER}
              onChange={() => handleSaveSendMessageHotKeyPreferences(METHOD_SEND_MESSAGE.ENTER)}
            />
            <Radio
              color='navy.0'
              disabled={shouldDisabled}
              sx={{ cursor: 'pointer' }}
              value={METHOD_SEND_MESSAGE.SHIFT_ENTER}
              label={t('method.send_message.enter_shift')}
              checked={methodSendMessage === METHOD_SEND_MESSAGE.SHIFT_ENTER}
              onChange={() =>
                handleSaveSendMessageHotKeyPreferences(METHOD_SEND_MESSAGE.SHIFT_ENTER)
              }
            />
          </Group>
        </Container>
        <Container className={classes.notifySettings} fluid>
          <RightToLeftTween>
            <Title className={classes.pageTitle} mt={'lg'}>
              {t('title')}
            </Title>
          </RightToLeftTween>
          <Group mt={13} style={{ justifyContent: 'space-between', marginRight: '85px' }}>
            <Text lineClamp={1} fw={700} size={rem(14)} c='gray.7' mr={80} h={'1.2rem'}>
              {t('notification.with_sound')}
            </Text>
            <Dropdown
              placeholder='Select an option'
              data={[
                {
                  value: NotificationSoundMode.OFF,
                  label: t('notification.sound.option.none'),
                },
                {
                  value: NotificationSoundMode.OnlyAtStart,
                  label: t('notification.sound.option.first'),
                },
                {
                  value: NotificationSoundMode.AllMessage,
                  label: t('notification.sound.option.all'),
                },
              ]}
              value={selectedSoundMode}
              onChange={(value) =>
                updateSoundSettingsHandler(undefined, value as NotificationSoundMode)
              }
              style={{ width: '410px' }}
            />
          </Group>
          <Group mt={13} style={{ justifyContent: 'space-between', marginRight: '85px' }}>
            {[NotificationSoundMode.AllMessage, NotificationSoundMode.OnlyAtStart].includes(
              selectedSoundMode
            ) && (
              <>
                <Text lineClamp={1} fw={700} size={rem(14)} c='gray.7' mr={80} h={'1.2rem'}>
                  {t('notification.sound')}
                </Text>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-around',
                  }}
                >
                  {selectedSoundMode !== 'off' &&
                    notificationSounds.map((sound, index) => (
                      <div
                        key={sound.id}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginRight: '20px',
                        }}
                      >
                        <Radio
                          mr={'xs'}
                          color='navy.0'
                          disabled={shouldDisabled}
                          sx={{ cursor: 'pointer' }}
                          value={sound.id}
                          label={t('notification.sound.label', {
                            index: index + 1,
                          })}
                          checked={selectedSound === sound.id}
                          onChange={() => {
                            setSelectedSound(sound.id);
                            updateSoundSettingsHandler(sound.id);
                          }}
                        />
                        <AudioPlayer src={sound.url} />
                      </div>
                    ))}
                </div>
              </>
            )}
          </Group>
          <BottomUpTween delay={0.2}>
            <Group mt={'lg'} mb={'sm'}>
              <Text lineClamp={1} fw={400} size={rem(14)} color='gray.7' h={'1.2rem'}>
                {t('description')}
              </Text>
            </Group>
          </BottomUpTween>
          {Types.map((type: NotificationSettingTypes) => {
            if (notificationSettings[type]) {
              return (
                <NotificationItemSettingsCom
                  key={type}
                  type={type}
                  onChange={handleChange}
                  disabled={shouldDisabled}
                  data={notificationSettings[type]}
                />
              );
            }
            return null;
          })}
        </Container>
      </Container>
    </AudioPlayerProvider>
  );
}

type ItemProps = Readonly<{
  disabled?: boolean;
  data: NotificationItemSettings;
  type: NotificationSettingTypes;
  // eslint-disable-next-line no-unused-vars
  onChange: (type: NotificationSettingTypes, value: string) => void;
}>;

function NotificationItemSettingsCom({ data, type, onChange, disabled = false }: ItemProps) {
  const { t } = usePersonalSettingContext();

  const { classes } = useStyle();

  return (
    <Grid justify='space-between' align='center' pt={16} gutter={{ base: 20 }}>
      <Grid.Col span={4}>
        <Text size={rem(14)} fw={700} c='dark.4' sx={{ lineHeight: '1.2rem' }}>
          {t(Labels[type])}
        </Text>
      </Grid.Col>
      <Grid.Col span={4}>
        <div className={classes.checkboxArea}>
          <Checkbox
            color='navy.0'
            disabled={disabled}
            checked={data.email}
            classNames={{
              label: classes.labelCheckbox,
            }}
            label={t('notification.type.email')}
            onChange={() => onChange(type, 'email')}
          />
        </div>
      </Grid.Col>
      <Grid.Col span={4}>
        {data.conditions ? (
          <SegmentedControl
            transitionDuration={0}
            color='navy.0'
            classNames={{
              control: classes.controlWrapper,
              root: classes.segmentControlRoot,
              indicator: classes.activeControlWrapper,
            }}
            onChange={(value) => onChange(type, value)}
            value={data.conditions.allMessage ? 'all' : 'once'}
            disabled={disabled}
            data={[
              {
                value: 'once',
                label: (
                  <Center>
                    <Box>{t('notification.notify.when_start_conversation')}</Box>
                  </Center>
                ),
              },
              {
                value: 'all',
                label: (
                  <Center>
                    <Box>{t('notification.notify.when_new_message')}</Box>
                  </Center>
                ),
              },
            ]}
          />
        ) : (
          <div style={{ width: '247px' }}>&nbsp;</div>
        )}
      </Grid.Col>
    </Grid>
  );
}
