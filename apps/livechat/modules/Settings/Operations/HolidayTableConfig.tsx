import { Button, Container, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import 'dayjs/locale/ja';
import React, { useEffect, useCallback } from 'react';
import useCustomStyle from '../../../hooks/useCustomStyle';
import useTranslationParams from '../../../hooks/useTranslationParams';
import useVisibilityControl from '../../../hooks/useVisibilityControl';
import { useOperationSettingContext } from '../../operationSettingContext';
import HolidayCreateModal from './Components/HolidayCreateModal';
import HolidayTable from './Components/HolidayTable';
import { IHolidays } from './models';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
    paddingBottom: '1.2em',
  },
  violet: {
    color: theme.colors.violet[5],
  },
  buttonViolet: {
    width: 'fit-content',
    backgroundColor: theme.colors.violet[5],
    fontWeight: 700,
    '&:hover': {
      backgroundColor: theme.colors.violet[9],
    },
  },
}));

export default function HolidayTableConfig() {
  const { classes, cx } = useStyle();
  const { t } = useTranslate('workspace');

  const { visible, toggle } = useVisibilityControl();
  const { isManager, holidays, holidayRulesList, setHolidayRulesList, handleCustomRules } =
    useOperationSettingContext();
  const { classes: customClasses } = useCustomStyle();

  const mappingRulesHoliday = useCallback(
    (holidays: IHolidays) => {
      if (!holidays) return;

      const { rules } = holidays;
      const mappingRules = handleCustomRules(rules);
      setHolidayRulesList(mappingRules);
    },
    [handleCustomRules, setHolidayRulesList]
  );

  const handleOpenModal = useCallback(() => {
    toggle();
  }, [toggle]);

  useEffect(() => {
    mappingRulesHoliday(holidays);
  }, [holidays, mappingRulesHoliday]);

  return (
    <Container fluid className={classes.container} mt={'md'}>
      <Title order={5} ml={'18px'} c='dark.4'>
        {t('operation_holiday_setting_title')}
      </Title>
      <Text mt={'md'} ml={'18px'} c='gray.7' lineClamp={1} fz='sm'>
        {t('operation_holiday_setting_description')}
      </Text>
      <Button
        className={cx(customClasses.button)}
        variant='filled'
        color='navy.0'
        ml={'18px'}
        mt={'sm'}
        size='xs'
        radius={'md'}
        disabled={!isManager}
        onClick={toggle}
      >
        <IconPlus size={'15px'} />
        <Text fw={700} fz={12} ml={'xs'}>
          {t('add_holiday_button')}
        </Text>
      </Button>
      {holidayRulesList.length ? <HolidayTable holidays={holidayRulesList} /> : null}
      <HolidayCreateModal visible={visible} onClose={handleOpenModal} isEdit={false} />
    </Container>
  );
}
