import { Box, Container, Flex, Group, Switch, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import { deepCopy } from '../../../utils/common';
import { useAppContext } from '../../appContext';
import { useOperationSettingContext } from '../../operationSettingContext';
import TimeInput from './TimeInput';
import { initBusinessHoursData } from './mockData';
import { BusinessHourDayType, ICalendarHours, PeriodTime } from './models';
import { ValidTimeFormat } from './types';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1rem',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
    position: 'relative',
  },
  modeViewOnly: {
    '& input + label.mantine-Switch-track': {
      backgroundColor: theme.colors.violet[3],
      borderColor: theme.colors.violet[3],
    },
  },
  primaryText: {
    '& .mantine-Switch-label': {
      color: theme.colors.dark[4],
      fontWeight: 700,
      textAlign: 'center',
      verticalAlign: 'middle',
    },
  },
  textGray: {
    '& .mantine-Switch-label': {
      color: theme.colors.gray[5],
      textAlign: 'center',
      verticalAlign: 'middle',
    },
  },
  greyOut: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: theme.colors.gray[0],
    top: 0,
    left: 0,
    opacity: 0.6,
  },
  unCheckedSwitch: {
    '& input:disabled + label.mantine-Switch-track': {
      backgroundColor: theme.colors.gray[2],
      borderColor: theme.colors.gray[2],
    },
  },
}));

export default function BusinessHourSettings() {
  const { lang } = useAppContext();
  const { classes, cx } = useStyle();
  const { t } = useTranslate('workspace');

  const { serviceOpen, calendarHours, payloadCalendarHours, updateOperationSettings, isManager } =
    useOperationSettingContext();

  const [businessHoursData, setBusinessHoursData] =
    useState<BusinessHourDayType[]>(initBusinessHoursData);

  const mappingBusinessHoursData = useCallback(
    (calendarHours: ICalendarHours) => {
      const data = businessHoursData.map((item) => {
        const tmpData = deepCopy(calendarHours[item.day] || {});
        return {
          ...item,
          ...tmpData,
        };
      }) as BusinessHourDayType[];
      setBusinessHoursData(data);
    },
    [businessHoursData]
  );

  const handleCheckValidTimeData = useCallback((businessHoursData: BusinessHourDayType[]) => {
    businessHoursData.forEach((item) => {
      const fromTimeHour = +item.fromTime.hour;
      const fromTimeMin = +item.fromTime.minute;
      const toTimeHour = +item.toTime.hour;
      const toTimeMin = +item.toTime.minute;
      if (fromTimeHour * 60 + fromTimeMin >= toTimeHour * 60 + toTimeMin) item.valid = false;
      else item.valid = true;
    });
    return businessHoursData.every((item) => {
      return item.valid;
    });
  }, []);

  const handleToggleDay = useCallback(
    async (item: BusinessHourDayType) => {
      payloadCalendarHours[item.day].enable = !item.enable;
      const businessHoursDataTmp = deepCopy(businessHoursData) as BusinessHourDayType[];
      businessHoursDataTmp.find((busItem) => busItem.id === item.id).enable = !item.enable;
      handleCheckValidTimeData(businessHoursDataTmp);
      setBusinessHoursData(businessHoursDataTmp);

      const payload = {
        calendar: payloadCalendarHours,
      };
      await updateOperationSettings(payload);
    },
    [businessHoursData, payloadCalendarHours, updateOperationSettings, handleCheckValidTimeData]
  );

  const handleUpdateBusinessHourDay = useCallback(
    async (item: BusinessHourDayType, typeTime: keyof PeriodTime, val: ValidTimeFormat) => {
      const preTime = `${item?.[typeTime].hour}:${item?.[typeTime].minute}`;

      if (val === preTime) return;

      const businessHoursDataTmp = deepCopy(businessHoursData) as BusinessHourDayType[];
      const [hour, minute] = val.split(':');
      // Update new data
      businessHoursDataTmp.find((busItem) => busItem.id === item.id)[typeTime] = {
        hour: +hour,
        minute: +minute,
      };
      const isValidTimeData = handleCheckValidTimeData(businessHoursDataTmp);
      setBusinessHoursData(businessHoursDataTmp);

      if (isValidTimeData) {
        const calendarTmp = {};
        businessHoursDataTmp.forEach((busItem) => {
          const { fromTime, toTime, enable, valid } = busItem;
          calendarTmp[busItem.day] = {
            fromTime,
            toTime,
            enable,
            valid,
          };
        });
        const payload = {
          calendar: calendarTmp as ICalendarHours,
        };
        await updateOperationSettings(payload);
      }
    },
    [businessHoursData, handleCheckValidTimeData, updateOperationSettings]
  );

  useEffect(() => {
    if (!calendarHours) return;
    mappingBusinessHoursData(calendarHours);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calendarHours]);

  return (
    <Container
      fluid
      mt={'md'}
      className={cx(classes.container, !isManager && classes.modeViewOnly)}
      pl={0}
      pr={0}
    >
      <Box pl={30} pb={16}>
        <Title order={5} mb={30} c='dark.4'>
          {t('operation_business_hour_title')}
        </Title>
        <Box pl={0} pr={0}>
          {businessHoursData.map((item, index) => (
            <Group mt={'md'} key={index} align='baseline'>
              <Switch
                mr={16}
                color='decaGreen.6'
                disabled={!isManager}
                checked={item.enable}
                onChange={() => handleToggleDay(item)}
                label={
                  <Text fw={700} fz={14} miw={lang === 'en' && 40}>
                    {t(item.label)}
                  </Text>
                }
                className={cx(
                  item.enable ? classes.primaryText : classes.textGray,
                  !isManager && !item.enable && classes.unCheckedSwitch
                )}
              />
              <Flex direction='column'>
                <Group>
                  <TimeInput
                    day={item.day}
                    validTime={item.valid}
                    disabled={!isManager || !item.enable}
                    onChange={(val) => handleUpdateBusinessHourDay(item, 'fromTime', val)}
                    value={`${item.fromTime.hour}:${item.fromTime.minute}` as ValidTimeFormat}
                  />
                  <Text>〜</Text>
                  <TimeInput
                    day={item.day}
                    validTime={item.valid}
                    disabled={!isManager || !item.enable}
                    onChange={(val) => handleUpdateBusinessHourDay(item, 'toTime', val)}
                    value={`${item.toTime.hour}:${item.toTime.minute}` as ValidTimeFormat}
                  />
                </Group>
                {[item.enable, !item.valid].every(Boolean) && (
                  <Text fz='sm' color='red.5' mt={5}>
                    {t('invalid_time_input')}
                  </Text>
                )}
              </Flex>
            </Group>
          ))}
        </Box>
      </Box>
      {!serviceOpen && <Box className={classes.greyOut} />}
    </Container>
  );
}
