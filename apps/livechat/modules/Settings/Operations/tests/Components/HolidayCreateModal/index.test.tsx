import '@testing-library/jest-dom';
import { fireEvent, screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import React from 'react';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useOperationSettingContext } from '../../../../../operationSettingContext';
import HolidayCreateModal from '../../../Components/HolidayCreateModal';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/settings/operations',
    pathname: '',
    query: {},
    asPath: '',
  }),
}));

jest.mock('../../../../../appContext', () => ({
  useAppContext: () => ({
    lang: 'en',
  }),
}));

jest.mock('../../../../../operationSettingContext', () => ({
  useOperationSettingContext: jest.fn(),
}));

describe('HolidayCreateModal', () => {
  const onClose = jest.fn();
  const isEdit = false;

  beforeEach(() => {
    (useOperationSettingContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders the modal title', () => {
    renderWithProvider(<HolidayCreateModal visible onClose={onClose} isEdit={isEdit} />);
    expect(screen.getByText('add_holiday_title')).toBeInTheDocument();
  });

  it('renders the holiday name input', () => {
    renderWithProvider(<HolidayCreateModal visible onClose={onClose} isEdit={isEdit} />);
    expect(screen.getByLabelText('input_holiday_name_label')).toBeInTheDocument();
  });

  it('renders the date range picker', () => {
    renderWithProvider(<HolidayCreateModal visible onClose={onClose} isEdit={isEdit} />);
    expect(screen.getByText('input_date_time_label')).toBeInTheDocument();
    expect(screen.getByText('date_time_selected')).toBeInTheDocument();
  });

  it('calls the onClose function when clicking the cancel button', () => {
    renderWithProvider(<HolidayCreateModal visible onClose={onClose} isEdit={isEdit} />);
    const cancelButton = screen.getByText('modal_operator.button_cancel');
    fireEvent.click(cancelButton);
    expect(onClose).toHaveBeenCalled();
  });
});
