import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import React from 'react';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useOperationSettingContext } from '../../../../../operationSettingContext';
import HolidayTable from '../../../Components/HolidayTable';
import { HolidayType } from '../../../types';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

const holidays: HolidayType[] = [
  {
    id: '1',
    name: 'New Year',
    fromDate: new Date('2023-01-01'),
    toDate: new Date('2023-01-03'),
    timePeriods: [{ fromTime: '10:0', toTime: '19:0' }],
  },
  {
    id: '2',
    name: 'sport day',
    fromDate: new Date('2023-11-28'),
    toDate: new Date('2023-11-30'),
    timePeriods: [{ fromTime: '10:0', toTime: '19:0' }],
  },
];

jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/settings/operations',
    pathname: '',
    query: {},
    asPath: '',
  }),
}));

jest.mock('../../../../../appContext', () => ({
  useAppContext: () => ({
    lang: 'en',
  }),
}));

jest.mock('../../../../../operationSettingContext', () => ({
  useOperationSettingContext: jest.fn(),
}));

describe('HolidayTable', () => {
  beforeEach(() => {
    (useOperationSettingContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders the table with the correct headers', () => {
    renderWithProvider(<HolidayTable holidays={holidays} />);
    expect(screen.getByText('input_holiday_name_label')).toBeInTheDocument();
    expect(screen.getByText('date_label')).toBeInTheDocument();
    expect(screen.getByText('edit_label')).toBeInTheDocument();
    expect(screen.getByText('delete_label')).toBeInTheDocument();
  });

  it('renders the holidays with the correct data', () => {
    renderWithProvider(<HolidayTable holidays={holidays} />);
    expect(screen.getByText('sport day')).toBeInTheDocument();
    expect(screen.getByText('New Year')).toBeInTheDocument();
  });
});
