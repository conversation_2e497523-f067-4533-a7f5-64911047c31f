import { useRichTextEditorContext } from '@mantine/tiptap';
import { fireEvent } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import React from 'react';
import { act } from 'react-dom/test-utils';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useTextEditorContext } from '../../../../../TextEditor/context';
import { useOperationSettingContext } from '../../../../../operationSettingContext';
import SaveAutoReplyMessageButton from '../../../Components/SaveAutoReplyMessageButton';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
}));

jest.mock('@mantine/tiptap', () => ({
  useRichTextEditorContext: jest.fn(),
}));

jest.mock('../../../../../TextEditor/context', () => ({
  useTextEditorContext: jest.fn(),
}));

jest.mock('../../../../../operationSettingContext', () => ({
  useOperationSettingContext: jest.fn(),
}));

describe('SaveAutoReplyMessageButton', () => {
  const mockRichTextEditorContext = useRichTextEditorContext as jest.Mock;

  beforeEach(() => {
    (useRichTextEditorContext as jest.Mock).mockReturnValue(mockRichTextEditorContext);
    (useTextEditorContext as jest.Mock).mockReturnValue({ t: (key) => key });
    (useOperationSettingContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should render a button with the text "Save"', () => {
    const { getByText } = renderWithProvider(<SaveAutoReplyMessageButton />);
    expect(getByText('save')).toBeInTheDocument();
  });

  it('should show saved text when clicked', async () => {
    const { getByText } = renderWithProvider(<SaveAutoReplyMessageButton />);
    const btnSave = document.querySelector('[data-testid="btn-save-auto-reply-id"]');
    if (!btnSave) {
      return;
    }
    await act(async () => {
      fireEvent.click(btnSave);
    });
    expect(getByText('saved')).toBeInTheDocument();
  });
});
