import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import React from 'react';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useOperationSettingContext } from '../../../../../operationSettingContext';
import OperationState from '../../../Components/OperationState';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/settings/operations',
    pathname: '',
    query: {},
    asPath: '',
  }),
}));

jest.mock('../../../../../operationSettingContext', () => ({
  useOperationSettingContext: jest.fn(),
}));

describe('OperationState', () => {
  beforeEach(() => {
    (useOperationSettingContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders the correct status dot color', () => {
    const { operationStatus } = mockContext;
    renderWithProvider(<OperationState />);
    if (operationStatus) {
      expect(screen.getByText('status_open')).toBeInTheDocument();
    } else {
      expect(screen.getByText('status_preparing')).toBeInTheDocument();
    }
  });
});
