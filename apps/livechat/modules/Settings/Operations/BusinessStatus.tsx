import { Container, Switch, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useOperationSettingContext } from '../../operationSettingContext';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em 1em 1em 1.875em',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
  },
  modeViewOnly: {
    '& input + label.mantine-Switch-track': {
      backgroundColor: theme.colors.violet[3],
      borderColor: theme.colors.violet[3],
    },
  },
  switchStyle: {
    '& .mantine-Switch-body': {
      alignItems: 'center',
    },
  },
  unCheckedSwitch: {
    '& input:disabled + label.mantine-Switch-track': {
      backgroundColor: theme.colors.gray[2],
      borderColor: theme.colors.gray[2],
    },
  },
}));

const BusinessStatus = () => {
  const { classes, cx } = useStyle();
  const { t } = useTranslate('workspace');

  const { serviceOpen, setServiceOpen, updateOperationSettings, isManager } =
    useOperationSettingContext();

  const handleChange = (e) => {
    const value = e.currentTarget.checked;
    setServiceOpen(value);
    updateOperationSettings({ open: value });
  };

  return (
    <Container
      fluid
      mt={'md'}
      className={cx(classes.container, !isManager && classes.modeViewOnly)}
    >
      <Switch
        disabled={!isManager}
        labelPosition='left'
        label={
          <Title order={5} c='dark.4' miw={'180px'}>
            {t('start_stop_services_title')}
          </Title>
        }
        color='decaGreen.6'
        className={cx(classes.switchStyle, !isManager && !serviceOpen && classes.unCheckedSwitch)}
        checked={serviceOpen}
        onChange={handleChange}
      />
      <Text mt={'md'} color='gray.7' fz='sm'>
        {t('operation_business_status_description')}
      </Text>
    </Container>
  );
};

export default BusinessStatus;
