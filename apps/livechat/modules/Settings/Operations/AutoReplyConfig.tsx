import { Box, Container, Switch, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React, { useState, useEffect, useCallback } from 'react';
import { useOperationSettingContext } from '../../operationSettingContext';
import AutoReplyInputSection from './Components/AutoReplyInputSection';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em 1em 1em 0',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
  },
  modeViewOnly: {
    '& input + label.mantine-Switch-track': {
      backgroundColor: theme.colors.violet[3],
      borderColor: theme.colors.violet[3],
    },
  },
  switchStyle: {
    paddingLeft: '1.875em',
    '& .mantine-Switch-body': {
      alignItems: 'center',
    },
  },
  unCheckedSwitch: {
    '& input:disabled + label.mantine-Switch-track': {
      backgroundColor: theme.colors.gray[2],
      borderColor: theme.colors.gray[2],
    },
  },
}));

const AutoReplyConfig = () => {
  const { classes, cx } = useStyle();
  const { t } = useTranslate('workspace');
  const [checked, setChecked] = useState<boolean>(false);
  const { replyOutsideBusinessHour, updateOperationSettings, isManager } =
    useOperationSettingContext();

  useEffect(() => {
    setChecked(replyOutsideBusinessHour.enable);
  }, [replyOutsideBusinessHour?.enable]);

  const handleChangeChecked = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.currentTarget.checked;
      const payload = { ...replyOutsideBusinessHour, enable: value };
      setChecked(value);
      updateOperationSettings({ outBusinessHour: payload });
    },
    [replyOutsideBusinessHour, updateOperationSettings]
  );

  return (
    <Container
      fluid
      mt={'md'}
      className={cx(classes.container, !isManager && classes.modeViewOnly)}
    >
      <Switch
        disabled={!isManager}
        labelPosition='left'
        label={
          <Title order={5} miw={'180px'} c='dark.4'>
            {t('operation_auto_reply_setting_title')}
          </Title>
        }
        color='decaGreen.6'
        className={cx(classes.switchStyle, !isManager && !checked && classes.unCheckedSwitch)}
        checked={checked}
        onChange={handleChangeChecked}
      />
      <Text mt={'md'} color='gray.7' pl={'1.875em'} fz='sm'>
        {checked
          ? t('operation_auto_reply_setting_description_on')
          : t('operation_auto_reply_setting_description_off')}
      </Text>
      {checked && (
        <Box pl={'0.75em'}>
          <AutoReplyInputSection />
        </Box>
      )}
    </Container>
  );
};

export default AutoReplyConfig;
