import { Container, Flex, Title } from '@mantine/core';
import { BottomUpTween, RightToLeftTween } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import ViewOnly from '../../../components/ViewOnly';
import { useUserContext } from '../../userContext';
import BusinessHourSettings from './BusinessHours';
import BusinessStatus from './BusinessStatus';
import OperationState from './Components/OperationState';
import HolidayTableConfig from './HolidayTableConfig';

const delays = [0.2, 0.3, 0.4, 0.55];

export default function OperationSettings() {
  const { t } = useTranslate('workspace');

  const { isManager, isLoadingProfile } = useUserContext();

  return (
    <Container p={'md'} size={'lg'}>
      <RightToLeftTween>
        <Flex justify='flex-start' align='center' direction='row' mt={'md'} mb={'lg'}>
          <Title c='dark.4' fz={'20px'}>
            {t('operation_title')}
          </Title>
          <OperationState />
          {!isLoadingProfile && !isManager && <ViewOnly />}
        </Flex>
      </RightToLeftTween>

      <BottomUpTween delay={delays[0]}>
        <BusinessStatus />
      </BottomUpTween>
      <BottomUpTween delay={delays[1]}>
        <BusinessHourSettings />
      </BottomUpTween>
      <BottomUpTween delay={delays[3]}>
        <HolidayTableConfig />
      </BottomUpTween>
    </Container>
  );
}
