import { BusinessHourDayType, IAutoReply } from '../models';

export const initBusinessHoursData: BusinessHourDayType[] = [
  {
    id: 1,
    label: 'day.monday',
    day: 'mon',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 2,
    label: 'day.tuesday',
    day: 'tue',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 3,
    label: 'day.wednesday',
    day: 'wed',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 4,
    label: 'day.thursday',
    day: 'thu',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 5,
    label: 'day.friday',
    day: 'fri',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 6,
    label: 'day.saturday',
    day: 'sat',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
  {
    id: 7,
    label: 'day.sunday',
    day: 'sun',
    enable: true,
    valid: true,
    fromTime: { hour: 0, minute: 0 },
    toTime: { hour: 23, minute: 55 },
  },
];

export const initHoursInWeek = {
  mon: {
    fromTime: null,
    toTime: null,
  },
  tue: {
    fromTime: null,
    toTime: null,
  },
  wed: {
    fromTime: null,
    toTime: null,
  },
  thu: {
    fromTime: null,
    toTime: null,
  },
  fri: {
    fromTime: null,
    toTime: null,
  },
  sat: {
    fromTime: null,
    toTime: null,
  },
  sun: {
    fromTime: null,
    toTime: null,
  },
};

export const initCalendarHours = {
  mon: {
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
    enable: false,
  },
  tue: {
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
    enable: false,
  },
  wed: {
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
    enable: false,
  },
  thu: {
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
    enable: false,
  },
  fri: {
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
    enable: false,
  },
  sat: {
    enable: false,
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
  },
  sun: {
    enable: false,
    fromTime: {
      hour: 10,
      minute: 0,
    },
    toTime: {
      hour: 19,
      minute: 0,
    },
  },
};

export const initHolidays = {
  year: 2023,
  rules: [],
};

export const initAutoReplyOutsideBusinessHours = {
  enable: true,
  message:
    'お問い合わせいただきありがとうございます。誠に申し訳ございませんが、ただいま準備中となっておりますため、ご返信までにお時間をいただいております。',
  image: '',
};

export const initAutoReplyConfig: IAutoReply = {
  enable: false,
  repeat: 1,
  messages: [
    {
      id: 'the_first_item_of_auto_reply_config',
      text: '',
      interval: 1,
    },
  ],
};
