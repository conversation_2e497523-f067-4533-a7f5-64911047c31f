import { Container, Flex, Group, Modal, Text, TextInput, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconTrash } from '@tabler/icons-react';
import dayjs from 'dayjs';
import 'dayjs/locale/ja';
import Image from 'next/image';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';

import { useTranslate } from '@tolgee/react';
import { deepCopy } from '../../../../../utils/common';
import { getPublicUrl } from '../../../../../utils/public';
import { Buttons, useDayFormat } from '../../../../Common';
import { useOperationSettingContext } from '../../../../operationSettingContext';
import TimeInput from '../../TimeInput';
import { HolidayType, ValidTimeFormat } from '../../types';
import CustomDatePicker from '../DatePicker/DatePicker';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
    paddingBottom: '1.2em',
  },
  cursorPointer: {
    cursor: 'pointer',
  },
  width155: {
    width: '155px',
  },
  navy: {
    color: '#1D2088',
  },
  modalStyle: {
    '& .mantine-Modal-content': {
      flexBasis: '42rem',
    },
  },
  buttonNavy: {
    width: 'fit-content',
    backgroundColor: '#1D2088',
    fontWeight: 700,
    '&:hover': {
      backgroundColor: '#3539BC',
    },
  },
  textInput: {
    '& .mantine-Input-input': {
      '&:focus': {
        borderColor: '#1D2088',
      },
    },
  },
  modalTitle: {
    fontSize: '1.25rem',
    fontWeight: 700,
    color: theme.colors.dark[4],
  },
}));

type ErrorTimePeriodsListType = {
  errorMsg: string;
  validTimePeriod: boolean;
};

type TimePeriodsType = {
  fromTime: ValidTimeFormat | null;
  toTime: ValidTimeFormat | null;
};

const HolidayCreateModal = ({
  visible,
  onClose,
  isEdit,
}: {
  visible: boolean;
  onClose: () => void;
  isEdit: boolean;
}) => {
  const {
    holidaySelected,
    setHolidaySelected,
    payloadHolidays,
    updateOperationSettings,
    customClosedTime,
  } = useOperationSettingContext();
  const { t } = useTranslate('workspace');

  const { classes, theme, cx } = useStyle();
  const timeDisplayFormat = useDayFormat();
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [timePeriods, setTimePeriods] = useState<TimePeriodsType[]>([
    { fromTime: '10:0', toTime: '19:0' },
  ]);
  const [holidayName, setHolidayName] = useState<string>(t('input_holiday_default_value'));
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');
  const [errorDatePicker, setErrorDatePicker] = useState<string>('');
  const [errorTimePeriodsList, setErrorTimePeriodsList] = useState<ErrorTimePeriodsListType[]>([
    { errorMsg: '', validTimePeriod: true },
  ]);
  const [isValidTimePeriod, setIsValidTimePeriods] = useState<boolean>(true);

  useEffect(() => {
    if (isEdit && holidaySelected) {
      mappingHolidaySelectedToModal(holidaySelected);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [holidaySelected]);

  const mappingHolidaySelectedToModal = (holidaySelected: HolidayType) => {
    setHolidayName(holidaySelected.name);
    setDateRange([new Date(holidaySelected.fromDate), new Date(holidaySelected.toDate)]);
    setTimePeriods(holidaySelected.timePeriods);
    holidaySelected.timePeriods.forEach((timePeriod, index) => {
      const errorTimePeriods = [...errorTimePeriodsList];
      errorTimePeriods[index] = { errorMsg: '', validTimePeriod: true };
      setErrorTimePeriodsList(() => errorTimePeriods);
    });
  };

  const selectedDateRange = useMemo(() => {
    if (dateRange.filter(Boolean).length == 2) {
      const fromTime = dateRange[0] ? dayjs(dateRange[0]).format(timeDisplayFormat) : '';
      const toTime = dateRange[1] ? dayjs(dateRange[1]).format(timeDisplayFormat) : '';

      setFromDate(dayjs(dateRange[0]).toISOString());
      setToDate(dayjs(dateRange[1]).toISOString());
      return fromTime !== toTime ? `${fromTime} 〜 ${toTime}` : fromTime;
    }
    return '';
  }, [dateRange, timeDisplayFormat]);

  const handleAddMoreTimePeriod = useCallback(() => {
    setTimePeriods((pre) => {
      pre = [...pre, { fromTime: '10:0', toTime: '19:0' }];
      return pre;
    });
    setErrorTimePeriodsList((pre) => {
      pre = [...pre, { errorMsg: '', validTimePeriod: true }];
      return pre;
    });
  }, []);

  const handleRemoveTimePeriod = useCallback(
    (index: number) => {
      const periods = [...timePeriods];
      periods.splice(index, 1);
      setTimePeriods(() => periods);
      setErrorTimePeriodsList((pre) => {
        pre.splice(index, 1);
        return pre;
      });
    },
    [timePeriods]
  );

  const handleResetData = () => {
    setHolidaySelected(null);
    setHolidayName(t('input_holiday_default_value'));
    setDateRange([null, null]);
    setTimePeriods([{ fromTime: '10:0', toTime: '19:0' }]);
    setErrorTimePeriodsList([{ errorMsg: '', validTimePeriod: true }]);
    setErrorDatePicker('');
  };

  const handleClose = () => {
    onClose();
    handleResetData();
  };

  const handleOnCancel = () => {
    onClose();
    handleResetData();
  };

  const handleOnConfirm = async () => {
    if (dateRange.filter(Boolean).length !== 2) {
      setErrorDatePicker(t('error_choose_date_range'));
      return;
    }
    if (!isValidTimePeriod) return;

    const closedTime = customClosedTime(timePeriods);
    const updatedItem = {
      name: holidayName,
      fromDate: fromDate,
      toDate: toDate,
      closedTime,
    };

    payloadHolidays.year = new Date().getFullYear();

    if (isEdit) {
      const index = payloadHolidays.rules.findIndex((item) => item.id === holidaySelected.id);
      const { name, fromDate, toDate, closedTime } = updatedItem;
      payloadHolidays.rules[index] = {
        ...payloadHolidays.rules[index],
        name,
        fromDate,
        toDate,
        closedTime,
      };
    } else {
      payloadHolidays.rules.push(updatedItem);
    }

    await updateOperationSettings({ holidays: payloadHolidays });

    handleResetData();
    onClose();
  };

  const validateTimePeriods = (timePeriods: TimePeriodsType[], index: number) => {
    const splitFromTime = timePeriods[index].fromTime.split(':');
    const splitToTime = timePeriods[index].toTime.split(':');
    const fromHour = Number(splitFromTime[0]);
    const fromMinute = Number(splitFromTime[1]);
    const toHour = Number(splitToTime[0]);
    const toMinute = Number(splitToTime[1]);
    const errorTimePeriods = [...errorTimePeriodsList];
    let isValid = true;
    if (fromHour > toHour) {
      errorTimePeriods[index] = {
        errorMsg: t('invalid_time_input'),
        validTimePeriod: false,
      };
      setErrorTimePeriodsList(() => errorTimePeriods);
      setIsValidTimePeriods(false);
      return;
    }
    if (fromHour === toHour && fromMinute >= toMinute) {
      errorTimePeriods[index] = {
        errorMsg: t('invalid_time_input'),
        validTimePeriod: false,
      };
      isValid = false;
    } else {
      errorTimePeriods[index] = { errorMsg: '', validTimePeriod: true };
      isValid = true;
    }
    setErrorTimePeriodsList(() => errorTimePeriods);
    setIsValidTimePeriods(isValid);
  };

  return (
    <Modal
      opened={visible}
      centered
      onClose={handleClose}
      withCloseButton={false}
      size={'lg'}
      className={classes.modalStyle}
    >
      <Modal.CloseButton className='' size={'lg'} style={{ right: '-94%' }} />
      <Modal.Title mb={'md'} pl={'md'} className={classes.modalTitle}>
        {isEdit ? t('edit_holiday_label') : t('add_holiday_title')}
      </Modal.Title>

      <Modal.Body mt={'lg'}>
        <Flex mb={'md'} direction={'row'} align={'center'}>
          <label htmlFor='team_name_id' className={classes.width155}>
            <Title order={6} c='dark.4'>
              {t('input_holiday_name_label')}
            </Title>
          </label>
          <TextInput
            className={classes.textInput}
            id='team_name_id'
            withAsterisk
            value={holidayName}
            onChange={(event) => setHolidayName(event.target.value)}
            onInput={null}
            onBlur={null}
            sx={{ flexGrow: 1 }}
            error={false}
          />
        </Flex>
        <Flex
          direction={'row'}
          sx={{
            display: 'grid',
            gridTemplateColumns: '25.5% 74.5%',
          }}
        >
          <label htmlFor='team_description_id' className={classes.width155}>
            <Title order={6} c='dark.4'>
              {t('input_date_time_label')}
              <span style={{ color: theme.colors.red[6] }}>*</span>
            </Title>
          </label>
          <CustomDatePicker value={dateRange} onChange={setDateRange} />
        </Flex>
        {errorDatePicker && (
          <Text fz='sm' c='red.4' ta='center' my={8}>
            {errorDatePicker}
          </Text>
        )}
        <Group mt={'md'}>
          <Text c='gray.6' size={'12px'}>
            {t('date_time_selected')} {selectedDateRange}
          </Text>
        </Group>

        <Container className={classes.container} mt={'md'}>
          <Flex direction={'row'}>
            <Container sx={{ flexGrow: 1 }} pl={0} pr={0}>
              {timePeriods.map((timePeriod, index) => {
                return (
                  <Flex direction='column' mb={10} key={index}>
                    <Group>
                      <TimeInput
                        value={timePeriod.fromTime}
                        onChange={(val) => {
                          const periods = deepCopy(timePeriods);
                          periods[index].fromTime = val;
                          setTimePeriods(() => periods);
                          validateTimePeriods(periods, index);
                        }}
                        validTime={errorTimePeriodsList[index].validTimePeriod}
                      />
                      <Text size={'10px'}>〜</Text>
                      <TimeInput
                        value={timePeriod.toTime}
                        onChange={(val) => {
                          const periods = deepCopy(timePeriods);
                          periods[index].toTime = val;
                          setTimePeriods(() => periods);
                          validateTimePeriods(periods, index);
                        }}
                        validTime={errorTimePeriodsList[index].validTimePeriod}
                      />
                      <IconTrash
                        className={cx([classes.navy, classes.cursorPointer])}
                        size={'20px'}
                        onClick={() => handleRemoveTimePeriod(index)}
                      />
                    </Group>
                    {!errorTimePeriodsList[index].validTimePeriod && (
                      <Text c='red.5' fz='sm' mt={4}>
                        {errorTimePeriodsList[index].errorMsg}
                      </Text>
                    )}
                  </Flex>
                );
              })}
            </Container>
            <Group
              w={'40px'}
              sx={{
                alignItems: 'flex-start',
                justifyContent: 'center',
                paddingTop: '8px',
                width: '30px',
                cursor: 'pointer',
              }}
            >
              <span onClick={handleAddMoreTimePeriod}>
                <Image
                  width={22}
                  height={22}
                  alt='icon_plus'
                  src={getPublicUrl('/images/icon_circle_plus.svg')}
                />
              </span>
            </Group>
          </Flex>
        </Container>
      </Modal.Body>

      <Group justify='flex-end' mt={'xl'} mb={'lg'} mr={'lg'}>
        <Buttons.Cancel label={t('modal_operator.button_cancel')} onClick={handleOnCancel} />
        <Buttons.Confirm
          label={t('add_label')}
          onClick={handleOnConfirm}
          className={classes.buttonNavy}
        />
      </Group>
    </Modal>
  );
};

export default memo(HolidayCreateModal);
