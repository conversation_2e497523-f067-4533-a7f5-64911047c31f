import { DatePicker, DatesRangeValue } from '@mantine/dates';
import { createStyles } from '@mantine/emotion';
import 'dayjs/locale/ja';
import { useAppContext } from '../../../../appContext';

const useStyle = createStyles((theme) => ({
  datePicker: {
    border: `1px solid ${theme.colors.gray[4]}`,
    borderRadius: theme.radius.sm,
    '& button.mantine-DatePicker-day[data-in-range], & button.mantine-DatePicker-day[data-first-in-range]':
      {
        position: 'relative',
        backgroundColor: 'rgba(245, 235, 255, 0.6)',
        '&:hover': {
          backgroundColor: 'rgba(227, 195, 255, 0.65)',
        },
      },
    '& button.mantine-DatePicker-day[data-selected][data-in-range], & button.mantine-DatePicker-day[data-first-in-range]':
      {
        backgroundColor: '#1D2088',
        '&:hover': {
          backgroundColor: '#3539BC',
        },
      },

    '.mantine-MonthLevelGroup-monthLevelGroup': {
      display: 'block',
      width: '100%',
    },
    '.mantine-CalendarHeader-calendarHeader': {
      maxWidth: '100%',
    },
    '.mantine-DatePicker-month': {
      width: '100%',
    },
    '.mantine-Day-day': {
      width: '100%',
    },
  },
}));

const CustomDatePicker = ({
  value,
  onChange,
}: {
  value: DatesRangeValue;
  // eslint-disable-next-line no-unused-vars
  onChange: (value: DatesRangeValue) => void;
}) => {
  const { lang } = useAppContext();
  const { classes, cx } = useStyle();
  return (
    <DatePicker
      type='range'
      locale={lang}
      value={value}
      onChange={onChange}
      allowSingleDateInRange
      className={cx('custom-date-picker', classes.datePicker)}
      styles={(theme) => ({
        calendarHeader: {
          display: 'flex',
          flexDirection: 'row',
        },
        calendarHeaderLevel: {
          flexGrow: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ':hover': {
            backgroundColor: theme.colors.gray[0],
          },
        },
        calendarHeaderControl: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '2.625rem',
          height: '2.625rem',
          ':hover': {
            backgroundColor: theme.colors.gray[0],
          },
        },
        calendarHeaderControlIcon: {
          '&[data-direction="previous"]': {
            transform: 'rotate(90deg)',
          },
          '&[data-direction="next"]': {
            transform: 'rotate(-90deg)',
          },
        },
        month: {
          borderSpacing: 0,
        },
        monthsList: {
          width: '100%',
        },
        monthsListControl: {
          width: '100%',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          userSelect: 'none',
          padding: '0.25rem',
          ':hover': {
            backgroundColor: theme.colors.gray[0],
          },
        },
        yearsList: {
          width: '100%',
        },
        yearsListControl: {
          width: '100%',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          userSelect: 'none',
          padding: '0.25rem',
          ':hover': {
            backgroundColor: theme.colors.gray[0],
          },
        },

        weekday: {
          color: theme.colors.gray[4],
          fontWeight: 400,
          paddingBottom: `calc(0.5rem)`,
        },
        monthTbody: {
          display: 'table-row-group',
          verticalAlign: 'middle',
        },
        monthCell: {
          '&[data-with-spacing]': {
            padding: '0.03125rem',
          },
        },
        day: {
          width: '100%',
          height: '2.625rem',
          display: 'inline-flex',
          border: 0,
          padding: 0,
          alignItems: 'center',
          justifyContent: 'center',
          userSelect: 'none',
          cursor: 'pointer',
          borderRadius: '0.25rem',
          '&[data-outside]': {
            color: theme.colors.gray[4],
          },
          ':hover': {
            backgroundColor: theme.colors.gray[0],
          },
          '&[data-in-range]': {
            borderRadius: '0px',
          },
          '&[data-first-in-range]': {
            color: '#ced4da',
            borderTopLeftRadius: '0.25rem',
            borderBottomLeftRadius: '0.25rem',
          },
          '&[data-last-in-range]': {
            color: '#ced4da',
            borderTopRightRadius: '0.25rem',
            borderBottomRightRadius: '0.25rem',
          },
        },
      })}
    />
  );
};
export default CustomDatePicker;
