import { Group, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import { useOperationSettingContext } from '../../../../operationSettingContext';

const useStyle = createStyles((theme) => ({
  statusDotGreen: {
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    backgroundColor: theme.colors.green[6],
    marginRight: '8px',
  },
  statusDotRed: {
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    backgroundColor: theme.colors.red[6],
    marginRight: '8px',
  },
}));

const OperationState: React.FC<any> = () => {
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');

  const { operationStatus, currentHoliday } = useOperationSettingContext();

  return (
    <Group gap={0} mx={24}>
      <span className={operationStatus ? classes.statusDotGreen : classes.statusDotRed}></span>
      <Text color='gray.7'>
        {operationStatus
          ? t('status_open')
          : `${t('status_preparing')} ${currentHoliday ? t('holiday_setting') : ''}`}
      </Text>
    </Group>
  );
};

export default OperationState;
