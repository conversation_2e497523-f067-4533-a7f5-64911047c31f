import { Container } from '@mantine/core';
import React from 'react';
import TextEditor from '../../../../TextEditor';
import { useOperationSettingContext } from '../../../../operationSettingContext';

interface AutoReplyInputSectionProps {}

const AutoReplyInputSection: React.FC<AutoReplyInputSectionProps> = ({}) => {
  const { lang } = useOperationSettingContext();

  return (
    <Container fluid p={0}>
      <TextEditor lang={lang} />
    </Container>
  );
};

export default AutoReplyInputSection;
