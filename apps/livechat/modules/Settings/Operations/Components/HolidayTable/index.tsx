import React, { useCallback } from 'react';
import { Container, Table, Text, ActionIcon, Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconEdit, IconX } from '@tabler/icons-react';
import { useFuncFormatDate } from '../../../../Common';
import { HolidayType } from '../../types';
import ConfirmModal from '../../../../Common/ConfirmModal';
import HolidayCreateModal from '../HolidayCreateModal';
import { useOperationSettingContext } from '../../../../operationSettingContext';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import useCustomStyle from '../../../../../hooks/useCustomStyle';
import { useTranslate } from '@tolgee/react';

const useStyle = createStyles(() => ({
    cursorPointer: {
        cursor: 'pointer',
    },
    navy: {
        color: '#1D2088',
    },
}));

const HolidayTable = ({ holidays = [] }: { holidays: HolidayType[] }) => {
    const { classes, cx } = useStyle();
    const { t } = useTranslate('workspace');

    const formatDate = useFuncFormatDate();
    const { visible: visibleEditModal, toggle: toggleEditModal } = useVisibilityControl();
    const { visible: visibleConfirmModal, toggle: toggleConfirmModal } = useVisibilityControl();
    const {
        holidaySelected,
        setHolidaySelected,
        updateOperationSettings,
        payloadHolidays,
        isManager,
    } = useOperationSettingContext();
    const { classes: customClasses } = useCustomStyle();

    const formatHours = useCallback(
        (holiday: HolidayType) => {
            if (holiday.timePeriods.length === 0) return '';

            let timePeriods = '';
            holiday.timePeriods.forEach((time) => {
                const fromHour = `${time.fromTime?.split(':')?.[0]}${t('hour')}`;
                const fromMinute = `${time.fromTime?.split(':')?.[1].padStart(2, '0')}${t('minute')}`;
                const toHour = `${time.toTime?.split(':')?.[0]}${t('hour')}`;
                const toMinute = `${time.toTime?.split(':')?.[1].padStart(2, '0')}${t('minute')}`;
                timePeriods += `${fromHour} ${fromMinute} 〜 ${toHour} ${toMinute}, `;
            });
            return timePeriods.trim().slice(0, -1);
        },
        [t],
    );

    const formatPeriodHoliday = useCallback(
        (holiday: HolidayType) => {
            const fromDate = formatDate(holiday.fromDate);
            const toDate = formatDate(holiday.toDate);

            const timeDate = fromDate !== toDate ? `${fromDate} 〜 ${toDate}` : fromDate;
            const timePeriods = formatHours(holiday);

            return (
                <Text title={`${timeDate} ${timePeriods}`} lineClamp={1} fz="sm">
                    {timeDate}
                    {'  '}
                    {timePeriods}
                </Text>
            );
        },
        [formatDate, formatHours],
    );

    const handleToggleEditModal = useCallback(() => {
        toggleEditModal();
    }, [toggleEditModal]);

    const handleConfirmDeleteHoliday = (confirm?: boolean) => {
        if (confirm) {
            const removePayloadHolidays = { ...payloadHolidays };
            removePayloadHolidays.rules = removePayloadHolidays.rules.filter(
                (item) => item.id !== holidaySelected.id,
            );
            updateOperationSettings({ holidays: removePayloadHolidays });
        }
        toggleConfirmModal();
    };

    return (
        <Container mt={'md'} maw={'100%'}>
            <Table
                pl={'18px'}
                pr={'18px'}
                verticalSpacing="xs"
                highlightOnHover
                withTableBorder
                withRowBorders
            >
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th style={{ maxWidth: '100px', textAlign: 'left' }}>
                            <Text c="dark.2" fz="sm" fw={700}>
                                {t('input_holiday_name_label')}
                            </Text>
                        </Table.Th>
                        <Table.Th style={{ textAlign: 'left' }}>
                            <Text c="dark.2" fz="sm" fw={700}>
                                {t('date_label')}
                            </Text>
                        </Table.Th>
                        <Table.Th style={{ width: '60px', textAlign: 'left' }}>
                            <Text c="dark.2" fz="sm" fw={700}>
                                {t('edit_label')}
                            </Text>
                        </Table.Th>
                        <Table.Th style={{ width: '60px', textAlign: 'left' }}>
                            <Text c="dark.2" fz="sm" fw={700}>
                                {t('delete_label')}
                            </Text>
                        </Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                    {holidays.map((holiday, index) => {
                        return (
                            <Table.Tr key={`${index}${holiday.name}`}>
                                <Table.Td>
                                    <Text lineClamp={1} c="dark.4" fz="sm">
                                        {holiday.name}
                                    </Text>
                                </Table.Td>
                                <Table.Td>
                                    <Box className={customClasses.text} fz="sm">
                                        {formatPeriodHoliday(holiday)}
                                    </Box>
                                </Table.Td>
                                <Table.Td>
                                    {isManager ? (
                                        <IconEdit
                                            className={cx([classes.navy, classes.cursorPointer])}
                                            size={'20px'}
                                            onClick={() => {
                                                handleToggleEditModal();
                                                setHolidaySelected(holiday);
                                            }}
                                        />
                                    ) : (
                                        <ActionIcon disabled className={customClasses.actionIcon}>
                                            <IconEdit size={'20px'} />
                                        </ActionIcon>
                                    )}
                                </Table.Td>
                                <Table.Td>
                                    {isManager ? (
                                        <IconX
                                            className={cx([classes.navy, classes.cursorPointer])}
                                            size={'20px'}
                                            onClick={() => {
                                                toggleConfirmModal();
                                                setHolidaySelected(holiday);
                                            }}
                                        />
                                    ) : (
                                        <ActionIcon disabled className={customClasses.actionIcon}>
                                            <IconX size={'20px'} />
                                        </ActionIcon>
                                    )}
                                </Table.Td>
                            </Table.Tr>
                        );
                    })}
                </Table.Tbody>
            </Table>
            <HolidayCreateModal
                visible={visibleEditModal}
                onClose={handleToggleEditModal}
                isEdit={true}
            />
            <ConfirmModal
                reverse
                opened={visibleConfirmModal}
                titleConfirm={t('delete_holiday_confirm_message')}
                onClose={handleConfirmDeleteHoliday}
                btnConfirmLabel={t('delete_btn_label')}
            />
        </Container>
    );
};

export default HolidayTable;
