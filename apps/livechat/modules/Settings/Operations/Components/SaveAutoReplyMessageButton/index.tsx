import { But<PERSON>, Loader, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useRichTextEditorContext } from '@mantine/tiptap';
import { IconCheck } from '@tabler/icons-react';
import React, { useCallback, useState } from 'react';
import useCustomStyle from '../../../../../hooks/useCustomStyle';
import { useTextEditorContext } from '../../../../TextEditor/context';
import { useOperationSettingContext } from '../../../../operationSettingContext';

const useStyle = createStyles(() => ({
  buttonNavy: {
    width: 'fit-content',
    backgroundColor: '#1D2088',
    fontWeight: 700,
    '&:hover': {
      backgroundColor: '#3539BC',
    },
  },
  textLoading: {
    display: 'flex',
    alignItems: 'center',
  },
}));

const SaveAutoReplyMessageButton: React.FC<any> = () => {
  const { classes, cx } = useStyle();
  const [isSaved, setIsSaved] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { classes: customClasses } = useCustomStyle();
  const { t } = useTextEditorContext();
  const { editor } = useRichTextEditorContext();
  const { replyOutsideBusinessHour, updateOperationSettings, isManager } =
    useOperationSettingContext();

  const handleSave = useCallback(async () => {
    setIsLoading(true);
    const payload = {
      ...replyOutsideBusinessHour,
      message: editor && editor.getText(),
    };

    await updateOperationSettings({ outBusinessHour: payload });
    setIsLoading(false);
    setIsSaved(true);
    setTimeout(() => {
      setIsSaved(false);
    }, 1000);
  }, [editor, replyOutsideBusinessHour, updateOperationSettings]);

  if (!isManager) {
    return (
      <Button
        className={cx(classes.buttonNavy, customClasses.button)}
        variant='filled'
        color='violet'
        mt={'sm'}
        size='xs'
        radius={'md'}
        disabled={true}
      >
        <Text>{t('save')}</Text>
      </Button>
    );
  }

  return (
    <Button
      data-testid='btn-save-auto-reply-id'
      className={classes.buttonNavy}
      variant='filled'
      color='violet'
      mt={'sm'}
      size='xs'
      radius={'md'}
      onClick={handleSave}
      disabled={isLoading}
    >
      {isLoading ? (
        <Text className={classes.textLoading}>
          <Loader size='xs' color='violet.5' mr={8} />
          {t('save')}
        </Text>
      ) : isSaved ? (
        <Text className={classes.textLoading}>
          <IconCheck size={24} strokeWidth={2} color={'#FFFFFF'} />
          {t('saved')}
        </Text>
      ) : (
        <Text>{t('save')}</Text>
      )}
    </Button>
  );
};

export default SaveAutoReplyMessageButton;
