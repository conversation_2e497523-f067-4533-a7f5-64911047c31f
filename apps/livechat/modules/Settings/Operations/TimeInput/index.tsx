import { Group, Select, SelectProps } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTolgee, useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { TimeOptionType } from '../models';
import { HourType, MinuteType, ValidTimeFormat } from '../types';

type TimeInputType = {
  value: ValidTimeFormat;
  // eslint-disable-next-line no-unused-vars
  onChange?: (value: ValidTimeFormat) => void;
  disabled?: boolean;
  day?: string;
  validTime?: boolean;
};

const useStyle = createStyles((theme) => ({
  selectStyle: {
    width: '100px',
    '& .mantine-Select-input': {
      '&:focus': {
        borderColor: '#1D2088',
      },
    },
  },
  selectErrorStyle: {
    '& .mantine-Select-input': {
      '&:focus': {
        borderColor: theme.colors.red[5],
      },
      borderColor: theme.colors.red[5],
    },
  },
}));

export default function TimeInput({
  value,
  onChange,
  disabled = false,
  validTime = true,
}: TimeInputType) {
  const { t } = useTranslate('workspace');
  const { getLanguage } = useTolgee();

  const { classes, cx } = useStyle();

  const [hour, setHour] = useState<HourType>(() =>
    !!value ? (value.split(':').at(0) as HourType) : '0'
  );
  const [minute, setMinute] = useState<MinuteType>(
    !!value ? (value.split(':').at(1) as MinuteType) : '0'
  );

  const HOUR_OPTIONS = useMemo<TimeOptionType[]>(() => {
    return new Array(24).fill(0).map((item, index) => ({
      label: `${index.toString().padStart(2, '0')}${getLanguage() === 'ja' ? t('hour') : ''}`,
      value: `${index.toString().padStart(1, '0')}`,
    }));
  }, [t, getLanguage]);

  const MINUTE_OPTIONS = useMemo<TimeOptionType[]>(() => {
    return new Array(60).fill(0).map((item, index) => ({
      label: `${index.toString().padStart(2, '0')}${getLanguage() === 'ja' ? t('minute') : ''}`,
      value: `${index.toString().padStart(1, '0')}`,
    }));
  }, [t, getLanguage]);

  const handleChange = useCallback(
    async (type: 'hour' | 'minute', value: HourType | MinuteType) => {
      let currHour = hour;
      let currMinute = minute;
      if (type === 'hour') {
        setHour(value as HourType);
        currHour = value as HourType;
      }

      if (type === 'minute') {
        setMinute(value as MinuteType);
        currMinute = value as MinuteType;
      }

      onChange?.(`${currHour}:${currMinute}` as ValidTimeFormat);
    },
    [hour, minute, onChange]
  );

  useEffect(() => {
    if (!value) return;
    try {
      const [hourIn, minuteIn] = (value as string).split(':');
      if (hour !== hourIn) setHour(hourIn as HourType);
      if (minuteIn !== minute) setMinute(minuteIn as MinuteType);
    } catch (e) {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <Group>
      <CustomSelect
        className={cx(classes.selectStyle, validTime === false && classes.selectErrorStyle)}
        value={hour}
        data={HOUR_OPTIONS}
        disabled={disabled}
        withCheckIcon={false}
        onChange={(value) => handleChange('hour', value as HourType)}
      ></CustomSelect>
      <CustomSelect
        className={cx(classes.selectStyle, validTime === false && classes.selectErrorStyle)}
        value={minute}
        data={MINUTE_OPTIONS}
        disabled={disabled}
        onChange={(value) => handleChange('minute', value as MinuteType)}
      ></CustomSelect>
    </Group>
  );
}

const CustomSelect = (props: SelectProps & React.RefAttributes<HTMLInputElement>) => {
  return (
    <Select
      {...props}
      withCheckIcon={false}
      styles={(theme) => ({
        option: {
          '&[data-checked="true"]': {
            '&, &:hover': {
              backgroundColor: theme.colors.navy[0],
              color: theme.white,
            },
          },
        },
        input: {
          '&:focus': {
            borderColor: theme.colors.navy[5],
          },
        },
      })}
    />
  );
};
