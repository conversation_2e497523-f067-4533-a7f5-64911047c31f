import {
  Avatar,
  Button,
  Container,
  FileButton,
  Flex,
  Group,
  Modal,
  Text,
  TextInput,
  Textarea,
  rem,
} from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import { CreateTeamInfo } from '@resola-ai/models';
import { IconLoader, IconUsers } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import useFileInfo from '../../../../hooks/useFileInfo';
import { Buttons } from '../../../Common';
import { useTeamDetailSettingContext } from '../TeamDetail/TeamDetailContext';
import { useTeamListSettingsContext } from '../TeamListSettingsPanel/TeamListSettingContext';

const MAX_FILE_SIZE_ACCEPT = 1;

type CreateTeamModalType = {
  mode: 'create' | 'edit';
  editData?: CreateTeamInfo;
};

export const rotate = keyframes({
  '0%': { transform: `translate(-50%, -50%) rotate(0deg)` },
  '100%': { transform: `translate(-50%, -50%) rotate(360deg)` },
});

const useStyle = createStyles((theme) => ({
  colorRed: {
    color: theme.colors.red[7],
  },
  cursorPointer: {
    cursor: 'pointer',
  },
  fontNormal: {
    fontWeight: 700,
    fontSize: '14px',
    color: theme.colors.dark[4],
  },
  width110px: {
    width: '110px',
  },
  inputStyle: {
    '& input:focus:not([data-invalid])': {
      borderColor: theme.colors.navy[0],
    },
  },
  textAreaStyle: {
    '& textarea:focus:not([data-invalid])': {
      borderColor: theme.colors.navy[0],
    },
  },
  modalTitle: {
    fontSize: '20px',
    fontWeight: 700,
    color: theme.colors.dark[4],
  },
  divLoading: {
    width: '60px',
    height: '60px',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: '#3333339c',
    borderRadius: '50%',
  },
  loadingStyle: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    animation: `${rotate} 3s linear infinite`,
  },
}));

const useAdjustForCorrectContext = () => {
  try {
    const { visibleCreateEditTeamModal, toggleCreateEditTeamModal, onCreateTeam } =
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useTeamListSettingsContext();
    return {
      visibleCreateEditTeamModal,
      toggleCreateEditTeamModal,
      onConfirmInputData: onCreateTeam,
    };
  } catch (e) {}
  const { visibleCreateEditTeamModal, toggleCreateEditTeamModal, onEditTeam } =
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useTeamDetailSettingContext();
  return {
    visibleCreateEditTeamModal,
    toggleCreateEditTeamModal,
    onConfirmInputData: onEditTeam,
  };
};

const maxCharName = 20 as const;
const maxCharDes = 200 as const;

export default function CreateEditTeamModal({ mode = 'create', editData }: CreateTeamModalType) {
  const { t } = useTranslate('workspace');

  const { visibleCreateEditTeamModal, toggleCreateEditTeamModal, onConfirmInputData } =
    useAdjustForCorrectContext();
  const { classes, cx } = useStyle();
  const [isLoading, setIsLoading] = useState(false);

  const [error, setError] = useState<{
    team_name: boolean | string;
    team_description: boolean | string;
  }>({
    team_name: false,
    team_description: false,
  });

  const [file, setFile] = useState<File | string | null>(null);
  const [fileInfo] = useFileInfo(file);

  const teamNameRef = useRef<HTMLInputElement>(null);
  const teamDescriptionRef = useRef<HTMLTextAreaElement>(null);

  const resetRef = useRef<() => void>(null);

  const handleSelectFile = useCallback((file) => {
    if (file instanceof File && file.name.split('.').pop().toLowerCase() !== 'png') {
      return;
    }
    setFile(file);
  }, []);

  const clearFile = useCallback(() => {
    setFile(null);
    resetRef.current?.();
  }, []);

  /**
   * When in edit mode, must have editData to show the value.
   */
  useEffect(() => {
    let timeout = null;
    if (mode === 'edit' && editData && visibleCreateEditTeamModal) {
      timeout = setTimeout(() => {
        teamNameRef.current && (teamNameRef.current.value = editData?.name);
        teamDescriptionRef.current && (teamDescriptionRef.current.value = editData?.description);
        if (editData?.picture) {
          setFile(editData?.picture);
        }
      }, 100);
    }
    return () => clearTimeout(timeout);
  }, [editData, mode, visibleCreateEditTeamModal]);

  const isErrorFile = useCallback(() => {
    if ((fileInfo?.size || 0) <= MAX_FILE_SIZE_ACCEPT) {
      return false;
    }
    return true;
  }, [fileInfo?.size]);

  const isError = useCallback(() => {
    let teamName = teamNameRef.current?.value.trim() || '';
    let teamDescription = teamDescriptionRef.current?.value.trim() || '';

    let preError: { team_name: string | boolean; team_description: string | boolean } = {
      team_name: false,
      team_description: false,
    };
    let hasError = false;

    if (!teamName) {
      preError.team_name = t('field_required_message');
      hasError = true;
    } else if (teamName.length > maxCharName) {
      preError.team_name = t('only_input_number_chars', { number: maxCharName });
      hasError = true;
    }

    if (teamDescription.length > maxCharDes) {
      preError.team_description = t('only_input_number_chars', { number: maxCharDes });
      hasError = true;
    }

    if (isErrorFile()) {
      hasError = true;
    }

    if (!hasError) {
      setError((pre) => ({ ...pre, team_name: false, team_description: false }));
      return false;
    }

    setError((pre) => ({ ...pre, ...preError }));
    return true;
  }, [isErrorFile, t]);

  const clearError = () => {
    setError((pre) => ({ ...pre, team_name: false }));
  };

  const handleConfirm = useCallback(async () => {
    if (isError()) return;
    setIsLoading(true);
    await onConfirmInputData(
      {
        name: teamNameRef.current.value,
        description: teamDescriptionRef.current.value,
        picture: file,
      },
      (message) => {
        if (message === 'TEAM_WITH_NAME_EXISTED') {
          setError((pre) => ({
            ...pre,
            team_name: t('duplicate_team_name'),
          }));
        }
      }
    );
    setIsLoading(false);
  }, [file, isError, onConfirmInputData, t]);

  const handleOnCancel = useCallback(() => {
    teamNameRef.current && (teamNameRef.current.value = '');
    teamDescriptionRef.current && (teamDescriptionRef.current.value = '');
    clearFile();
    clearError();
    toggleCreateEditTeamModal();
  }, [clearFile, toggleCreateEditTeamModal]);

  return (
    <Modal
      opened={visibleCreateEditTeamModal}
      centered
      onClose={handleOnCancel}
      withCloseButton={false}
      size={'lg'}
    >
      <Modal.CloseButton className='' size={'lg'} style={{ right: '-94%' }} />
      <Modal.Title
        title={t('tab.team_list.create_team_modal.title')}
        mb={'lg'}
        pl={'md'}
        className={classes.modalTitle}
      >
        {t(
          mode === 'create'
            ? 'tab.team_list.create_team_modal.title'
            : 'tab.team_list.edit_team_modal.title'
        )}
      </Modal.Title>

      <Modal.Body mt={'lg'}>
        <Flex mb={'md'} direction={'row'}>
          <label
            htmlFor='team_name_id'
            className={cx(classes.fontNormal, classes.width110px, classes.cursorPointer)}
          >
            {t('tab.team_list.create_team_modal.name')}
            <span className={classes.colorRed}>*</span>
          </label>
          <TextInput
            id='team_name_id'
            withAsterisk
            ref={teamNameRef}
            onInput={isError}
            onBlur={isError}
            sx={{ flexGrow: 1 }}
            disabled={isLoading}
            placeholder={t('tab.team_list.create_team_modal.name.placeholder')}
            className={classes.inputStyle}
            error={error.team_name}
          />
        </Flex>
        <Flex direction={'row'}>
          <label
            htmlFor='team_description_id'
            className={cx(classes.fontNormal, classes.width110px, classes.cursorPointer)}
          >
            {t('tab.team_list.create_team_modal.description')}
          </label>
          <Textarea
            id='team_description_id'
            ref={teamDescriptionRef}
            sx={{ flexGrow: 1 }}
            onInput={isError}
            onBlur={isError}
            disabled={isLoading}
            className={classes.textAreaStyle}
            placeholder={t('tab.team_list.create_team_modal.description.placeholder')}
            error={error.team_description}
            rows={3}
          />
        </Flex>
        <Flex direction={'row'} mt={'md'}>
          <label className={cx(classes.fontNormal, classes.width110px, classes.cursorPointer)}>
            {t('image_label')}
          </label>
          <Container sx={{ marginLeft: 'unset', marginRight: 'unset' }} px={0}>
            <Text c={'gray.7'} size={rem(14)} fw={400}>
              {t('image_des')}
            </Text>
            <Flex
              mt={'md'}
              gap='md'
              direction='row'
              justify='flex-start'
              align='flex-start'
              wrap='nowrap'
            >
              {!isLoading ? (
                <Avatar h={60} w={60} radius='xl' color='dark.3' src={fileInfo?.url || ''}>
                  <IconUsers size={40} />
                </Avatar>
              ) : (
                <div style={{ position: 'relative' }}>
                  <Avatar h={60} w={60} radius='xl' color='dark.3' src={fileInfo?.url || ''}>
                    <IconUsers size={40} />
                  </Avatar>
                  <Flex className={classes.divLoading}>
                    <IconLoader
                      size={25}
                      stroke={2}
                      color={'white'}
                      className={classes.loadingStyle}
                    />
                  </Flex>
                </div>
              )}
              <Container sx={{ marginLeft: 'unset', marginRight: 'unset' }} px={0}>
                <FileButton resetRef={resetRef} onChange={handleSelectFile} accept='.png'>
                  {(props) => (
                    <Button
                      {...props}
                      variant='outline'
                      color='navy.0'
                      size='compact-md'
                      radius='sm'
                      disabled={isLoading}
                      mb={'xs'}
                    >
                      <Text size={rem(12)}>{t('update_image_button')}</Text>
                    </Button>
                  )}
                </FileButton>
                <Text size={rem(12)} fw={400} c='gray.6'>
                  {t('image_upload_des')}
                </Text>
                {fileInfo?.size > MAX_FILE_SIZE_ACCEPT && (
                  <Text size={rem(12)} fw={400} c='red.5' mt={'xs'}>
                    {t('image_exceed_size', {
                      number: Math.ceil(fileInfo?.size || MAX_FILE_SIZE_ACCEPT),
                    })}
                  </Text>
                )}
              </Container>
            </Flex>
          </Container>
        </Flex>
      </Modal.Body>
      <Group justify='flex-end' mt={'xl'} mb={'lg'} mr={'lg'}>
        <Buttons.Cancel label={t('modal_operator.button_cancel')} onClick={handleOnCancel} />
        <Buttons.Confirm
          label={t('modal_operator.button_confirm')}
          onClick={handleConfirm}
          disabled={isLoading}
        />
      </Group>
    </Modal>
  );
}
