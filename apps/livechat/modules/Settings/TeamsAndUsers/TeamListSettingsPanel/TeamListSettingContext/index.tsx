import React, {
    ReactNode,
    createContext,
    useCallback,
    useContext,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useTeamContext } from '../../../../teamContext';
import { useUserContext } from '../../../../userContext';
import { TeamListSettingItem, UserListSettingItem } from '../../types';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import ApiService from '../../../../../services/api';
import { CreateTeamInfo } from '@resola-ai/models';
import { StatusEnum } from '@resola-ai/models';
import { MemberData } from '../../EditMemberDataModal';
import {
    assignUserToTeam,
    editUserInfo,
    mappingTeamsSettingData,
    mappingUsersSettingData,
    updateUserRoleAndStatus,
} from '../../../Helpers';
import { delay } from '../../../../../utils/common';

const useTeamListSettings = () => {
    const {
        isManager,
        userProfile,
        listOperators,
        reloadListOperator,
        isLoadingListOperators,
        mutate: reloadUserProfile,
    } = useUserContext();
    const { teamList, isLoadingTeamList, reloadTeamList } = useTeamContext();
    const { visible: visibleCreateEditTeamModal, toggle: toggleCreateEditTeamModal } =
        useVisibilityControl();
    const { visible: visibleSuccessCreateTeamModal, toggle: toggleSuccessCreateTeamModal } =
        useVisibilityControl();
    const { visible: visibleAssignMemberModal, toggle: toggleAssignMemberModal } =
        useVisibilityControl();

    const {
        visible: visibleEditMemberModal,
        toggle: toggleEditMemberModal,
        close: closeEditMemberModal,
    } = useVisibilityControl();

    const memberEditRef = useRef<MemberData | null>(null);

    const [teamInfo, setTeamInfo] = useState<CreateTeamInfo>({
        name: '',
        description: '',
        picture: undefined,
    });

    const teamsSettingData: TeamListSettingItem[] = useMemo(
        () => mappingTeamsSettingData(teamList, listOperators),
        [listOperators, teamList],
    );

    const usersSettingData: UserListSettingItem[] = useMemo(
        () => mappingUsersSettingData(teamList, listOperators),
        [listOperators, teamList],
    );

    const userListForAssignMemberModal = useMemo(() => {
        if (teamInfo?.id && teamsSettingData.find((team) => team.id === teamInfo.id)) {
            const currentMember = teamsSettingData
                .find((team) => team.id === teamInfo.id)
                ?.members.map((mem) => mem.id);
            return usersSettingData.filter((user) => !currentMember.includes(user.id));
        }
        return usersSettingData;
    }, [teamInfo, teamsSettingData, usersSettingData]);

    const onCreateTeam = useCallback(
        // eslint-disable-next-line no-unused-vars
        async (teamInfo?: CreateTeamInfo, callback?: (message?: string) => void) => {
            if (!teamInfo) return;
            const createTeamInfo = { name: teamInfo.name, description: teamInfo.description };
            let picture = teamInfo?.picture;

            const resCreateTeam = await ApiService.createTeam(createTeamInfo);
            let resUpdateInfo = undefined;

            if (resCreateTeam.status === StatusEnum.Success) {
                // Create new team successfully
                // check if picture if file then, call API to post image
                const teamId = resCreateTeam.data?.id;
                if (picture && picture instanceof File) {
                    const res = await ApiService.uploadAsset(teamId, 'team', picture);
                    picture = res.url;
                    resUpdateInfo = await ApiService.teamInfoModification({
                        teamId,
                        name: teamInfo.name,
                        description: teamInfo?.description,
                        picture: picture,
                    });
                }
            }

            if (resCreateTeam.status === StatusEnum.Success) {
                toggleCreateEditTeamModal();
                setTeamInfo(() => ({
                    ...teamInfo,
                    id: resCreateTeam.data.id,
                    picture: resUpdateInfo?.status === StatusEnum.Success ? picture : undefined,
                }));
                toggleSuccessCreateTeamModal();
                reloadTeamList();
                reloadListOperator();
                reloadUserProfile();
                callback?.();
            } else {
                const message =
                    (resCreateTeam as { message: string })?.message || resUpdateInfo?.message || '';
                callback?.(message);
            }
        },
        [
            // addCurrentUserToTeam,
            reloadTeamList,
            reloadListOperator,
            toggleCreateEditTeamModal,
            toggleSuccessCreateTeamModal,
            reloadUserProfile,
        ],
    );

    const assignMembersToCurrentTeam = useCallback(
        async (userIds: string[] = []) => {
            if (!teamInfo?.id || !userIds.length) return;
            const arrPromise = userIds.map((user) => assignUserToTeam(teamInfo.id, user));
            try {
                await Promise.all(arrPromise);
                reloadTeamList();
                reloadListOperator();
                reloadUserProfile();
            } catch (error) {
                console.log(error);
            }
        },
        [teamInfo, reloadTeamList, reloadListOperator, reloadUserProfile],
    );

    const onWannaEditMemberInfo = useCallback(
        (memberEdit: MemberData) => {
            memberEditRef.current = memberEdit;
            toggleEditMemberModal();
        },
        [toggleEditMemberModal],
    );

    const confirmEditMemberInfo = useCallback(
        async (memberInfo?: MemberData) => {
            console.log({ memberInfo });

            if (!memberInfo) {
                memberEditRef.current = null;
                return closeEditMemberModal();
            }
            if (memberEditRef.current) {
                const arrOfPromise = editUserInfo(
                    memberEditRef.current.teams,
                    memberInfo.teams,
                    memberInfo.id,
                    '',
                );
                closeEditMemberModal();
                if (isManager) {
                    console.log('memberEditRef.current');

                    await updateUserRoleAndStatus(memberEditRef.current, memberInfo);
                }
                await Promise.all(arrOfPromise);
                await delay(300);
                reloadTeamList();
                reloadListOperator();
                reloadUserProfile();
            }
        },
        [reloadTeamList, reloadUserProfile, reloadListOperator, closeEditMemberModal, isManager],
    );

    return useMemo(
        () => ({
            isManager,
            userProfile,
            teamsSettingData,
            usersSettingData,
            visibleEditMemberModal,
            visibleAssignMemberModal,
            originTeamList: teamList,
            createTeamInfo: teamInfo,
            visibleCreateEditTeamModal,
            userListForAssignMemberModal,
            visibleSuccessCreateTeamModal,
            originUserList: listOperators,
            currEditingMember: memberEditRef.current,
            isLoading: isLoadingTeamList || isLoadingListOperators,
            onCreateTeam,
            reloadTeamList,
            reloadListOperator,
            onWannaEditMemberInfo,
            toggleEditMemberModal,
            confirmEditMemberInfo,
            toggleAssignMemberModal,
            toggleCreateEditTeamModal,
            assignMembersToCurrentTeam,
            toggleSuccessCreateTeamModal,
        }),
        [
            teamInfo,
            teamList,
            isManager,
            userProfile,
            listOperators,
            teamsSettingData,
            usersSettingData,
            isLoadingTeamList,
            isLoadingListOperators,
            visibleEditMemberModal,
            visibleAssignMemberModal,
            visibleCreateEditTeamModal,
            userListForAssignMemberModal,
            visibleSuccessCreateTeamModal,
            onCreateTeam,
            reloadTeamList,
            reloadListOperator,
            confirmEditMemberInfo,
            onWannaEditMemberInfo,
            toggleEditMemberModal,
            toggleAssignMemberModal,
            toggleCreateEditTeamModal,
            assignMembersToCurrentTeam,
            toggleSuccessCreateTeamModal,
        ],
    );
};

export type TeamListSettingsType = ReturnType<typeof useTeamListSettings>;

export const TeamListSettingsContext = createContext<TeamListSettingsType | null>(null);

export const TeamListSettingContextProvider = ({ children }: { children: ReactNode }) => {
    const data = useTeamListSettings();
    return (
        <TeamListSettingsContext.Provider value={data}>{children}</TeamListSettingsContext.Provider>
    );
};

export const useTeamListSettingsContext = () => {
    const data = useContext(TeamListSettingsContext);

    if (!data) {
        throw new Error('useTeamListSettingsContext should be used in TeamListSettingsContext');
    }
    return data;
};
