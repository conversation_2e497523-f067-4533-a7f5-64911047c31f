import { Avatar, Checkbox, Container, Flex, Group, Modal, ScrollArea, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';
import { Buttons } from '../../../../Common';
import { useTeamDetailSettingContext } from '../../TeamDetail/TeamDetailContext';
import { useTeamListSettingsContext } from '../TeamListSettingContext';

const useStyle = createStyles((theme) => ({
  memberCheckbox: {
    '& .mantine-Checkbox-body': {
      display: 'flex',
      alignItems: 'center',
      paddingTop: '4px',
      paddingBottom: '4px',
    },
  },
  modalTitle: {
    fontSize: '1.25rem',
    fontWeight: 700,
    color: theme.colors.dark[4],
  },
}));

const useAdjustForCorrectContext = () => {
  try {
    const {
      visibleAssignMemberModal,
      toggleAssignMemberModal,
      userProfile,
      userListForAssignMemberModal,
      assignMembersToCurrentTeam,
      // eslint-disable-next-line react-hooks/rules-of-hooks
    } = useTeamListSettingsContext();
    return {
      visibleAssignMemberModal,
      toggleAssignMemberModal,
      userListForAssignMemberModal,
      assignMembersToCurrentTeam,
      userProfile,
    };
  } catch (e) {}
  const {
    visibleAssignMemberModal,
    toggleAssignMemberModal,
    userProfile,
    userListForAssignMemberModal,
    assignMembersToCurrentTeam,
    // eslint-disable-next-line react-hooks/rules-of-hooks
  } = useTeamDetailSettingContext();
  return {
    visibleAssignMemberModal,
    toggleAssignMemberModal,
    userListForAssignMemberModal,
    assignMembersToCurrentTeam,
    userProfile,
  };
};

export default function AssignMemberModal() {
  const { t } = useTranslate('workspace');

  const { classes } = useStyle();
  const {
    visibleAssignMemberModal,
    toggleAssignMemberModal,
    userListForAssignMemberModal,
    assignMembersToCurrentTeam,
  } = useAdjustForCorrectContext();
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = useCallback((id: string, removeFlag: boolean) => {
    if (removeFlag) return setSelectedUsers((pre) => pre.filter((user) => user !== id));
    return setSelectedUsers((pre) => [...pre, id]);
  }, []);

  const handleOnConfirm = async () => {
    const userList = [...selectedUsers];
    setSelectedUsers([]);
    toggleAssignMemberModal();
    setIsLoading(true);
    await assignMembersToCurrentTeam(userList);
    setIsLoading(false);
  };

  const handleOnCancel = () => {
    setSelectedUsers([]);
    toggleAssignMemberModal();
  };

  return (
    <Modal
      opened={visibleAssignMemberModal}
      centered
      onClose={toggleAssignMemberModal}
      withCloseButton={false}
      size={'lg'}
    >
      <Modal.CloseButton className='' size={'lg'} style={{ right: '-94%' }} />
      <Modal.Title
        mb={'lg'}
        pl={'md'}
        className={classes.modalTitle}
        title={t('modal.add_team_member_title')}
      >
        {t('modal.add_team_member_title')}
      </Modal.Title>
      <Group pl={'md'} mb={'lg'}>
        <Text fz='sm' color='gray.7'>
          {t('modal.add_team_member_description')}
        </Text>
      </Group>
      <Container fluid>
        <ScrollArea h={'406px'} sx={{ border: '1px solid gray', borderRadius: '5px' }}>
          <Flex w={'100%'} direction={'column'}>
            {userListForAssignMemberModal.map((mem) => (
              <MemberSelectOption
                key={mem.id}
                id={mem.id}
                name={mem.name}
                avatar={mem.avatar}
                checked={selectedUsers.includes(mem.id)}
                onClick={handleClick}
              />
            ))}
          </Flex>
        </ScrollArea>
      </Container>
      <Group mt={'xl'} mb={'lg'} justify='flex-end' mr={'lg'}>
        <Buttons.Cancel label={t('modal_operator.button_cancel')} onClick={handleOnCancel} />
        <Buttons.Confirm
          disabled={!selectedUsers.length || isLoading}
          label={t('add_member_button')}
          onClick={handleOnConfirm}
        />
      </Group>
    </Modal>
  );
}

type MemberType = {
  id: string;
  name: string;
  avatar: string;
  checked: boolean;
  // eslint-disable-next-line no-unused-vars
  onClick: (id: string, removeFlag: boolean) => void;
};

function MemberSelectOption({ id, name, avatar, checked, onClick }: MemberType) {
  const { classes } = useStyle();
  return (
    <Flex
      direction={'column'}
      w={'100%'}
      align={'flex-start'}
      justify={'center'}
      sx={(theme) => ({
        '&:hover': {
          backgroundColor: theme.colors.gray[1],
        },
      })}
      px={'xl'}
      py={'sm'}
    >
      <Checkbox
        className={classes.memberCheckbox}
        color={'navy.0'}
        sx={{ cursor: 'pointer' }}
        onChange={() => onClick(id, checked)}
        checked={checked}
        label={
          <Group>
            <Avatar radius={'xl'} size={'md'} src={avatar} />
            <Text title={name} lineClamp={1}>
              {name}
            </Text>
          </Group>
        }
      />
    </Flex>
  );
}
