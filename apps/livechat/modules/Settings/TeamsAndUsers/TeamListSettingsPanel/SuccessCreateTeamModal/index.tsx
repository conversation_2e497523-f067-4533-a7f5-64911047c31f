import { Center, Group, Modal, Text } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { Buttons } from '../../../../Common';
import { useTeamListSettingsContext } from '../TeamListSettingContext';

export default function SuccessCreateTeamModal() {
  const { t } = useTranslate('workspace');
  const {
    visibleSuccessCreateTeamModal,
    toggleSuccessCreateTeamModal,
    createTeamInfo,
    toggleAssignMemberModal,
  } = useTeamListSettingsContext();

  const handleOnConfirm = () => {
    toggleSuccessCreateTeamModal();
    toggleAssignMemberModal();
  };

  const handleOnCancel = () => {
    toggleSuccessCreateTeamModal();
  };

  return (
    <Modal
      opened={visibleSuccessCreateTeamModal}
      centered
      onClose={toggleSuccessCreateTeamModal}
      withCloseButton={false}
      size={'lg'}
    >
      <Modal.CloseButton className='' size={'lg'} />
      <Modal.Title
        mb={'lg'}
        pl={'md'}
        sx={{ fontSize: '20px', fontWeight: 700, color: 'black' }}
        title={t('tab.team_list.success_create_team_modal.name', {
          name: createTeamInfo.name,
        })}
      >
        <Center>
          {t('tab.team_list.success_create_team_modal.name', {
            name: createTeamInfo.name,
          })}
        </Center>
      </Modal.Title>
      <Center>
        <Text>{t('tab.team_list.success_create_team_modal.description')}</Text>
      </Center>
      <Center>
        <Group mt={'xl'} mb={'lg'}>
          <Buttons.Cancel label={t('close_label')} onClick={handleOnCancel} />
          <Buttons.Confirm
            label={
              <>
                <IconPlus size={'20px'} />
                <Text size={'md'}>{t('add_member_button_label')}</Text>
              </>
            }
            onClick={handleOnConfirm}
          />
        </Group>
      </Center>
    </Modal>
  );
}
