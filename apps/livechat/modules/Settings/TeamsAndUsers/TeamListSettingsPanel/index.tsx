import { Box, Container, Flex, Group, Text, TextInput } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { BottomUpTween, RightToLeftTween } from '@resola-ai/ui';
import { IconSearch } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useMemo, useState } from 'react';
import BarLoader from '../../../../components/barLoader';
import CustomPagination from '../Pagination';
import usePagination from '../hooks/usePagination';
import { TeamListSettingItem } from '../types';
import AssignMemberModal from './AssignMemberModal';
import CreateTeamModalWithButton from './CreateEditTeamButtonModal';
import SuccessCreateTeamModal from './SuccessCreateTeamModal';
import TeamInfoItem from './TeamInfoItem';
import { useTeamListSettingsContext } from './TeamListSettingContext';

const useStyle = createStyles((theme) => ({
  description: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray[7],
  },
  groupDes: {
    justifyContent: 'space-between',
  },
  inputStyle: {
    minWidth: '400px',
    '& input:focus': {
      borderColor: theme.colors.navy[0],
    },
  },
  emptyState: {
    color: '#868E96',
  },
}));

export default function TeamListSettingsPanel() {
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');

  const [searchValue, setSearchValue] = useState('');
  const { teamsSettingData, isLoading } = useTeamListSettingsContext();

  const { activePage, totalPage, onChange, dataDisplay } = usePagination({
    perPageNum: 20,
    currentList: teamsSettingData,
    searchValue,
  });

  const hasData = useMemo(() => !!dataDisplay.length, [dataDisplay.length]);

  const renderEmpty = useMemo(() => {
    return (
      <Flex direction='column' h='100%' justify='center' align='center'>
        <Text className={classes.emptyState} mb='30px'>
          {searchValue ? t('search_no_result') : t('no_team')}
        </Text>
        {!searchValue && <CreateTeamModalWithButton />}
      </Flex>
    );
  }, [classes.emptyState, searchValue, t]);

  const showSearchBox = useMemo(() => (searchValue && !hasData) || hasData, [hasData, searchValue]);

  return (
    <Container p={'0'} maw='100%' h='100%'>
      <Group mt={'1.8rem'} className={classes.groupDes}>
        <Text className={classes.description}>{t('tab.team_list.description')}</Text>
        {showSearchBox && <CreateTeamModalWithButton />}
        <SuccessCreateTeamModal />
        <AssignMemberModal />
      </Group>
      {isLoading ? (
        <BarLoader />
      ) : (
        <>
          {!!showSearchBox && (
            <AnimateSearchInput searchValue={searchValue} onSearch={setSearchValue} />
          )}
          {hasData ? (
            <>
              <Flex mt={'md'} direction={'column'} gap={'md'} mb={'md'}>
                {dataDisplay.map((team, index) => {
                  return (
                    <BottomUpTween key={`${team.name}${team.description}`} delay={0.1 + index / 20}>
                      <TeamInfoItem
                        members={team.members as TeamListSettingItem['members']}
                        name={team.name}
                        id={team.id}
                        description={team.description}
                        isDisabled={!!team?.isDisabled}
                        picture={team.picture}
                        isDefault={!!team?.default}
                      />
                    </BottomUpTween>
                  );
                })}
              </Flex>

              <CustomPagination
                dataDisplay={dataDisplay}
                totalPage={totalPage}
                activePage={activePage}
                onChange={onChange}
              />
            </>
          ) : (
            renderEmpty
          )}
        </>
      )}
    </Container>
  );
}

function AnimateSearchInput({
  searchValue,
  onSearch,
}: {
  searchValue: string;
  // eslint-disable-next-line no-unused-vars
  onSearch: (val: string) => void;
}) {
  const { t } = useTranslate('workspace');

  const { classes } = useStyle();

  return (
    <RightToLeftTween>
      <Group mt={'md'}>
        <Box sx={{ position: 'relative' }}>
          <TextInput
            leftSection={<IconSearch color='#1D2088' />}
            className={classes.inputStyle}
            type='text'
            placeholder={t('tab.team_list.input_placeholder')}
            value={searchValue}
            onChange={(e) => onSearch(e.target.value)}
          />
          <Text
            sx={{
              position: 'absolute',
              fontWeight: 700,
              fontSize: '14px',
              display: !searchValue ? 'none' : 'block',
              right: 0,
              top: '21%',
              marginRight: '10px',
              cursor: 'pointer',
            }}
            color='navy.0'
            onClick={() => onSearch('')}
          >
            {t('modal_operator.button.clear')}
          </Text>
        </Box>
      </Group>
    </RightToLeftTween>
  );
}
