import { Button, Space, Text } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import useCustomStyle from '../../../../../hooks/useCustomStyle';
import CreateEditTeamModal from '../../CreateEditTeamModal';
import { useTeamListSettingsContext } from '../TeamListSettingContext';

export default function CreateTeamModalWithButton() {
  const { classes } = useCustomStyle();
  const { t } = useTranslate('workspace');

  const { isManager, toggleCreateEditTeamModal } = useTeamListSettingsContext();

  return (
    <>
      <Button
        className={classes.button}
        color='navy.0'
        variant='filled'
        radius={'md'}
        disabled={!isManager}
        onClick={toggleCreateEditTeamModal}
      >
        <IconPlus size={'18'} />
        <Space w={'xs'} />
        <Text fw={700} fz='sm'>
          {t('tab.team_list.button_add_team_label')}
        </Text>
      </Button>
      <CreateEditTeamModal mode='create' />
    </>
  );
}
