import { Box, Container, Flex, Tabs, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React, { useEffect } from 'react';
import ViewOnly from '../../../components/ViewOnly';
import { COMMON_ROOT_HEIGHT } from '../../../constants';
import { addActiveTabAsParamToCurrentUrl } from '../../../utils/queryParam';
import { useTeamUserListContext } from '../../teamUserListContext';
import { useUserContext } from '../../userContext';
import TeamListSettingsPanel from './TeamListSettingsPanel';
import UserListSettingsPanel from './UserListSettingsPanel';
import { TeamUserTabType } from './types';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1rem',
    backgroundColor: 'white',
    height: COMMON_ROOT_HEIGHT,
  },
  pageTitle: {
    color: theme.colors.dark[4],
    fontSize: '1.25rem',
    marginTop: '0px !important',
  },
  tabItem: {
    padding: '10px 36px',
    borderBottomWidth: '3px',
    '&:hover': {
      backgroundColor: 'transparent',
    },
    '& .mantine-Tabs-tabLabel': {
      color: theme.colors.dark[4],
      fontSize: '1rem',
    },
  },
}));

export default function TeamsAndUserList() {
  const { t } = useTranslate('workspace');

  const { classes } = useStyle();
  const { teamUserListActiveTab, setTeamUserListActiveTab } = useTeamUserListContext();
  const { isManager, isLoadingProfile } = useUserContext();

  useEffect(() => {
    addActiveTabAsParamToCurrentUrl(teamUserListActiveTab);
  }, [teamUserListActiveTab]);

  const handleTabChange = (value: string) => {
    setTeamUserListActiveTab(value as TeamUserTabType);
  };

  return (
    <Container
      className={classes.container}
      size={'lg'}
      sx={{
        '.mantine-AppShell-main': {
          marginLeft: '40px !important',
        },
      }}
    >
      <Flex align={'center'} mt={'lg'} gap='20px'>
        <Title className={classes.pageTitle} mt={'lg'}>{`${t('pageTitle')}`}</Title>
        {!isLoadingProfile && !isManager && <ViewOnly />}
      </Flex>

      <Box mt={30}>
        <Tabs
          defaultValue='team-list'
          color='navy.0'
          value={teamUserListActiveTab}
          onChange={(value) => handleTabChange(value)}
          h='100%'
        >
          <Tabs.List>
            <Tabs.Tab value='team-list' className={classes.tabItem}>
              <b>{t('tab.team_list_label')}</b>
            </Tabs.Tab>
            <Tabs.Tab value='user-list' className={classes.tabItem}>
              <b>{t('tab.user_list_label')}</b>
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value='team-list' h='90%'>
            <TeamListSettingsPanel />
          </Tabs.Panel>
          <Tabs.Panel value='user-list' h='90%'>
            <UserListSettingsPanel />
          </Tabs.Panel>
        </Tabs>
      </Box>
    </Container>
  );
}
