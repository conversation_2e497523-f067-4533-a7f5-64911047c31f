import { Button, Group, Pagination } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import React from 'react';

interface Props {
  dataDisplay: any[];
  totalPage: number;
  activePage: number;
  onChange: (value: number) => void;
}

export default function CustomPagination({
  dataDisplay,
  totalPage,
  activePage,
  onChange,
}: Readonly<Props>) {
  const { t } = useTranslate('workspace');

  return (
    <Group
      justify='flex-end'
      mt={'lg'}
      pb='30px'
      sx={{
        visibility: dataDisplay?.length ? 'visible' : 'hidden',
      }}
    >
      <Pagination.Root total={totalPage} onChange={onChange} value={activePage} color='navy.0'>
        <Group gap={5} justify='center'>
          <Button
            variant='outline'
            color='navy.0'
            size='xs'
            pl={'xs'}
            pr={'xs'}
            mr={'md'}
            disabled={activePage === 1}
            onClick={() => activePage > 1 && onChange(activePage - 1)}
          >
            {t('tab.user_list.pagination.forward')}
          </Button>
          <Pagination.Items />
          <Button
            variant='outline'
            color='navy.0'
            size='xs'
            pl={'xs'}
            pr={'xs'}
            ml={'md'}
            disabled={activePage === totalPage}
            onClick={() => activePage < totalPage && onChange(activePage + 1)}
          >
            {t('tab.user_list.pagination.next')}
          </Button>
        </Group>
      </Pagination.Root>
    </Group>
  );
}
