import { ActionIcon, Text, Tooltip } from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';

const TeamDefaultTooltip = () => {
  const { t } = useTranslate('workspace');
  return (
    <Tooltip
      withArrow
      color='white'
      arrowSize={6}
      position='bottom'
      label={
        <Text c='#525252' fz='sm' fw={400}>
          {t('teamListDefaultTeamTooltip')}
        </Text>
      }
      styles={{
        tooltip: {
          boxShadow: '0 0 2px #a6a6a6',
        },
        arrow: {
          boxShadow: '-1px -1px 1px #a6a6a6',
        },
      }}
    >
      <ActionIcon
        ml={-5}
        color='gray'
        variant='transparent'
        sx={(theme) => ({
          '&:hover': {
            color: theme.colors.navy[0],
          },
        })}
      >
        <IconInfoCircle size={18} />
      </ActionIcon>
    </Tooltip>
  );
};
export default TeamDefaultTooltip;
