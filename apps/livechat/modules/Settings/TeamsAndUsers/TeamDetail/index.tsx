import {
  ActionIcon,
  Avatar,
  Badge,
  Container,
  Flex,
  Group,
  Space,
  Text,
  Tooltip,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { RoleType, Status } from '@resola-ai/models';
import { BottomUpTween, LeftToRightTween } from '@resola-ai/ui';
import { IconArrowLeft, IconDots, IconEdit, IconPlus, IconUserX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import Link from 'next/link';
import { useState } from 'react';
import { ImageStatus } from '../../../../components/ImageStatus';
import useCustomStyle from '../../../../hooks/useCustomStyle';
import { Buttons } from '../../../Common';
import ConfirmModal from '../../../Common/ConfirmModal';
import TeamDefaultTooltip from '../Common/TeamDefaultTooltip';
import CreateEditTeamModal from '../CreateEditTeamModal';
import EditMemberDataModal, { MemberData } from '../EditMemberDataModal';
import AssignMemberModal from '../TeamListSettingsPanel/AssignMemberModal';
import { useTeamDetailSettingContext } from './TeamDetailContext';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1rem',
    backgroundColor: 'white',
  },
  navy: {
    color: theme.colors.navy[0],
    cursor: 'pointer',
  },
  buttonAddUser: {
    borderRadius: '5px',
    backgroundColor: theme.colors.navy[0],
    '&:hover': {
      backgroundColor: theme.colors.navy[1],
    },
    '&:disabled, &:hover:disabled': {
      backgroundColor: '#c6c7d7 !important',
      color: 'white',
    },
  },
}));

function BackToTeamListLink() {
  const { classes, theme } = useStyle();
  const { t } = useTranslate('workspace');

  return (
    <LeftToRightTween exitAni>
      <Group mb='md' mt='md'>
        <Link href={'/settings/team-user-list'} style={{ textDecoration: 'none' }}>
          <Group>
            <IconArrowLeft className={classes.navy} />
            <Text fw={700} sx={{ color: theme.colors.navy[0] }}>
              {t('back_to_team_list_label')}
            </Text>
          </Group>
        </Link>
      </Group>
    </LeftToRightTween>
  );
}

function TeamBasicInfo({
  isDisabledTeam = false,
  // onClickCheckBox,
}: {
  isDisabledTeam: boolean;
  onClickCheckBox: () => void;
}) {
  const { classes } = useStyle();
  const { classes: customClasses } = useCustomStyle();
  const { t } = useTranslate('workspace');

  const {
    team,
    isManager,
    currTeamMembers,
    visibleCreateEditTeamModal,
    visibleConfirmDisableTeamModal,
    toggleCreateEditTeamModal,
    toggleAssignMemberModal,
  } = useTeamDetailSettingContext();

  return (
    <LeftToRightTween exitAni>
      <Group justify='space-between' w={'100%'}>
        <Flex
          sx={{ marginLeft: 0, width: '100%' }}
          direction={'row'}
          justify='space-between'
          align={'center'}
        >
          <Group justify='flex-start' sx={{ flexWrap: 'nowrap' }}>
            <Avatar
              src={team?.picture || undefined}
              alt={team?.description || ''}
              radius='xl'
              w={40}
              h={40}
            />
            <Text fw={700} fz='md' title={team?.name || ''} lineClamp={1} c='dark.4'>
              {team?.name || ''}
            </Text>
            <Group gap='xs' style={{ minWidth: '110px' }}>
              <Text c='gray.6' fz='md'>
                {t('tab.team_list.member_count_label', {
                  count: currTeamMembers.length,
                })}
              </Text>
              {!!team?.default && <TeamDefaultTooltip />}
              <span style={{ display: !isDisabledTeam ? 'block' : 'none' }}>
                <Tooltip
                  label={t('tooltip.edit_label')}
                  color='dark'
                  position='right'
                  withArrow
                  arrowPosition='center'
                >
                  {visibleCreateEditTeamModal || visibleConfirmDisableTeamModal ? (
                    <IconDots className={classes.navy} style={{ marginLeft: '-4px' }} />
                  ) : (
                    <ActionIcon
                      ml={-4}
                      color='navy.0'
                      disabled={!isManager}
                      variant='transparent'
                      className={customClasses.actionIcon}
                      onClick={toggleCreateEditTeamModal}
                    >
                      <IconEdit />
                    </ActionIcon>
                  )}
                </Tooltip>
              </span>
            </Group>
          </Group>
          <Group sx={{ display: isDisabledTeam ? 'none' : 'block' }}>
            <Buttons.Confirm
              disabled={!isManager || !!team?.default}
              onClick={() => toggleAssignMemberModal()}
              className={`${classes.buttonAddUser}`}
              label={
                <>
                  <IconPlus color='white' size={'20px'} />
                  <Space w={'sm'}></Space>
                  <Text fz='sm' fw={700}>
                    {t('add_member_button_label')}
                  </Text>
                </>
              }
            />
          </Group>
        </Flex>
        <Container sx={{ marginLeft: 0 }} p={0}>
          <Text title={team?.description || ''} c='gray.7' fz='sm'>
            {team?.description || ''}
          </Text>
        </Container>
      </Group>
      {/* <Group mt={'md'}>
                <Checkbox
                    disabled={!isManager}
                    label={t('check_box_disable_team_label')}
                    checked={!isDisabledTeam}
                    onChange={onClickCheckBox}
                    color="violet.5"
                    sx={{ cursor: 'pointer' }}
                />
            </Group> */}
    </LeftToRightTween>
  );
}

export default function TeamDetail() {
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');

  const {
    team,
    isManager,
    currTeamMembers,
    visibleRemoveMemberModal,
    visibleConfirmDisableTeamModal,
    onWannaEditMemberInfo,
    onWannaRemoveUserFromTeam,
    onConfirmRemoveUserFromTeam,
    toggleConfirmDisableTeamModal,
  } = useTeamDetailSettingContext();
  const [isDisabledTeam, setIsDisabledTeam] = useState(false);

  const handleClickDisableTeamCheckBox = () => {
    if (isDisabledTeam) return setIsDisabledTeam(!isDisabledTeam);
    return toggleConfirmDisableTeamModal();
  };
  const handleConfirmDisableTeam = (confirm?: boolean) => {
    if (confirm) {
      setIsDisabledTeam(confirm);
    }
    toggleConfirmDisableTeamModal();
  };

  return (
    <>
      <Container className={classes.container}>
        <BackToTeamListLink />
        <TeamBasicInfo
          isDisabledTeam={isDisabledTeam}
          onClickCheckBox={handleClickDisableTeamCheckBox}
        />
        <BottomUpTween>
          <Container
            fluid
            mt={'md'}
            mb={'md'}
            px={'16px'}
            py={'12px'}
            sx={(theme) => ({
              position: 'relative',
              border: `1px solid ${theme.colors.gray[4]}`,
              borderRadius: theme.radius.md,
              overflow: 'hidden',
            })}
          >
            <Container
              sx={(theme) => ({
                position: 'absolute',
                top: '1px',
                left: '1px',
                right: '1px',
                bottom: '1px',
                backgroundColor: '#F1F3F596',
                zIndex: 100,
                display: isDisabledTeam ? 'block' : 'none',
                borderRadius: theme.radius.md,
              })}
            />
            {currTeamMembers.map((mem) => (
              <MemberInfo
                key={mem.id}
                role={mem.roles}
                {...mem}
                disabled={!isManager || isDisabledTeam}
                openEditModal={onWannaEditMemberInfo}
                openRemoveModal={onWannaRemoveUserFromTeam}
                disabledRemove={!!team?.default}
              />
            ))}
          </Container>
        </BottomUpTween>
      </Container>
      <CreateEditTeamModal mode='edit' editData={team} />
      <ConfirmModal
        opened={visibleConfirmDisableTeamModal}
        onClose={handleConfirmDisableTeam}
        titleConfirm={t('modal_confirm_disable_team_title')}
        btnConfirmLabel={t('modal_confirm_disable_team_button_confirm')}
      />
      <ConfirmModal
        opened={visibleRemoveMemberModal}
        onClose={onConfirmRemoveUserFromTeam}
        titleConfirm={t('title_remove_user_from_team')}
        content={t('content_remove_user_from_team')}
        btnCancelLabel={t('cancel_button')}
        btnConfirmLabel={t('remove_button')}
        btnConfirmColor='red'
        reverse
      />
      <EditMemberDataModal />
      <AssignMemberModal />
    </>
  );
}

type MemberInfoType = {
  id: string;
  name: string;
  avatar: string;
  role: RoleType;
  teams: string[];
  disabled: boolean;
  disabledRemove?: boolean;
  status: Status;
  // eslint-disable-next-line no-unused-vars
  openEditModal: (memberData: MemberData) => void;
  // eslint-disable-next-line no-unused-vars
  openRemoveModal: (memberData: MemberData) => void;
};
function MemberInfo({
  id,
  name,
  avatar,
  role,
  teams,
  status = Status.Offline,
  openEditModal,
  openRemoveModal,
  disabled = false,
  disabledRemove = false,
}: MemberInfoType) {
  const { classes } = useCustomStyle();
  const { t } = useTranslate('workspace');

  const handleEditMember = () => {
    openEditModal({ id, name, role, avatar, teams, status });
  };

  const handleRemoveMember = () => {
    openRemoveModal({ id, name, role, avatar, teams, status });
  };

  return (
    <BottomUpTween exitAni>
      <Flex
        w={'100%'}
        pt={'8px'}
        px={'12px'}
        pb={'12px'}
        align={'center'}
        direction={'row'}
        justify='space-between'
        sx={(theme) => ({
          '&:hover': {
            backgroundColor: theme.colors.gray[1],
          },
          borderRadius: theme.radius.md,
        })}
      >
        <Group>
          <ImageStatus picture={avatar} status={status} />
          <Text title={name} lineClamp={1} c='dark.4' fz='sm'>
            {name}
          </Text>
          {role === 'manager' ? (
            <Badge variant='light' color='indigo' size='lg' radius={'xs'}>
              {t('tab.user_list.user_role.admin')}
            </Badge>
          ) : null}
        </Group>
        <Group mr='md' style={{ visibility: disabled ? 'hidden' : 'visible' }}>
          <Tooltip
            withArrow
            color='dark'
            position='top'
            arrowPosition='center'
            label={t('tooltip.edit_label')}
          >
            <ActionIcon
              color='navy.0'
              disabled={disabled}
              variant='transparent'
              onClick={handleEditMember}
              className={classes.actionIcon}
            >
              <IconEdit />
            </ActionIcon>
          </Tooltip>
          <Tooltip
            withArrow
            color='dark'
            position='top'
            arrowPosition='center'
            label={t('tooltip.delete_label')}
          >
            <ActionIcon
              color={'navy.0'}
              variant='transparent'
              onClick={handleRemoveMember}
              className={classes.actionIcon}
              disabled={disabled || disabledRemove}
            >
              <IconUserX />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Flex>
    </BottomUpTween>
  );
}
