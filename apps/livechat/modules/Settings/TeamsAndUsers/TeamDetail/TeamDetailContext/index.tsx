import { CreateTeamInfo, ITeamDetail } from '@resola-ai/models';
import { StatusEnum } from '@resola-ai/models';
import React, { ReactNode, createContext, useCallback, useContext, useMemo, useRef } from 'react';
import useSWR from 'swr';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import ApiService from '../../../../../services/api';
import { useTeamContext } from '../../../../teamContext';
import { useUserContext } from '../../../../userContext';
import {
  assignUserToTeam,
  editUserInfo,
  mappingTeamsSettingData,
  mappingUsersSettingData,
  updateUserRoleAndStatus,
} from '../../../Helpers';
import { MemberData } from '../../EditMemberDataModal';
import { TeamListSettingItem, UserListSettingItem } from '../../types';

function useTeamInfo(teamId: string | null | undefined) {
  const {
    data: team = null,
    isLoading: isLoadingTeam,
    error: errorLoadingTeam,
    mutate: reloadTeam,
  } = useSWR<ITeamDetail>(
    teamId ? `/get-team-info/${teamId}` : null,
    async () => ApiService.getTeam(teamId),
    { revalidateIfStale: true, revalidateOnMount: true }
  );

  return {
    team: team
      ? {
          ...team,
          description: (team as unknown as Record<string, string>)?.description || '',
        }
      : null,
    isLoadingTeam,
    errorLoadingTeam,
    reloadTeam,
  };
}

const useTeamDetailSettings = (teamId: string | null = null) => {
  const {
    isManager,
    userProfile,
    listOperators,
    reloadListOperator,
    isLoadingListOperators,
    mutate: reloadUserProfile,
  } = useUserContext();
  const { team, isLoadingTeam, reloadTeam } = useTeamInfo(teamId);
  const { teamList, isLoadingTeamList, reloadTeamList } = useTeamContext();
  const { visible: visibleCreateEditTeamModal, toggle: toggleCreateEditTeamModal } =
    useVisibilityControl();
  const { visible: visibleAssignMemberModal, toggle: toggleAssignMemberModal } =
    useVisibilityControl();

  const { visible: visibleConfirmDisableTeamModal, toggle: toggleConfirmDisableTeamModal } =
    useVisibilityControl();

  const {
    visible: visibleEditMemberModal,
    toggle: toggleEditMemberModal,
    close: closeEditMemberModal,
  } = useVisibilityControl();
  const { visible: visibleRemoveMemberModal, toggle: toggleRemoveMemberModal } =
    useVisibilityControl();

  const memberEditRef = useRef<MemberData | null>(null);

  const teamsSettingData: TeamListSettingItem[] = useMemo(
    () => mappingTeamsSettingData(teamList, listOperators),
    [listOperators, teamList]
  );

  const usersSettingData: UserListSettingItem[] = useMemo(
    () => mappingUsersSettingData(teamList, listOperators),
    [listOperators, teamList]
  );

  const currTeamMembers = useMemo(() => {
    let data = [];
    if (teamsSettingData.length && teamId) {
      const members = teamsSettingData.find((team) => team.id === teamId)?.members || [];
      if (members.length) {
        data = members.map((memOut) => ({
          ...memOut,
          teams: teamList
            .filter((team) => team.members.findIndex((mem) => mem.id === memOut.id) > -1)
            .map((team) => team.id),
        }));
      }
    }
    return data;
  }, [teamsSettingData, teamId, teamList]);

  const userListForAssignMemberModal = useMemo(() => {
    if (team?.id && teamsSettingData.find((team) => team.id === teamId)) {
      const currentMember = teamsSettingData
        .find((team) => team.id === teamId)
        ?.members.map((mem) => mem.id);
      return usersSettingData.filter((user) => !currentMember.includes(user.id));
    }
    return usersSettingData;
  }, [team, teamsSettingData, usersSettingData, teamId]);

  const changeTeamInfo = useCallback(
    (name: string, description: string, picture?: string) => {
      return ApiService.teamInfoModification({ teamId, name, description, picture });
    },
    [teamId]
  );

  const uploadTeamImage = useCallback((teamId: string, file: File) => {
    return ApiService.uploadAsset(teamId, 'team', file);
  }, []);

  const onEditTeam = useCallback(
    // eslint-disable-next-line no-unused-vars
    async (teamInfo?: CreateTeamInfo, callback?: (message?: string) => void) => {
      try {
        if (!teamInfo || !teamId) {
          toggleCreateEditTeamModal();
          return;
        }
        let picture = teamInfo?.picture || null;

        if (picture && picture instanceof File) {
          const res = await uploadTeamImage(teamId, picture);
          picture = res.url;
        }
        const res = await changeTeamInfo(teamInfo.name, teamInfo.description, picture as string);

        if (res.status === StatusEnum.Success) {
          toggleCreateEditTeamModal();
          reloadTeam();
          reloadTeamList();
          callback?.();
        } else {
          callback?.(res.message);
        }
      } catch (e) {}
    },
    [teamId, changeTeamInfo, toggleCreateEditTeamModal, uploadTeamImage, reloadTeam, reloadTeamList]
  );

  const assignMembersToCurrentTeam = useCallback(
    async (userIds: string[] = [], typeAction: 'add' | 'remove' = 'add') => {
      if (!team || !userIds.length) return;
      const arrPromise = userIds.map((user) => assignUserToTeam(team.id, user, typeAction));
      try {
        await Promise.all(arrPromise);
        reloadTeam();
        reloadTeamList();
        reloadListOperator();
        reloadUserProfile();
      } catch (error) {
        console.log(error);
      }
    },
    [team, reloadTeam, reloadTeamList, reloadListOperator, reloadUserProfile]
  );

  const onWannaEditMemberInfo = useCallback(
    (memberEdit: MemberData) => {
      memberEditRef.current = memberEdit;
      toggleEditMemberModal();
    },
    [toggleEditMemberModal]
  );

  const onWannaRemoveUserFromTeam = useCallback(
    (memberEdit: MemberData) => {
      memberEditRef.current = memberEdit;
      toggleRemoveMemberModal();
    },
    [toggleRemoveMemberModal]
  );

  const confirmEditMemberInfo = useCallback(
    async (memberInfo?: MemberData) => {
      if (!memberInfo) {
        memberEditRef.current = null;
        return closeEditMemberModal();
      }
      if (memberEditRef.current) {
        const arrOfPromise = editUserInfo(
          memberEditRef.current.teams,
          memberInfo.teams,
          memberInfo.id,
          ''
        );

        closeEditMemberModal();
        if (isManager) {
          await updateUserRoleAndStatus(memberEditRef.current, memberInfo);
        }
        await Promise.all(arrOfPromise);
        reloadTeamList();
        reloadListOperator();
        reloadUserProfile();
      }
    },
    [reloadTeamList, reloadUserProfile, reloadListOperator, closeEditMemberModal, isManager]
  );

  const onConfirmRemoveUserFromTeam = useCallback(
    async (confirm?: boolean) => {
      if (confirm && teamId && memberEditRef.current) {
        await assignUserToTeam(teamId, memberEditRef.current.id, 'remove');
      }
      memberEditRef.current = null;
      toggleRemoveMemberModal();
      reloadTeamList();
      reloadUserProfile();
    },
    [reloadTeamList, teamId, toggleRemoveMemberModal, reloadUserProfile]
  );

  return useMemo(
    () => ({
      team,
      isManager,
      userProfile,
      currTeamMembers,
      teamsSettingData,
      usersSettingData,
      visibleEditMemberModal,
      visibleAssignMemberModal,
      visibleRemoveMemberModal,
      originTeamList: teamList,
      visibleCreateEditTeamModal,
      userListForAssignMemberModal,
      visibleConfirmDisableTeamModal,
      originUserList: listOperators,
      currEditingMember: memberEditRef.current,
      isLoading: isLoadingTeamList || isLoadingListOperators || isLoadingTeam,
      onEditTeam,
      reloadTeam,
      reloadTeamList,
      reloadListOperator,
      confirmEditMemberInfo,
      toggleEditMemberModal,
      onWannaEditMemberInfo,
      toggleAssignMemberModal,
      toggleRemoveMemberModal,
      toggleCreateEditTeamModal,
      onWannaRemoveUserFromTeam,
      assignMembersToCurrentTeam,
      onConfirmRemoveUserFromTeam,
      toggleConfirmDisableTeamModal,
    }),
    [
      team,
      teamList,
      isManager,
      userProfile,
      listOperators,
      isLoadingTeam,
      currTeamMembers,
      teamsSettingData,
      usersSettingData,
      isLoadingTeamList,
      visibleEditMemberModal,
      isLoadingListOperators,
      visibleRemoveMemberModal,
      visibleAssignMemberModal,
      visibleCreateEditTeamModal,
      userListForAssignMemberModal,
      visibleConfirmDisableTeamModal,
      onEditTeam,
      reloadTeam,
      reloadTeamList,
      reloadListOperator,
      toggleEditMemberModal,
      onWannaEditMemberInfo,
      confirmEditMemberInfo,
      toggleRemoveMemberModal,
      toggleAssignMemberModal,
      toggleCreateEditTeamModal,
      onWannaRemoveUserFromTeam,
      assignMembersToCurrentTeam,
      onConfirmRemoveUserFromTeam,
      toggleConfirmDisableTeamModal,
    ]
  );
};

export type TeamDetailSettingsType = ReturnType<typeof useTeamDetailSettings>;

export const TeamDetailSettingContext = createContext<TeamDetailSettingsType | null>(null);

export const TeamDetailSettingContextProvider = ({
  children,
  teamId = null,
}: {
  children: ReactNode;
  teamId: string;
}) => {
  const data = useTeamDetailSettings(teamId);
  return (
    <TeamDetailSettingContext.Provider value={data}>{children}</TeamDetailSettingContext.Provider>
  );
};

export const useTeamDetailSettingContext = () => {
  const data = useContext(TeamDetailSettingContext);

  if (!data) {
    throw new Error('useTeamDetailSettingContext should be used in TeamDetailSettingContext');
  }
  return data;
};
