import {
  ActionIcon,
  Badge,
  Box,
  Container,
  Flex,
  Group,
  Table,
  Text,
  TextInput,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { RoleType } from '@resola-ai/models';
import { BottomUpTween } from '@resola-ai/ui';
import { IconEdit, IconSearch } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import Image from 'next/image';
import { useCallback, useState } from 'react';
import { ImageStatus } from '../../../../components/ImageStatus';
import useCustomStyle from '../../../../hooks/useCustomStyle';
import { getPublicUrl } from '../../../../utils/public';
import EditMemberDataModal from '../EditMemberDataModal';
import CustomPagination from '../Pagination';
import { useTeamListSettingsContext } from '../TeamListSettingsPanel/TeamListSettingContext';
import usePagination from '../hooks/usePagination';

const useStyle = createStyles((theme) => ({
  description: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray[7],
  },
  groupDes: {
    justifyContent: 'space-between',
  },
  tableStyle: {
    borderCollapse: 'unset',
    border: '1px solid #C1C2C5',
    borderRadius: theme.radius.md,
    overflow: 'hidden',
  },
  tdBorderAround: {
    border: `1px solid #C1C2C5`,
    overflow: 'hidden',
    borderRadius: theme.radius.md,
  },
  trTdNoBorderTop: {
    textAlign: 'center',
    verticalAlign: 'middle',
    height: '65px',
    '&:hover': {
      backgroundColor: theme.colors.gray[2],
    },
    '&:not(:first-of-type) td': {
      borderTop: '0',
      borderBottom: '0 !important',
    },
  },
  inputStyle: {
    minWidth: '400px',
    '& input:focus': {
      borderColor: theme.colors.navy[0],
    },
  },
  columnName: {
    color: theme.colors.dark[2],
  },
  customThTd: {
    padding: '1.25rem',
  },
  emptyState: {
    color: theme.colors.navy[0],
  },
}));

export default function UserListSettingsPanel() {
  const { t } = useTranslate('workspace');

  const { classes } = useStyle();
  const { classes: customClasses } = useCustomStyle();
  const { usersSettingData, onWannaEditMemberInfo, isManager } = useTeamListSettingsContext();

  const [searchValue, setSearchValue] = useState('');
  const { activePage, totalPage, onChange, dataDisplay } = usePagination({
    perPageNum: 20,
    currentList: usersSettingData,
    searchValue,
  });
  const renderEmpty = () => {
    return (
      <Flex direction='column' h='100%' justify='center' align='center'>
        {searchValue ? (
          <Text className={classes.emptyState} mb='30px'>
            {t('search_no_result')}
          </Text>
        ) : (
          <Flex
            justify={'center'}
            align={'center'}
            h={420}
            w={'100%'}
            direction={'column'}
            bg={'#F9F9FA'}
            gap={'md'}
          >
            <Image
              height={144}
              width={184}
              src={getPublicUrl('/images/no-user.svg')}
              alt='No User'
            />
            <Text mb='30px' c='#7A7A7A' fz='sm'>
              {t('no_user')}
            </Text>
          </Flex>
        )}
      </Flex>
    );
  };

  const hasData = dataDisplay.length;
  const showSearchBox = (searchValue && !hasData) || hasData;

  const getRoleName = useCallback(
    (role: string) => {
      if (['manager', 'operator'].includes(role))
        return t(`tab.user_list.user_role.${role === 'manager' ? 'admin' : 'operator'}`);
      return '';
    },
    [t]
  );

  return (
    <>
      <Container p={'0'} maw='100%' h='100%'>
        <Group mt={'1.8rem'} className={classes.groupDes}>
          <Text className={classes.description}>{t('tab.user_list.description')}</Text>
          {/* Since the function is not designed yet. Let hide button for now  */}
          {/* <Button color="violet.5" variant="filled" radius={'md'}>
                        <IconPlus size={'18'} />
                        <Space w={'xs'} />
                        <Text fw={700} fz="sm">
                            {t('tab.user_list.button_add_user_label')}
                        </Text>
                    </Button> */}
        </Group>
        <Group mt={'md'}>
          <Box sx={{ position: 'relative' }}>
            {!!showSearchBox && (
              <TextInput
                leftSection={<IconSearch color='#1D2088' />}
                type='text'
                placeholder={t('tab.user_list.input_placeholder')}
                value={searchValue}
                className={classes.inputStyle}
                onChange={(e) => setSearchValue(e.target.value)}
              />
            )}
            <Text
              sx={{
                position: 'absolute',
                fontWeight: 700,
                fontSize: '14px',
                display: !searchValue ? 'none' : 'block',
                right: 0,
                top: '21%',
                marginRight: '10px',
                cursor: 'pointer',
              }}
              color='navy.0'
              onClick={() => setSearchValue('')}
            >
              {t('modal_operator.button.clear')}
            </Text>
          </Box>
        </Group>
        <Container fluid p={0} mt={'md'} h='100%'>
          {hasData ? (
            <>
              <div
                style={{
                  overflow: 'hidden',
                  border: `1px solid #C1C2C5`,
                  borderRadius: '7px',
                }}
              >
                <Table fz='md' highlightOnHover verticalSpacing='lg' horizontalSpacing='lg'>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>
                        <Text c='dark.2' fz='sm' fw={700}>
                          {t('tab.user_list.table.user_name')}
                        </Text>
                      </Table.Th>
                      <Table.Th>
                        <Text c='dark.2' fz='sm' fw={700}>
                          {t('tab.user_list.table.user_mail')}
                        </Text>
                      </Table.Th>
                      <Table.Th>
                        <Text c='dark.2' fz='sm' fw={700}>
                          {t('tab.user_list.table.user_role')}
                        </Text>
                      </Table.Th>
                      <Table.Th>
                        <Text c='dark.2' fz='sm' fw={700}>
                          {t('tab.user_list.table.team_name')}
                        </Text>
                      </Table.Th>
                      <Table.Th style={{ textAlign: 'center', minWidth: '70px' }}>
                        <Text c='dark.2' fz='sm' fw={700}>
                          {t('tab.user_list.table.edit_label')}
                        </Text>
                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <tbody style={{ overflow: 'hidden' }}>
                    {dataDisplay.map((user, index) => {
                      const teamNames = user.teams.map((team) => team.name).join(', ');
                      const { id, name, teams, avatar, status } = user;
                      const role = user.role.name as RoleType;
                      return (
                        <BottomUpTween
                          key={user.id + index}
                          delay={index / 100}
                          style={{ display: 'table-row' }}
                          className={classes.trTdNoBorderTop}
                        >
                          <td
                            style={{
                              display: 'table-cell',
                              paddingLeft: '1.25rem',
                              verticalAlign: 'middle',
                              textAlign: 'left',
                            }}
                          >
                            <Group
                              sx={{
                                flexWrap: 'nowrap',
                              }}
                            >
                              <ImageStatus picture={user.avatar} status={user?.status} />
                              <Text lineClamp={1} fw={700} c='dark.4' fz='sm'>
                                {user.name}
                              </Text>
                            </Group>
                          </td>
                          <td
                            style={{
                              textAlign: 'left',
                              paddingLeft: '1.25rem',
                            }}
                          >
                            <Text
                              maw={250}
                              lineClamp={1}
                              title={user?.email || ''}
                              c='dark.4'
                              fz='sm'
                            >
                              {user?.email || ''}
                            </Text>
                          </td>
                          <td>
                            <Badge
                              autoContrast
                              fw={700}
                              c={role === 'operator' ? 'indigo.4' : 'gray.8'}
                              bg={role === 'operator' ? 'indigo.0' : 'gray.0'}
                              size='lg'
                              radius='xs'
                            >
                              {getRoleName(role)}
                            </Badge>
                          </td>
                          <td
                            style={{
                              textAlign: 'left',
                              paddingLeft: '1.25rem',
                            }}
                          >
                            <Text maw={350} lineClamp={1} title={teamNames} c='dark.4' fz='sm'>
                              {teamNames}
                            </Text>
                          </td>
                          <td
                            style={{
                              textAlign: 'center',
                              minWidth: '70px',
                            }}
                          >
                            <ActionIcon
                              className={customClasses.actionIcon}
                              variant='transparent'
                              color='navy.0'
                              disabled={!isManager}
                              w={'100%'}
                              onClick={() =>
                                onWannaEditMemberInfo({
                                  id,
                                  name,
                                  role,
                                  avatar,
                                  teams: teams.map((team) => team.id),
                                  status,
                                })
                              }
                            >
                              <IconEdit />
                            </ActionIcon>
                          </td>
                        </BottomUpTween>
                      );
                    })}
                  </tbody>
                </Table>
              </div>
              <CustomPagination
                dataDisplay={dataDisplay}
                totalPage={totalPage}
                activePage={activePage}
                onChange={onChange}
              />
            </>
          ) : (
            renderEmpty()
          )}
        </Container>
      </Container>
      <EditMemberDataModal />
    </>
  );
}
