import { useEffect, useMemo, useState } from 'react';
import {
    Modal,
    Flex,
    Group,
    Avatar,
    Text,
    Radio,
    TextInput,
    Checkbox,
    ScrollArea,
    Button,
    rem,
    Indicator,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconSearch } from '@tabler/icons-react';
import { RoleType, Status } from '@resola-ai/models';
import { Buttons } from '../../../Common';
import { useTeamDetailSettingContext } from '../TeamDetail/TeamDetailContext';
import { useTeamListSettingsContext } from '../TeamListSettingsPanel/TeamListSettingContext';
import { useUserContext } from '../../../userContext';
import { useTranslate } from '@tolgee/react';
import useVisibilityControl from '../../../../hooks/useVisibilityControl';

export type MemberData = {
    id: string;
    name: string;
    role: RoleType;
    avatar: string;
    teams: string[];
    status: Status;
};

const useStyle = createStyles((theme) => ({
    teamCheckBoxFlex: {
        border: `1px solid ${theme.colors.gray[4]}`,
        borderRadius: theme.radius.md,
        padding: theme.spacing.sm,
        height: '358px',
        '& .mantine-Checkbox-root': {
            padding: theme.spacing.md,
            '&:hover': {
                backgroundColor: theme.colors.gray[1],
            },
        },
    },
    colorViolet: {
        color: theme.colors.gray[8],
    },
    inputStyle: {
        '& input:focus:not([data-invalid])': {
            borderColor: theme.colors.navy[0],
        },
    },
    modalTitle: {
        fontSize: '20px',
        fontWeight: 700,
        color: theme.colors.dark[4],
    },
    labelRadio: {
        color: theme.colors.dark[4],
        fontWeight: 500,
    },
}));
const useAdjustForCorrectContext = () => {
    const { visible: openedConfirm, toggle: toggleConfirmModal } = useVisibilityControl();
    try {
        const {
            visibleEditMemberModal,
            teamsSettingData,
            currEditingMember,
            confirmEditMemberInfo,
            toggleEditMemberModal,
            // eslint-disable-next-line react-hooks/rules-of-hooks
        } = useTeamListSettingsContext();
        return {
            visibleEditMemberModal,
            teamsSettingData,
            currEditingMember,
            confirmEditMemberInfo,
            toggleEditMemberModal,
            openedConfirm,
            toggleConfirmModal,
        };
    } catch (e) {}
    const {
        visibleEditMemberModal,
        teamsSettingData,
        currEditingMember,
        confirmEditMemberInfo,
        toggleEditMemberModal,
    } =
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useTeamDetailSettingContext();
    return {
        visibleEditMemberModal,
        teamsSettingData,
        currEditingMember,
        confirmEditMemberInfo,
        toggleEditMemberModal,
        openedConfirm,
        toggleConfirmModal,
    };
};

export default function EditMemberDataModal() {
    const { t } = useTranslate('workspace');
    const { theme, classes } = useStyle();
    const {
        visibleEditMemberModal,
        teamsSettingData,
        currEditingMember,
        confirmEditMemberInfo,
        toggleEditMemberModal,
        openedConfirm,
        toggleConfirmModal,
    } = useAdjustForCorrectContext();

    const [userRole, setUserRole] = useState<string>('operator');
    const [userStatus, setUserStatus] = useState<Status>(currEditingMember?.status || Status.Away);
    const [teamsSelected, setTeamsSelected] = useState<string[]>([]);
    const [searchValue, setSearchValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const { isManager } = useUserContext();

    useEffect(() => {
        if (currEditingMember) {
            setUserRole(currEditingMember.role);
            setUserStatus(currEditingMember.status);
            setTeamsSelected(currEditingMember.teams);
        }
    }, [currEditingMember]);

    const handleSearch = (search: string) => {
        setSearchValue(search);
    };

    const handleCheckBoxTeam = (teamId: string) => {
        const teams = [...teamsSelected];
        const idx = teams.findIndex((item) => item === teamId);

        if (idx > -1) {
            teams.splice(idx, 1);
        } else {
            teams.push(teamId);
        }
        setTeamsSelected(() => teams);
    };

    const handleOnConfirmStep1 = async () => {
        if (!teamsSelected.length) return;
        if (userStatus !== currEditingMember?.status) {
            toggleEditMemberModal();
            toggleConfirmModal();
            return;
        }
        setIsLoading(true);
        console.log('userRole', userRole);

        await confirmEditMemberInfo({
            ...currEditingMember,
            role: userRole as RoleType,
            teams: teamsSelected,
        });
        console.log('userRole 11212', userRole);
        setIsLoading(false);
    };

    const handleOnConfirmStep2 = async () => {
        if (!teamsSelected.length) return;
        setIsLoading(true);
        await confirmEditMemberInfo({
            ...currEditingMember,
            role: userRole as RoleType,
            status: userStatus,
            teams: teamsSelected,
        });
        setIsLoading(false);
        toggleConfirmModal();
    };

    const handleOnCancel = async () => {
        await confirmEditMemberInfo();
    };

    const handleCancelConfirm = async () => {
        toggleConfirmModal();
        await confirmEditMemberInfo();
    };

    const teamListBySearch = useMemo(() => {
        if (!searchValue) return teamsSettingData;
        return teamsSettingData.filter((team) =>
            team.name.toLowerCase().includes(searchValue.toLowerCase()),
        );
    }, [searchValue, teamsSettingData]);

    return (
        <>
            <Modal
                opened={visibleEditMemberModal}
                centered
                onClose={handleOnCancel}
                withCloseButton={false}
                size={'lg'}
                style={{ position: 'relative' }}
            >
                <Modal.CloseButton
                    className=""
                    size={'lg'}
                    style={{ position: 'absolute', right: '4%', top: '20px' }}
                />
                <Modal.Title
                    mt={14}
                    mb={'md'}
                    pl={'md'}
                    className={classes.modalTitle}
                    title={t('modal_edit_member_title')}
                >
                    {t('modal_edit_member_title')}
                </Modal.Title>

                <Modal.Body mt={'lg'}>
                    <Group>
                        <Indicator
                            size={11}
                            offset={5}
                            withBorder
                            radius="xs"
                            position="bottom-end"
                            color={
                                currEditingMember?.status === Status.Available ? 'green' : 'gray'
                            }
                        >
                            <Avatar src={currEditingMember?.avatar || ''} radius="xl" size={'md'} />
                        </Indicator>
                        <Text fw={700} c="dark.4">
                            {currEditingMember?.name || ''}
                        </Text>
                    </Group>
                    <Flex
                        mt={'md'}
                        w={'100%'}
                        bg={'#f2f2f6'}
                        direction={'column'}
                        style={{ borderRadius: '6px' }}
                    >
                        <Group pl={'md'} py={'sm'} style={{ borderBottom: '1px solid #cfcfd7' }}>
                            <label htmlFor="radio_group_status_id" style={{ width: '110px' }}>
                                <Text c="dark.4" fw={700} fz={14}>
                                    {t('team_detail.user.status.title')}
                                </Text>
                            </label>
                            <Group>
                                <Radio
                                    w={140}
                                    disabled={!isManager}
                                    classNames={{
                                        label: classes.labelRadio,
                                    }}
                                    value="operator"
                                    color={'navy.0'}
                                    label={
                                        <Text fz={14} fw={700} style={{ lineHeight: '1.1rem' }}>
                                            {t('inbox.myassigned.online.title')}
                                        </Text>
                                    }
                                    checked={userStatus === Status.Available}
                                    onChange={() => setUserStatus(Status.Available)}
                                />
                                <Radio
                                    disabled={!isManager}
                                    classNames={{
                                        label: classes.labelRadio,
                                    }}
                                    value="manager"
                                    color={'navy.0'}
                                    label={
                                        <Text fz={14} fw={700} style={{ lineHeight: '1.1rem' }}>
                                            {t('inbox.myassigned.offline.title')}
                                        </Text>
                                    }
                                    checked={userStatus !== Status.Available}
                                    onChange={() => setUserStatus(Status.Offline)}
                                />
                            </Group>
                        </Group>
                        <Group pl={'md'} py={'sm'}>
                            <label htmlFor="radio_group_role_id" style={{ width: '110px' }}>
                                <Text c="dark.4" fw={700} fz={14}>
                                    {t('tab.user_list.table.user_role')}
                                </Text>
                            </label>
                            <Group justify="left">
                                <Radio
                                    w={140}
                                    disabled={!isManager}
                                    classNames={{
                                        label: classes.labelRadio,
                                    }}
                                    value="operator"
                                    color={'navy.0'}
                                    label={
                                        <Text fz={14} fw={700} style={{ lineHeight: '1.1rem' }}>
                                            {t('tab.user_list.user_role.operator')}
                                        </Text>
                                    }
                                    checked={userRole === 'operator'}
                                    onChange={() => setUserRole('operator')}
                                />
                                <Radio
                                    disabled={!isManager}
                                    classNames={{
                                        label: classes.labelRadio,
                                    }}
                                    value="manager"
                                    color={'navy.0'}
                                    label={
                                        <Text fz={14} fw={700} style={{ lineHeight: '1.1rem' }}>
                                            {t('tab.user_list.user_role.admin')}
                                        </Text>
                                    }
                                    checked={userRole === 'manager'}
                                    onChange={() => setUserRole('manager')}
                                />
                            </Group>
                        </Group>
                    </Flex>

                    <Group mt={'md'} gap={'0'}>
                        <Text c="dark.4">
                            {t('modal_edit_member_team_count', {
                                number: teamsSelected.length,
                            })}
                        </Text>
                        <span style={{ color: theme.colors.red[5] }}>*</span>
                    </Group>
                    <Flex
                        w={'100%'}
                        direction={'column'}
                        mt={'md'}
                        gap={'sm'}
                        className={classes.teamCheckBoxFlex}
                    >
                        <Group grow>
                            <TextInput
                                leftSection={
                                    <IconSearch className={classes.colorViolet} size={17} />
                                }
                                placeholder={t('tab.team_list.input_placeholder')}
                                value={searchValue}
                                className={classes.inputStyle}
                                onChange={(e) => handleSearch(e.target.value)}
                            />
                        </Group>
                        <ScrollArea>
                            {teamListBySearch.map((item) => {
                                return (
                                    <Checkbox
                                        key={item.id}
                                        color={'navy.0'}
                                        value={item.id}
                                        disabled={!!item?.default}
                                        label={
                                            !!item.default ? (
                                                <Group sx={{ marginTop: '-5px' }}>
                                                    <Text>{item.name}</Text>
                                                    <Button
                                                        sx={{
                                                            borderRadius: rem(16),
                                                            color: '#666666 !important',
                                                            fontWeight: 'lighter',
                                                            padding: '2px 7px',
                                                            lineHeight: '10px',
                                                            height: '1.5rem',
                                                        }}
                                                        size="xs"
                                                        disabled
                                                    >
                                                        {t('default')}
                                                    </Button>
                                                </Group>
                                            ) : (
                                                item.name
                                            )
                                        }
                                        checked={teamsSelected.includes(item.id)}
                                        onChange={() => handleCheckBoxTeam(item.id)}
                                    />
                                );
                            })}
                        </ScrollArea>
                    </Flex>
                </Modal.Body>
                <Group justify="flex-end" mt={'xl'} mb={'lg'} mr={'lg'}>
                    <Buttons.Cancel
                        label={t('modal_operator.button_cancel')}
                        onClick={handleOnCancel}
                    />
                    <Buttons.Confirm
                        label={t('modal_save_button_label')}
                        onClick={handleOnConfirmStep1}
                        disabled={!teamsSelected.length || isLoading}
                    />
                </Group>
            </Modal>
            <Modal
                opened={openedConfirm}
                centered
                onClose={handleCancelConfirm}
                withCloseButton={false}
                style={{ position: 'relative' }}
            >
                <Modal.Header style={{ padding: 0, height: 50, minHeight: 'unset' }}>
                    <Modal.CloseButton className="" size={'lg'} style={{ top: '-10px' }} />
                </Modal.Header>
                <Modal.Body p={0}>
                    <Text fz={18} fw={700} style={{ textAlign: 'left', whiteSpace: 'pre-line' }}>
                        {t('edit_user.change_status.confirm_text')}
                    </Text>
                </Modal.Body>
                <Group justify="flex-end" mt={'20px'}>
                    <Buttons.Cancel
                        label={t('modal_operator.button_cancel')}
                        onClick={handleCancelConfirm}
                    />
                    <Buttons.Confirm
                        label={t('edit_user.change_status.confirm.button')}
                        onClick={handleOnConfirmStep2}
                        disabled={!teamsSelected.length || isLoading}
                    />
                </Group>
            </Modal>
        </>
    );
}
