import {
  ActionIcon,
  Button,
  Container,
  Group,
  Radio,
  Switch,
  Text,
  Title,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Show } from '@resola-ai/ui';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { BaseSyntheticEvent, Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { TextAreaEditor } from '../../../components/TextAreaEditor/TextAreaEditor';
import ViewOnly from '../../../components/ViewOnly';
import { deepCopy } from '../../../utils/common';
import { SelectStyled } from '../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import { useUserContext } from '../../userContext';
import { initAutoReplyConfig, initAutoReplyOutsideBusinessHours } from '../Operations/mockData';
import { IAutoReply, IOutBusinessHour } from '../Operations/models';
import { useAutoReplySettingContext } from './AutoReplySettingContext';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em 1.875em 1em 1.875em',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
  },
  modeViewOnly: {
    '& input + label.mantine-Switch-track': {
      backgroundColor: theme.colors.violet[3],
      borderColor: theme.colors.violet[3],
    },
  },
  title: {
    color: theme.colors.gray[8],
    fontSize: '20px',
    fontWeight: 700,
    marginBottom: '20px',
  },
  switchStyle: {
    color: theme.colors.gray[8],
    fontSize: '16px',
    fontWeight: 700,
    display: 'flex',
    alignItems: 'flex-start',
    '& .mantine-Switch-body': {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      '&:has(input:disabled) label.mantine-Switch-track': {
        backgroundColor: theme.colors.violet[3],
        borderColor: theme.colors.violet[3],
      },
    },
  },
  labelCheckbox: {
    color: theme.colors.dark[4],
    fontWeight: 600,
  },
  activeControlWrapper: {
    '.mantine-SegmentedControl-label': {
      color: '#FFFFFF',
      '&:hover': {
        color: '#FFFFFF',
      },
    },
  },
  controlWrapper: {
    '.mantine-SegmentedControl-label': {
      color: theme.colors.navy[9],
      fontWeight: 700,
      '&:hover': {
        color: theme.colors.navy[9],
      },
    },
  },
  segmentControlRoot: {
    backgroundColor: theme.colors.gray[2],
    borderRadius: '6px',
  },
  checkboxArea: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  addButton: {
    background: '#ffffff',
    border: '1px solid #CED4DA',
    padding: '5px 10px',
    fontSize: '12px',
    marginLeft: '1.2rem',
    borderRadius: '3px',
  },
  customRadio: {
    '& .mantine-Radio-inner': {
      marginTop: '8px',
    },
  },
  button: {
    '&:disabled': {
      backgroundColor: '#9ea1f7',
      color: '#fff',
    },
  },
  actionIcon: {
    '&:disabled': {
      color: '#9ea1f7',
    },
  },
}));

const arrayIntervalOptions = new Array(60).fill(0).map((_, index) => `${index + 1}`);
const arrayRepeatOptions = new Array(10).fill(0).map((_, index) => `${index + 1}`);

function generateId(prefix = '') {
  // Use crypto.randomUUID() for better uniqueness and security
  // Fallback to timestamp + random for older browsers
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return `${prefix}${crypto.randomUUID()}`;
  }

  // Fallback implementation
  const timestamp = Date.now();
  const randomNum = Math.floor(Math.random() * 10000);
  return `${prefix}${timestamp}-${randomNum}`;
}

export function AutoReplySettings() {
  const { classes, cx } = useStyle();
  const { isManager } = useUserContext();
  const [loading, setLoading] = useState(false);
  const [localReplyOutOfBusinessHour, setLocalOutOfBusinessHour] = useState<IOutBusinessHour>(
    initAutoReplyOutsideBusinessHours
  );
  const { t, replyOutsideBusinessHour, updateOperationSettings, reloadOperationSettings } =
    useAutoReplySettingContext();

  const saveOutOfBusinessHour = useCallback(
    async (flags?: boolean) => {
      setLoading(true);
      try {
        const tmp = deepCopy(localReplyOutOfBusinessHour) as IOutBusinessHour;
        if (flags !== undefined) tmp.enable = flags;
        await updateOperationSettings({ outBusinessHour: tmp });
      } catch (e) {
      } finally {
        setLoading(false);
      }
    },
    [localReplyOutOfBusinessHour, updateOperationSettings]
  );

  const handleToggleOutOfBusinessHour = useCallback(
    (e: BaseSyntheticEvent) => {
      const checked = e.target.checked;
      setLocalOutOfBusinessHour((pre) => {
        return { ...pre, enable: e.target.checked };
      });
      setTimeout(() => saveOutOfBusinessHour(checked), 100);
    },
    [saveOutOfBusinessHour]
  );

  const handleUpdateOutOfBusinessHourMessage = useCallback((message: string) => {
    setLocalOutOfBusinessHour((pre) => {
      return { ...pre, message: message };
    });
  }, []);

  useEffect(() => {
    setLocalOutOfBusinessHour(deepCopy(replyOutsideBusinessHour));
  }, [replyOutsideBusinessHour]);

  useEffect(() => {
    return () => {
      reloadOperationSettings();
    };
  }, [reloadOperationSettings]);

  return (
    <>
      <Container size={'lg'} mb={'lg'} p={'1rem'}>
        <Group justify='flex-start'>
          <Title className={classes.title} mt={'lg'} tt='capitalize'>
            {t('auto_reply.title')}
          </Title>
          {!isManager && <ViewOnly />}
        </Group>

        <Container fluid className={cx(classes.container, !isManager && classes.modeViewOnly)}>
          <Switch
            mt={'lg'}
            mb={'sm'}
            miw={'180px'}
            color='decaGreen.6'
            labelPosition='left'
            disabled={!isManager || loading}
            checked={localReplyOutOfBusinessHour.enable}
            onChange={handleToggleOutOfBusinessHour}
            className={classes.switchStyle}
            label={
              <Text className={classes.switchStyle} miw={'180px'}>
                {t('auto_reply.out_of_working.title')}
              </Text>
            }
          />

          <Text
            pb={3}
            mb={'sm'}
            mr={80}
            fw={400}
            size={rem(14)}
            lineClamp={1}
            c='gray.7'
            sx={{ zIndex: 1, lineHeight: '1.2rem' }}
          >
            {localReplyOutOfBusinessHour.enable
              ? t('auto_reply.out_of_working.description.on')
              : t('auto_reply.out_of_working.description.off')}
          </Text>

          <Show condition={localReplyOutOfBusinessHour?.enable}>
            <TextAreaEditor
              autosize
              minRows={3}
              maxRows={5}
              disabled={!isManager}
              value={localReplyOutOfBusinessHour?.message || ''}
              onChange={handleUpdateOutOfBusinessHourMessage}
            />

            <Group justify='flex-end' mt={'sm'}>
              <Button
                color='navy.0'
                loading={loading}
                disabled={!isManager}
                onClick={() => saveOutOfBusinessHour()}
                className={classes.button}
              >
                {t('save_btn')}
              </Button>
            </Group>
          </Show>
        </Container>

        <AutoReplyConfigComponent />
      </Container>
    </>
  );
}

type LocalAutoConfig = IAutoReply & { repeatOption?: 'yes' | 'no' };

function AutoReplyConfigComponent() {
  const { classes } = useStyle();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});
  const { isManager } = useUserContext();
  const { t, autoReplyConfig, updateOperationSettings } = useAutoReplySettingContext();

  const [localAutoReplyConfig, setLocalAutoReplyConfig] =
    useState<LocalAutoConfig>(initAutoReplyConfig);

  const currentLengthAutoReplyItem = useMemo(
    () => localAutoReplyConfig?.messages?.length || 0,
    [localAutoReplyConfig?.messages?.length]
  );

  const checkIsValid = useCallback(() => {
    const messages = localAutoReplyConfig?.messages;
    if (!messages?.length) return;
    const errors: Record<string, string | undefined> = {};
    messages
      .filter((item) => !item.text || !item?.text?.length)
      .forEach((item) => {
        errors[item.id] = t('required_field');
      });
    setErrors(() => {
      return errors;
    });
    return Object.keys(errors)?.length <= 0;
  }, [localAutoReplyConfig.messages, t]);

  const handleSaveAutoReplyConfig = useCallback(
    async (flags?: boolean) => {
      const newAutoConfig = deepCopy(localAutoReplyConfig) as LocalAutoConfig;
      if (flags !== undefined) {
        newAutoConfig.enable = flags;
      }

      if (flags === undefined && !checkIsValid()) return;

      if (newAutoConfig.repeatOption === 'no') {
        newAutoConfig.repeat = 0;
      }
      delete newAutoConfig?.repeatOption;
      setLoading(true);
      try {
        await updateOperationSettings({ autoReply: newAutoConfig });
      } catch (e) {
      } finally {
        setLoading(false);
      }
    },
    [checkIsValid, localAutoReplyConfig, updateOperationSettings]
  );

  const handleToggleAutoReply = useCallback(
    (e: BaseSyntheticEvent) => {
      const checked = e.target.checked;
      setLocalAutoReplyConfig((pre) => {
        return {
          ...pre,
          enable: checked,
        };
      });
      handleSaveAutoReplyConfig(checked);
    },
    [handleSaveAutoReplyConfig]
  );

  const removeAutoReplyItem = useCallback((id?: string) => {
    if (!id) return;
    setLocalAutoReplyConfig((pre) => {
      return {
        ...pre,
        messages: pre.messages.filter((item) => item.id !== id),
      };
    });
  }, []);

  const handleChangeMessageAutoReplyItem = useCallback(
    (id: string | undefined, message: string) => {
      if (!id) return;
      setLocalAutoReplyConfig((pre) => {
        const messages = pre.messages;
        messages.find((item) => item.id === id).text = message;
        return {
          ...pre,
          messages: [...messages],
        };
      });
    },
    []
  );

  const handleChangeIntervalAutoReplyItem = useCallback(
    (id: string | undefined, interVal: string) => {
      if (!id) return;
      setLocalAutoReplyConfig((pre) => {
        const messages = pre.messages;
        messages.find((item) => item.id === id).interval = +interVal;
        return {
          ...pre,
          messages: [...messages],
        };
      });
    },
    []
  );

  const handleChangeRepeatOption = useCallback(
    (option: 'yes' | 'no') => {
      const repeatOption = localAutoReplyConfig.repeatOption || 'no';
      if (repeatOption === option) return;

      setLocalAutoReplyConfig((pre) => {
        const newObj = {
          ...pre,
          repeatOption: option,
          repeat: pre.repeat === 0 && option === 'yes' ? 1 : pre.repeat,
        };
        return newObj;
      });
    },
    [localAutoReplyConfig.repeatOption]
  );

  const changeRepeatTime = useCallback((val: string) => {
    setLocalAutoReplyConfig((pre) => {
      return {
        ...pre,
        repeat: +val,
      };
    });
  }, []);

  const handleAddNewAutoReplyItem = useCallback(() => {
    if (currentLengthAutoReplyItem >= 10) return;
    setLocalAutoReplyConfig((pre) => {
      return {
        ...pre,
        messages: [
          ...pre.messages,
          { id: generateId(), text: '', interval: 1 } as IAutoReply['messages'][0],
        ],
      };
    });
  }, [currentLengthAutoReplyItem]);

  const autoReplyItemsNotFirst = useMemo(() => {
    return localAutoReplyConfig?.messages?.slice(1);
  }, [localAutoReplyConfig.messages]);

  useEffect(() => {
    let autoConfigTmp = deepCopy(autoReplyConfig) as LocalAutoConfig;
    if (!(autoConfigTmp as Object).hasOwnProperty('messages'))
      autoConfigTmp.messages = initAutoReplyConfig.messages;
    if (!(autoConfigTmp as Object).hasOwnProperty('enable'))
      autoConfigTmp.enable = initAutoReplyConfig.enable;

    let repeat = autoConfigTmp?.repeat;
    if (isNaN(repeat)) repeat = 0;
    else {
      repeat = +repeat;
    }
    autoConfigTmp = {
      ...autoConfigTmp,
      repeatOption: repeat > 0 ? 'yes' : 'no',
    };
    setLocalAutoReplyConfig(autoConfigTmp);
  }, [autoReplyConfig]);

  return (
    <Container className={classes.container} fluid mb={'lg'} mt={30}>
      <Switch
        mt={'lg'}
        miw={'180px'}
        labelPosition='left'
        className={classes.switchStyle}
        disabled={!isManager || loading}
        onChange={handleToggleAutoReply}
        checked={localAutoReplyConfig.enable}
        label={
          <Text className={classes.switchStyle} miw={'180px'}>
            {t('auto_reply.send_message_to_awaiting_user.title')}
          </Text>
        }
        color='decaGreen.6'
      />
      <Group mt='xs'>
        <Text fw={400} size={rem(14)} c='gray.7' mr={80} sx={{ lineHeight: '1.25rem' }}>
          {t('auto_reply.send_message_to_awaiting_user.description')}
        </Text>
      </Group>
      <Show condition={localAutoReplyConfig.enable}>
        <Group mt='xs' mb={'sm'}>
          <Text lineClamp={1} fw={500} size={rem(14)} c='gray.7'>
            {t('auto_reply.send_message_to_awaiting_user.setting_time.first')}
          </Text>
          <SelectStyled
            placeholder='1'
            disabled={!isManager}
            data={arrayIntervalOptions}
            style={{ width: '80px', margin: 0 }}
            value={`${localAutoReplyConfig.messages?.[0].interval}` || `1`}
            onChange={(interVal) =>
              handleChangeIntervalAutoReplyItem(localAutoReplyConfig.messages?.[0]?.id, interVal)
            }
          />
          <Text lineClamp={1} fw={500} size={rem(14)} c='gray.7'>
            {t('auto_reply.send_message_to_awaiting_user.setting_time.first.later_part')}
          </Text>
        </Group>
        <TextAreaEditor
          autosize
          minRows={3}
          maxRows={5}
          disabled={!isManager}
          error={errors?.[localAutoReplyConfig.messages[0]?.id]}
          value={localAutoReplyConfig.messages?.[0].text}
          onChange={(val) =>
            handleChangeMessageAutoReplyItem(localAutoReplyConfig.messages?.[0].id, val)
          }
        />

        {autoReplyItemsNotFirst?.length > 0 &&
          autoReplyItemsNotFirst.map((autoConfig) => {
            return (
              <Fragment key={autoConfig.id}>
                <Group my={'sm'} justify='space-between'>
                  <Group justify='flex-start'>
                    <Text lineClamp={1} fw={500} size={rem(14)} c='gray.7'>
                      {t('auto_reply.send_message_to_awaiting_user.setting_time.first.1')}
                    </Text>
                    <SelectStyled
                      placeholder='1'
                      disabled={!isManager}
                      data={arrayIntervalOptions}
                      style={{ width: '80px', margin: 0 }}
                      value={`${autoConfig?.interval || 1}`}
                      onChange={(interVal) =>
                        handleChangeIntervalAutoReplyItem(autoConfig?.id, interVal)
                      }
                    />
                    <Text lineClamp={1} fw={500} size={rem(14)} c='gray.7'>
                      {t(
                        'auto_reply.send_message_to_awaiting_user.setting_time.first.later_part.1'
                      )}
                    </Text>
                  </Group>
                  <Group>
                    <ActionIcon
                      size={25}
                      color='navy.0'
                      variant='transparent'
                      disabled={!isManager}
                      className={classes.actionIcon}
                      onClick={() => removeAutoReplyItem(autoConfig?.id)}
                    >
                      <IconTrash />
                    </ActionIcon>
                  </Group>
                </Group>
                <TextAreaEditor
                  autosize
                  minRows={3}
                  maxRows={5}
                  disabled={!isManager}
                  value={autoConfig.text}
                  error={errors?.[autoConfig?.id]}
                  onChange={(val) => handleChangeMessageAutoReplyItem(autoConfig.id, val)}
                />
              </Fragment>
            );
          })}

        <Button
          my={'sm'}
          color='dark'
          variant='default'
          className={classes.button}
          onClick={handleAddNewAutoReplyItem}
          disabled={!isManager || currentLengthAutoReplyItem >= 10}
        >
          <IconPlus size={15} />
          <Text fz={14} fw={700} ml={10}>
            {t('auto_reply.send_message_to_awaiting_user.add_new_setting_time.button')}
          </Text>
        </Button>

        <Container px={0} mx={0}>
          <Radio
            mr={'lg'}
            color='navy.0'
            sx={{ cursor: 'pointer' }}
            disabled={!isManager}
            checked={localAutoReplyConfig.repeatOption === 'yes'}
            onChange={() => handleChangeRepeatOption('yes')}
            className={classes.customRadio}
            label={
              <Group>
                <Text fz={14} c='gray.7'>
                  {t('auto_reply.send_message_to_awaiting_user.option_send.option_1.first_part')}
                </Text>
                <SelectStyled
                  placeholder='1'
                  label={''}
                  disabled={!isManager || localAutoReplyConfig.repeatOption === 'no'}
                  data={arrayRepeatOptions}
                  style={{ width: '80px', margin: 0 }}
                  value={`${localAutoReplyConfig.repeat}`}
                  onChange={(val) => changeRepeatTime(val)}
                />
                <Text size={rem(14)} c='gray.7'>
                  {t('auto_reply.send_message_to_awaiting_user.option_send.option_1.later_part')}
                </Text>
              </Group>
            }
          />
          <Radio
            mt={10}
            color='navy.0'
            disabled={!isManager}
            sx={{ cursor: 'pointer' }}
            checked={localAutoReplyConfig.repeatOption === 'no'}
            label={t('auto_reply.send_message_to_awaiting_user.option_send.option_2')}
            onChange={() => handleChangeRepeatOption('no')}
          />
        </Container>

        <Group justify='flex-end' mt={'sm'} mb={30}>
          <Button
            color='navy.0'
            loading={loading}
            disabled={!isManager}
            onClick={() => handleSaveAutoReplyConfig()}
            className={classes.button}
          >
            {t('save_btn')}
          </Button>
        </Group>
      </Show>
    </Container>
  );
}
