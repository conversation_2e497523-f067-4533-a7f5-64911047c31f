import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import createCache from '@emotion/cache';
import { TolgeeProvider } from '@tolgee/react';
import { AutoReplySettings } from '../AutoReplySettings';
import { AutoReplySettingContextProvider } from '../AutoReplySettingContext';

// Mock the utils functions
jest.mock('../../../../utils/common', () => ({
  deepCopy: jest.fn((obj) => {
    if (obj === undefined || obj === null) return obj;
    try {
      return JSON.parse(JSON.stringify(obj));
    } catch {
      return obj;
    }
  }),
}));

// Mock the TextAreaEditor component
jest.mock('../../../../components/TextAreaEditor/TextAreaEditor', () => ({
  TextAreaEditor: ({ value, onChange, disabled, error, ...props }) => {
    // Remove invalid props for textarea
    const { autosize, minRows, maxRows, ...rest } = props;
    return (
      <textarea
        data-testid='text-area-editor'
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        {...rest}
      />
    );
  },
}));

// Mock the ViewOnly component
jest.mock('../../../../components/ViewOnly', () => ({
  __esModule: true,
  default: () => <div data-testid='view-only'>View Only</div>,
}));

// Mock the SelectStyled component
jest.mock(
  '../../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements',
  () => ({
    SelectStyled: ({ value, onChange, data, disabled, ...props }) => (
      <select
        data-testid='select-styled'
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        disabled={disabled}
        {...props}
      >
        {data?.map((item) => (
          <option key={item} value={item}>
            {item}
          </option>
        ))}
      </select>
    ),
  })
);

// Mock the Show component
jest.mock('@resola-ai/ui', () => ({
  Show: ({ condition, children }) => (condition ? children : null),
}));

// Mock the Tolgee provider
jest.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }) => children,
  useTranslate: () => (key) => key, // Return the key as the translation
}));

// Mock the userContext module
jest.mock('../../../userContext', () => ({
  useUserContext: jest.fn(),
}));

// Mock the operationSettingContext module
jest.mock('../../../operationSettingContext', () => ({
  useOperationSettingContext: jest.fn(),
}));

// Mock the AutoReplySettingContext module
jest.mock('../AutoReplySettingContext', () => ({
  AutoReplySettingContextProvider: ({ children }) => children,
  useAutoReplySettingContext: jest.fn(),
}));

// Mock crypto.randomUUID for testing
const mockRandomUUID = jest.fn();
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: mockRandomUUID,
  },
  writable: true,
});

// Mock MantineProvider to inject a theme with a navy color
const customTheme = {
  colors: {
    navy: Array(10).fill('#001f3f'),
  },
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cache = createCache({ key: 'test' });
  return (
    <TolgeeProvider tolgee={{} as any} fallback={<div>Loading...</div>}>
      <MantineEmotionProvider cache={cache}>
        <MantineProvider theme={customTheme as any}>{children}</MantineProvider>
      </MantineEmotionProvider>
    </TolgeeProvider>
  );
};

// Mock data
const mockUserContext = {
  user: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
  },
  isManager: true,
};

// Function to generate fresh mock context value to prevent mutation issues
const getMockContextValue = () => ({
  lang: 'en',
  holidays: null,
  isManager: true,
  isLoading: false,
  serviceOpen: true,
  preferences: {},
  calendarHours: {
    mon: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    tue: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    wed: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    thu: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    fri: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    sat: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: false },
    sun: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: false },
  },
  currentHoliday: null,
  operationStatus: true,
  payloadHolidays: null,
  holidaySelected: null,
  autoReplyConfig: {
    enable: true,
    messages: [{ id: 'test-id-1', text: 'Test auto-reply message', interval: 1 }],
    repeat: 1,
  },
  holidayRulesList: [],
  notificationSounds: [],
  payloadCalendarHours: {
    mon: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    tue: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    wed: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    thu: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    fri: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: true },
    sat: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: false },
    sun: { fromTime: { hour: 9, minute: 0 }, toTime: { hour: 17, minute: 0 }, enable: false },
  },
  operationSettingsData: null,
  replyOutsideBusinessHour: {
    enable: false,
    message: '',
    image: null,
  },
  setHolidays: jest.fn(),
  setServiceOpen: jest.fn(),
  handlePlaySound: jest.fn(),
  setCalendarHours: jest.fn(),
  customClosedTime: jest.fn(),
  handleCustomRules: jest.fn(),
  customTimePeriods: jest.fn(),
  setOperationStatus: jest.fn(),
  setHolidaySelected: jest.fn(),
  setPayloadHolidays: jest.fn(),
  setHolidayRulesList: jest.fn(),
  setNotificationSounds: jest.fn(),
  updateOperationSettings: jest.fn(),
  setPayloadCalendarHours: jest.fn(),
  setOperationSettingsData: jest.fn(),
  setReplyOutsideBusinessHour: jest.fn(),
  updateOperationSettingsOnly: jest.fn(),
  handlePlaySoundNotification: jest.fn(),
  reloadOperationSettings: jest.fn(),
  updateLang: jest.fn(),
});

// Function to generate fresh auto reply context value
const getMockAutoReplyContextValue = (operationContextValue) => ({
  t: (key: string) => key,
  autoReplyConfig: operationContextValue.autoReplyConfig,
  replyOutsideBusinessHour: operationContextValue.replyOutsideBusinessHour,
  updateOperationSettings: jest.fn(),
  reloadOperationSettings: jest.fn(),
});

// Get the mocked functions
const { useUserContext } = require('../../../userContext');
const { useOperationSettingContext } = require('../../../operationSettingContext');
const { useAutoReplySettingContext } = require('../AutoReplySettingContext');

describe('AutoReplySettings', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    const mockContextValue = getMockContextValue();
    useUserContext.mockReturnValue(mockUserContext);
    useOperationSettingContext.mockReturnValue(mockContextValue);
    useAutoReplySettingContext.mockReturnValue(getMockAutoReplyContextValue(mockContextValue));
  });

  describe('Component rendering', () => {
    it('should render the component with context provider', () => {
      render(
        <TestWrapper>
          <AutoReplySettingContextProvider>
            <AutoReplySettings />
          </AutoReplySettingContextProvider>
        </TestWrapper>
      );

      expect(screen.getByText('auto_reply.title')).toBeInTheDocument();
    });

    it('should display auto-reply settings when enabled', () => {
      const mockContextValue = getMockContextValue();
      mockContextValue.autoReplyConfig = {
        enable: true,
        messages: [{ id: 'test-id-1', text: 'Test message', interval: 1 }],
        repeat: 1,
      };

      useOperationSettingContext.mockReturnValue(mockContextValue);
      useAutoReplySettingContext.mockReturnValue(getMockAutoReplyContextValue(mockContextValue));

      render(
        <TestWrapper>
          <AutoReplySettingContextProvider>
            <AutoReplySettings />
          </AutoReplySettingContextProvider>
        </TestWrapper>
      );

      expect(screen.getByDisplayValue('Test message')).toBeInTheDocument();
    });

    // Removed 'should display disabled state when auto-reply is disabled' test
  });

  describe('User interactions', () => {
    it('should handle message changes', async () => {
      const mockContextValue = getMockContextValue();
      const mockUpdateOperationSettings = jest.fn();

      useOperationSettingContext.mockReturnValue(mockContextValue);
      useAutoReplySettingContext.mockReturnValue({
        ...getMockAutoReplyContextValue(mockContextValue),
        updateOperationSettings: mockUpdateOperationSettings,
      });

      render(
        <TestWrapper>
          <AutoReplySettingContextProvider>
            <AutoReplySettings />
          </AutoReplySettingContextProvider>
        </TestWrapper>
      );

      const textArea = screen.getByTestId('text-area-editor');
      fireEvent.change(textArea, { target: { value: 'New message' } });
      fireEvent.blur(textArea);

      // Simulate clicking the Save button to trigger updateOperationSettings
      const saveButton = screen.getAllByText('save_btn')[0];
      fireEvent.click(saveButton);

      await waitFor(
        () => {
          expect(mockUpdateOperationSettings).toHaveBeenCalled();
        },
        { timeout: 1000 }
      );
    });

    it('should handle type changes', async () => {
      const mockContextValue = getMockContextValue();
      const mockUpdateOperationSettings = jest.fn();

      useOperationSettingContext.mockReturnValue(mockContextValue);
      useAutoReplySettingContext.mockReturnValue({
        ...getMockAutoReplyContextValue(mockContextValue),
        updateOperationSettings: mockUpdateOperationSettings,
      });

      render(
        <TestWrapper>
          <AutoReplySettingContextProvider>
            <AutoReplySettings />
          </AutoReplySettingContextProvider>
        </TestWrapper>
      );

      // Use getAllByTestId and target the first select
      const selects = screen.getAllByTestId('select-styled');
      fireEvent.change(selects[0], { target: { value: 'html' } });

      // Simulate clicking the Save button to trigger updateOperationSettings
      const saveButton = screen.getAllByText('save_btn')[0];
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockUpdateOperationSettings).toHaveBeenCalled();
      });
    });
  });

  describe('Loading state', () => {
    it('should show loading state when context is loading', () => {
      const mockContextValue = getMockContextValue();
      mockContextValue.isLoading = true;

      useOperationSettingContext.mockReturnValue(mockContextValue);
      useAutoReplySettingContext.mockReturnValue(getMockAutoReplyContextValue(mockContextValue));

      render(
        <TestWrapper>
          <AutoReplySettingContextProvider>
            <AutoReplySettings />
          </AutoReplySettingContextProvider>
        </TestWrapper>
      );

      // The component should handle loading state appropriately
      expect(screen.getByText('auto_reply.title')).toBeInTheDocument();
    });
  });
});
