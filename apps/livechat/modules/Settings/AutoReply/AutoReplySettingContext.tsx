import { ReactNode, createContext, useContext, useMemo } from 'react';
import { useOperationSettingContext } from '../../operationSettingContext';
import { useTranslate } from '@tolgee/react';

export const useAutoReplySetting = () => {
  const { t } = useTranslate('workspace');
  const {
    autoReplyConfig,
    replyOutsideBusinessHour,
    reloadOperationSettings,
    updateOperationSettingsOnly: updateOperationSettings,
  } = useOperationSettingContext();

  return useMemo(
    () => ({
      t,
      autoReplyConfig,
      replyOutsideBusinessHour,
      reloadOperationSettings,
      updateOperationSettings,
    }),
    [t, autoReplyConfig, replyOutsideBusinessHour, updateOperationSettings, reloadOperationSettings]
  );
};

type AutoReplySettingType = ReturnType<typeof useAutoReplySetting>;

const AutoReplySettingContext = createContext<AutoReplySettingType | null>(null);

export const AutoReplySettingContextProvider = ({ children }: { children: ReactNode }) => {
  const data = useAutoReplySetting();
  return (
    <AutoReplySettingContext.Provider value={data}>{children}</AutoReplySettingContext.Provider>
  );
};

export const useAutoReplySettingContext = () => {
  const data = useContext(AutoReplySettingContext);

  if (!data) {
    throw new Error('AutoReplySettingContext should be used in AutoReplySettingContextProvider');
  }

  return data;
};
