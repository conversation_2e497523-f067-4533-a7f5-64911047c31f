import {
  But<PERSON>,
  Checkbox,
  Container,
  CopyButton,
  Flex,
  Group,
  Loader,
  Modal,
  Space,
  Text,
  Textarea,
  Title,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPencil } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useState } from 'react';
import { TextAreaEditor } from '../../../components/TextAreaEditor/TextAreaEditor';
import ViewOnly from '../../../components/ViewOnly';
import { MAX_CONCURRENT_CONVERSATION } from '../../../constants';
import { delay } from '../../../utils/common';
import { SelectStyled } from '../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import ButtonGroup from '../../Widgets/ButtonGroup';
import { useOperationSettingContext } from '../../operationSettingContext';
import { useUserContext } from '../../userContext';
import { useWarningOnExit } from '../Integrations/Webhook/useWarningOnExit';
import { IPreferences } from '../Operations/models';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1em 1em 1em 1.875em',
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.md,
  },
  resetButton: {
    fontSize: '0.875rem',
  },
  title: {
    fontSize: '1.25rem',
    fontWeight: 'bold',
    color: theme.colors.dark[4],
    // paddingBottom: '16px',
  },
  closeButton: {
    svg: {
      width: '24px !important',
      height: '24px !important',
    },
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: '16px 0',
  },
  aiPromptBottomText: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '1rem',
  },
  errorText: {
    color: '#f93549',
  },
  errorBorder: {
    borderColor: '#f93549',
  },
}));

// eslint-disable-next-line no-unused-vars
const enum FunctionEnum {
  // eslint-disable-next-line no-unused-vars
  SHOW_AUTO = 'show-auto',
  // eslint-disable-next-line no-unused-vars
  ASSIGN_AFTER_DONE = 'assign-after-done',
  // eslint-disable-next-line no-unused-vars
  CAN_CLOSE = 'close',
  // eslint-disable-next-line no-unused-vars
  AUTO_CLOSE = 'auto-close',
  // eslint-disable-next-line no-unused-vars
  AUTO_CLOSE_INTERVAL = 'auto-close-interval',
  // eslint-disable-next-line no-unused-vars
  AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE = 'auto-close-send-message-before-complete',
  // eslint-disable-next-line no-unused-vars
  AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_INTERVAL = 'auto-close-send-message-before-complete-interval',
  // eslint-disable-next-line no-unused-vars
  AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_MESSGE = 'auto-close-send-message-before-complete-message',
  // eslint-disable-next-line no-unused-vars
  AI_PROMPT = 'ai-prompt',
  // eslint-disable-next-line no-unused-vars
  CREATE_NEW_CONV = 'create-new',
  // eslint-disable-next-line no-unused-vars
  MAX_CONCURRENT_CONVERSATION_SETTING = 'max-concurrent-conversations-setting',
}

const OptionsForMaxConversationSettings = new Array(100)
  .fill(0)
  .map((item, index) => `${index + 1}`);

export const Functions = () => {
  const { classes } = useStyle();
  const { t } = useTranslate('workspace');

  const { isManager, isValidatingProfile } = useUserContext();
  const { preferences, updateOperationSettings } = useOperationSettingContext();
  const [promptSummary, setPromptSummary] = useState('');
  const [aiPromptModalDisplay, setAIPromptModalDisplay] = useState(false);

  const defaultPrompt = t('defaultAIPrompt');
  const defaultMessageBeforeComplete = t('autoSendMessageBeforeComplete_defaultMessage');

  const allowCloseConv = preferences?.closeConversationOnlyInprogress || false;
  const showAutoBtnIfOff = preferences?.showAutoBtnIfOff || false;
  const presetAssignAfterDone = preferences?.assignAfterDone || false;

  const allowAutoClose = preferences?.autoCompletedInactiveConversation?.enable || false;
  const presetAutoCloseInterval = preferences?.autoCompletedInactiveConversation?.interval;
  const presetSendMessageBeforeComplete =
    preferences?.autoCompletedInactiveConversation?.sendMessageBeforeComplete || false;
  const presetSendMessageBeforeCompleteInterval =
    preferences?.autoCompletedInactiveConversation?.sendMessageBeforeCompleteInterval;
  const presetMessageBeforeComplete =
    preferences?.autoCompletedInactiveConversation?.messageBeforeComplete ||
    defaultMessageBeforeComplete;

  const presetMaxActiveConversation = +(
    preferences?.maxActiveConversation || MAX_CONCURRENT_CONVERSATION
  );

  const presetCreateNewConversation = preferences?.enableCreateNewConversation || false;
  const conversationPrompt = preferences?.summaryConversationPrompt || '';

  const [closeConvoCheck, setCloseConvoCheck] = useState(allowCloseConv);
  const [showAutoBtnCheck, setShowBtnAutoCheck] = useState(showAutoBtnIfOff);
  const [assignAfterDoneCheck, setAssignAfterDoneCheck] = useState(presetAssignAfterDone);
  const [autoCloseCheck, setAutoCloseCheck] = useState(allowAutoClose);
  const [autoCloseInterval, setAutoCloseInterval] = useState(presetAutoCloseInterval);
  const [sendMessageBeforeComplete, setSendMessageBeforeComplete] = useState(
    presetSendMessageBeforeComplete
  );
  const [sendMessageBeforeCompleteInterval, setSendMessageBeforeCompleteInterval] = useState(
    presetSendMessageBeforeCompleteInterval
  );
  const [messageBeforeComplete, setMessageBeforeComplete] = useState(presetMessageBeforeComplete);
  const [maxActiveConversation, setMaxActiveConversation] = useState(presetMaxActiveConversation);

  const [createNewConv, setCreateNewConv] = useState(presetCreateNewConversation);

  const optionsChangeAutoCloseTime = useMemo(() => {
    return [
      { label: '5' + t('minutes'), value: '5' },
      { label: '10' + t('minutes'), value: '10' },
      { label: '15' + t('minutes'), value: '15' },
      { label: '20' + t('minutes'), value: '20' },
      { label: '25' + t('minutes'), value: '25' },
      { label: '30' + t('minutes'), value: '30' },
      { label: '35' + t('minutes'), value: '35' },
      { label: '40' + t('minutes'), value: '40' },
      { label: '45' + t('minutes'), value: '45' },
      { label: '50' + t('minutes'), value: '50' },
      { label: '55' + t('minutes'), value: '55' },
      { label: '1' + t('hours'), value: '60' },
      { label: '2' + t('hours'), value: '120' },
      { label: '3' + t('hours'), value: '180' },
    ];
  }, [t]);

  const optionSendAutoMessageBeforeComplete = useMemo<
    { label: string; value: string; disabled: boolean }[]
  >(() => {
    return [
      {
        label: '1' + t('minutes'),
        value: '1',
        disabled: +autoCloseInterval <= 1,
      },
      {
        label: '2' + t('minutes'),
        value: '2',
        disabled: +autoCloseInterval <= 2,
      },
      {
        label: '3' + t('minutes'),
        value: '3',
        disabled: +autoCloseInterval <= 3,
      },
      {
        label: '4' + t('minutes'),
        value: '4',
        disabled: +autoCloseInterval <= 4,
      },
      {
        label: '5' + t('minutes'),
        value: '5',
        disabled: +autoCloseInterval <= 5,
      },
      {
        label: '10' + t('minutes'),
        value: '10',
        disabled: +autoCloseInterval <= 10,
      },
      {
        label: '15' + t('minutes'),
        value: '15',
        disabled: +autoCloseInterval <= 15,
      },
    ];
  }, [autoCloseInterval, t]);

  const getCorrectSendMessageBeforeCompleteInterval = (autoCloseInterval: number): number => {
    const sendAutoMessageBeforeCompleteInterval = [15, 10, 5, 4, 3, 2, 1];
    return sendAutoMessageBeforeCompleteInterval.find((item) => autoCloseInterval > item);
  };

  const handleCancelEditPrompt = async () => {
    setAIPromptModalDisplay(false);
    const originalPrompt = preferences?.summaryConversationPrompt
      ? preferences.summaryConversationPrompt
      : defaultPrompt;
    await delay(300);
    setPromptSummary(originalPrompt);
  };

  const handleChange = (value: boolean, selection: FunctionEnum, interval?: number) => {
    const newSettings: IPreferences = {
      showAutoBtnIfOff: showAutoBtnCheck,
      closeConversationOnlyInprogress: closeConvoCheck,
      autoCompletedInactiveConversation: {
        enable: autoCloseCheck,
        interval: autoCloseInterval,
        sendMessageBeforeComplete: sendMessageBeforeComplete,
        sendMessageBeforeCompleteInterval: sendMessageBeforeCompleteInterval,
        messageBeforeComplete: messageBeforeComplete,
      },
      enableCreateNewConversation: createNewConv,
      summaryConversationPrompt: promptSummary,
      maxActiveConversation: maxActiveConversation,
      assignAfterDone: assignAfterDoneCheck,
    };
    switch (selection) {
      case FunctionEnum.SHOW_AUTO:
        setShowBtnAutoCheck(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            showAutoBtnIfOff: value,
          },
        });
        break;
      case FunctionEnum.CAN_CLOSE:
        setCloseConvoCheck(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            closeConversationOnlyInprogress: value,
          },
        });
        break;
      case FunctionEnum.AUTO_CLOSE:
        setAutoCloseCheck(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            autoCompletedInactiveConversation: {
              ...newSettings.autoCompletedInactiveConversation,
              enable: value,
            },
          },
        });
        break;
      case FunctionEnum.AUTO_CLOSE_INTERVAL:
        setAutoCloseInterval(interval);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            autoCompletedInactiveConversation: {
              ...newSettings.autoCompletedInactiveConversation,
              interval: interval,
              sendMessageBeforeCompleteInterval:
                interval <=
                newSettings.autoCompletedInactiveConversation.sendMessageBeforeCompleteInterval
                  ? getCorrectSendMessageBeforeCompleteInterval(interval)
                  : newSettings.autoCompletedInactiveConversation.sendMessageBeforeCompleteInterval,
            },
          },
        });
        break;
      case FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE:
        setSendMessageBeforeComplete(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            autoCompletedInactiveConversation: {
              ...newSettings.autoCompletedInactiveConversation,
              sendMessageBeforeComplete: value,
            },
          },
        });
        break;
      case FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_INTERVAL:
        setSendMessageBeforeCompleteInterval(interval);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            autoCompletedInactiveConversation: {
              ...newSettings.autoCompletedInactiveConversation,
              sendMessageBeforeCompleteInterval: interval,
            },
          },
        });
        break;
      case FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_MESSGE:
        setMessageBeforeComplete(value as unknown as string);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            autoCompletedInactiveConversation: {
              ...newSettings.autoCompletedInactiveConversation,
              messageBeforeComplete: value as unknown as string,
            },
          },
        });
        break;
      case FunctionEnum.CREATE_NEW_CONV:
        setCreateNewConv(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            enableCreateNewConversation: value,
          },
        });
        break;
      case FunctionEnum.AI_PROMPT:
        setAIPromptModalDisplay(false);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            summaryConversationPrompt:
              (value as unknown as string) || promptSummary?.trim() || conversationPrompt,
          },
        });
        break;
      case FunctionEnum.MAX_CONCURRENT_CONVERSATION_SETTING:
        setMaxActiveConversation(interval);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            maxActiveConversation: interval,
          },
        });
        break;
      case FunctionEnum.ASSIGN_AFTER_DONE:
        setAssignAfterDoneCheck(value);
        updateOperationSettings({
          preferences: {
            ...newSettings,
            assignAfterDone: value,
          },
        });
        break;
      default:
        break;
    }
  };

  const handleCustomPromptEdit = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPromptSummary(e.target.value);
  };

  const handleResetPrompt = () => {
    setPromptSummary(defaultPrompt);
  };

  useWarningOnExit(presetMessageBeforeComplete !== messageBeforeComplete, t('warning_exit'));

  useEffect(() => {
    if (preferences?.summaryConversationPrompt) {
      setPromptSummary(preferences.summaryConversationPrompt);
    } else {
      setPromptSummary(defaultPrompt);
    }
  }, [defaultPrompt, preferences]);

  useEffect(() => {
    setCloseConvoCheck(allowCloseConv);
    setShowBtnAutoCheck(showAutoBtnIfOff);
    setAutoCloseCheck(allowAutoClose);
    setCreateNewConv(presetCreateNewConversation);
    if (presetAutoCloseInterval) {
      setAutoCloseInterval(presetAutoCloseInterval);
    } else {
      setAutoCloseInterval(5);
    }
    setSendMessageBeforeComplete(presetSendMessageBeforeComplete);
    setSendMessageBeforeCompleteInterval(presetSendMessageBeforeCompleteInterval || 3);
    setMessageBeforeComplete(presetMessageBeforeComplete);
    setMaxActiveConversation(presetMaxActiveConversation);
    setAssignAfterDoneCheck(presetAssignAfterDone);
  }, [
    allowCloseConv,
    allowAutoClose,
    showAutoBtnIfOff,
    presetAssignAfterDone,
    presetAutoCloseInterval,
    presetCreateNewConversation,
    presetMaxActiveConversation,
    presetMessageBeforeComplete,
    presetSendMessageBeforeComplete,
    presetSendMessageBeforeCompleteInterval,
  ]);

  if (isValidatingProfile) {
    return (
      <Flex
        w={'100%'}
        align={'center'}
        justify={'center'}
        h={`calc(100dvh - var(--app-shell-header-height, 0px) + 0rem)`}
      >
        <Loader color={'navy.5'} />
      </Flex>
    );
  }

  return (
    <Container p={'md'} size={'lg'}>
      <Flex align={'center'} mt={'lg'} mb={'xl'} gap='20px'>
        <Title c='dark.4' fz={'20px'}>
          {t('functionSettings')}
        </Title>
        {!isManager && <ViewOnly />}
      </Flex>
      <Flex className={classes.container} direction={'column'} gap={30} py={30}>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('autoConvAssign')}
          </Text>
          <Flex direction={'column'}>
            <Checkbox
              disabled={!isManager}
              checked={showAutoBtnCheck}
              onChange={(e) => handleChange(e.target.checked, FunctionEnum.SHOW_AUTO)}
              color={'navy.5'}
              id='showAutoBtnIfOff'
              key={'showAutoBtnIfOff'}
              label={<Text fz='sm'>{t('showAutoBtnIfOff')}</Text>}
            />
            <Text ml={rem(32)} color='gray.6' fz='xs'>
              {t('showAutoBtnIfOffDesc')}
            </Text>
            <Checkbox
              mt={'lg'}
              color={'navy.5'}
              disabled={!isManager}
              checked={assignAfterDoneCheck}
              onChange={(e) => handleChange(e.target.checked, FunctionEnum.ASSIGN_AFTER_DONE)}
              id='showAssignAfterDoneBtn'
              key={'showAssignAfterDoneBtn'}
              label={<Text fz='sm'>{t('action.assign_method.option_6')}</Text>}
            />
            <Text ml={rem(32)} color='gray.6' fz='xs'>
              {t('action.assign_method.option_6.tooltip')}
            </Text>
          </Flex>
        </Flex>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('convEndMethod')}
          </Text>
          <Flex direction={'column'}>
            <Checkbox
              disabled={!isManager}
              checked={closeConvoCheck}
              onChange={(e) => handleChange(e.target.checked, FunctionEnum.CAN_CLOSE)}
              color={'navy.5'}
              id='closeConversationOnlyInprogress'
              key={'closeConversationOnlyInprogress'}
              label={<Text fz='sm'>{t('canEndWithoutReply')}</Text>}
            />
            <Text ml={rem(32)} color='gray.6' fz='xs'>
              {t('canEndWithoutReplyDesc')}
            </Text>
          </Flex>
        </Flex>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('changeAutoClose')}
          </Text>
          <Flex direction={'column'} w={'100%'} pr={'1rem'}>
            <Checkbox
              disabled={!isManager}
              checked={autoCloseCheck}
              onChange={(e) =>
                handleChange(e.target.checked, FunctionEnum.AUTO_CLOSE, autoCloseInterval)
              }
              color={'navy.5'}
              id='autoCloseSetting'
              key={'autoCloseSetting'}
              label={
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <Text fz='sm'>{t('changeAutoCloseTextOne')}</Text>
                  <SelectStyled
                    placeholder='1'
                    disabled={!isManager || !autoCloseCheck}
                    data={optionsChangeAutoCloseTime}
                    style={{
                      width: '100px',
                      marginLeft: '10px',
                      marginRight: '10px',
                      marginTop: '-7px',
                    }}
                    value={String(autoCloseInterval)}
                    onChange={(interVal) =>
                      handleChange(true, FunctionEnum.AUTO_CLOSE_INTERVAL, +interVal)
                    }
                  />
                  <Text fz='sm'>{t('changeAutoCloseTextTwo')}</Text>
                </div>
              }
            />
            {autoCloseCheck && (
              <Flex direction='column' mt='md' ml={33} gap='md'>
                <Checkbox
                  disabled={!isManager}
                  checked={sendMessageBeforeComplete}
                  onChange={(e) =>
                    handleChange(e.target.checked, FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE)
                  }
                  color={'navy.5'}
                  id='autoCloseSendMessageBeforeComplete'
                  key={'autoCloseSendMessageBeforeComplete'}
                  label={
                    <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Text fz='sm'>{t('autoSendMessageBeforeComplete_1')}</Text>
                      <SelectStyled
                        placeholder='1'
                        disabled={!isManager || !sendMessageBeforeComplete}
                        data={optionSendAutoMessageBeforeComplete}
                        style={{
                          width: '100px',
                          marginLeft: '10px',
                          marginRight: '10px',
                          marginTop: '-7px',
                        }}
                        value={String(sendMessageBeforeCompleteInterval)}
                        onChange={(interVal) =>
                          handleChange(
                            true,
                            FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_INTERVAL,
                            +interVal
                          )
                        }
                      />
                      <Text fz='sm'>{t('autoSendMessageBeforeComplete_2')}</Text>
                    </div>
                  }
                />
                <Text ml={rem(32)} c='gray.6' fz='xs' mt={'-12px'}>
                  {t('autoSendMessageBeforeComplete_explain')}
                </Text>
                {sendMessageBeforeComplete && (
                  <>
                    <Container fluid mx={'unset'} px={'unset'}>
                      <TextAreaEditor
                        autosize
                        minRows={3}
                        maxRows={5}
                        disabled={!isManager}
                        value={messageBeforeComplete}
                        onChange={(val) => setMessageBeforeComplete(val)}
                        colorIcon='gray.6'
                        maxLength={200}
                      />
                    </Container>
                    <Group justify='flex-end'>
                      <CopyButton value=''>
                        {({ copied, copy }) => (
                          <Button
                            color='navy.0'
                            size='xs'
                            loading={false}
                            disabled={!isManager || !messageBeforeComplete?.trim()}
                            onClick={() => {
                              if (copied) return;
                              copy();
                              handleChange(
                                messageBeforeComplete as unknown as boolean,
                                FunctionEnum.AUTO_CLOSE_SEND_MES_BEFORE_COMPLETE_MESSGE
                              );
                            }}
                            className={''}
                          >
                            {copied ? t('button.saved') : t('button.save')}
                          </Button>
                        )}
                      </CopyButton>
                    </Group>
                  </>
                )}
              </Flex>
            )}
          </Flex>
        </Flex>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('createNewConversationLabel')}
          </Text>
          <Flex direction={'column'}>
            <Checkbox
              disabled={!isManager}
              checked={createNewConv}
              onChange={(e) => handleChange(e.target.checked, FunctionEnum.CREATE_NEW_CONV)}
              color={'navy.5'}
              id='createNewConversation'
              key={'createNewConversation'}
              label={<Text fz='sm'>{t('createNewConversationOption')}</Text>}
            />
            <Text ml={rem(32)} color='gray.6' fz='xs'>
              {t('createNewConversationText')}
            </Text>
          </Flex>
        </Flex>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('aiSummaryFunction')}
          </Text>
          <Flex direction={'column'}>
            <Button
              fz='sm'
              size='sm'
              c={'decaGrey.5'}
              variant='default'
              color='decaGrey.5'
              disabled={!isManager}
              onClick={() => setAIPromptModalDisplay(true)}
            >
              <IconPencil size={20} stroke={1.3} />
              <Space w={8} />
              <Text fw={400} fz={14}>
                {t('editPrompt')}
              </Text>
            </Button>
          </Flex>
        </Flex>
        <Flex>
          <Text fz='sm' w={'25%'} miw={265} fw={600}>
            {t('maxConversationSettingsTitle')}
          </Text>
          <Flex direction={'column'}>
            <Text c='gray.6' fz={12}>
              {t('maxConversationSettingsDescription')}
            </Text>
            <SelectStyled
              placeholder='1'
              style={{
                width: '100px',
                marginTop: '10px',
                marginRight: '10px',
              }}
              disabled={!isManager}
              allowDeselect={false}
              data={OptionsForMaxConversationSettings}
              value={String(maxActiveConversation)}
              onChange={(interVal) =>
                handleChange(true, FunctionEnum.MAX_CONCURRENT_CONVERSATION_SETTING, +interVal)
              }
            />
          </Flex>
        </Flex>
      </Flex>
      <Modal
        centered
        size='lg'
        radius='md'
        classNames={{
          title: classes.title,
          close: classes.closeButton,
        }}
        closeOnEscape={false}
        withCloseButton={false}
        closeOnClickOutside={false}
        opened={aiPromptModalDisplay}
        onClose={handleCancelEditPrompt}
      >
        <Modal.CloseButton className='' size={'lg'} style={{ right: '-94%' }} />
        <Modal.Title mb={'md'} pl={'md'} className={classes.title}>
          {t('editSummaryPrompts')}
        </Modal.Title>
        <Modal.Body mt={'lg'} pb={0}>
          <Textarea
            autosize
            minRows={10}
            maxRows={10}
            maxLength={300}
            value={promptSummary}
            error={!promptSummary?.trim()?.length}
            onChange={handleCustomPromptEdit}
            placeholder={t('aiSummaryPromptPlaceholder')}
          />
          <div className={classes.aiPromptBottomText}>
            {!promptSummary?.trim()?.length ? (
              <Text ta='left' className={classes.errorText}>
                {t('requiredEntryField')}
              </Text>
            ) : (
              <div />
            )}
            <Text ta='right' c='#7A7A7A'>
              {promptSummary.length}/300{t('characterCount')}
            </Text>
          </div>
          <div className={classes.buttonContainer}>
            <Button
              size='xs'
              radius='md'
              variant='filled'
              className={classes.resetButton}
              onClick={() => handleResetPrompt()}
            >
              {t('reset')}
            </Button>
            <ButtonGroup
              position='flex-end'
              saveText={`${t('button.save')}`}
              cancelText={`${t('button.cancel')}`}
              disabled={!promptSummary?.trim()?.length}
              onCancel={handleCancelEditPrompt}
              onSave={() =>
                handleChange(promptSummary as unknown as boolean, FunctionEnum.AI_PROMPT)
              }
            />
          </div>
        </Modal.Body>
      </Modal>
    </Container>
  );
};
