import {
  ActionIcon,
  Box,
  Button,
  Flex,
  Group,
  Modal,
  Table,
  Tabs,
  Text,
  TextInput,
  Tooltip,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { UseFormReturnType } from '@mantine/form';
import { useElementSize } from '@mantine/hooks';
import { IconTrash, IconX } from '@tabler/icons-react';
import JsonView from '@uiw/react-json-view';
import { githubLightTheme } from '@uiw/react-json-view/githubLight';
import '@uiw/react-textarea-code-editor/dist.css';
import jsonata from 'jsonata';
import dynamic from 'next/dynamic';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { v4 as uuidV4 } from 'uuid';
import { deepCopy, delay } from '../../../../utils/common';
import { SelectStyled } from '../../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import { WebhookFormType } from '../models';
import { useWebhookDetailContext } from './WebhookDetailContext';
import { prettierFormatted } from './constant';

const CodeEditor = dynamic(
  () => import('@uiw/react-textarea-code-editor').then((mod) => mod.default),
  { ssr: false }
);

const useStyle = createStyles((theme) => ({
  modal: {
    '& .mantine-Modal-body': {
      padding: '0',
    },
  },
  navy: {
    color: theme.colors.navy[0],
  },
  red: {
    color: theme.colors.red[5],
  },
  editorStyle: {
    '& .w-tc-editor-text.w-tc-editor-text, .w-tc-editor-preview.w-tc-editor-preview': {
      paddingLeft: 0,
    },
  },
  selectStyle: {
    '& .mantine-Select-item': {
      wordBreak: 'break-word',
    },
  },
  tableStyle: {
    '& > thead > tr > th': {
      textAlign: 'center',
      fontWeight: 700,
    },
  },
  tabStyle: {
    '& .mantine-Tabs-tabsList': {
      borderBottom: 'unset',
    },

    '& .mantine-Tabs-tabsList .mantine-Tabs-tab': {
      marginBottom: 'unset',
      fontWeight: 'bold',
      color: theme.colors.gray[9],
      borderBottom: `0.125rem solid #dee2e6`,
    },

    "& .mantine-Tabs-tabsList .mantine-Tabs-tab[data-active='true']": {
      borderColor: theme.colors.navy[0],
    },
    "& .mantine-Tabs-tabsList .mantine-Tabs-tab:not([data-active='true'])": {
      color: theme.colors.gray[4],
    },
  },
  tooltipStyle: {
    '& .mantine-Tooltip-tooltip': {
      boxShadow: '0 0 2px #a6a6a6',
    },
    '& .mantine-Tooltip-tooltip .mantine-Tooltip-arrow': {
      boxShadow: '1px 1px 1px #a6a6a670',
    },
  },
  customButton: {
    '&[data-disabled]': {
      pointerEvents: 'unset',
    },
  },
}));

async function evaluateJsonata(dataSource: JSON | string, expr: string) {
  let data = dataSource;
  if (typeof dataSource === 'string') {
    try {
      data = JSON.parse(dataSource);
    } catch (e) {
      return { error: true, message: 'Data Source is not a well-formed JSON' };
    }
  }

  if (!expr) {
    return {
      error: true,
      message: 'Please input the transform rule',
    };
  }

  try {
    const expression = jsonata(expr);
    let result = await expression.evaluate(data);
    try {
      result = JSON.parse(
        JSON.stringify(
          result,
          function (key, val) {
            return typeof val !== 'undefined' && val !== null && val.toPrecision
              ? Number(val.toPrecision(13))
              : val && (val._jsonata_lambda === true || val._jsonata_function === true)
                ? '{function:' + (val.signature ? val.signature.definition : '') + '}'
                : typeof val === 'function'
                  ? '<native function>#' + val.length
                  : val;
          },
          2
        )
      );
    } catch (e) {}

    return result;
  } catch (e) {
    return {
      error: true,
      message: e.message,
      position: e?.position,
    };
  }
}

const customTheme = {
  ...githubLightTheme,
  '--w-rjv-quotes-color': '#1D2088',
  '--w-rjv-key-string': '#1D2088',
  '--w-rjv-type-string-color': 'red',
  '--w-rjv-key-number': 'green',
  '--w-rjv-type-boolean-color': 'green',
  '--w-rjv-quotes-string-color': '#1D2088',
};

const jsonataEditorId = 'jsonata_editor';
const textHeight = 'max(100%, 200px)';
const keyCharLength = 20;
const valueCharLength = 128;

type EnvironmentVariable = WebhookFormType['payload']['variables'][0];
type ErrorTableData = Record<string, { signature: string; errors: string }>;

export function CustomTransformModal({
  opened,
  onClose,
  form,
}: {
  opened: boolean;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  form: UseFormReturnType<WebhookFormType, (values: WebhookFormType) => WebhookFormType>;
}) {
  const { classes } = useStyle();
  const { ref: refFlexBox, height: heightFlexBox } = useElementSize();
  const { ref: refTabList, height: heightTabList } = useElementSize();
  const { ref: refGroupToolTip, height: heightGroupToolTip } = useElementSize();

  const {
    t,
    webHookSampleOptions,
    webhookSampleTypeMapPayload,
    webhookSampleTypeMapTransform,
    systemVariables,
  } = useWebhookDetailContext();
  const [review, setReview] = useState<string | any>({});
  const [transformRule, setTransformRule] = useState<string>('');
  const [editorHeight, setEditorHeight] = useState(textHeight);
  const [typeTransform, setTypeTransform] = useState<string>(
    webHookSampleOptions?.[0]?.value || ''
  );
  const [showToolTipBtnAddVar, setShowToolTipBtnAddVar] = useState(false);
  const [environmentVariables, setEnvironmentVariables] = useState<EnvironmentVariable[]>([]);
  const [errorTableData, setErrorTableData] = useState<ErrorTableData>({});

  const formTransformType = useMemo(
    () => form.values.payload.transformPayload.type,
    [form.values.payload.transformPayload.type]
  );

  const formTransformRule = useMemo(
    () => form.values.payload.transformPayload.transformRule,
    [form.values.payload.transformPayload.transformRule]
  );

  const variables = useMemo(() => form.values.payload.variables, [form.values.payload.variables]);

  const environmentVariablesObj = useMemo(() => {
    const environmentVariablesObj = {};
    systemVariables?.length > 0 &&
      systemVariables.forEach((item) => {
        environmentVariablesObj[item.name] = t(
          'modal.transformer.variable.variable_value_runtime_content'
        );
      });
    return environmentVariablesObj;
  }, [systemVariables, t]);

  const sampleJsonView = useMemo(() => {
    const sample = deepCopy(webhookSampleTypeMapPayload?.[typeTransform] || {});
    const envObj = deepCopy(environmentVariablesObj);
    environmentVariables
      .filter((item) => item.name.trim().length > 0)
      .forEach((item) => {
        envObj[`${item.name.trim()}`] = item.value;
      });
    sample['environmentVariables'] = envObj;
    return sample;
  }, [environmentVariables, environmentVariablesObj, typeTransform, webhookSampleTypeMapPayload]);

  const previewData = useCallback(async () => {
    const dataSource = sampleJsonView;
    const expression = transformRule || '';
    let result = await evaluateJsonata(dataSource as JSON, expression);
    if (result === undefined) result = {};
    setReview(result);
  }, [sampleJsonView, transformRule]);

  const handleTypeTransform = useCallback((val: string) => {
    setTypeTransform(val);
  }, []);

  const handleUpdateHeight = useCallback(() => {
    if (!document) return;
    const jsonataEditor = document.getElementById(jsonataEditorId);
    if (!jsonataEditor) return;
    const divNext = jsonataEditor.nextElementSibling as HTMLDivElement;
    if (!divNext) return;
    const editorHeight = jsonataEditor.offsetHeight;
    const divNextHeight = divNext.offsetHeight;

    if (editorHeight <= divNextHeight) {
      jsonataEditor.style.height = `max(${divNextHeight}px, 200px)`;
      setEditorHeight(`max(${divNextHeight}px, 200px)`);
    } else {
      setEditorHeight(textHeight);
      jsonataEditor.style.height !== textHeight && (jsonataEditor.style.height = textHeight);
    }
  }, []);

  const handleTransformRule = useCallback(({ currentTarget }: ChangeEvent<HTMLTextAreaElement>) => {
    let value = currentTarget.value;
    setTransformRule(value);
  }, []);

  const handleChangeEnvironmentVariable = useCallback(
    (field: 'name' | 'value', id: string, val: string) => {
      const foundIndex = environmentVariables.findIndex((item) => item?.id === id);
      if (foundIndex == -1) return;
      const maxLengthAccepted = field === 'name' ? keyCharLength : valueCharLength;
      setEnvironmentVariables((pre) => {
        pre[foundIndex][field] = val.slice(0, maxLengthAccepted);
        return [...pre];
      });
    },
    [environmentVariables]
  );

  const handleCleanEnvironmentData = useCallback(
    (id: string, field: 'name' | 'value') => {
      const foundIndex = environmentVariables.findIndex((item) => item?.id === id);
      if (foundIndex == -1) return;
      setEnvironmentVariables((pre) => {
        pre[foundIndex][field] = pre[foundIndex][field].trim();
        return [...pre];
      });
    },
    [environmentVariables]
  );

  const removeRowEnvironmentVariable = useCallback(
    (idRow: string) => {
      const found = environmentVariables.find((item) => item?.id === idRow);
      if (!found) return;
      setEnvironmentVariables((pre) => {
        return pre.filter((item) => item.id !== idRow);
      });
    },
    [environmentVariables]
  );

  const handleAddNewRowEnvironmentVar = useCallback(() => {
    if (environmentVariables.length >= 9) return;
    setEnvironmentVariables((pre) => {
      pre.push({
        id: uuidV4(),
        name: '',
        value: '',
        type: 'custom',
      });
      return [...pre];
    });
  }, [environmentVariables.length]);

  const environmentVariablesValidateData = useCallback(() => {
    const hashMapVariables: ErrorTableData = {};
    const systemVariablesName = new Set([...systemVariables.map((item) => item.name)]);
    environmentVariables.forEach((item) => {
      const tempHash = { signature: item.name, errors: '' };
      if (!item.name) {
        tempHash.errors = tempHash.errors.concat('name#error_enter_required_field');
      }

      if (!item.value) {
        tempHash.errors = tempHash.errors.concat('value#error_enter_required_field');
      }

      if (systemVariablesName.has(item.name)) {
        tempHash.errors = tempHash.errors.concat('name#error_same_variable_name');
      }

      if (
        !!item.name &&
        Object.values(hashMapVariables).find((hashItem) => hashItem.signature === item.name)
      ) {
        tempHash.errors = tempHash.errors.concat('name#error_same_variable_name');
      }
      hashMapVariables[item.id] = tempHash;
    });
    return {
      hashMapVariables,
      error: Object.values(hashMapVariables).some((item) => item.errors?.length > 0),
    };
  }, [systemVariables, environmentVariables]);

  /***
   * Close modal would save data or not whether error when evaluate preview existed.
   */
  const handleSaveData = useCallback(async () => {
    const envValidateData = environmentVariablesValidateData();
    const isError =
      (typeof review === 'object' && Object.keys(review).includes('error')) ||
      envValidateData.error;
    const hasPreTimesError = Object.values(errorTableData).some((item) => item.errors?.length > 0);
    setErrorTableData(deepCopy(envValidateData.hashMapVariables));
    if (isError) {
      return;
    }

    if (
      (typeof review === 'object' && !Object.keys(review).includes('error')) ||
      typeof review === 'string'
    ) {
      form.setFieldValue('payload.transformPayload.type', typeTransform);
      form.setFieldValue('payload.transformPayload.transformRule', transformRule);
    }

    if (environmentVariables.length) {
      form.setFieldValue('payload.variables', environmentVariables);
    }
    // This help delays a bit to let user knows it is working correctly.
    hasPreTimesError && (await delay(500));
    onClose();
  }, [
    form,
    review,
    onClose,
    transformRule,
    typeTransform,
    errorTableData,
    environmentVariables,
    environmentVariablesValidateData,
  ]);

  useEffect(() => {
    opened &&
      setTimeout(() => {
        handleUpdateHeight();
      }, 1000);
  }, [handleUpdateHeight, opened]);

  useEffect(() => {
    const timeout = setTimeout(async () => {
      await previewData();
    }, 500);
    return () => clearTimeout(timeout);
  }, [previewData]);

  useEffect(() => {
    if (!opened || !webHookSampleOptions?.length) return;
    if (webHookSampleOptions.find((item) => item.value === formTransformType)) {
      setTypeTransform(formTransformType);
    } else {
      setTypeTransform(webHookSampleOptions?.[0]?.value);
    }
  }, [opened, formTransformType, webHookSampleOptions]);

  useEffect(() => {
    if (!opened || !webHookSampleOptions?.length) return;
    if (!formTransformRule) {
      try {
        setTransformRule(
          prettierFormatted(
            webhookSampleTypeMapTransform?.[webHookSampleOptions?.[0].value] as unknown as string
          )
        );
      } catch (e) {}
    }
    if (formTransformRule) {
      setTransformRule(formTransformRule);
    }
  }, [opened, webHookSampleOptions, webhookSampleTypeMapTransform, formTransformRule]);

  useEffect(() => {
    if (!typeTransform) return;
    if (!formTransformRule) {
      try {
        // setTransformRule(prettierFormatted(webhookSampleTypeMapTransform[typeTransform]));
        setTransformRule(webhookSampleTypeMapTransform[typeTransform]);
      } catch (e) {}
    }
  }, [formTransformRule, typeTransform, webhookSampleTypeMapTransform]);

  useEffect(() => {
    if (!opened || !variables?.length) return;
    setEnvironmentVariables(variables.map((item) => ({ ...item, id: uuidV4() })));
  }, [opened, variables]);

  return (
    <Modal
      opened={opened}
      withCloseButton={false}
      onClose={() => null}
      size='70%'
      className={classes.modal}
    >
      <Box sx={{ display: 'flex', height: `calc(100vh - 100px)`, aspectRatio: 'unset' }}>
        <Box
          sx={{
            flex: 1,
            padding: '1rem',
            paddingBottom: '0rem',
            display: 'flex',
            flexDirection: 'column',
          }}
          w={'50%'}
        >
          <Group sx={{ flexWrap: 'nowrap' }} justify='space-between'>
            <Text fw={500} truncate miw={200}>
              {t('webhook.field.transform.modal.title')}
            </Text>
            <SelectStyled
              className={classes.selectStyle}
              data={webHookSampleOptions}
              defaultValue={webHookSampleOptions?.[0]?.value || ''}
              maw={'100%'}
              value={typeTransform}
              onChange={handleTypeTransform}
            />
          </Group>
          <Flex
            mt={'sm'}
            mr={'-1rem'}
            pr={'1rem'}
            direction='column'
            sx={{ flexGrow: 1, maxHeight: '100%', overflowY: 'auto' }}
          >
            <JsonView
              value={sampleJsonView}
              displayDataTypes={false}
              displayObjectSize={false}
              enableClipboard={false}
              style={customTheme}
            />
          </Flex>
        </Box>

        <Box sx={{ flex: 1, padding: '1rem', borderLeft: '1px solid #e9ecef' }} w={'50%'} pb={0}>
          <Flex
            h={'50%'}
            direction={'column'}
            sx={{ position: 'relative' }}
            ref={refFlexBox}
            mx={'-1rem'}
          >
            <div style={{ position: 'absolute', top: '0', right: '1rem', zIndex: 100 }}>
              <ActionIcon variant='transparent' color='gray' onClick={handleSaveData}>
                <IconX />
              </ActionIcon>
            </div>
            <Tabs
              defaultValue='first'
              sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1 }}
              color='navy.0'
              className={classes.tabStyle}
            >
              <Tabs.List ref={refTabList} h={45}>
                <Tabs.Tab value='first' maw={160} miw={130}>
                  {t('webhook.field.transform.modal.edit')}
                </Tabs.Tab>
                <Tabs.Tab value='second' maw={160} miw={130}>
                  {t('webhook.field.transform.modal.environment_variable')}
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value='first' sx={{ flexGrow: 1, overflowY: 'auto' }}>
                <Flex
                  pb={'0rem'}
                  direction='column'
                  sx={{
                    flexGrow: 1,
                    maxHeight: `${heightFlexBox - heightTabList}px`,
                    overflowY: 'auto',
                  }}
                  className={classes.editorStyle}
                >
                  <CodeEditor
                    id={jsonataEditorId}
                    value={transformRule}
                    onChange={handleTransformRule}
                    language='json'
                    onKeyDown={handleUpdateHeight}
                    onPaste={handleUpdateHeight}
                    onClick={handleUpdateHeight}
                    placeholder={t('webhook.field.transform.modal.edit.placeholder')}
                    padding={15}
                    data-color-mode='light'
                    style={{
                      height: editorHeight,
                      fontSize: '0.8rem',
                      backgroundColor: 'white',
                      overflowY: 'auto',
                      fontFamily:
                        'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                    }}
                  />
                </Flex>
              </Tabs.Panel>

              <Tabs.Panel value='second' pt='xs'>
                <Flex
                  direction='column'
                  mah={300}
                  px={'1rem'}
                  sx={{
                    overflowY: 'auto',
                    maxHeight: `${heightFlexBox - heightTabList - heightGroupToolTip - 35}px`,
                  }}
                >
                  <Table
                    highlightOnHover
                    withTableBorder
                    withColumnBorders
                    sx={{ textAlign: 'center' }}
                    className={classes.tableStyle}
                  >
                    <Table.Thead>
                      <Table.Tr
                        style={{
                          backgroundColor: '#F9F9FA',
                          height: '40px',
                        }}
                      >
                        <Table.Th style={{ width: '45%' }}>
                          {t('modal.transformer.variable.variable_key')}
                          <span className={classes.red}> * </span>
                        </Table.Th>
                        <Table.Th style={{ width: '45%' }}>
                          {t('modal.transformer.variable.variable_value')}
                          <span className={classes.red}> * </span>
                        </Table.Th>
                        <Table.Th>&nbsp;</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {systemVariables.map((item, index) => {
                        return (
                          <Table.Tr
                            key={item?.name + index}
                            style={{
                              backgroundColor: '#F9F9FA',
                              height: '36px',
                            }}
                          >
                            <Table.Td>{item.name}</Table.Td>
                            <Table.Td>
                              {t('modal.transformer.variable.variable_value_runtime_content')}
                            </Table.Td>
                            <Table.Td style={{ width: '49px' }}>&nbsp;</Table.Td>
                          </Table.Tr>
                        );
                      })}

                      {environmentVariables.map((environmentVariable) => {
                        return (
                          <Table.Tr key={environmentVariable?.id}>
                            <Table.Td
                              style={{
                                padding: '0 0',
                                height: '36px',
                                outline: errorTableData?.[
                                  environmentVariable?.id
                                ]?.errors?.includes('name')
                                  ? '1px solid red'
                                  : 'none',
                              }}
                            >
                              <TextInput
                                placeholder={t('modal.transformer.variable.placeholder')}
                                variant='unstyled'
                                sx={{
                                  '& .mantine-TextInput-input': {
                                    textAlign: 'center',
                                  },
                                }}
                                value={environmentVariable.name}
                                onChange={(e) =>
                                  handleChangeEnvironmentVariable(
                                    'name',
                                    environmentVariable?.id,
                                    e.target.value
                                  )
                                }
                                onBlur={() =>
                                  handleCleanEnvironmentData(environmentVariable.id, 'name')
                                }
                              />
                            </Table.Td>
                            <Table.Td
                              style={{
                                padding: '0 0',
                                height: '36px',
                                outline: errorTableData?.[
                                  environmentVariable?.id
                                ]?.errors?.includes('value')
                                  ? '1px solid red'
                                  : 'none',
                              }}
                            >
                              <TextInput
                                placeholder={t('modal.transformer.variable.placeholder')}
                                variant='unstyled'
                                sx={{
                                  '& .mantine-TextInput-input': {
                                    textAlign: 'center',
                                  },
                                }}
                                value={environmentVariable.value}
                                onChange={(e) =>
                                  handleChangeEnvironmentVariable(
                                    'value',
                                    environmentVariable?.id,
                                    e.target.value
                                  )
                                }
                                onBlur={() =>
                                  handleCleanEnvironmentData(environmentVariable.id, 'value')
                                }
                              />
                            </Table.Td>
                            <Table.Td
                              style={{
                                textAlign: 'center',
                                verticalAlign: 'middle',
                                width: '49px',
                                height: '36px',
                              }}
                            >
                              <Flex direction='row' justify='center'>
                                <ActionIcon
                                  styles={(theme) => ({
                                    root: {
                                      '&:hover': {
                                        color: theme.colors.navy[0],
                                      },
                                    },
                                  })}
                                  variant='transparent'
                                  className={classes.navy}
                                  onClick={() =>
                                    removeRowEnvironmentVariable(environmentVariable?.id)
                                  }
                                >
                                  <IconTrash />
                                </ActionIcon>
                              </Flex>
                            </Table.Td>
                          </Table.Tr>
                        );
                      })}
                    </Table.Tbody>
                  </Table>
                </Flex>
                <Group
                  mt='md'
                  mx='1rem'
                  className={`${classes.tooltipStyle} tooltip`}
                  ref={refGroupToolTip}
                  justify='space-between'
                >
                  <Flex direction='column'>
                    {Object.values(errorTableData).some((item) =>
                      item.errors.includes('error_same_variable_name')
                    ) && (
                      <Text size={rem(12)} color='red'>
                        {t('modal.transformer.variable.error_same_variable_name')}
                      </Text>
                    )}
                    {Object.values(errorTableData).some((item) =>
                      item.errors.includes('error_enter_required_field')
                    ) && (
                      <Text size={rem(12)} color='red'>
                        {t('modal.transformer.variable.error_enter_required_field')}
                      </Text>
                    )}
                  </Flex>
                  <Tooltip
                    color='white'
                    label={
                      <Text fz={14} sx={{ textAlign: 'center', whiteSpace: 'pre' }} c='gray.7'>
                        {t('modal.transformer.variable.button_add_row.error_tooltip')}
                      </Text>
                    }
                    styles={{
                      tooltip: {
                        boxShadow: '0 0 2px #a6a6a6',
                      },
                      arrow: {
                        boxShadow: '1px 1px 1px #a6a6a670',
                      },
                    }}
                    withArrow
                    arrowSize={8}
                    opened={showToolTipBtnAddVar}
                  >
                    <Button
                      variant='filled'
                      color='navy.0'
                      size='xs'
                      onMouseEnter={() =>
                        environmentVariables?.length >= 9 && setShowToolTipBtnAddVar(true)
                      }
                      onMouseOut={() =>
                        environmentVariables?.length >= 9 && setShowToolTipBtnAddVar(false)
                      }
                      onMouseOver={() =>
                        environmentVariables?.length >= 9 && setShowToolTipBtnAddVar(true)
                      }
                      className={classes.customButton}
                      onClick={handleAddNewRowEnvironmentVar}
                      disabled={environmentVariables?.length >= 9}
                    >
                      {t('modal.transformer.variable.button_add_row')}
                    </Button>
                  </Tooltip>
                </Group>
              </Tabs.Panel>
            </Tabs>
          </Flex>
          <PreviewBox review={review} />
        </Box>
      </Box>
    </Modal>
  );
}

const PreviewBox = ({ review }: { review: any }) => {
  const { t } = useWebhookDetailContext();
  return (
    <Box
      sx={{
        borderTop: '1px solid #e9ecef',
        paddingTop: '1rem',
        marginLeft: '-1rem',
        marginRight: '-1rem',
        display: 'flex',
        flexDirection: 'column',
        maxHeight: '50%',
        height: '50%',
      }}
      px={'1rem'}
    >
      <Text color='dimmed' fw={700}>
        {t('webhook.field.transform.modal.preview')}
      </Text>
      <Flex
        mt={'sm'}
        mr={'-1rem'}
        pr={'1rem'}
        direction='column'
        sx={{
          flexGrow: 1,
          maxHeight: '100%',
          height: '100%',
          overflowY: 'auto',
        }}
      >
        {typeof review === 'string' ? (
          <Text
            size={rem(12)}
            color='gray.9'
            sx={{ whiteSpace: 'pre', wordBreak: 'break-word', fontFamily: 'monospace' }}
          >
            {review}
          </Text>
        ) : (
          <JsonView
            value={review}
            displayDataTypes={false}
            enableClipboard={true}
            style={customTheme}
            shortenTextAfterLength={0}
          ></JsonView>
        )}
      </Flex>
    </Box>
  );
};
