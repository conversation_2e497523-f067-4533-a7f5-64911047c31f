import { useRouter } from 'next/router';
import { useEffect } from 'react';

export const useWarningOnExit = (
  shouldWarn: boolean,
  warningText: string,
  clearWarn?: () => void
) => {
  const router = useRouter();
  useEffect(() => {
    const routeChangeStart = (url: string) => {
      if (router?.asPath !== url && shouldWarn) {
        const result = window.confirm(warningText);
        if (result) {
          return;
        }
        clearWarn?.();
        router?.events.emit('routeChangeError');
        throw 'Abort route change. Please ignore this error.';
      }
    };
    const beforeUnload = (e: BeforeUnloadEvent) => {
      if (shouldWarn) {
        const event = e || window.event;
        event.returnValue = warningText;
        return warningText;
      }
      return null;
    };
    router?.events.on('routeChangeStart', routeChangeStart);
    window.addEventListener('beforeunload', beforeUnload);
    return () => {
      router?.events.off('routeChangeStart', routeChangeStart);
      window.removeEventListener('beforeunload', beforeUnload);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [warningText, shouldWarn]);
};
