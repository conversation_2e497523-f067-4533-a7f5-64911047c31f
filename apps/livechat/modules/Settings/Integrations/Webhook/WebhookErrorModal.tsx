import { ActionIcon, Button, Flex, Group, Modal, Text } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import Link from 'next/link';
import React, { useMemo } from 'react';
import { useAppContext } from '../../../appContext';
import { useWebhookDetailContext } from './WebhookDetailContext';

const WebhookErrorModal: React.FC = () => {
  const { lang } = useAppContext();
  const { t, visible, toggle, automationIdInUse } = useWebhookDetailContext();
  const linkToAutomationDetailPage = useMemo(() => {
    return !!automationIdInUse ? `/automation/edit/${automationIdInUse}` : '';
  }, [automationIdInUse]);
  return (
    <Modal opened={visible} withCloseButton={false} onClose={() => null} centered>
      <Group justify='flex-end'>
        <ActionIcon variant='subtle' onClick={toggle}>
          <IconX />
        </ActionIcon>
      </Group>

      <Flex mt={'md'} direction={'column'} align={'center'} justify={'center'}>
        <Text
          fw={500}
          ta='center'
          sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', lineBreak: 'auto' }}
        >
          {t('webook_error_modal_description_first')}
        </Text>
        <Text fw={500} ta='center'>
          {lang === 'ja' ? (
            <>
              <Link
                href={linkToAutomationDetailPage}
                style={{ color: '#1084ff', textDecoration: 'underline' }}
              >
                {t('webook_error_modal_description_link')}
              </Link>
              {t('webook_error_modal_description_last')}
            </>
          ) : (
            <>
              {t('webook_error_modal_description_last')}
              <Link
                href={linkToAutomationDetailPage}
                style={{ color: '#1084ff', textDecoration: 'underline' }}
              >
                {t('webook_error_modal_description_link')}
              </Link>
            </>
          )}
        </Text>
        <Button variant='default' onClick={toggle} sx={{ margin: '1.5rem 0' }} fw={400}>
          {t('webook_error_modal_btn')}
        </Button>
      </Flex>
    </Modal>
  );
};

export default WebhookErrorModal;
