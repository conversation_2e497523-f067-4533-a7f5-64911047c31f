import { Checkbox, Flex, PasswordInput, Text } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { useCallback } from 'react';
import {
  SelectStyled,
  TextInputStyled,
} from '../../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import { AUTH0_TYPE, WebhookFormType } from '../models';
import { useWebhookDetailContext } from './WebhookDetailContext';
import { prefixSchema, removeWhiteSpaceChar } from './constant';
import { useWebhookStyle } from './useWebhookStyle';

export function Auth0Form({
  form,
}: {
  // eslint-disable-next-line no-unused-vars
  form: UseFormReturnType<WebhookFormType, (values: WebhookFormType) => WebhookFormType>;
}) {
  const { classes } = useWebhookStyle();
  const { t } = useWebhookDetailContext();

  const formPropsForAccessTokenUrlInput = useCallback(() => {
    const props = form.getInputProps('credentials.config.tokenHost');
    const formOnChangeFunc = props.onChange;

    const customOnChange = ({ currentTarget }: React.ChangeEvent<HTMLInputElement>) => {
      let value = currentTarget.value;
      value = prefixSchema(value);
      formOnChangeFunc(value);
    };
    return {
      ...props,
      onChange: customOnChange,
    };
  }, [form]);

  const getRemoveWhiteSpaceCharInputProps = useCallback(
    (
      path: Parameters<
        UseFormReturnType<
          WebhookFormType,
          // eslint-disable-next-line no-unused-vars
          (values: WebhookFormType) => WebhookFormType
        >['getInputProps']
      >['0']
    ) => {
      const props = form.getInputProps(path);
      const formOnChangeFunc = props.onChange;
      const customOnChange = ({
        currentTarget,
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        let value =
          currentTarget.type === 'checkbox'
            ? (currentTarget as EventTarget & HTMLInputElement).checked
            : currentTarget.value;
        value = removeWhiteSpaceChar(value);
        formOnChangeFunc(value);
      };
      return { ...props, onChange: customOnChange };
    },
    [form]
  );

  return (
    <>
      <Flex mt={'lg'}>
        <Text className={classes.label}>{t('webhook.field.auth0.checkbox.label')}</Text>
        <Flex>
          <Checkbox
            {...form.getInputProps('credentials.enable', { type: 'checkbox' })}
            color={'navy.5'}
            defaultChecked={false}
            id='auth0_checkbox_form'
            key={'auth0_checkbox_form'}
            label={<Text fz={14}>{t('webhook.field.auth0.checkbox.description')}</Text>}
          />
        </Flex>
      </Flex>
      {!!form.values?.credentials?.enable && (
        <>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.grant_type.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <SelectStyled
                data={[
                  {
                    value: AUTH0_TYPE.PASSWORD,
                    label: t('webhook.field.auth0.grant_type.option_password'),
                  },
                ]}
                style={{ width: '100%' }}
                defaultValue={AUTH0_TYPE.PASSWORD}
                required
                {...form.getInputProps('credentials.config.grantType')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.client_id.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <TextInputStyled
                required
                placeholder={t('webhook.field.auth0.client_id.placeholder')}
                style={{ width: '100%' }}
                {...getRemoveWhiteSpaceCharInputProps('credentials.config.clientId')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.client_secret.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <PasswordInput
                style={{ width: '100%' }}
                required
                placeholder={t('webhook.field.auth0.client_secret.placeholder')}
                {...getRemoveWhiteSpaceCharInputProps('credentials.config.clientSecret')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.user_id.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <TextInputStyled
                required
                placeholder={t('webhook.field.auth0.user_id.placeholder')}
                style={{ width: '100%' }}
                {...getRemoveWhiteSpaceCharInputProps('credentials.config.username')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.password.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <PasswordInput
                required
                placeholder={t('webhook.field.auth0.password.placeholder')}
                style={{ width: '100%' }}
                {...getRemoveWhiteSpaceCharInputProps('credentials.config.password')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.scope.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <TextInputStyled
                required
                placeholder={t('webhook.field.auth0.scope.placeholder')}
                style={{ width: '100%' }}
                {...form.getInputProps('credentials.config.scope')}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>{t('webhook.field.auth0.access_token_url.label')}</Text>
            <Flex sx={{ flexGrow: 1 }}>
              <TextInputStyled
                required
                placeholder={t('webhook.field.auth0.access_token_url.placeholder')}
                style={{ width: '100%' }}
                {...formPropsForAccessTokenUrlInput()}
              />
            </Flex>
          </Flex>
          <Flex mt={'lg'}>
            <Text className={classes.label}>
              {t('webhook.field.auth0.access_token_path.label')}
            </Text>
            <Flex sx={{ flexGrow: 1 }}>
              <TextInputStyled
                required
                placeholder={t('webhook.field.auth0.access_token_path.placeholder')}
                style={{ width: '100%' }}
                {...form.getInputProps('credentials.config.tokenPath')}
              />
            </Flex>
          </Flex>
        </>
      )}
    </>
  );
}
