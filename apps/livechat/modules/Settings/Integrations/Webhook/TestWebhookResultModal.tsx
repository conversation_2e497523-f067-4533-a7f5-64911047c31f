import { Button, Flex, Group, Modal, Text, rem } from '@mantine/core';
import { IconAlertCircle, IconCircleCheck } from '@tabler/icons-react';
import { useWebhookDetailContext } from './WebhookDetailContext';

export function TestWebhookResultModal({
  result,
  opened,
  onClose,
}: {
  result?: 'success' | 'failed';
  opened: boolean;
  onClose: () => void;
}) {
  const { t } = useWebhookDetailContext();
  return (
    <Modal opened={opened} withCloseButton={false} onClose={() => null} centered>
      <Flex mt={'md'} direction={'column'} align={'center'} justify={'center'}>
        <Group justify='center' gap={8}>
          {result === 'success' ? (
            <IconCircleCheck color='#58C11E' />
          ) : (
            <IconAlertCircle color='#F93549' />
          )}
          <Text
            fw={700}
            size={rem(18)}
            ta='center'
            sx={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              lineBreak: 'auto',
              color: result === 'success' ? '#58C11E' : '#F93549',
            }}
          >
            {result === 'success' ? t('success_label') : t('error_label')}
          </Text>
        </Group>

        <Text fw={500} ta='center' size={rem(16)} color='#333333' mt={20}>
          {result === 'failed' ? t('error_description') : ''}
        </Text>
        <Button
          variant='filled'
          onClick={onClose}
          sx={{ margin: '1.5rem 0', width: '248px' }}
          fw={400}
          color='navy.0'
        >
          OK
        </Button>
      </Flex>
    </Modal>
  );
}
