import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useVisibilityControl from '../../../../hooks/useVisibilityControl';
import ApiService from '../../../../services/api';
import { useExternalToolsContext } from '../../../externalToolsContext';
import { IPayloadCreateToolIntegration, WebhookIntegrationConfigType } from '../models';
import { useWebhookData } from './useWebhookData';
import { useWebhookSampleData } from './useWebhookSampleData';

export function useWebhookDetail(webhookId: string | null | undefined) {
  const router = useRouter();
  const { reload: reloadIntegratedList } = useExternalToolsContext();
  const { t } = useTranslate('workspace');
  const { visible, toggle } = useVisibilityControl();
  const isEditWebhook = useMemo(() => !!webhookId, [webhookId]);
  const errorRef = useRef<{ id: string }>(null);
  const [isAnyLoadScreen, setIsAnyLoadingScreen] = useState(false);
  const {
    webhookData,
    integration,
    saveWebhookData,
    isLoading: isLoadingGetData,
  } = useWebhookData(webhookId);
  const {
    webhookSamples,
    systemVariables,
    webHookSampleOptions,
    webhookSampleTypeMapPayload,
    webhookSampleTypeMapTransform,
  } = useWebhookSampleData();

  const sendTestWebhookData = useCallback(async (value?: WebhookIntegrationConfigType) => {
    if (!value) return;
    const payload: IPayloadCreateToolIntegration = {
      name: value.name,
      integrationType: value.integrationType,
      config: { ...value },
    };
    try {
      const res = await ApiService.sendWebhookTestData(payload);
      return res;
    } catch (e) {
      return null;
    }
  }, []);

  const deleteWebhookIntegration = useCallback(async () => {
    if (!integration) return;
    setIsAnyLoadingScreen(true);
    try {
      const res = await ApiService.deleteToolsIntegration(integration?.id);
      if (res?.data) {
        errorRef.current = { id: res.data?.id };
        toggle();
      } else {
        reloadIntegratedList();
        router.push(`/settings/integrations`);
      }
      setIsAnyLoadingScreen(false);
    } catch (e) {
    } finally {
      setIsAnyLoadingScreen(false);
    }
  }, [integration, toggle, router, reloadIntegratedList]);

  useEffect(() => {
    setIsAnyLoadingScreen(isLoadingGetData);
  }, [isLoadingGetData]);

  return useMemo(
    () => ({
      t,
      router,
      toggle,
      visible,
      webhookData,
      isEditWebhook,
      saveWebhookData,
      isLoadingGetData,
      sendTestWebhookData,
      deleteWebhookIntegration,
      reloadIntegratedList,
      isAnyLoadScreen,
      webhookSamples,
      systemVariables,
      webHookSampleOptions,
      webhookSampleTypeMapPayload,
      webhookSampleTypeMapTransform,
      automationIdInUse: errorRef.current?.id || null,
    }),
    [
      t,
      router,
      toggle,
      visible,
      webhookData,
      isEditWebhook,
      webhookSamples,
      systemVariables,
      saveWebhookData,
      isAnyLoadScreen,
      isLoadingGetData,
      sendTestWebhookData,
      reloadIntegratedList,
      deleteWebhookIntegration,
      webHookSampleOptions,
      webhookSampleTypeMapPayload,
      webhookSampleTypeMapTransform,
    ]
  );
}
