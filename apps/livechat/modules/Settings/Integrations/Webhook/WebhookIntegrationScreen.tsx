import { Center, Container, Flex, Group, Loader, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowLeft } from '@tabler/icons-react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback } from 'react';
import { useWebhookDetailContext } from './WebhookDetailContext';
import WebhookErrorModal from './WebhookErrorModal';
import { WebhookIntegrationForm } from './WebhookIntegrationForm';

const useStyle = createStyles((theme) => ({
  navy: {
    color: theme.colors.navy[0],
  },
}));

export function WebhookIntegrationScreen() {
  const { push } = useRouter();
  const handleOnSave = useCallback(
    (webhookId?: string) => {
      if (webhookId) {
        push(`/settings/integrations/webhook/edit/${webhookId}`);
      }
    },
    [push]
  );
  return (
    <Container pb={50} sx={{ position: 'relative', height: '100%', maxHeight: '100%' }}>
      <LoadingIndicator />
      <BackToIntegrationList />
      <TitleDescription />
      <WebhookIntegrationForm onSave={handleOnSave} />
      <WebhookErrorModal />
    </Container>
  );
}

function LoadingIndicator() {
  const { isAnyLoadScreen } = useWebhookDetailContext();
  return (
    <Flex
      style={{
        display: isAnyLoadScreen ? 'flex' : 'none',
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        zIndex: 100,
      }}
      align={'center'}
      justify={'center'}
      sx={(theme) => ({
        flexGrow: 1,
        backgroundColor: theme.colors.gray[3],
        opacity: 0.5,
      })}
    >
      <Center>
        <Loader color='navy.0' />
      </Center>
    </Flex>
  );
}

function BackToIntegrationList() {
  const { t } = useWebhookDetailContext();
  const { classes } = useStyle();
  return (
    <Group mb='md' pt={'md'}>
      <Link href={'/settings/integrations'} style={{ textDecoration: 'none' }} prefetch>
        <Group>
          <IconArrowLeft className={classes.navy} />
          <Text fw={400} size={rem(14)} color='navy.0'>
            {t('webhook.back_link_title')}
          </Text>
        </Group>
      </Link>
    </Group>
  );
}

function TitleDescription() {
  const { t } = useWebhookDetailContext();
  return (
    <>
      <Group justify='flex-start' align='flex-end'>
        <Title order={3} mt={'lg'} fw={700}>
          {t('webhook.page_title')}
        </Title>
      </Group>

      <Text mt={'sm'} size={'14px'} color={'gray.7'}>
        {t('webhook.page_description')}
      </Text>
    </>
  );
}
