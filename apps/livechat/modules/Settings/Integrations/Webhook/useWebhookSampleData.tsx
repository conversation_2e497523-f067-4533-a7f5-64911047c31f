import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import ApiService from '../../../../services/api';
import { VariableType, WebTransformConfigs, WebhookSampleType } from '../models';

export function useWebhookSampleData() {
  const [webhookSamples, setWebhookSamples] = useState<WebhookSampleType[]>([]);
  const [systemVariables, setSystemVariables] = useState<VariableType[]>([]);
  const { t } = useTranslate('workspace');
  const {
    data = null,
    isLoading,
    error,
    mutate: reload,
  } = useSWR<WebTransformConfigs>(
    `get-integration-webhook-sample-data`,
    () => ApiService.getWebhookSamples(),
    {
      revalidateOnMount: true,
    }
  );

  const webHookSampleOptions = useMemo(() => {
    return webhookSamples?.length
      ? webhookSamples.map((item) => ({ value: item.type, label: t(item.type) }))
      : [];
  }, [webhookSamples, t]);

  const webhookSampleTypeMapPayload = useMemo(() => {
    const map: Record<WebhookSampleType['type'], WebhookSampleType['payload']> = {};
    if (webhookSamples.length) {
      webhookSamples.forEach((item) => {
        map[item?.type] = item?.payload;
      });
    }
    return map;
  }, [webhookSamples]);

  const webhookSampleTypeMapTransform = useMemo(() => {
    const map: Record<WebhookSampleType['type'], string> = {};
    if (webhookSamples.length) {
      const a = '  ';
      webhookSamples.forEach((item) => {
        if (typeof item?.transform === 'string') {
          map[item?.type] = item?.transform || '';
        } else {
          map[item?.type] =
            `{\n${a}` +
            Object.entries(item.transform)
              .map(([key, val]) => `"${key}": ${val}`)
              .join(`,\n${a}`) +
            `\n}`;
        }
      });
    }
    return map;
  }, [webhookSamples]);

  useEffect(() => {
    if (data) {
      setSystemVariables(data?.systemVariables || []);
      setWebhookSamples(data?.samplePayload || []);
    }
  }, [data]);

  return useMemo(
    () => ({
      error,
      reload,
      isLoading,
      webhookSamples,
      systemVariables,
      webHookSampleOptions,
      webhookSampleTypeMapPayload,
      webhookSampleTypeMapTransform,
    }),
    [
      error,
      reload,
      isLoading,
      webhookSamples,
      systemVariables,
      webHookSampleOptions,
      webhookSampleTypeMapPayload,
      webhookSampleTypeMapTransform,
    ]
  );
}
