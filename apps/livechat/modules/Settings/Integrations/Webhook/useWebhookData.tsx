import { useCallback, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import ApiService from '../../../../services/api';
import { useExternalToolsContext } from '../../../externalToolsContext';
import {
  IPayloadCreateToolIntegration,
  IWebhookIntegration,
  PAYLOAD_TRANSFORM,
  PAYLOAD_TYPE,
  WebhookFormType,
  WebhookIntegrationConfigType,
} from '../models';
import { initWebhookConfig, prettierFormattedStringOfObject } from './constant';

export const useWebhookData = (webhookId: string | undefined) => {
  const [integration, setIntegration] = useState<IWebhookIntegration>(null);
  const [webhookData, setWebhookData] = useState<WebhookFormType>(initWebhookConfig);
  const { handleCreateToolsIntegration, updateToolIntegration } = useExternalToolsContext();
  const {
    data = null,
    isLoading,
    error,
    mutate: reload,
  } = useSWR<IWebhookIntegration>(
    webhookId ? `get-integration-webhook-detail-id=${webhookId}` : null,
    () => ApiService.getWebhookIntegration(webhookId),
    {
      revalidateOnMount: true,
      revalidateIfStale: true,
    }
  );

  const saveWebhookData = useCallback(
    async (value: WebhookIntegrationConfigType) => {
      const data: IPayloadCreateToolIntegration = {
        name: value.name,
        integrationType: value.integrationType,
        config: {
          ...value,
        },
      };
      if (webhookId) {
        return await updateToolIntegration(webhookId, data);
      } else {
        return await handleCreateToolsIntegration(data);
      }
    },
    [handleCreateToolsIntegration, updateToolIntegration, webhookId]
  );

  useEffect(() => {
    if (data) {
      setIntegration(data);
      const config: WebhookFormType = {
        ...data.config,
        name: data?.name || '',
        headers: Object.keys(data.config?.headers)?.length
          ? Object.entries(data.config.headers).map((entry) => ({
              key: entry[0],
              value: entry[1],
            }))
          : initWebhookConfig.headers,
        payload: {
          type: data.config?.payload?.type || PAYLOAD_TYPE.PAYLOAD_TRANSFORM,
          transformPayload: {
            type: data.config?.payload?.transformPayload?.type || PAYLOAD_TRANSFORM,
            transformRule: data.config?.payload?.transformPayload?.transformRule || '',
          },
          customPayload: prettierFormattedStringOfObject(data.config?.payload?.customPayload || ''),
          variables: data.config?.payload?.variables || initWebhookConfig.payload.variables,
        },
        credentials: {
          type: initWebhookConfig.credentials.type,
          config: {
            grantType:
              data.config?.credentials?.config?.grantType ||
              initWebhookConfig.credentials.config.grantType,
            clientId:
              data.config?.credentials?.config?.clientId ||
              initWebhookConfig.credentials.config.clientId,
            clientSecret:
              data.config?.credentials?.config?.clientSecret ||
              initWebhookConfig.credentials.config.clientSecret,
            username:
              data.config?.credentials?.config?.username ||
              initWebhookConfig.credentials.config.username,
            password:
              data.config?.credentials?.config?.password ||
              initWebhookConfig.credentials.config.password,
            scope:
              data.config?.credentials?.config?.scope || initWebhookConfig.credentials.config.scope,
            tokenHost:
              data.config?.credentials?.config?.tokenHost ||
              initWebhookConfig.credentials.config.tokenHost,
            tokenPath:
              data.config?.credentials?.config?.tokenPath ||
              initWebhookConfig.credentials.config.tokenPath,
          },
          enable:
            Object.hasOwn(data.config, 'credentials') &&
            Object.keys(data.config?.credentials)?.length > 0,
        },
      };
      setWebhookData(config);
    }
  }, [data]);

  return useMemo(
    () => ({ integration, webhookData, isLoading, error, reload, saveWebhookData }),
    [error, isLoading, reload, webhookData, integration, saveWebhookData]
  );
};
