import { ActionIcon, Box, Button, Flex, Group, Radio, Stack, Text, rem } from '@mantine/core';
import { useForm } from '@mantine/form';
import { IconCirclePlus, IconTrash } from '@tabler/icons-react';
import '@uiw/react-textarea-code-editor/dist.css';
import dynamic from 'next/dynamic';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useVisibilityControl from '../../../../hooks/useVisibilityControl';
import { deepCopy } from '../../../../utils/common';
import {
  SelectStyled,
  TextInputStyled,
} from '../../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import ConfirmModal from '../../../Common/ConfirmModal';
import {
  PAYLOAD_CUSTOM,
  PAYLOAD_TRANSFORM,
  PAYLOAD_TYPE,
  WEBHOOK_METHOD,
  WebhookFormType,
  WebhookIntegrationConfigType,
} from '../models';
import { Auth0Form } from './Auth0Form';
import { CustomTransformModal } from './CustomTransformModal';
import { TestWebhookResultModal } from './TestWebhookResultModal';
import { useWebhookDetailContext } from './WebhookDetailContext';
import { initWebhookConfig, isValidUrl, prefixSchema, removeWhiteSpaceChar } from './constant';
import { useWarningOnExit } from './useWarningOnExit';
import { useWebhookStyle } from './useWebhookStyle';

const CodeEditor = dynamic(
  () => import('@uiw/react-textarea-code-editor').then((mod) => mod.default),
  { ssr: false }
);

const MAX_NAME = 50;
const MAX_HEADER_INCLUDE = 10;
// eslint-disable-next-line no-unused-vars
export function WebhookIntegrationForm({ onSave }: { onSave: (webhookId?: string) => void }) {
  const { classes, cx } = useWebhookStyle();
  const [isSaving, setIsSaving] = useState(false);
  const [isSendTest, setIsSendTest] = useState(false);
  const {
    t,
    webhookData,
    isEditWebhook,
    saveWebhookData,
    sendTestWebhookData,
    deleteWebhookIntegration,
  } = useWebhookDetailContext();
  const { visible, toggle } = useVisibilityControl();
  const { visible: visibleConfirmModal, toggle: toggleConfirmModal } = useVisibilityControl();
  const { visible: visibleTestResult, toggle: toggleTestResultModal } = useVisibilityControl();
  const testResultRef = useRef<{ message: 'success' | 'failed' }>(null);
  const form = useForm<WebhookFormType>({
    initialValues: deepCopy(initWebhookConfig),
    validate: {
      name: (value) => (!value.trim()?.length ? t('requiredField') : null),
      url: (value) => {
        if (!value.startsWith('https://') || value.length <= 'https://'.length)
          return t('wrongFormat');
        if (!isValidUrl(value)) return t('wrongFormat');
        return null;
      },
      payload: {
        customPayload: (value, values) => {
          if (values.payload.type !== PAYLOAD_TYPE.PAYLOAD_CUSTOM) return null;
          if (!value?.trim().length) return null;
          try {
            JSON.parse(value.trim());
            return null;
          } catch (e) {
            return t('wrongFormat');
          }
        },

        transformPayload: {
          // transformRule: (value, values) => {
          //     if (values.payload.type !== PAYLOAD_TYPE.PAYLOAD_TRANSFORM) return null;
          //     return value.trim().length ? null : t('requiredField');
          // },
        },
      },
      credentials: {
        config: {
          tokenHost: (value, values) => {
            if (!values.credentials.enable) return null;
            if (!value.startsWith('https://') || value.length <= 'https://'.length) {
              return t('wrongFormat');
            }
            if (!isValidUrl(value)) return t('wrongFormat');
            return null;
          },
        },
      },
    },
    transformValues: (values) => {
      const transformedData = {
        ...values,
        name: values.name.trim(),
        headers: ((values) => {
          const headers = {};
          values.headers.forEach((item) => {
            if (item.key && item.value) {
              headers[item.key] = item.value.trim();
            }
          });
          return headers;
        })(values) as unknown as WebhookFormType['headers'],
        payload: {
          type: values.payload.type,
          [values.payload.type === PAYLOAD_TRANSFORM ? 'transformPayload' : 'customPayload']:
            values.payload.type === PAYLOAD_TRANSFORM
              ? values.payload.transformPayload
              : values.payload.customPayload,
          variables: values.payload?.variables || [],
        },
        credentials: {
          ...values?.credentials,
          config: {
            ...values.credentials?.config,
            tokenHost:
              values?.credentials?.config?.tokenHost === 'https://'
                ? ''
                : values?.credentials?.config?.tokenHost,
          },
        },
      };
      return transformedData;
    },
  });

  const handleAddHeader = useCallback(() => {
    form.insertListItem('headers', { key: '', value: '' });
  }, [form]);

  const handleRemoveHeader = useCallback(
    (index) => {
      form.removeListItem('headers', index);
    },
    [form]
  );

  const formPropsForUrlInput = useCallback(() => {
    const props = form.getInputProps('url');
    const formOnChangeFunc = props.onChange;

    const customOnChange = ({ currentTarget }: React.ChangeEvent<HTMLInputElement>) => {
      let value = currentTarget.value;
      value = prefixSchema(value);
      formOnChangeFunc(value);
    };
    return {
      ...props,
      onChange: customOnChange,
    };
  }, [form]);

  const formPropsForKeyInput = useCallback(
    (index: number) => {
      const props = form.getInputProps(`headers.${index}.key`);
      const formOnChangeFunc = props.onChange;
      const customOnChange = ({
        currentTarget,
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        let value =
          currentTarget.type === 'checkbox'
            ? (currentTarget as EventTarget & HTMLInputElement).checked
            : currentTarget.value;
        value = removeWhiteSpaceChar(value);
        formOnChangeFunc(value);
      };
      return { ...props, onChange: customOnChange };
    },
    [form]
  );

  const propsName = useMemo(() => {
    const { error, ...props } = form.getInputProps('name');
    return {
      hasError: !!error,
      props: props,
    };
  }, [form]);

  const PayloadTransformRadio = useMemo(() => {
    const data = form.getInputProps('payload.type', { type: 'checkbox' });
    return { ...data, checked: data.checked === PAYLOAD_TRANSFORM, value: PAYLOAD_TRANSFORM };
  }, [form]);

  const PayloadCustomRadio = useMemo(() => {
    const data = form.getInputProps('payload.type', { type: 'checkbox' });
    return { ...data, checked: data.checked === PAYLOAD_CUSTOM, value: PAYLOAD_CUSTOM };
  }, [form]);

  const validateForm = useCallback(() => {
    const validateResult = form.validate();
    return validateResult.hasErrors;
  }, [form]);

  const getValuesBEValidData = useCallback((values: WebhookIntegrationConfigType) => {
    const innerValues = deepCopy(values);
    if (!innerValues.credentials?.enable) {
      delete innerValues.credentials;
    } else {
      delete innerValues.credentials?.enable;
    }
    return innerValues;
  }, []);

  const handleDeleteIntegration = useCallback(
    (confirm?: boolean) => {
      toggleConfirmModal();
      if (!!confirm) deleteWebhookIntegration();
    },
    [deleteWebhookIntegration, toggleConfirmModal]
  );

  const handleSendTestWebhook = useCallback(async () => {
    if (validateForm()) return;
    const values = getValuesBEValidData(
      form.getTransformedValues() as unknown as WebhookIntegrationConfigType
    );
    setIsSendTest(true);
    const res = await sendTestWebhookData(values);
    if (res) {
      testResultRef.current = res;
      toggleTestResultModal();
    }
    setIsSendTest(false);
  }, [validateForm, getValuesBEValidData, form, sendTestWebhookData, toggleTestResultModal]);

  const handleSaveForm = useCallback(async () => {
    if (validateForm()) return;
    const values = getValuesBEValidData(
      form.getTransformedValues() as unknown as WebhookIntegrationConfigType
    );
    setIsSaving(true);

    const res = await saveWebhookData(values);
    setIsSaving(false);
    form.resetTouched();
    form.resetDirty();
    if (res && !isEditWebhook) {
      // move to edit page
      setTimeout(() => {
        onSave(res.data.id);
      }, 500);
    }
  }, [form, getValuesBEValidData, isEditWebhook, onSave, saveWebhookData, validateForm]);

  const isFormHasBeenEditing = useMemo(() => {
    // console.log({ touched: form.isTouched(), dirty: form.isDirty() });
    return form.isTouched() || (!isEditWebhook && form.isDirty());
  }, [form, isEditWebhook]);

  useWarningOnExit(isFormHasBeenEditing, t('warning_exit'));

  useEffect(() => {
    if (webhookData) {
      form.setValues({ ...webhookData });
    }

    setTimeout(() => {
      form.resetTouched();
      form.resetDirty();
    }, 500);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [webhookData]);

  return (
    <>
      <Flex mt={30}>
        <Text className={classes.label}>
          {t('webhook.field.name.label')}
          <span className={classes.red}> * </span>
        </Text>
        <Stack sx={{ flexGrow: 1, gap: '5px' }}>
          <TextInputStyled
            required
            width={'100%'}
            maxLength={MAX_NAME}
            placeholder={t('webhook.field.name.placeholder')}
            className={propsName?.hasError ? classes.inputError : undefined}
            {...propsName.props}
          />
          <Group mt={0} justify='flex-end'>
            <Text size={rem(12)} className={classes.red} fw={400}>
              {!!form.errors?.['name'] ? form.errors?.['name'] : <span>&nbsp;</span>}
            </Text>
            <Text size={rem(12)} c='gray.7'>
              {`${(form.values?.['name'] || '').length}`}/{MAX_NAME}
              {t('char')}
            </Text>
          </Group>
        </Stack>
      </Flex>
      <Flex mt={'lg'}>
        <Text className={classes.label}>
          {t('webhook.field.method.label')}
          <span className={classes.red}> * </span>
        </Text>
        <Flex sx={{ flexGrow: 1 }}>
          <SelectStyled
            data={[
              { value: WEBHOOK_METHOD.POST, label: WEBHOOK_METHOD.POST },
              // { value: WEBHOOK_METHOD.PUT, label: WEBHOOK_METHOD.PUT },
            ]}
            style={{ width: '100%' }}
            defaultValue={WEBHOOK_METHOD.POST}
            required
            {...form.getInputProps('method')}
          />
        </Flex>
      </Flex>
      <Flex mt={'lg'}>
        <Text className={classes.label}>
          {t('webhook.field.url.label')}
          <span className={classes.red}> * </span>
        </Text>
        <Flex sx={{ flexGrow: 1 }}>
          <TextInputStyled
            placeholder='https://'
            style={{ width: '100%' }}
            required
            {...formPropsForUrlInput()}
          />
        </Flex>
      </Flex>
      <Flex mt={'lg'}>
        <Text className={classes.label}>{t('webhook.field.header.label')}</Text>
        <Flex sx={{ flexGrow: 1 }} direction='column' align='flex-end' justify={'center'}>
          {form.values?.headers?.map((item, index) => (
            <Box
              key={index}
              style={{
                display: 'flex',
                gap: '0rem',
                alignItems: 'center',
                width: '100%',
              }}
              mb={'md'}
            >
              <Box style={{ display: 'flex', flex: 1, gap: '1rem' }}>
                <TextInputStyled
                  placeholder={t('webhook.field.header.placeholder.key')}
                  {...formPropsForKeyInput(index)}
                  style={{ flex: 1 }}
                />
                <TextInputStyled
                  mr={'sm'}
                  placeholder={t('webhook.field.header.placeholder.value')}
                  {...form.getInputProps(`headers.${index}.value`)}
                  style={{ flex: 1 }}
                />
              </Box>
              <Box style={{ display: 'flex', gap: '0.5rem' }}>
                <ActionIcon
                  styles={(theme) => ({
                    root: {
                      '&:hover': {
                        color: theme.colors.navy[0],
                      },
                    },
                  })}
                  variant='transparent'
                  className={classes.navy}
                  onClick={handleAddHeader}
                  disabled={form.values?.headers?.length >= MAX_HEADER_INCLUDE}
                >
                  <IconCirclePlus />
                </ActionIcon>
                <ActionIcon
                  styles={(theme) => ({
                    root: {
                      '&:hover': {
                        color: theme.colors.navy[0],
                      },
                    },
                  })}
                  variant='transparent'
                  className={classes.navy}
                  onClick={() => handleRemoveHeader(index)}
                  sx={{
                    visibility: form.values?.headers?.length > 1 ? 'visible' : 'hidden',
                  }}
                >
                  <IconTrash />
                </ActionIcon>
              </Box>
            </Box>
          ))}
        </Flex>
      </Flex>
      <Flex mt={'lg'}>
        <Text className={classes.label}>{t('webhook.field.payload.label')}</Text>
        <Group>
          <Radio
            color='navy.0'
            label={t('webhook.field.payload.option.transform')}
            {...PayloadTransformRadio}
          />
          <Radio
            ml={80}
            color='navy.0'
            label={t('webhook.field.payload.option.custom')}
            {...PayloadCustomRadio}
          />
        </Group>
      </Flex>
      {form.values?.['payload']?.type === PAYLOAD_TYPE.PAYLOAD_TRANSFORM ? (
        <Flex mt={'lg'}>
          <Text className={classes.label}>{t('webhook.field.transform.label')}</Text>
          <Flex direction='column' sx={{ flexGrow: 1 }}>
            <Button variant='default' onClick={toggle} w={'auto'} maw={160}>
              <Text c='gray.7' fw={500} fz={14}>
                {t('webhook.field.transform.button.label')}
              </Text>
            </Button>
            {!!form.errors?.['payload.transformPayload.transformRule'] && (
              <Text size={rem(12)} className={classes.red} mt={5}>
                {form.errors?.['payload.transformPayload.transformRule']}
              </Text>
            )}
          </Flex>
        </Flex>
      ) : (
        <Flex mt={'lg'}>
          <Text className={classes.label}>&nbsp;</Text>
          <Flex sx={{ flexGrow: 1 }} direction={'column'}>
            <CodeEditor
              {...form.getInputProps('payload.customPayload')}
              language='json'
              placeholder={t('webhook.field.transform.custom_payload.edit.placeholder')}
              padding={15}
              data-color-mode='light'
              style={{
                height: '100%',
                width: '100%',
                minHeight: '200px',
                maxHeight: '200px',
                fontSize: '0.8rem',
                backgroundColor: 'white',
                border: `0.0625rem solid ${
                  !!form.errors?.['payload.customPayload'] ? 'red' : '#ced4da'
                }`,
                borderRadius: '5px',
                overflowY: 'auto',
                fontFamily:
                  'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
              }}
            />
            {!!form.errors?.['payload.customPayload'] && (
              <Text size={rem(12)} className={classes.red} mt={5}>
                {form.errors?.['payload.customPayload']}
              </Text>
            )}
          </Flex>
        </Flex>
      )}
      <Auth0Form form={form} />
      <Flex mt={40}>
        {isEditWebhook && (
          <Text
            className={cx([classes.label, classes.red, classes.clickable])}
            fw={700}
            onClick={toggleConfirmModal}
          >
            {t('webhook.remove_integration.label')}
          </Text>
        )}
        <Group justify='flex-end' sx={{ flexGrow: 1 }}>
          <Button
            variant='outline'
            onClick={handleSendTestWebhook}
            loading={isSendTest}
            disabled={isSaving}
          >
            {t('webhook.button.test.label')}
          </Button>
          <Button color='navy.0' onClick={handleSaveForm} loading={isSaving} disabled={isSendTest}>
            {t('webhook.button.save.label')}
          </Button>
        </Group>
      </Flex>
      <CustomTransformModal form={form} opened={visible} onClose={toggle} />
      <ConfirmModal
        opened={visibleConfirmModal}
        titleConfirm={t('cancelConfirmMessage')}
        onClose={handleDeleteIntegration}
        btnConfirmLabel={t('button.unlock')}
        btnConfirmColor='red'
        size={'md'}
        reverse
      />
      <TestWebhookResultModal
        result={testResultRef.current?.message || 'failed'}
        opened={visibleTestResult}
        onClose={toggleTestResultModal}
      />
    </>
  );
}
