import { Box, Container, Flex, Loader, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { RightToLeftTween } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import React, { useEffect } from 'react';
import ViewOnly from '../../../components/ViewOnly';
import { useExternalToolsContext } from '../../externalToolsContext';
import TabsControl from './TabsControl';

const useStyle = createStyles((theme) => ({
  container: {
    padding: '1rem',
    backgroundColor: 'white',
  },
  pageTitle: {
    color: theme.colors.dark[4],
    fontSize: '1.25rem',
  },
}));

export default function IntegrationSettings() {
  const { t } = useTranslate('workspace');

  const { classes } = useStyle();
  const { isLoading, isManager, reload } = useExternalToolsContext();
  useEffect(() => {
    reload();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return (
      <Flex
        align='center'
        justify='center'
        h={`calc(100dvh - var(--app-shell-header-height, 0px) + 0rem)`}
      >
        <Loader color='navy.0' />
      </Flex>
    );
  }

  return (
    <RightToLeftTween>
      <Container className={classes.container} size={'lg'}>
        <Flex align={'center'} mt={'lg'} gap='20px'>
          <Title className={classes.pageTitle}>{`${t('externalService.pageTitle')}`}</Title>
          {!isManager && <ViewOnly />}
        </Flex>
        <Box mt={30}>
          <TabsControl />
        </Box>
      </Container>
    </RightToLeftTween>
  );
}
