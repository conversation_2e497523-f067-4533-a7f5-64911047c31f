import { screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useExternalToolsContext } from '../../../../../externalToolsContext';
import NoIntegratedTools from '../../../Components/NoIntegratedTools';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/settings/integrations',
    pathname: '',
    query: {},
    asPath: '',
  }),
}));

jest.mock('../../../../../externalToolsContext.tsx', () => ({
  useExternalToolsContext: jest.fn(),
}));

describe('NoIntegratedTools', () => {
  beforeEach(() => {
    (useExternalToolsContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('renders a message indicating no integrated tools', () => {
    renderWithProvider(<NoIntegratedTools onNavigateToTabAllTools={() => null} />);
    const message = screen.getByText('no.integrated.tool');
    expect(message).toBeInTheDocument();
  });

  it('renders a link to the external tools page', () => {
    const { getByText } = renderWithProvider(
      <NoIntegratedTools onNavigateToTabAllTools={() => null} />
    );
    expect(getByText('take.a.look.available.tooks')).toBeInTheDocument();
  });
});
