import { render } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import { renderWithProvider } from '../../../../../../utils/testing';
import IntegratedIcon from '../../../Components/IntegratedIcon';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

describe('IntegratedIcon', () => {
  it('should render the integrated icon', () => {
    const { getByAltText, getByText } = renderWithProvider(<IntegratedIcon />);
    expect(getByAltText('integrated-icon')).toBeInTheDocument();
    expect(getByText('tool.integrated')).toBeInTheDocument();
  });
});
