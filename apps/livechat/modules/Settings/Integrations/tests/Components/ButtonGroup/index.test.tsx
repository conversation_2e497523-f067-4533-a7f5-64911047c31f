import { fireEvent, render } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import React from 'react';
import { renderWithProvider } from '../../../../../../utils/testing';
import { useExternalToolsContext } from '../../../../../externalToolsContext';
import ButtonGroup from '../../../Components/ButtonGroup';
import mockContext from '../../mockContext';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/settings/integrations',
    pathname: '',
    query: {},
    asPath: '',
  }),
}));

jest.mock('../../../../../externalToolsContext.tsx', () => ({
  useExternalToolsContext: jest.fn(),
}));

describe('ButtonGroup', () => {
  const onCancel = jest.fn();
  const onSave = jest.fn();

  beforeEach(() => {
    (useExternalToolsContext as jest.Mock).mockReturnValue(mockContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should render the component with the correct text and buttons', () => {
    const { getByText } = renderWithProvider(
      <ButtonGroup
        cancelText={`button.cancel`}
        saveText={`button.integrated`}
        position='center'
        onCancel={onCancel}
        onSave={onSave}
        isDisabled={false}
      />
    );

    expect(getByText('button.cancel')).toBeInTheDocument();
    expect(getByText('button.integrated')).toBeInTheDocument();
  });

  it('should call the onCancel function when the cancel button is clicked', () => {
    const { getByText } = renderWithProvider(
      <ButtonGroup
        cancelText={`button.cancel`}
        saveText={`button.integrated`}
        position='center'
        onCancel={onCancel}
        onSave={onSave}
        isDisabled={false}
      />
    );

    fireEvent.click(getByText('button.cancel'));

    expect(onCancel).toHaveBeenCalledTimes(1);
  });

  it('should call the onSave function when the save button is clicked', () => {
    const { getByText } = renderWithProvider(
      <ButtonGroup
        cancelText={`button.cancel`}
        saveText={`button.integrated`}
        position='center'
        onCancel={onCancel}
        onSave={onSave}
        isDisabled={false}
      />
    );

    fireEvent.click(getByText('button.integrated'));

    expect(onSave).toHaveBeenCalledTimes(1);
  });
});
