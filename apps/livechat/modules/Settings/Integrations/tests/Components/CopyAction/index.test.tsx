import { act, fireEvent, render, screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import { renderWithProvider } from '../../../../../../utils/testing';
import CopyAction from '../../../Components/CopyAction';
jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));
describe('CopyAction', () => {
  it('renders a copy icon', () => {
    renderWithProvider(<CopyAction value='test' />);
    const copyIcon = screen.getByTestId('copy-icon');
    expect(copyIcon).toBeInTheDocument();
  });

  it('copies the text to clipboard when clicked', async () => {
    const mockCopy = jest.fn(() => Promise.resolve('test'));
    Object.assign(navigator, {
      clipboard: {
        writeText: mockCopy,
      },
    });

    renderWithProvider(<CopyAction value='test' />);
    const copyIcon = screen.getByTestId('copy-icon');
    await act(async () => {
      await fireEvent.click(copyIcon);
    });

    expect(mockCopy).toHaveBeenCalledWith('test');
  });
});
