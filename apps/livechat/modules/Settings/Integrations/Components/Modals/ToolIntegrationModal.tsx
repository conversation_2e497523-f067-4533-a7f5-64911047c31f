import React, { useState, useEffect } from 'react';
import { Box, Flex, Modal, Title, Text, Grid, Container, TextInput } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useToggle } from '@mantine/hooks';
import { useExternalToolsContext } from '../../../../externalToolsContext';
import ButtonGroup from '../ButtonGroup';
import { initIntegrationToolForm } from '../../mockData';
import { ILineIntegration } from '../../models';
import CopyAction from '../CopyAction';
import ConfirmModal from '../../../../Common/ConfirmModal';
import { deepCopy } from '../../../../../utils/common';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles((theme) => ({
    title: {
        fontSize: '1.25rem',
        fontWeight: 'bold',
        color: theme.colors.dark[4],
    },
    closeButton: {
        svg: {
            width: '24px !important',
            height: '24px !important',
        },
    },
    rowInput: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: '20px',
    },
    warningField: {
        color: theme.colors.red[5],
    },
    wrapInput: {
        position: 'relative',
    },
    styleInputWebhook: {
        '& .mantine-Input-input': {
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            paddingRight: '35px',
        },
    },
    removeText: {
        cursor: 'pointer',
    },
    textInput: {
        '& .mantine-Input-input': {
            '&:focus': {
                borderColor: theme.colors.navy[0],
            },
        },
    },
    errorInput: {
        '& .mantine-Input-input': {
            borderColor: theme.colors.red[5],
            '&:focus': {
                borderColor: theme.colors.red[5],
            },
        },
    },
    errorText: {
        display: 'inline-block',
    },
    countText: {
        float: 'right',
        display: 'inline-block',
        color: theme.colors.gray[5],
    },
}));

const MAX_LENGTH_CHANNEL_NAME = 100;
interface IProps {
    visible: boolean;
    onClose: () => void;
    isEdit: boolean;
}

const ToolIntegrationModal: React.FC<IProps> = ({ visible, onClose, isEdit }) => {
    const { t } = useTranslate('workspace');

    const { classes, cx } = useStyles();
    const [isDeleting, toggleIsDeleting] = useToggle();
    const {
        toolSelected,
        visibleConfirmModal,
        integratedToolsList,
        reload,
        toggleConfirmModal,
        updateToolIntegration,
        deleteToolIntegration,
        setIntegrationActiveTab,
        handleCreateToolsIntegration,
    } = useExternalToolsContext();
    const [formIntegration, setFormIntegration] = useState<ILineIntegration>(
        initIntegrationToolForm as unknown as ILineIntegration,
    );
    const [errorMsgFields, setErrorMsgFields] = useState({
        name: '',
        channelId: '',
        channelSecret: '',
        channelAccessToken: '',
    });
    const inputFields = [
        {
            id: 1,
            label: 'channelNameLabel',
            name: 'name',
            placeHolder: 'channelNamePlaceholder',
            value: formIntegration.name,
            required: true,
            isDisabled: false,
            iconCopy: false,
            errorMsg: errorMsgFields.name,
        },
        {
            id: 2,
            label: 'yourUserIdLabel',
            name: 'botId',
            placeHolder: '',
            value: formIntegration.config.botId,
            required: false,
            isDisabled: false,
            iconCopy: false,
            errorMsg: '',
        },
        {
            id: 3,
            label: 'webhookUrlLabel',
            name: 'hookUrl',
            placeHolder: '',
            value: formIntegration.config.hookUrl,
            required: false,
            isDisabled: true,
            iconCopy: true,
            errorMsg: '',
        },
        {
            id: 4,
            label: 'channelIDLabel',
            name: 'channelId',
            placeHolder: '',
            value: formIntegration.config.channelId,
            required: true,
            isDisabled: false,
            iconCopy: false,
            errorMsg: errorMsgFields.channelId,
        },
        {
            id: 5,
            name: 'channelSecret',
            label: 'channelSecretLabel',
            placeHolder: '',
            value: formIntegration.config.channelSecret,
            required: true,
            isDisabled: false,
            iconCopy: false,
            errorMsg: errorMsgFields.channelSecret,
        },
        {
            id: 6,
            name: 'channelAccessToken',
            label: 'accessTokenLabel',
            placeHolder: '',
            value: formIntegration.config.channelAccessToken,
            required: true,
            isDisabled: false,
            iconCopy: false,
            errorMsg: errorMsgFields.channelAccessToken,
        },
        {
            id: 7,
            name: 'basicId',
            label: 'basicIDLabel',
            placeHolder: '',
            value: formIntegration.config.basicId,
            required: false,
            isDisabled: false,
            iconCopy: false,
            errorMsg: '',
        },
    ];

    const countLengthOfChannelName = MAX_LENGTH_CHANNEL_NAME - formIntegration.name.length;

    useEffect(() => {
        if (isEdit && toolSelected) {
            setFormIntegration(toolSelected);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [toolSelected, onClose]);

    const handleClose = () => {
        handleClearErrorMsg();
        setFormIntegration(initIntegrationToolForm as unknown as ILineIntegration);
        onClose();
    };

    const handleCancel = () => {
        handleClose();
    };

    const checkDuplicatedData = (field: string) => {
        let isDuplicated = [];
        const integratedListToValidation = isEdit
            ? integratedToolsList.filter((item) => item.id !== toolSelected.id)
            : integratedToolsList;

        if (field === 'name') {
            isDuplicated = integratedListToValidation.filter(
                (item) => item.name === formIntegration.name,
            );
        } else if (field === 'channelId') {
            isDuplicated = integratedListToValidation.filter(
                (item) => item.config.channelId === formIntegration.config.channelId,
            );
        }

        return isDuplicated;
    };

    const validationFields = () => {
        let isError = false;
        const requiredFields = ['name', 'channelId', 'channelSecret', 'channelAccessToken'];
        let updatedErrorMsgFields = deepCopy(errorMsgFields);
        requiredFields.forEach((item) => {
            if (item === 'name') {
                const isDuplicated = checkDuplicatedData(item);
                if (isDuplicated.length > 0) {
                    updatedErrorMsgFields.name = t('error.msg.field.channelName.duplicated');
                    isError = true;
                } else if (!formIntegration['name']) {
                    updatedErrorMsgFields.name = t('error.msg.field.required');
                    isError = true;
                } else {
                    updatedErrorMsgFields.name = '';
                }
            } else {
                const isDuplicatedChannelId = checkDuplicatedData(item);
                if (isDuplicatedChannelId.length > 0) {
                    updatedErrorMsgFields[item] = t('error.msg.field.channelId.duplicated');
                    isError = true;
                } else if (!formIntegration.config[item]) {
                    updatedErrorMsgFields[item] = t('error.msg.field.required');
                    isError = true;
                } else {
                    updatedErrorMsgFields[item] = '';
                }
            }
        });
        setErrorMsgFields(updatedErrorMsgFields);
        return isError;
    };

    const handleClearErrorMsg = () => {
        setErrorMsgFields({
            name: '',
            channelId: '',
            channelSecret: '',
            channelAccessToken: '',
        });
    };

    const handleIntegration = async () => {
        const getFieldsError = validationFields();
        if (getFieldsError) return;

        const { name } = formIntegration;
        const { channelId, channelSecret, channelAccessToken, basicId, botId } =
            formIntegration.config;
        const payload = {
            name,
            integrationType: '',
            config: {
                channelId,
                channelSecret,
                channelAccessToken,
                basicId,
                botId,
                integrationType: '',
            },
        };

        if (isEdit) {
            const { integrationType } = formIntegration.config;
            payload.integrationType = integrationType;
            payload.config.integrationType = integrationType;

            await updateToolIntegration(toolSelected.id, payload);
        } else {
            const { integrationType } = toolSelected.config;
            payload.integrationType = integrationType;
            payload.config.integrationType = integrationType;

            await handleCreateToolsIntegration(payload);
        }

        handleClose();
        setIntegrationActiveTab('integrated');
        reload();
    };

    const handleConfirmRemoveTool = async (confirm?: boolean) => {
        if (confirm) {
            toggleIsDeleting();
            try {
                await deleteToolIntegration(toolSelected.id);
            } finally {
                toggleIsDeleting();
            }
            reload();
        }
        toggleConfirmModal();
    };

    const onChangeInput = (name: string, value: string) => {
        let updatedFields = deepCopy(formIntegration);
        if (name === 'name') {
            updatedFields.name = value;
        } else {
            updatedFields.config[name] = value;
        }
        setFormIntegration(updatedFields);
    };

    const handleCancelToolPackage = () => {
        handleClose();
        toggleConfirmModal();
    };

    const formatMaskedValue = (field: string, value: string | undefined) => {
        const repeatCount = field === 'channelSecret' ? 25 : 65;
        const maskedText = `${(value || '')?.slice(0, 3)}${'*'.repeat(repeatCount)}${(value || '')?.slice(-3)}`;
        return maskedText;
    };

    return (
        <Box>
            <Modal
                classNames={{
                    title: classes.title,
                    close: classes.closeButton,
                }}
                radius="md"
                size="xl"
                centered
                onClose={handleClose}
                opened={visible}
                closeOnClickOutside={false}
                closeOnEscape={false}
                withCloseButton={false}
            >
                <Modal.CloseButton className="" size={'lg'} style={{ right: '-94%' }} />
                <Modal.Title mb={'md'} pl={'md'} className={classes.title}>
                    {`${toolSelected.name}${t('cooperation')}`}
                </Modal.Title>
                <Modal.Body mt={'lg'} pb={0}>
                    <Text fz="sm" color="gray.7" mb={24}>
                        {toolSelected.config.integrationType === 'line' &&
                            t('lineSettingDescription')}
                    </Text>
                    <Container fluid px={0}>
                        {inputFields.map((item, index) => (
                            <Grid className={classes.rowInput} key={index}>
                                <Grid.Col span={3}>
                                    <Title order={6}>
                                        {t(item.label)}
                                        {item.required && (
                                            <span className={classes.warningField}> *</span>
                                        )}
                                    </Title>
                                </Grid.Col>
                                {toolSelected?.id &&
                                (item.name === 'channelAccessToken' ||
                                    item.name === 'channelSecret') ? (
                                    <Grid.Col span={9} className={classes.wrapInput}>
                                        <TextInput
                                            className={cx(
                                                classes.textInput,
                                                item.errorMsg && classes.errorInput,
                                            )}
                                            placeholder={t(item.placeHolder)}
                                            disabled={true}
                                            value={formatMaskedValue(item.name, item?.value)}
                                            onChange={(e) =>
                                                onChangeInput(item.name, e.target.value)
                                            }
                                        />
                                    </Grid.Col>
                                ) : (
                                    <Grid.Col span={9} className={classes.wrapInput}>
                                        <TextInput
                                            className={cx(
                                                classes.textInput,
                                                item.iconCopy && classes.styleInputWebhook,
                                                item.errorMsg && classes.errorInput,
                                                item.name === 'name' &&
                                                    countLengthOfChannelName < 0 &&
                                                    classes.errorInput,
                                            )}
                                            maxLength={
                                                item.name === 'name'
                                                    ? MAX_LENGTH_CHANNEL_NAME
                                                    : null
                                            }
                                            placeholder={t(item.placeHolder)}
                                            disabled={item.isDisabled}
                                            value={item.value}
                                            onChange={(e) =>
                                                onChangeInput(item.name, e.target.value)
                                            }
                                        />
                                        {item.iconCopy && <CopyAction value={item.value} />}
                                        <Box>
                                            {item.errorMsg && (
                                                <Text
                                                    className={classes.errorText}
                                                    fz="sm"
                                                    color="red.5"
                                                    mt="4px"
                                                >
                                                    {item.errorMsg}
                                                </Text>
                                            )}
                                            {item.name === 'name' && (
                                                <Text
                                                    className={cx(
                                                        classes.countText,
                                                        countLengthOfChannelName < 0 &&
                                                            classes.warningField,
                                                    )}
                                                    fz="xs"
                                                    mt="4px"
                                                >
                                                    {t('count.characters', {
                                                        totalChars: countLengthOfChannelName,
                                                    })}
                                                </Text>
                                            )}
                                        </Box>
                                    </Grid.Col>
                                )}
                            </Grid>
                        ))}
                    </Container>
                </Modal.Body>
                <Flex
                    pb={20}
                    align="center"
                    px={12}
                    justify={formIntegration.status === 'active' ? 'space-between' : 'flex-end'}
                >
                    {formIntegration.status === 'active' && (
                        <Title
                            c="red.5"
                            order={6}
                            pl={4}
                            className={classes.removeText}
                            onClick={handleCancelToolPackage}
                        >
                            {t('cancelToolPackage')}
                        </Title>
                    )}
                    <ButtonGroup
                        isDisabled={false}
                        position="right"
                        cancelText={`${t('button.cancel')}`}
                        saveText={`${t('button.integrated')}`}
                        onCancel={handleCancel}
                        onSave={handleIntegration}
                    />
                </Flex>
            </Modal>
            <ConfirmModal
                reverse
                opened={visibleConfirmModal}
                titleConfirm={t('cancelConfirmMessage')}
                onClose={handleConfirmRemoveTool}
                btnConfirmLabel={t('button.unlock')}
                loadingConfirm={isDeleting}
            />
        </Box>
    );
};

export default ToolIntegrationModal;
