import React from 'react';
import { Button, Group } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
const useStyles = createStyles((theme) => ({
  commonButton: {
    fontSize: '0.875rem',
  },
  buttonConfirm: {
    color: '#FFFFFF',
    '&:hover': {
      backgroundColor: '#3539BC',
    },
  },
  buttonCancel: {
    color: theme.colors.gray[9],
    '&:hover': {
      backgroundColor: theme.colors.gray[2],
    },
  },
}));

interface ButtonGroupProps {
  cancelText: string;
  saveText: string;
  position: 'left' | 'right' | 'center' | 'apart';
  onCancel: () => void;
  onSave: () => void;
  isDisabled: boolean;
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({
  cancelText,
  saveText,
  position,
  onCancel,
  onSave,
  isDisabled,
}) => {
  const { classes, cx } = useStyles();

  return (
    <Group justify={position}>
      <Button
        className={cx(classes.commonButton, classes.buttonCancel)}
        variant='outline'
        radius='md'
        color='gray.9'
        size='xs'
        onClick={onCancel}
      >
        {cancelText}
      </Button>
      <Button
        className={cx(classes.commonButton, classes.buttonConfirm)}
        variant='filled'
        radius='md'
        color='navy.0'
        size='xs'
        disabled={isDisabled}
        onClick={onSave}
      >
        {saveText}
      </Button>
    </Group>
  );
};

export default ButtonGroup;
