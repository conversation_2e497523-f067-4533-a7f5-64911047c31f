import { Flex, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import Image from 'next/image';
import { getPublicUrl } from '../../../../../utils/public';

const IntegratedIcon = () => {
  const { t } = useTranslate('workspace');

  return (
    <Flex gap='xs' direction='row' align='center' justify='flex-start'>
      <Text fz='xs' c='gray.5' sx={{ whiteSpace: 'nowrap' }}>{`${t('tool.integrated')}`}</Text>
      <Image
        src={getPublicUrl('/favicon/circle-check.svg')}
        alt='integrated-icon'
        width={30}
        height={30}
      />
    </Flex>
  );
};

export default IntegratedIcon;
