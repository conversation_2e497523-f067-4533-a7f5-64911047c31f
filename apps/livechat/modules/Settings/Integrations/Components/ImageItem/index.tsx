import { Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';

const useStyles = createStyles(() => ({
  img: {
    height: '100%',
    width: '100%',
    borderRadius: '10px',
  },
}));

interface ImageItemProps {
  imageLink: string;
  width: string;
  height: string;
}

const ImageItem: React.FC<ImageItemProps> = ({ imageLink, width, height }) => {
  const { classes } = useStyles();
  return (
    <Box w={width} h={height}>
      {imageLink && <img className={classes.img} src={imageLink} />}
    </Box>
  );
};

export default ImageItem;
