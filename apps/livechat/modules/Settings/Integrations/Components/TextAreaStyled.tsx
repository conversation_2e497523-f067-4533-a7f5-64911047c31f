import { Textarea, TextareaProps } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

const useStyle = createStyles((theme) => ({
  textAreaStyle: {
    '& .mantine-Textarea-input:focus': {
      borderColor: theme.colors.navy?.[0],
    },
  },
}));
export const TextAreaStyled = (props: TextareaProps & React.RefAttributes<HTMLTextAreaElement>) => {
  const { classes } = useStyle();
  return <Textarea {...props} className={`${classes.textAreaStyle} ${props?.className || ''}`} />;
};
