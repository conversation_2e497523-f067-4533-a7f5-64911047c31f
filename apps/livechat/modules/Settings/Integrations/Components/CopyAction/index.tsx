import { CopyButton, ActionIcon, Tooltip, Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCopy, IconCheck } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles(() => ({
  iconCopy: {
    position: 'absolute',
    right: '12px',
    top: '12px',
  },
}));

const CopyAction = ({ value }: { value: string }) => {
  const { t } = useTranslate('workspace');
  const { classes } = useStyles();

  return (
    <Box className={classes.iconCopy}>
      <CopyButton value={value} timeout={500}>
        {({ copied, copy }) =>
          copied ? (
            <ActionIcon color='navy.0'>
              <IconCheck size='20px' />
            </ActionIcon>
          ) : (
            <Tooltip label={`${t('copy')}`} withArrow position='top'>
              <ActionIcon
                color='navy.0'
                onClick={copy}
                data-testid='copy-icon'
                variant='transparent'
              >
                <IconCopy size='20px' />
              </ActionIcon>
            </Tooltip>
          )
        }
      </CopyButton>
    </Box>
  );
};

export default CopyAction;
