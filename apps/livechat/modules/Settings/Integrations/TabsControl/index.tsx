import { Tabs } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useEffect } from 'react';
import { addActiveTabAsParamToCurrentUrl } from '../../../../utils/queryParam';
import { useExternalToolsContext } from '../../../externalToolsContext';
import { IntegrationTabType } from '../models';
import TabAllTools from './TabAllTools';
import TabIntegratedTools from './TabIntegratedTools';

const useStyles = createStyles((theme) => ({
  tabItem: {
    padding: '10px 36px',
    borderBottomWidth: '3px',
    '&:hover': {
      backgroundColor: 'transparent',
    },
    '& .mantine-Tabs-tabLabel': {
      color: theme.colors.dark[4],
      fontSize: '1rem',
    },
  },
}));

const TabsControl: React.FC<any> = () => {
  const { t } = useTranslate('workspace');

  const { classes } = useStyles();
  const { integrationActiveTab, setIntegrationActiveTab } = useExternalToolsContext();

  useEffect(() => {
    addActiveTabAsParamToCurrentUrl(integrationActiveTab);
  }, [integrationActiveTab]);

  const handleTabChange = useCallback(
    (value: string) => {
      setIntegrationActiveTab(value as IntegrationTabType);
    },
    [setIntegrationActiveTab]
  );

  return (
    <Tabs color='navy.0' value={integrationActiveTab} onChange={(value) => handleTabChange(value)}>
      <Tabs.List mb={16}>
        <Tabs.Tab className={classes.tabItem} value='integrated'>
          <b>{`${t('tab.integrated.tools')}`}</b>
        </Tabs.Tab>
        <Tabs.Tab className={classes.tabItem} value='all'>
          <b>{`${t('tab.all.tools')}`}</b>
        </Tabs.Tab>
      </Tabs.List>
      <Tabs.Panel value='integrated' pt='xs'>
        <TabIntegratedTools onNavigateToTabAllTools={handleTabChange} />
      </Tabs.Panel>
      <Tabs.Panel value='all' pt='xs'>
        <TabAllTools />
      </Tabs.Panel>
    </Tabs>
  );
};
export default TabsControl;
