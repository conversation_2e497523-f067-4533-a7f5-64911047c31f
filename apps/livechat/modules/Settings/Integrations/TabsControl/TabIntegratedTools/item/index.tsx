import { Box, Flex, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React from 'react';
import { useUserContext } from '../../../../../userContext';
import ImageItem from '../../../Components/ImageItem';
import IntegratedIcon from '../../../Components/IntegratedIcon';
import { AllTypeIntegration } from '../../../models';

const useStyles = createStyles((theme) => ({
  boxItem: {
    border: '1px solid #C1C2C5',
    padding: '20px',
    borderRadius: '6px',
    boxShadow: '0px 0px 4px 0px #00000033',
    minHeight: '120px',
    marginBottom: '16px',
  },
  boxClickable: {
    cursor: 'pointer',
    '&:hover': {
      borderColor: theme.colors.navy[0],
    },
  },
  titleItem: {
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 1,
    WebkitBoxOrient: 'vertical',
  },
  nameItem: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 1,
    WebkitBoxOrient: 'vertical',
    flexGrow: 1,
    wordBreak: 'break-word',
    overflowWrap: 'unset',
  },
  textItem: {
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
  },
  pointerCursor: {
    cursor: 'pointer',
  },
  wrapperContent: {
    width: '100%',
  },
  flexChildItem: {
    flex: 1,
  },
}));

interface Props {
  item: AllTypeIntegration;
}

const IntegratedItem: React.FC<Props> = ({ item }) => {
  const { classes, cx } = useStyles();
  const { isManager } = useUserContext();

  return (
    <Box className={cx(classes.boxItem, isManager && classes.boxClickable)}>
      <Flex gap='md' justify='flex-start' align='center' direction='row'>
        <Box>
          <ImageItem imageLink={item?.icon} width='80px' height='80px' />
        </Box>
        <Box className={classes.flexChildItem}>
          <Flex direction='row' align='center' justify='space-between' mb={12} gap={'md'}>
            <Title order={4} className={classes.nameItem} title={item.name}>
              {item.name}
            </Title>
            <IntegratedIcon />
          </Flex>
          <Box mt={16}>
            <Text fz='sm' className={classes.textItem} color='gray.7' lineClamp={2}>
              {item.description}
            </Text>
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

export default IntegratedItem;
