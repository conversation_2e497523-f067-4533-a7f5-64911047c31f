import { Box, Container, Grid, Group, Loader } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import { BottomUpTween } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import React, { useEffect, useRef } from 'react';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import { SelectStyled } from '../../../../Automation/AutomationDetailScreen/ModalPlacementHolders/CustomFormElements/CustomFormElements';
import { useExternalToolsContext } from '../../../../externalToolsContext';
import { useUserContext } from '../../../../userContext';
import ToolIntegrationModal from '../../Components/Modals/ToolIntegrationModal';
import NoIntegratedTools from '../../Components/NoIntegratedTools';
import {
  AllTypeIntegration,
  ILineIntegration,
  IntegrationType,
  LINE_INTEGRATION,
  WEBHOOK_INTEGRATION,
} from '../../models';
import IntegratedItem from './item';

type Props = {
  // eslint-disable-next-line no-unused-vars
  onNavigateToTabAllTools: (value: string) => void;
};

const TabIntegratedTools: React.FC<Props> = ({ onNavigateToTabAllTools }) => {
  const { push } = useRouter();
  const { t } = useTranslate('workspace');

  const {
    isNext,
    filterType,
    integratedList,
    isLoadingListPage,
    integratedTypeOptions,
    loadMore,
    setFilterType,
    setToolSelected,
  } = useExternalToolsContext();
  const { visible: visibleEditModal, toggle: toggleEditModal } = useVisibilityControl();
  const { isManager } = useUserContext();
  const loadMoreRef = useRef(null);

  const { ref, entry } = useIntersection({
    root: loadMoreRef.current,
    threshold: 0.5,
  });

  useEffect(() => {
    if (entry?.isIntersecting) {
      loadMore();
    }
  }, [entry?.isIntersecting, loadMore]);

  const handleSelectTool = (item: AllTypeIntegration) => {
    if (!isManager) return;
    if (item.integrationType === LINE_INTEGRATION) {
      setToolSelected(item as ILineIntegration);
      toggleEditModal();
    }

    if (item.integrationType === WEBHOOK_INTEGRATION) {
      push(`/settings/integrations/webhook/edit/${item?.id}`);
    }
  };

  if (!isLoadingListPage && !integratedList?.length && !filterType) {
    return <NoIntegratedTools onNavigateToTabAllTools={onNavigateToTabAllTools} />;
  }

  return (
    <Container fluid px={0}>
      <Group>
        <SelectStyled
          clearable
          value={filterType}
          data={integratedTypeOptions}
          onChange={(val) => setFilterType(val as IntegrationType)}
          placeholder={t('integration_type')}
        />
      </Group>
      {!isLoadingListPage && !integratedList?.length && (
        <NoIntegratedTools onNavigateToTabAllTools={onNavigateToTabAllTools} />
      )}
      <Grid mt={30}>
        {integratedList.map((item: AllTypeIntegration, index) => (
          <Grid.Col span={{ base: 12, lg: 6 }} key={index}>
            <BottomUpTween delay={index * 0.1}>
              <Box onClick={() => handleSelectTool(item)}>
                <IntegratedItem item={item} />
              </Box>
            </BottomUpTween>
          </Grid.Col>
        ))}
      </Grid>
      {isLoadingListPage && (
        <Container w={'100%'} sx={{ textAlign: 'center' }}>
          <Loader color={'navy.0'} type='bar' />
        </Container>
      )}

      {isNext && <Box ref={ref}></Box>}
      <ToolIntegrationModal isEdit={true} visible={visibleEditModal} onClose={toggleEditModal} />
    </Container>
  );
};

export default TabIntegratedTools;
