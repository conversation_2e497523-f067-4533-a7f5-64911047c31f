import { Box, Container, Grid } from '@mantine/core';
import { BottomUpTween } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import React, { useCallback, useMemo } from 'react';
import useVisibilityControl from '../../../../../hooks/useVisibilityControl';
import { useExternalToolsContext } from '../../../../externalToolsContext';
import { useUserContext } from '../../../../userContext';
import ToolIntegrationModal from '../../Components/Modals/ToolIntegrationModal';
import {
  AllTypeIntegration,
  ILineIntegration,
  LINE_INTEGRATION,
  WEBHOOK_INTEGRATION,
} from '../../models';
import ToolItem from './item';

const TabAllTools: React.FC<any> = () => {
  const { push } = useRouter();
  const { t } = useTranslate('workspace');

  const { allToolsList, setToolSelected } = useExternalToolsContext();
  const { visible: visibleCreateSettingModal, toggle: toggleCreateModal } = useVisibilityControl();
  const { isManager } = useUserContext();

  const handleOpenConfigTool = (item: AllTypeIntegration) => {
    if (!isManager) return;
    if (item.config?.integrationType === LINE_INTEGRATION) {
      setToolSelected(item as ILineIntegration);
      toggleCreateModal();
    }

    if (item.config?.integrationType === WEBHOOK_INTEGRATION) {
      // navigate to webhook form page.
      push('/settings/integrations/webhook/create');
    }
  };

  const allToolsListMapDes = useMemo(() => {
    if (allToolsList && allToolsList?.length) {
      return allToolsList.map((item) => {
        let description = item?.description || '';
        if (item?.config?.integrationType === LINE_INTEGRATION)
          description = t('lineIntegrationDescription');
        if (item?.config?.integrationType === WEBHOOK_INTEGRATION)
          description = t('webhookIntegrationDescription');
        return {
          ...item,
          description: description,
        };
      });
    }
    return [];
  }, [allToolsList, t]);

  return (
    <Container fluid px={0}>
      <Grid>
        {allToolsListMapDes.map((item: AllTypeIntegration, index) => (
          <Grid.Col span={{ base: 12, lg: 6 }} key={index}>
            <BottomUpTween delay={index * 0.1}>
              <Box onClick={() => handleOpenConfigTool(item)}>
                <ToolItem item={item} />
              </Box>
            </BottomUpTween>
          </Grid.Col>
        ))}
      </Grid>
      <ToolIntegrationModal
        isEdit={false}
        visible={visibleCreateSettingModal}
        onClose={toggleCreateModal}
      />
    </Container>
  );
};

export default TabAllTools;
