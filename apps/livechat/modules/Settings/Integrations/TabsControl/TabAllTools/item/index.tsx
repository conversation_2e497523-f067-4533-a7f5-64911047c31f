import { Box, Button, Flex, Text, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import React from 'react';
import useCustomStyle from '../../../../../../hooks/useCustomStyle';
import { useUserContext } from '../../../../../userContext';
import ImageItem from '../../../Components/ImageItem';
import { AllTypeIntegration } from '../../../models';

const useStyles = createStyles((theme) => ({
  boxItem: {
    border: '1px solid #C1C2C5',
    padding: '20px',
    borderRadius: '6px',
    boxShadow: '0px 0px 4px 0px #00000033',
    minHeight: '140px',
  },
  boxClickable: {
    cursor: 'pointer',
    '&:hover': {
      borderColor: theme.colors.navy[0],
    },
  },
  nameItem: {
    maxWidth: '202px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 1,
    WebkitBoxOrient: 'vertical',
  },
  textItem: {
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
  },
  pointerCursor: {
    cursor: 'pointer',
  },
  flexChildItem: {
    flex: 1,
  },
}));

interface Props {
  item: AllTypeIntegration;
}

const ToolItem: React.FC<Props> = ({ item }) => {
  const { classes, cx } = useStyles();
  const { isManager } = useUserContext();
  const { t } = useTranslate('workspace');

  const { classes: customClasses } = useCustomStyle();

  return (
    <Box className={cx(classes.boxItem, isManager && classes.boxClickable)}>
      <Flex gap='md' justify='flex-start' align='center' direction='row'>
        <Box>
          <ImageItem imageLink={item?.icon} width='80px' height='80px' />
        </Box>
        <Box className={classes.flexChildItem}>
          <Flex direction='row' align='center' justify='space-between' mb={12}>
            <Title order={4} className={classes.nameItem}>
              {item.name}
            </Title>
            <Button
              size='xs'
              radius='md'
              color='navy.0'
              disabled={!isManager}
              className={customClasses.button}
            >
              {`${t('button.integrated')}`}
            </Button>
          </Flex>
          <Box mt={16}>
            <Text fz='sm' className={classes.textItem} color='gray.7' lineClamp={2}>
              {item.description}
            </Text>
          </Box>
        </Box>
      </Flex>
    </Box>
  );
};

export default ToolItem;
