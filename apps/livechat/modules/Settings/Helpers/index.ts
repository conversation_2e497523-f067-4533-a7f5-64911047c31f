import { IOperatorProfile, ITeamMemberDetail, Status } from '@resola-ai/models';
import { ITeamDetail } from '@resola-ai/models';
import ApiService from '../../../services/api';
import { MemberData } from '../TeamsAndUsers/EditMemberDataModal';
import { TeamListSettingItem, UserListSettingItem } from '../TeamsAndUsers/types';

function getTeamsToAddAndRemove(
  currTeams: string[] = [],
  futureTeams: string[] = [],
  remainTeamId: string
) {
  const teamAdd: string[] = [];
  const teamRemove: string[] = [];
  if (currTeams.length && !futureTeams.length) {
    teamRemove.push(...currTeams);
  }
  if (!currTeams.length && futureTeams.length) {
    teamAdd.push(...futureTeams);
  }

  if (currTeams.length && futureTeams.length) {
    // find team id in common
    // we don't want to remove the current team on that current team detail screen of team id
    // so add to remainTeams the current teamId
    const remainTeams = [
      ...currTeams.filter((curTeam) => futureTeams.includes(curTeam)),
      remainTeamId,
    ];
    teamRemove.push(...currTeams.filter((curTeam) => !remainTeams.includes(curTeam)));
    teamAdd.push(...futureTeams.filter((futureTeam) => !remainTeams.includes(futureTeam)));
  }

  return {
    teamAdd,
    teamRemove,
  };
}

function getMemberData(
  members: ITeamMemberDetail[] = [],
  listOperators: IOperatorProfile[] = [],
  teamList: ITeamDetail[] = []
) {
  if (!members?.length || !listOperators?.length) {
    return [] as TeamListSettingItem['members'];
  }

  const memberIds = members.map((member) => member.id);
  const listOperatorsInTeam = listOperators.filter((operator) => memberIds.includes(operator.id));
  return members.map((member) => {
    const operator = listOperatorsInTeam.find((operator) => operator.id == member.id);
    return {
      id: operator?.id || member?.id || '',
      role: operator?.role || member?.role || '',
      name: operator?.name || member?.name || '',
      teams: teamList
        .filter((team) => team.members.filter((mem) => mem.id === (operator?.id || member?.id)))
        .map((team) => ({
          id: team.id,
          name: team.name,
        })),
      avatar: operator?.picture || '',
      status: operator?.status,
    };
  }) as unknown as TeamListSettingItem['members'];
}

function assignUserToTeam(teamId: string, user: string, action: 'add' | 'remove' = 'add') {
  return ApiService.teamInfoModification({ teamId, action: action, user });
}

function mappingUsersSettingData(teamList: ITeamDetail[], listOperators: IOperatorProfile[]) {
  const usersSettingData: UserListSettingItem[] = [];
  if (listOperators.length) {
    listOperators.forEach((operator) => {
      usersSettingData.push({
        id: operator.id,
        name: operator?.name || operator.email,
        email: operator.email,
        avatar: operator.picture,
        role: {
          id: operator.id + Math.random() * 10000,
          name: operator.role || 'operator',
        },
        teams: teamList
          ? teamList
              .filter((team) => team.members.findIndex((mem) => mem.id === operator.id) > -1)
              .map((team) => ({
                id: team.id,
                name: team.name,
              }))
          : [],
        orgId: operator.orgId,
        status: operator?.status || Status.Offline,
      });
    });
  }

  return usersSettingData;
}

function mappingTeamsSettingData(teamList: ITeamDetail[], listOperators: IOperatorProfile[]) {
  const teamSettings: TeamListSettingItem[] = [];
  if (teamList.length) {
    teamList
      .sort((a, b) => new Date(a.createAt).getTime() - new Date(b.createAt).getTime())
      .forEach((item) => {
        const memberData = getMemberData(item.members, listOperators, teamList);
        teamSettings.push({
          id: item.id,
          name: item.name,
          description: (item as unknown as Record<string, string>)?.description || '',
          members: memberData,
          isDisabled: false,
          picture: item?.picture || undefined,
          default: item?.default,
        });
      });
  }
  return teamSettings;
}

const editUserInfo = (currentTeams, updateTeams, userId, teamId) => {
  const arrOfPromise = [];
  const { teamAdd, teamRemove } = getTeamsToAddAndRemove(currentTeams, updateTeams, teamId);
  teamAdd.length &&
    arrOfPromise.push(...teamAdd.map((tId) => assignUserToTeam(tId, userId, 'add')));
  teamRemove.length &&
    arrOfPromise.push(...teamRemove.map((tId) => assignUserToTeam(tId, userId, 'remove')));
  return arrOfPromise;
};

const updateUserRoleAndStatus = async (currentData: MemberData, nextData: MemberData) => {
  const arrPromise = [];
  if (!nextData?.id) return;
  if (!!nextData?.role && currentData?.role !== nextData?.role) {
    arrPromise.push(
      ApiService.updateUserProfile(nextData.id, {
        role: nextData.role,
      })
    );
  }
  if (!!nextData?.status && currentData?.status !== nextData?.status) {
    arrPromise.push(
      ApiService.updateUserProfile(nextData.id, {
        status: nextData?.status,
      })
    );
  }

  if (arrPromise.length) await Promise.all(arrPromise);
};

export {
  getTeamsToAddAndRemove,
  assignUserToTeam,
  mappingUsersSettingData,
  mappingTeamsSettingData,
  editUserInfo,
  updateUserRoleAndStatus,
};
