import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { useConversationActionContext } from './conversationActionContext';

const LIMIT_SIZE = 5 * 1024 ** 2; // 5MB
const LIMIT_FILE_PER_UPLOAD = 1;
const ALLOW_MIME_TYPES = [
  'image/png',
  'image/jpg',
  'image/jpeg',
  'image/svg+xml',
  'application/pdf',
  'video/mp4',
  'video/quicktime',
  'video/x-msvideo',
  'video/3gpp',
  'video/webm',
  'video/ogg',
  'video/x-ms-wmv',
  'video/x-matroska',
  'video/avi',
  'video/x-ms-asf',
];

const useUploadAsset = () => {
  const { currentConversationId } = useConversationActionContext();
  const [rejectErrors, setRejectErrors] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (currentConversationId) {
      setRejectErrors({});
    }
  }, [currentConversationId]);

  return useMemo(
    () => ({
      rejectErrors,
      LIMIT_SIZE,
      ALLOW_MIME_TYPES,
      LIMIT_FILE_PER_UPLOAD,
      setRejectErrors,
    }),
    [rejectErrors]
  );
};

export type UploadAssetContextType = ReturnType<typeof useUploadAsset>;

const context = createContext<UploadAssetContextType | null>(null);

export const UploadAssetContextProvider: React.FC<any> = ({ children }) => {
  const value = useUploadAsset();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useUploadAssetContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useUploadAssetContext must be used inside UploadAssetContextProvider');
  }

  return value;
};
