/* eslint-disable turbo/no-undeclared-env-vars */
const isPr = () => {
  if (process.env.AWS_PULL_REQUEST_ID) {
    return true;
  } else {
    return false;
  }
};

const getPreviewEnvironmentVariables = () => {
  const dotenv = require('dotenv');
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getPreviewEnvironmentVariables] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    const parsed = previewEnvironmentVariables.parsed;
    // add PREVIEW_ prefix to all env variables
    const previewEnv = Object.keys(parsed).reduce((acc, key) => {
      acc[`PREVIEW_${key}`] = parsed[key];
      return acc;
    }, {});
    return previewEnv;
  } else {
    // console.log('[getPreviewEnvironmentVariables] use default env');
    return undefined;
  }
};
const getCdnPrefix = () => {
  const dotenv = require('dotenv');
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getCdnPrefix] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    return previewEnvironmentVariables.parsed.NEXT_PUBLIC_CDN_PREFIX;
  } else {
    // console.log('[getCdnPrefix] use default env');
    return process.env.NEXT_PUBLIC_CDN_PREFIX ?? undefined;
  }
};
const getBaseUrl = () => {
  const dotenv = require('dotenv');
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getBaseUrl] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    return previewEnvironmentVariables.parsed.NEXT_PUBLIC_BASE_URL;
  } else {
    console.log('[getBaseUrl] use default env');
    return process.env.NEXT_PUBLIC_BASE_URL ?? '/';
  }
};
const isProd = process.env.NODE_ENV !== 'development';
const basePath = getBaseUrl();
const basePathWithoutSlashAtTheEnd = basePath.replace(/\/$/, '');
const cdnPrefix = getCdnPrefix();

module.exports = {
  reactStrictMode: false,
  transpilePackages: [
    '@resola-ai/ui',
    '@resola-ai/widget-engine',
    '@resola-ai/utils',
    '@resola-ai/models',
  ],
  output: 'export',
  env: {
    IS_PR: isPr(),
    ...getPreviewEnvironmentVariables(),
  },
  images: { unoptimized: true },
  basePath: basePathWithoutSlashAtTheEnd,
  assetPrefix: isProd ? cdnPrefix : undefined,
};
