import { useViewportSize } from '@mantine/hooks';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { NAVIGATOR_CONTAINER_ID_NAME, WORKSPACE_CONTAINER_ID_NAME } from '../constants';

const useUiCorrection = () => {
  const { width, height } = useViewportSize();
  const router = useRouter();
  useEffect(() => {
    setTimeout(() => {
      const workspaceContainer = document.getElementById(WORKSPACE_CONTAINER_ID_NAME);
      const navigatorContainer = document.getElementById(NAVIGATOR_CONTAINER_ID_NAME);
      if (workspaceContainer === null) {
        // handle for other pages that not have workspace
        const widthOfNavigator = navigatorContainer?.offsetWidth;
        const mainContainer = document.getElementsByTagName('main');
        if (mainContainer?.length) {
          mainContainer[0].style.paddingLeft = `${widthOfNavigator}px`;
        }
        return;
      }
      const widthOfWorkspaceAndNavigator =
        workspaceContainer?.offsetWidth + navigatorContainer?.offsetWidth;

      const mainContainer = document.getElementsByTagName('main');
      // set padding left of main container equal to width of workspace and navigator
      if (mainContainer?.length) {
        mainContainer[0].style.paddingLeft = `${widthOfWorkspaceAndNavigator}px`;
      }
    }, 100);
  }, [width, height, router]);
};

export default useUiCorrection;
