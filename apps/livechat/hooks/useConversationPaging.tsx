import { useDebouncedState } from '@mantine/hooks';
import { IConversation, ISuccessConversationResponse } from '@resola-ai/models';
import { sendCustomEvent } from '@resola-ai/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CONVERSATION_LIST_DEFAULT_PAGE_SIZE } from '../constants';
import { ConversationListParams } from '../models/conversationParams';
import ApiService from '../services/api';
import { deepCopy } from '../utils/common';
import {
  PAGE_TYPE,
  generateConversationDomElementId,
  getConversationKey,
  getPageType,
} from '../utils/conversation';

const addOrUpdateConversationsToAllConversations = (
  currentAllConversations: Record<string, IConversation[]>,
  mainKey: string,
  conversations: IConversation[]
) => {
  const oldOnes = currentAllConversations[mainKey] ?? [];
  const all = oldOnes.concat(
    conversations.filter((item) => !oldOnes.find((i) => i.id === item.id))
  );
  return {
    ...currentAllConversations,
    [mainKey]: all,
  };
};

const addNewConversationsToAllConversations = (
  currentAllConversations: Record<string, IConversation[]>,
  mainKey: string,
  conversations: IConversation[]
) => {
  return {
    ...currentAllConversations,
    [mainKey]: conversations?.length ? conversations : [],
  };
};

const useConversationPaging = () => {
  const [loading, setLoading] = useState(false);
  const [nextPageFirstConversation, setNextPageFirstConversation] = useState<
    IConversation | undefined
  >(undefined);

  const [allConversations, setAllConversations] = useState<Record<string, IConversation[]>>({});

  const [showLoadingMore, setShowLoadingMore] = useDebouncedState(true, 1000);

  const [conversationsParams, setConversationsParams] = useState<ConversationListParams>({
    status: '',
  });

  const [next, setNext] = useState<string | undefined>(undefined);

  const conversations = useMemo(() => {
    return allConversations[getConversationKey(conversationsParams)] ?? [];
  }, [allConversations, conversationsParams]);

  const isEmpty = conversations.length === 0;
  const isReachingEnd = isEmpty || conversations.length < CONVERSATION_LIST_DEFAULT_PAGE_SIZE;

  // console.log({ key: Object.keys(allConversations) });
  // console.log({ conversationsParams });
  // console.log({ keyFromParams: getConversationKey(conversationsParams) });
  // console.log({ pageType: getPageType(getConversationKey(conversationsParams)) });

  const callApiToGetConversations = useCallback(
    async (params: ConversationListParams) => {
      try {
        setLoading(true);

        const response = params.bookmark
          ? await ApiService.getBookmarkConversations(params)
          : await ApiService.getConversations(params);

        setNext(response.pagination?.next);

        if (
          response.data.length < CONVERSATION_LIST_DEFAULT_PAGE_SIZE ||
          response.data.length === 0
        ) {
          setNext(undefined);
          setShowLoadingMore(false);
        } else {
          setShowLoadingMore(true);
        }
        return response;
      } catch (error) {
        console.log('error', error);
        setNext(undefined);
        return {
          data: [],
          pagination: {
            next: undefined,
          },
          status: 'fail',
        } as ISuccessConversationResponse;
      } finally {
        setLoading(false);
      }
    },
    [setShowLoadingMore]
  );

  const refetchConversListByNewParams = useCallback(
    (params: ConversationListParams) => {
      const currentRef = deepCopy(conversationsParams);
      const newParams = {
        ...currentRef,
        ...params,
      };

      setConversationsParams(newParams);
      sendCustomEvent('deca-livechat-conversation-list-param-update-workspace-menu', {
        team: newParams.team,
        status: newParams.status,
        assigned: newParams.assigned,
        bookmark: newParams.bookmark,
      });
      // then call api here
      callApiToGetConversations(newParams).then((response) => {
        if (!response) return;

        if (newParams.next !== undefined) {
          setAllConversations((prev) => {
            return addOrUpdateConversationsToAllConversations(
              prev,
              getConversationKey(newParams),
              response?.data
            );
          });
          return;
        }
        setAllConversations((prev) => {
          return addNewConversationsToAllConversations(
            prev,
            getConversationKey(newParams),
            response?.data
          );
        });
      });
    },
    [callApiToGetConversations, conversationsParams]
  );

  const updateExistingConversationInAllConversationsHandler = useCallback(
    (changeConversation: IConversation) => {
      setAllConversations((prev) => {
        const conversationKey = getConversationKey(conversationsParams);
        const prevConversations = prev?.[conversationKey];
        return {
          ...prev,
          [conversationKey]: prevConversations?.map((item) => {
            if (item.id === changeConversation.id) {
              return changeConversation;
            }
            return item;
          }),
        };
      });
    },
    [conversationsParams]
  );

  const updateOrCreateConvInAllConvers = useCallback(
    (changeConversation: IConversation) => {
      setAllConversations((prev) => {
        const conversationParamsNewComing = {
          ...deepCopy(conversationsParams),
          assigned: !changeConversation?.assigneeId?.startsWith('UNKNOWN'),
          status: changeConversation.status,
          team: !changeConversation?.teamId.startsWith('UNKNOWN')
            ? changeConversation.teamId
            : undefined,
        };

        const conversationKey = getConversationKey(conversationParamsNewComing);
        const prevConversations = prev?.[conversationKey] || [];
        const found = prevConversations?.find((item) => item.id === changeConversation.id);
        if (found) {
          return {
            ...prev,
            [conversationKey]: prevConversations?.map((item) => {
              if (item.id === changeConversation.id) {
                return changeConversation;
              }
              return item;
            }),
          };
        }

        return {
          ...prev,
          [conversationKey]: [...prevConversations, changeConversation],
        };
      });
    },
    [conversationsParams]
  );

  const updateStatusConversationInAll = useCallback(
    (conversation: IConversation, update = true) => {
      // Just need to care about current params,
      // If conversation is included in current list by current params keys,
      // Check if update or remove this list , because if navigate to other link( different params)
      // then we just need to reload the list to get the latest data from server.
      if (!conversationsParams) return;
      const currentKey = getConversationKey(deepCopy(conversationsParams));
      const currentPageType = getPageType(currentKey);
      const team = conversationsParams?.team;

      const imcomningParams = {
        status: conversation.status,
        assigned: !conversation?.assigneeId?.startsWith('UNKNOWN'),
        team: !conversation?.teamId.startsWith('UNKNOWN') ? conversation.teamId : undefined,
        bookmark: conversation?.isBookmark,
      };

      const isConversationSuitsForUpdateFlag = [
        currentPageType === PAGE_TYPE.ALL_CONVERSATION &&
          currentKey ===
            getConversationKey({
              status: conversation.status,
              assigned: true,
              team: undefined,
            }),
        currentPageType === PAGE_TYPE.SPECIFIED_TEAM_CONVERSATION &&
          team === conversation.teamId &&
          imcomningParams.assigned === true &&
          currentKey === getConversationKey({ ...imcomningParams, assigned: undefined }),

        currentPageType === PAGE_TYPE.SPECIFIED_TEAM_UNASSIGNED_CONVERSATION &&
          currentKey === getConversationKey({ ...imcomningParams, assigned: false }),

        currentPageType === PAGE_TYPE.BOOKMAKRED_CONVERSATION && !!imcomningParams.bookmark,
      ];

      if (update && isConversationSuitsForUpdateFlag.some(Boolean)) {
        setAllConversations((preData) => {
          const preCurData = preData?.[currentKey] || [];
          return {
            ...preData,
            [currentKey]: preCurData.map((item) => {
              if (item.id === conversation.id) {
                return {
                  ...conversation,
                  isBookmark: [true, 'true'].includes(conversation.isBookmark),
                };
              }
              return item;
            }),
          };
        });
      }
    },
    [conversationsParams]
  );

  const updateOrCreateConversationsInAllConversationsHandler = useCallback(
    (changeConversations: IConversation[]) => {
      setAllConversations((prev) => {
        const conversationKey = getConversationKey(conversationsParams);
        const prevConversations = prev[conversationKey];
        if (!prevConversations) {
          return {
            ...prev,
            [conversationKey]: changeConversations,
          };
        }
        const newConversations: IConversation[] = [];
        changeConversations.forEach((changeConversation) => {
          const found = prevConversations.find((item) => item.id === changeConversation.id);
          if (found) {
            newConversations.push(changeConversation);
          } else {
            newConversations.push(changeConversation);
          }
        });
        return {
          ...prev,
          [conversationKey]: newConversations,
        };
      });
    },
    [conversationsParams]
  );

  const getConversationsOnly = useCallback(
    async (params?: ConversationListParams) => {
      try {
        delete (params as any)?.params;
        setLoading(true);
        const response = await ApiService.getConversations(params);

        setNext(response.pagination?.next);

        if (
          response.data.length < CONVERSATION_LIST_DEFAULT_PAGE_SIZE ||
          response.data.length === 0
        ) {
          setShowLoadingMore(false);
        } else {
          setShowLoadingMore(true);
        }
        return response;
      } catch (error) {
        console.log('error', error);
        setNext(undefined);
      } finally {
        setLoading(false);
      }
    },
    [setShowLoadingMore]
  );

  const getConversations = useCallback(
    async (params?: ConversationListParams) => {
      try {
        setLoading(true);
        const response = await ApiService.getConversations(params);
        refetchConversListByNewParams({
          ...params,
          per_page: CONVERSATION_LIST_DEFAULT_PAGE_SIZE,
          // next: response.pagination.next,
        });
        setNext(response.pagination.next);
        if (response.data?.length > 0) {
          setNextPageFirstConversation(response.data[0]);
        }

        if (
          response.data.length < CONVERSATION_LIST_DEFAULT_PAGE_SIZE ||
          response.data.length === 0
        ) {
          setShowLoadingMore(false);
        } else {
          setShowLoadingMore(true);
        }
        return response;
      } finally {
        setLoading(false);
      }
    },
    [setShowLoadingMore, refetchConversListByNewParams]
  );

  const getConversationsHandler = useCallback(
    (params?: ConversationListParams) => {
      getConversations(params).then();
    },
    [getConversations]
  );

  const getMoreConversations = useCallback(async () => {
    try {
      const params = {
        ...deepCopy(conversationsParams),
        per_page: CONVERSATION_LIST_DEFAULT_PAGE_SIZE,
      };
      const response = await ApiService.getConversations({
        ...params,
        next: next,
      });
      if (!response) {
        return;
      }
      if (response && response.data.length === 0) {
        setShowLoadingMore(false);
        return;
      } else {
        setShowLoadingMore(true);
      }
      refetchConversListByNewParams({
        ...params,
        // next: response.pagination.next,
        next: undefined,
      });
      setNext(response.pagination?.next);

      setAllConversations((prev) => {
        return addNewConversationsToAllConversations(
          prev,
          getConversationKey(deepCopy(conversationsParams)),
          response?.data
        );
      });
    } finally {
    }
  }, [setShowLoadingMore, refetchConversListByNewParams, next, conversationsParams]);

  const fetchConversationsOnly = useCallback(
    async (params: ConversationListParams) => {
      const response = await getConversationsOnly(params);
      if (response) {
        setAllConversations((prev) => {
          return addNewConversationsToAllConversations(
            prev,
            getConversationKey(params),
            response?.data
          );
        });
      }
    },
    [getConversationsOnly]
  );

  const refetchConversations = useCallback(
    (fetchNewList?: boolean) => {
      if (!conversationsParams) return;
      if (!!fetchNewList) {
        setConversationsParams((pre) => {
          return { ...pre, next: undefined };
        });
      }
      fetchConversationsOnly({
        ...conversationsParams,
        next: !fetchNewList ? next : undefined,
      }).then();
    },
    [fetchConversationsOnly, next, conversationsParams]
  );

  useEffect(() => {
    if (conversations.length > 0 && !isReachingEnd) {
      setShowLoadingMore(true);
    } else {
      setShowLoadingMore(false);
    }
  }, [conversations.length, isReachingEnd, setShowLoadingMore]);

  const intervalId = useRef(undefined);

  useEffect(() => {
    // set interval to check for current conversation message number, if the number of messages is not equal or smaller than the default one, then
    // set the showLoadingMore to false
    if (intervalId.current) {
      clearInterval(intervalId.current);
    }
    intervalId.current = setInterval(() => {
      if (conversations.length < CONVERSATION_LIST_DEFAULT_PAGE_SIZE) {
        setShowLoadingMore(false);
        clearInterval(intervalId.current);
      }
    }, 1000);
    return () => {
      if (intervalId.current) {
        clearInterval(intervalId.current);
      }
    };
  }, [conversations, setShowLoadingMore]);

  useEffect(() => {
    if (nextPageFirstConversation && nextPageFirstConversation?.id) {
      document
        .getElementById(generateConversationDomElementId(nextPageFirstConversation.id))
        ?.scrollIntoView();
    }
  }, [nextPageFirstConversation]);

  //clean up effect
  useEffect(() => {
    return () => {
      setAllConversations({});
    };
  }, []);

  return useMemo(
    () => ({
      next,
      loading,
      isEmpty,
      conversations,
      isReachingEnd,
      showLoadingMore,
      conversationsParams,
      setNext,
      refetchConversations,
      getConversationsHandler,
      updateStatusConversationInAll,
      refetchConversListByNewParams,
      updateOrCreateConvInAllConvers,
      getMoreConversationsHandler: getMoreConversations,
      updateExistingConversationInAllConversationsHandler,
      updateOrCreateConversationsInAllConversationsHandler,
    }),
    [
      next,
      isEmpty,
      loading,
      conversations,
      isReachingEnd,
      showLoadingMore,
      conversationsParams,
      getMoreConversations,
      refetchConversations,
      getConversationsHandler,
      updateStatusConversationInAll,
      refetchConversListByNewParams,
      updateExistingConversationInAllConversationsHandler,
      updateOrCreateConvInAllConvers,
      updateOrCreateConversationsInAllConversationsHandler,
    ]
  );
};

export default useConversationPaging;
