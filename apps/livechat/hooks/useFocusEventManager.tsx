import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import {
  ALIAS_USER_FOCUS_EVENT,
  FOCUS_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
  FOCUS_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
  FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
  GLOBAL_REALTIME_EVENT_NAME,
  USER_FOCUS_IN_CONVERSATION,
  USER_UNFOCUS_IN_CONVERSATION,
} from '../constants';
import { isMinutesPast } from '../utils/common';

const Focus = {
  USER_FOCUS_IN_CONVERSATION: USER_FOCUS_IN_CONVERSATION,
  USER_UNFOCUS_IN_CONVERSATION: USER_UNFOCUS_IN_CONVERSATION,
};

type FocusType = (typeof Focus)[keyof typeof Focus];

type FocusDataType = {
  conversationId: string;
  enduserId: string;
  orgId: string;
  status: FocusType;
  teamId: string;
  type: string;
  createAt: string; // Date
};

export type FocusReturnDataType = {
  filter?: string;
  callerId?: string;
  conversationId?: string;
  data: FocusDataType[];
};

const TIME_TO_SCAN_FOR_NEW_FOCUS_STATE = 2000; // 2 seconds to start scanning again
const MINUTES_PAST_CHECK = 1;
// To prevent pile up to many items that no longer focus by enduser.
const MINUTES_TO_REMOVE_ITEMS_FOCUS = 20;

const useFocusEventManager = () => {
  const intervalIdRef = useRef<number>();
  const intervalRemoveRef = useRef<number>();
  const focusMapRef = useRef(new Map<string, FocusDataType>());

  useEffect(() => {
    // Remove all items that has status === USER_UNFOCUS_IN_CONVERSATION
    // and createAt is older MINUTES_TO_REMOVE_ITEMS_FOCUS.
    intervalRemoveRef.current = setInterval(
      () => {
        const currTime = dayjs();
        const keysToRemove: string[] = [];
        focusMapRef.current.forEach((item, key) => {
          if (
            item.status === USER_UNFOCUS_IN_CONVERSATION &&
            isMinutesPast(currTime, item.createAt, MINUTES_TO_REMOVE_ITEMS_FOCUS)
          ) {
            keysToRemove.push(key);
          }
        });

        keysToRemove.forEach((key) => {
          focusMapRef.current.delete(key);
        });
      },
      MINUTES_TO_REMOVE_ITEMS_FOCUS * 60 * 1000 // Convert minutes to milliseconds
    ) as unknown as number;

    return () => {
      intervalRemoveRef.current && clearInterval(intervalRemoveRef.current);
    };
  }, []);

  useEffect(() => {
    intervalIdRef.current = setInterval(() => {
      const focusMap = focusMapRef.current;
      if (!focusMap.size) return;

      const currTime = dayjs();
      const currTimeStr = currTime.toISOString();
      const dataUpdateToUnfocus = [];
      const dataWithinTimeRange = [];

      focusMap.forEach((item) => {
        const isWithinPastMinutes = !isMinutesPast(currTime, item.createAt, MINUTES_PAST_CHECK);

        if (isWithinPastMinutes) {
          dataWithinTimeRange.push(item);
        } else if (item.status === USER_FOCUS_IN_CONVERSATION) {
          dataUpdateToUnfocus.push({
            ...item,
            createAt: currTimeStr,
            status: USER_UNFOCUS_IN_CONVERSATION,
          });
        }
      });

      dataUpdateToUnfocus.forEach((item) => {
        focusMap.has(item.conversationId) &&
          focusMap.set(item.conversationId, {
            ...item,
            status: USER_UNFOCUS_IN_CONVERSATION,
            createAt: currTimeStr,
          });
      });

      if (!dataUpdateToUnfocus.length && !dataWithinTimeRange.length) return;

      sendCustomEvent(FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
        data: {
          data: [...dataUpdateToUnfocus, ...dataWithinTimeRange],
        } as FocusReturnDataType,
      });
    }, TIME_TO_SCAN_FOR_NEW_FOCUS_STATE) as unknown as number;

    return () => {
      intervalIdRef.current && clearInterval(intervalIdRef.current);
    };
  }, []);

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;
    const focusMap = focusMapRef.current;

    const globalEventHandler = (e: CustomEvent<{ data: FocusDataType }>) => {
      const { data } = e.detail;
      if (data.type !== ALIAS_USER_FOCUS_EVENT) return;
      if (!data?.conversationId || !data?.status) return; // invalid data, cann't check
      const newData: FocusDataType = { ...data, createAt: new Date().toISOString() };
      focusMap.set(data.conversationId, newData);

      // Push new update to listener immediately
      sendCustomEvent(FOCUS_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
        data: {
          data: [newData],
        } as FocusReturnDataType,
      });
    };

    const getCurrentDataListHandler = (
      e: CustomEvent<{ callerId: string; filter?: FocusType; conversationId?: string }>
    ) => {
      const { filter, callerId, conversationId } = e.detail;
      let dataReturnList: FocusDataType[] = [];

      if (filter === USER_FOCUS_IN_CONVERSATION) {
        focusMap.forEach((item) => {
          if (item.status === USER_FOCUS_IN_CONVERSATION) dataReturnList.push(item);
        });
      }
      if (filter === USER_UNFOCUS_IN_CONVERSATION) {
        focusMap.forEach((item) => {
          if (item.status === USER_UNFOCUS_IN_CONVERSATION) dataReturnList.push(item);
        });
      }

      if (!filter) {
        dataReturnList.push(...Array.from(focusMap.values()));
      }

      if (!!conversationId) {
        dataReturnList = dataReturnList.filter((item) => item.conversationId === conversationId);
      }

      sendCustomEvent(FOCUS_STATUS_MANAGER_BROADCAST_TO_RECEIVER, {
        data: {
          filter,
          callerId,
          conversationId,
          data: dataReturnList,
        } as FocusReturnDataType,
      });
    };

    createCustomEventListener(GLOBAL_REALTIME_EVENT_NAME, globalEventHandler, signal);
    createCustomEventListener(
      FOCUS_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
      getCurrentDataListHandler,
      signal
    );

    return () => {
      controller.abort();
      focusMap.clear();
    };
  }, []);
};

export default useFocusEventManager;
