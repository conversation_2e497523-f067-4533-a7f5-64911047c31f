import { useEffect, useState } from 'react';
// todo: useObjectUrls is used across multiple apps, consider moving it to a shared package
type InputType = File | string | (File | string)[];

function useObjectUrls(input: InputType): string[] {
  const [objectURLs, setObjectURLs] = useState<string[]>([]);

  useEffect(() => {
    let urls: string[] = [];

    const revokeURLs = () => {
      urls.forEach((url) => URL.revokeObjectURL(url));
    };

    if (input instanceof File) {
      const objectURL = URL.createObjectURL(input);
      urls = [objectURL];
    } else if (typeof input === 'string') {
      urls = [input];
    } else if (Array.isArray(input)) {
      urls = input.map((item) => {
        if (item instanceof File) {
          return URL.createObjectURL(item);
        } else if (typeof item === 'string') {
          return item;
        }
        return '';
      });
    }

    setObjectURLs(urls);

    return () => {
      revokeURLs();
    };
  }, [input]);

  return objectURLs;
}

export default useObjectUrls;
