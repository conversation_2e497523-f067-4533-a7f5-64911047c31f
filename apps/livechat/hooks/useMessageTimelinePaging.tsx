import { IMessage } from '@resola-ai/models';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { preload } from 'swr';
import { CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE } from '../constants';
import ApiService from '../services/api';
import { createMessageId } from '../utils/message';
import { scrollToMessageListBottom } from '../utils/scroll';
import useVisibilityControl from './useVisibilityControl';

type MessagesRepos = Record<
  string,
  {
    lastTimeStampCursor: string | undefined; // Date ISO string
    messages: IMessage[]; // list of message in the conversation.
  }
>;

const sortOldestFirst = (messages: IMessage[]) => {
  return messages.sort((a, b) => {
    return new Date(a?.created).getTime() - new Date(b?.created).getTime();
  });
};

const updateMessagesReposHelper = (
  messagesRepos: MessagesRepos,
  converId: string,
  messages: IMessage[],
  switching?: boolean
) => {
  if (!messages?.length) return messagesRepos;

  const repo = messagesRepos[converId];

  if (!repo || (switching && !messages.some((msg) => msg.id === repo.messages?.at(-1)?.id))) {
    // If no repo exists or switching without overlapping data, override with new messages.
    messagesRepos[converId] = {
      lastTimeStampCursor: messages[0].created || undefined,
      messages: sortOldestFirst(messages),
    };
    return { ...messagesRepos };
  }

  // Merge new messages, avoiding duplicates
  const allMessages = [
    ...repo.messages,
    ...messages.filter((msg) => !repo.messages.some((m) => m.id === msg.id)),
  ];
  const sortedMessages = sortOldestFirst(allMessages);

  messagesRepos[converId] = {
    lastTimeStampCursor: sortedMessages[0].created || undefined,
    messages: sortedMessages,
  };

  return { ...messagesRepos };
};

const useFetchMessagesTimelineBase = () => {
  const [loading, setLoading] = useState(false);

  const loadMessagesAtCursor = useCallback(
    async (converId: string, dedicatedCursor?: string | undefined) => {
      setLoading(true);
      const messages = await ApiService.getConversationMessagesTimelineBase(converId, {
        per_page: CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE,
        preTs: dedicatedCursor,
      });
      setLoading(false);
      return messages;
    },
    []
  );

  const prefetchMessagesAtCursor = useCallback(
    ({
      conversationId,
      cursor = undefined,
    }: {
      conversationId: string | undefined;
      cursor?: string | undefined;
    }) => {
      if (!conversationId) return;

      return preload(['messages-api-cache-key', conversationId, cursor], () =>
        ApiService.getConversationMessagesTimelineBase(conversationId, {
          per_page: CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE,
          preTs: cursor,
        })
      );
    },
    []
  );

  return useMemo(
    () => ({
      loading,
      loadMessagesAtCursor,
      prefetchMessagesAtCursor,
    }),
    [loading, loadMessagesAtCursor, prefetchMessagesAtCursor]
  );
};

const useMessageTimelinePaging = (converId: string) => {
  const [messagesRepos, updateMessagesRepos] = useState<MessagesRepos>({});
  const [temporaryOffLoadingIndicator, setTemporaryOffLoadingIndicator] = useState(false);
  const [forceStop, setForceStop] = useState(false); // turn true when no message return from api call
  const {
    visible: initialSwitchConver,
    open: setSwitchingConver,
    close: setUnswichtingConver,
  } = useVisibilityControl(true);
  const messagesOfConver = useMemo(() => {
    return messagesRepos?.[converId]?.messages || [];
  }, [converId, messagesRepos]);

  const lastTimeStampCursor = useMemo(() => {
    return messagesRepos?.[converId]?.lastTimeStampCursor || undefined;
  }, [converId, messagesRepos]);

  const noMessage = useMemo(() => !!messagesOfConver?.length, [messagesOfConver?.length]);

  const isReachedEnd = useMemo(
    () =>
      (!!converId && messagesOfConver?.length === 0) ||
      !!messagesOfConver?.[0]?.isFirstEnduserMessage ||
      forceStop,
    [messagesOfConver, converId, forceStop]
  );

  const { loading, loadMessagesAtCursor, prefetchMessagesAtCursor } =
    useFetchMessagesTimelineBase();

  const handleLoadConversationAtCursor = useCallback(
    async (cursor: string) => {
      setTemporaryOffLoadingIndicator(true);
      const messages = await loadMessagesAtCursor(converId, cursor);
      if (!messages.length) {
        setForceStop(true);
        setTemporaryOffLoadingIndicator(false);
        return;
      }

      updateMessagesRepos((prev) => {
        return updateMessagesReposHelper(prev, converId, messages);
      });
      const lastMessageOfCurrentList = sortOldestFirst(messages)?.[messages.length - 1];
      const scrollToMessagesId = createMessageId(lastMessageOfCurrentList);

      setTimeout(() => {
        document
          .getElementById(scrollToMessagesId)
          ?.scrollIntoView({ behavior: 'instant', block: 'start' });
        setTemporaryOffLoadingIndicator(false);
      }, 100);

      sendCustomEvent('deca-livechat-widgets-event-message-new', {
        conversationId: converId,
      });
    },
    [converId, loadMessagesAtCursor]
  );

  const addMessagesToRepo = useCallback(
    (messages: IMessage[] = []) => {
      if (!Array.isArray(messages) || !messages?.length) return;
      updateMessagesRepos((prev) => {
        return updateMessagesReposHelper(prev, converId, messages);
      });
    },
    [converId]
  );

  const handleGetMessageWhenSwitching = useCallback(
    async (converId) => {
      setSwitchingConver();
      const messages = await loadMessagesAtCursor(converId, undefined);
      if (!messages.length) {
        setForceStop(true);
        return;
      }
      updateMessagesRepos((prev) => {
        return updateMessagesReposHelper(prev, converId, messages, true);
      });
      setTimeout(() => {
        scrollToMessageListBottom(true);
        sendCustomEvent('deca-livechat-widgets-event-message-new', {
          conversationId: converId,
        });
      }, 200);
      setTimeout(() => {
        setUnswichtingConver();
      }, 300);
    },
    [loadMessagesAtCursor, setSwitchingConver, setUnswichtingConver]
  );

  useEffect(() => {
    if (!converId) return;
    setForceStop(false);
    handleGetMessageWhenSwitching(converId);
  }, [converId, handleGetMessageWhenSwitching]);

  /**
   * Use this effect to send a custom event , to notify the other users about change of the messages
   * Just to reduce the rendering cycles , improve performance.
   */
  useEffect(() => {
    const handleGetLastMessage = () => {
      const endMessageTextArr = [
        'conversation.completed',
        'conversation.completed.enduser',
        'conversation.inwrapup',
      ];
      if (messagesOfConver.length) {
        const messageEndedThisConversation = messagesOfConver.find((item) => {
          return endMessageTextArr.includes(item?.data?.text);
        });
        const messageSendToListener = messageEndedThisConversation
          ? messageEndedThisConversation
          : messagesOfConver.slice(-1)?.[0];

        sendCustomEvent('deca-livechat-update-message-latest', {
          message: messageSendToListener || {},
        });
      }
    };
    // eslint-disable-next-line no-unused-vars
    const sendLastMessage = (event?: CustomEvent) => {
      handleGetLastMessage();
      setTimeout(() => handleGetLastMessage(), 500);
      setTimeout(() => handleGetLastMessage(), 1000);
    };
    const cleanUp = createCustomEventListener('deca-livechat-messages-get-last-messages', () => {
      sendLastMessage;
    });

    // Auto send last message for audiences.
    sendLastMessage();

    return () => {
      cleanUp();
    };
  }, [messagesOfConver]);

  return useMemo(
    () => ({
      loading,
      noMessage,
      isReachedEnd,
      addMessagesToRepo,
      initialSwitchConver,
      lastTimeStampCursor,
      prefetchMessagesAtCursor, // use intersection threshold low or program to prefetch
      messages: messagesOfConver,
      temporaryOffLoadingIndicator,
      handleLoadConversationAtCursor,
    }),
    [
      loading,
      noMessage,
      isReachedEnd,
      messagesOfConver,
      addMessagesToRepo,
      initialSwitchConver,
      lastTimeStampCursor,
      prefetchMessagesAtCursor,
      temporaryOffLoadingIndicator,
      handleLoadConversationAtCursor,
    ]
  );
};

export default useMessageTimelinePaging;
