import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { act, renderHook } from '@testing-library/react';
import dayjs from 'dayjs';
import {
  ALIAS_USER_TYPE_EVENT,
  GLOBAL_REALTIME_EVENT_NAME,
  TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
  TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
  TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
  USER_TYPING_STATUS_HAPPENING,
  USER_TYPING_STATUS_STOP,
} from '../constants';
import useTypingEventManager, {
  TypingDataType,
  TypingReturnDataType,
} from './useTypingEventManager';

// Mocking dayjs to control time in tests
jest.mock('dayjs', () => {
  let mockTime = new Date('2023-10-27T10:00:00.000Z');
  const dayjsMock = jest.fn(() => ({
    toISOString: () => mockTime.toISOString(),
    diff: jest.fn((other: string | Date, unit: dayjs.OpUnitType) => {
      const otherTime = typeof other === 'string' ? new Date(other) : other;
      const diffInSeconds = Math.abs(mockTime.getTime() - otherTime.getTime()) / 1000;
      if (unit === 'second') return diffInSeconds;
      const diffInMinutes = diffInSeconds / 60;
      if (unit === 'minute') return diffInMinutes;
      return 0;
    }),
  }));
  (dayjsMock as any).mockSetTime = (newTime: Date) => {
    mockTime = newTime;
  };
  return dayjsMock;
});

// Mocking custom event functions
jest.mock('@resola-ai/utils', () => ({
  createCustomEventListener: jest.fn(),
  sendCustomEvent: jest.fn(),
}));

describe('useTypingEventManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (dayjs as any).mockSetTime(new Date('2023-10-27T10:00:00.000Z'));
  });

  it('should initialize without errors', () => {
    const { result } = renderHook(() => useTypingEventManager());
    expect(result.current).toBeUndefined();
  });

  it('should register event listeners on mount', () => {
    renderHook(() => useTypingEventManager());
    expect(createCustomEventListener).toHaveBeenCalledTimes(2);
    expect(createCustomEventListener).toHaveBeenCalledWith(
      GLOBAL_REALTIME_EVENT_NAME,
      expect.any(Function),
      expect.any(Object)
    );
    expect(createCustomEventListener).toHaveBeenCalledWith(
      TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
      expect.any(Function),
      expect.any(Object)
    );
  });

  it('should handle GLOBAL_REALTIME_EVENT_NAME event with valid data and USER_TYPING_STATUS_HAPPENING', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user2',
      orgId: 'org1',
      status: USER_TYPING_STATUS_HAPPENING,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };

    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });
    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER, {
      data: {
        data: [expect.objectContaining({ ...mockData, createAt: dayjs().toISOString() })],
      } as TypingReturnDataType,
    });
  });

  it('should handle GLOBAL_REALTIME_EVENT_NAME event and update existing status', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user2',
      orgId: 'org1',
      status: USER_TYPING_STATUS_HAPPENING,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });
    const mockData2: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user2',
      orgId: 'org1',
      status: USER_TYPING_STATUS_STOP,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData2 } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    expect(sendCustomEvent).toHaveBeenLastCalledWith(TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER, {
      data: {
        data: [expect.objectContaining({ ...mockData2, createAt: dayjs().toISOString() })],
      } as TypingReturnDataType,
    });
  });
  it('should handle TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST with filter USER_TYPING_STATUS_HAPPENING', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const getDataListHandler = (createCustomEventListener as jest.Mock).mock.calls[1][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user1',
      orgId: 'org1',
      status: USER_TYPING_STATUS_HAPPENING,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    act(() => {
      getDataListHandler({
        detail: { callerId: 'caller1', filter: USER_TYPING_STATUS_HAPPENING },
      } as CustomEvent<{ callerId: string; filter: string }>);
    });

    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
      data: {
        filter: USER_TYPING_STATUS_HAPPENING,
        callerId: 'caller1',
        conversationId: undefined,
        data: [expect.objectContaining(mockData)],
      } as TypingReturnDataType,
    });
  });
  it('should handle TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST with filter USER_TYPING_STATUS_STOP', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const getDataListHandler = (createCustomEventListener as jest.Mock).mock.calls[1][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user1',
      orgId: 'org1',
      status: USER_TYPING_STATUS_STOP,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    act(() => {
      getDataListHandler({
        detail: { callerId: 'caller1', filter: USER_TYPING_STATUS_STOP },
      } as CustomEvent<{ callerId: string; filter: string }>);
    });
    const res = [] as any[];
    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
      data: {
        filter: USER_TYPING_STATUS_STOP,
        callerId: 'caller1',
        conversationId: undefined,
        data: expect.arrayContaining(res),
      } as TypingReturnDataType,
    });
  });
  it('should handle TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST without filter', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const getDataListHandler = (createCustomEventListener as jest.Mock).mock.calls[1][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user1',
      orgId: 'org1',
      status: USER_TYPING_STATUS_STOP,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    act(() => {
      getDataListHandler({ detail: { callerId: 'caller1' } } as CustomEvent<{
        callerId: string;
        filter: string;
      }>);
    });

    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
      data: {
        filter: undefined,
        callerId: 'caller1',
        conversationId: undefined,
        data: expect.arrayContaining([]),
      } as TypingReturnDataType,
    });
  });
  it('should handle TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST with conversationId', () => {
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const getDataListHandler = (createCustomEventListener as jest.Mock).mock.calls[1][1];
    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user1',
      orgId: 'org1',
      status: USER_TYPING_STATUS_STOP,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    const mockData2: TypingDataType = {
      conversationId: 'conv2',
      enduserId: 'user2',
      orgId: 'org1',
      status: USER_TYPING_STATUS_HAPPENING,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
      globalEventHandler({ detail: { data: mockData2 } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    act(() => {
      getDataListHandler({
        detail: { callerId: 'caller1', conversationId: 'conv1' },
      } as CustomEvent<{ callerId: string; conversationId: string }>);
    });

    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
      data: {
        filter: undefined,
        callerId: 'caller1',
        conversationId: 'conv1',
        data: expect.arrayContaining([]),
      } as TypingReturnDataType,
    });
  });

  it('should remove stop typing record after MINUTES_TO_REMOVE_ITEMS_TYPING', () => {
    jest.useFakeTimers();
    const { result } = renderHook(() => useTypingEventManager());
    const globalEventHandler = (createCustomEventListener as jest.Mock).mock.calls[0][1];
    const getDataListHandler = (createCustomEventListener as jest.Mock).mock.calls[1][1];

    const mockData: TypingDataType = {
      conversationId: 'conv1',
      enduserId: 'user1',
      orgId: 'org1',
      status: USER_TYPING_STATUS_STOP,
      teamId: 'team1',
      type: ALIAS_USER_TYPE_EVENT,
      createAt: '2023-10-27T10:00:00.000Z',
    };
    act(() => {
      globalEventHandler({ detail: { data: mockData } } as CustomEvent<{
        data: TypingDataType;
      }>);
    });

    (dayjs as any).mockSetTime(new Date('2023-10-27T10:10:01.000Z'));
    jest.advanceTimersByTime(10 * 60 * 1000 + 1001);

    act(() => {
      getDataListHandler({ detail: { callerId: 'caller1' } } as CustomEvent<{
        callerId: string;
        filter: string;
      }>);
    });

    expect(sendCustomEvent).toHaveBeenCalledWith(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
      data: {
        filter: undefined,
        callerId: 'caller1',
        conversationId: undefined,
        data: [],
      } as TypingReturnDataType,
    });
    jest.useRealTimers();
  });
});
