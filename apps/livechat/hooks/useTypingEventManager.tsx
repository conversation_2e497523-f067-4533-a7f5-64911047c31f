import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import {
  ALIAS_USER_TYPE_EVENT,
  AUTO_UNTYPING_EVENT_IDLE_MILISECONDS,
  GLOBAL_REALTIME_EVENT_NAME,
  TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER,
  TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
  TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST,
  USER_TYPING_STATUS_HAPPENING,
  USER_TYPING_STATUS_STOP,
} from '../constants';
import { isMinutesPast, isSecondsPast } from '../utils/common';

const Typing = {
  USER_TYPING_STATUS_STOP: USER_TYPING_STATUS_STOP,
  USER_TYPING_STATUS_HAPPENING: USER_TYPING_STATUS_HAPPENING,
};

type TypingType = (typeof Typing)[keyof typeof Typing];

export type TypingDataType = {
  conversationId: string;
  enduserId: string;
  orgId: string;
  status: TypingType;
  teamId: string;
  type: string;
  createAt: string; // Date
};

export type TypingReturnDataType = {
  filter?: string;
  callerId?: string;
  conversationId?: string;
  data: TypingDataType[];
};

const MILISECONDS_TO_SCAN_UPDATE = 1000; //
const MINUTES_TO_REMOVE_ITEMS_TYPING = 10; // mins

const useTypingEventManager = () => {
  const typingMapRef = useRef(new Map<string, TypingDataType>());
  const intervalRemoveRef = useRef<number>();
  const intervalBroadCastRef = useRef<number>();

  useEffect(() => {
    intervalRemoveRef.current = setInterval(() => {
      const currTime = dayjs();
      typingMapRef.current.forEach((item, key) => {
        if (
          item.status === USER_TYPING_STATUS_STOP &&
          isMinutesPast(currTime, item.createAt, MINUTES_TO_REMOVE_ITEMS_TYPING)
        ) {
          typingMapRef.current.delete(key);
        }
      });
    }, MINUTES_TO_REMOVE_ITEMS_TYPING) as unknown as number;

    return () => {
      intervalRemoveRef.current && clearInterval(intervalRemoveRef.current);
    };
  }, []);

  useEffect(() => {
    intervalBroadCastRef.current = setInterval(() => {
      const typingMap = typingMapRef.current;
      if (!typingMap.size) return;

      const currTime = dayjs();
      const currTimeStr = currTime.toISOString();
      const dataUpdateToUntyping = [];
      const dataWithinTimeRange = [];

      typingMap.forEach((item) => {
        const isWithinPastMinutes = !isSecondsPast(
          currTime,
          item.createAt,
          AUTO_UNTYPING_EVENT_IDLE_MILISECONDS / 1000
        );

        if (isWithinPastMinutes) {
          dataWithinTimeRange.push(item);
        } else if (item.status === USER_TYPING_STATUS_HAPPENING) {
          dataUpdateToUntyping.push({
            ...item,
            createAt: currTimeStr,
            status: USER_TYPING_STATUS_STOP,
          });
        }
      });

      dataUpdateToUntyping.forEach((item) => {
        typingMap.has(item.conversationId) &&
          typingMap.set(item.conversationId, {
            ...item,
            status: USER_TYPING_STATUS_STOP,
            createAt: currTimeStr,
          });
      });

      if (!dataUpdateToUntyping.length && !dataWithinTimeRange.length) return;

      sendCustomEvent(TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER, {
        data: {
          data: [...dataUpdateToUntyping, ...dataWithinTimeRange],
        } as TypingReturnDataType,
      });
    }, MILISECONDS_TO_SCAN_UPDATE) as unknown as number;
    return () => {
      intervalBroadCastRef.current && clearInterval(intervalBroadCastRef.current);
    };
  }, []);

  useEffect(() => {
    const controller = new AbortController();
    const typingMap = typingMapRef.current;

    const updateTypingStatusDataAndBroadcast = (
      convId: string,
      currData: TypingDataType,
      status: string
    ) => {
      const updatedData = {
        ...currData,
        status: status,
        createAt: dayjs().toISOString(),
      };
      typingMap.set(convId, updatedData);
      sendCustomEvent(TYPING_STATUS_MANAGER_BROADCAST_TO_RECEIVER, {
        data: { data: [updatedData] } as TypingReturnDataType,
      });
    };

    const globalEventHandler = (e: CustomEvent<{ data: TypingDataType }>) => {
      const { data } = e.detail;
      if (data.type !== ALIAS_USER_TYPE_EVENT) return;
      if (!data?.conversationId || !data?.status) return; // invalid data, cann't check

      const existingData = typingMap.get(data.conversationId);
      if (existingData) {
        updateTypingStatusDataAndBroadcast(data.conversationId, existingData, data.status);
        return;
      }

      if (data.status === USER_TYPING_STATUS_HAPPENING) {
        updateTypingStatusDataAndBroadcast(data.conversationId, data, USER_TYPING_STATUS_HAPPENING);
      }
    };

    const getDataListHandler = (
      e: CustomEvent<{ callerId: string; filter?: TypingType; conversationId?: string }>
    ) => {
      const { filter, callerId, conversationId } = e.detail;
      let dataReturnList: TypingDataType[] = [];

      if (filter === USER_TYPING_STATUS_HAPPENING) {
        typingMap.forEach((item) => {
          if (item.status === USER_TYPING_STATUS_HAPPENING) dataReturnList.push(item);
        });
      }
      if (filter === USER_TYPING_STATUS_STOP) {
        typingMap.forEach((item) => {
          if (item.status === USER_TYPING_STATUS_STOP) dataReturnList.push(item);
        });
      }

      if (!filter) {
        dataReturnList.push(...Array.from(typingMap.values()));
      }

      if (!!conversationId) {
        dataReturnList = dataReturnList.filter((item) => item.conversationId === conversationId);
      }

      sendCustomEvent(TYPING_STATUS_MANAGER_PUSH_REQUEST_DATA_LIST, {
        data: {
          filter,
          callerId,
          conversationId,
          data: dataReturnList,
        } as TypingReturnDataType,
      });
    };

    createCustomEventListener(GLOBAL_REALTIME_EVENT_NAME, globalEventHandler, controller.signal);

    createCustomEventListener(
      TYPING_STATUS_MANAGER_GET_REQUEST_DATA_LIST,
      getDataListHandler,
      controller.signal
    );

    return () => {
      controller.abort();
    };
  }, []);
};

export default useTypingEventManager;
