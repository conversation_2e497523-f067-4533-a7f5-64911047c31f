import { IMessage } from '@resola-ai/models';
import { createCustomEventListener } from '@resola-ai/utils';
import { useCallback, useEffect } from 'react';
import { GLOBAL_REALTIME_EVENT_NAME } from '../constants';
import { isFromHumanMessage } from '../modules/messageContext';
import { useOperationSettingContext } from '../modules/operationSettingContext';
import { useUserContext } from '../modules/userContext';

export function useSoundNotification() {
  const { userId } = useUserContext();
  const { handlePlaySoundNotification } = useOperationSettingContext();

  const handlePlaySoundNotificationEvent = useCallback(
    (message: IMessage) => {
      if (
        message?.sender?.id === userId ||
        !isFromHumanMessage(message) ||
        message?.assigneeId !== userId
      ) {
        return;
      }

      handlePlaySoundNotification(message);
    },
    [handlePlaySoundNotification, userId]
  );

  useEffect(() => {
    const handleReceiveGlobalEvent = (event: CustomEvent) => {
      const { data } = event.detail;
      if (data.type === 'message.new') {
        handlePlaySoundNotificationEvent(data.data as IMessage);
      }
    };

    const cleanUp = createCustomEventListener(GLOBAL_REALTIME_EVENT_NAME, handleReceiveGlobalEvent);

    return () => {
      cleanUp();
    };
  }, [handlePlaySoundNotificationEvent]);
}
