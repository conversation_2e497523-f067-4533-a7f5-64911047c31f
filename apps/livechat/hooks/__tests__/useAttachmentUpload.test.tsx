// Mock the utils functions FIRST
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
}));

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

// Mock the API service as a class with static methods
const mockUploadAsset = jest.fn();
const mockDeleteAsset = jest.fn();
jest.mock('../../services/api', () => {
  return {
    __esModule: true,
    default: class {
      static uploadAsset(...args) {
        return mockUploadAsset(...args);
      }
      static deleteAsset(...args) {
        return mockDeleteAsset(...args);
      }
    },
  };
});

// Mock useDraftResources
const mockDraftResources = {
  addDraftResources: jest.fn(),
  clearDraftResources: jest.fn(),
  getDraftResources: jest.fn(),
};
jest.mock('../../modules/TextEditor/Editor/hooks/useDraftResource', () => ({
  useDraftResources: () => mockDraftResources,
}));

// Mock useUploadAssetContext
const mockSetRejectErrors = jest.fn();
jest.mock('../../modules/uploadAssetContext', () => ({
  useUploadAssetContext: () => ({
    setRejectErrors: mockSetRejectErrors,
    LIMIT_SIZE: 5 * 1024 ** 2,
    ALLOW_MIME_TYPES: ['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'video/mp4'],
    LIMIT_FILE_PER_UPLOAD: 1,
  }),
}));

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid'),
}));

// Mock the resource model
jest.mock('../../models/resource', () => ({
  IResource: jest.fn(),
}));

import createCache from '@emotion/cache';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { sendCustomEvent } from '@resola-ai/utils';
import '@testing-library/jest-dom';
import { act, renderHook } from '@testing-library/react';
import React from 'react';
import useAttachmentUpload from '../useAttachmentUpload';

// Mock the utils
jest.mock('../../utils/common', () => ({
  delay: jest.fn(() => Promise.resolve()),
}));

// Create a custom theme for testing
const testTheme = {
  colors: {
    decaBlue: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
    gray: [
      '#f8f9fa',
      '#f1f3f4',
      '#e8eaed',
      '#dadce0',
      '#bdc1c6',
      '#9aa0a6',
      '#80868b',
      '#5f6368',
      '#3c4043',
      '#202124',
    ] as const,
    navy: [
      '#E3F2FD',
      '#BBDEFB',
      '#90CAF9',
      '#64B5F6',
      '#42A5F5',
      '#2196F3',
      '#1E88E5',
      '#1976D2',
      '#1565C0',
      '#0D47A1',
    ] as const,
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
};

// Create emotion cache for testing
const emotionCache = createCache({ key: 'mantine' });

// Wrapper component for testing hooks
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MantineEmotionProvider cache={emotionCache}>
    <MantineProvider theme={testTheme}>{children}</MantineProvider>
  </MantineEmotionProvider>
);

beforeAll(() => {
  global.URL.createObjectURL = jest.fn(() => 'blob:test-url');
});

describe('useAttachmentUpload', () => {
  const mockCurrentConversationId = 'test-conversation-id';

  beforeEach(() => {
    jest.clearAllMocks(); // Reset all mock call counts
    jest.useFakeTimers();
    // Ensure global mocks are available
    global.URL.createObjectURL = jest.fn(() => 'blob:http://localhost/fake-blob-url');
    global.URL.revokeObjectURL = jest.fn();

    // Reset mock functions
    mockUploadAsset.mockReset();
    mockDeleteAsset.mockReset();
    mockDraftResources.addDraftResources.mockReset();
    mockDraftResources.clearDraftResources.mockReset();
    mockDraftResources.getDraftResources.mockReset();
    mockSetRejectErrors.mockReset();

    mockDraftResources.getDraftResources.mockReturnValue([]);
  });

  afterEach(() => {
    jest.useRealTimers();
    // Clean up URL.createObjectURL mock
    delete global.URL.createObjectURL;
  });

  const renderHookWithWrapper = () => {
    return renderHook(() => useAttachmentUpload(mockCurrentConversationId), {
      wrapper: TestWrapper,
    });
  };

  describe('initial state', () => {
    it('should initialize with default values', () => {
      const { result } = renderHookWithWrapper();

      expect(result.current.resources).toEqual([]);
      expect(result.current.uploadResourceLoading).toBe(false);
      expect(typeof result.current.removeResources).toBe('function');
      expect(typeof result.current.addAttachedFiles).toBe('function');
      expect(typeof result.current.resetResourcesHandler).toBe('function');
      expect(typeof result.current.setUploadResourceLoading).toBe('function');
    });

    it('should load saved resources on mount', () => {
      const savedResources = [
        {
          id: 'saved-1',
          ref: mockCurrentConversationId,
          name: 'saved-file.jpg',
          type: 'image',
          statusCreated: 'status-1',
          url: 'https://example.com/saved-file.jpg',
        },
      ];

      mockDraftResources.getDraftResources.mockReturnValue(savedResources);

      renderHookWithWrapper();

      expect(mockDraftResources.getDraftResources).toHaveBeenCalledWith(mockCurrentConversationId);
    });
  });

  describe('API service mock', () => {
    it('should have working API service mock', () => {
      // Test that the mock is properly set up
      expect(mockUploadAsset).toBeDefined();
      expect(typeof mockUploadAsset).toBe('function');
      expect(mockDeleteAsset).toBeDefined();
      expect(typeof mockDeleteAsset).toBe('function');

      // Test that the mock can be called
      mockUploadAsset.mockResolvedValue({ id: 'test' });
      expect(mockUploadAsset).toHaveBeenCalledTimes(0);
    });
  });

  describe('file validation', () => {
    it('should validate file type correctly', () => {
      const { result } = renderHookWithWrapper();

      const validFile = new File(['test'], 'test.png', { type: 'image/png' });
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

      act(() => {
        result.current.addAttachedFiles([validFile]);
      });

      // First call should be with a function to clear errors
      const calls = mockSetRejectErrors.mock.calls;
      expect(typeof calls[0][0]).toBe('function');
      expect(calls[0][0]({})).toEqual({});

      act(() => {
        result.current.addAttachedFiles([invalidFile]);
      });

      // Third call should be with a function for the error (second call clears, third sets error)
      expect(typeof calls[2][0]).toBe('function');
      const errorResult = calls[2][0]({});
      expect(errorResult).toEqual({ 'file-not-support': true });
    });

    it('should validate file size correctly', () => {
      const { result } = renderHookWithWrapper();

      const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', {
        type: 'image/jpeg',
      });

      act(() => {
        result.current.addAttachedFiles([largeFile]);
      });

      // First call should be with a function to clear errors
      const calls = mockSetRejectErrors.mock.calls;
      expect(typeof calls[0][0]).toBe('function');
      expect(calls[0][0]({})).toEqual({});

      // Second call should be with a function for the error
      expect(typeof calls[1][0]).toBe('function');
      const errorResult = calls[1][0]({});
      expect(errorResult).toEqual({ 'file-too-large': true });
    });

    it('should handle null file', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.addAttachedFiles([null as any]);
      });

      // setRejectErrors is called twice: first to clear, then to set error
      const calls = mockSetRejectErrors.mock.calls;
      expect(calls.length).toBe(2);
      // First call: clear errors
      expect(typeof calls[0][0]).toBe('function');
      expect(calls[0][0]({})).toEqual({});
      // Second call: set error
      expect(typeof calls[1][0]).toBe('function');
      const errorResult = calls[1][0]({});
      expect(errorResult).toEqual({ 'file-not-support': true });
    });
  });

  describe('file upload', () => {
    it('should upload valid files successfully', async () => {
      const { result } = renderHookWithWrapper();

      const mockResponse = {
        id: 'uploaded-1',
        ref: mockCurrentConversationId,
        name: 'test.jpg',
        statusCreated: 'status-1',
        url: 'https://example.com/uploaded.jpg',
      };

      mockUploadAsset.mockResolvedValue(mockResponse);

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      act(() => {
        result.current.addAttachedFiles([file]);
      });

      // Check that resources were added (this happens immediately)
      expect(result.current.resources.length).toBe(1);
      expect(result.current.resources[0].name).toBe('test.jpg');
      expect(result.current.resources[0].type).toBe('image');

      // Advance timer to trigger upload process
      act(() => {
        jest.advanceTimersByTime(100);
      });

      // Check that upload loading state is set
      expect(result.current.uploadResourceLoading).toBe(true);
    });

    it('should handle upload errors gracefully', async () => {
      const { result } = renderHookWithWrapper();

      mockUploadAsset.mockRejectedValue(new Error('Upload failed'));

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      act(() => {
        result.current.addAttachedFiles([file]);
      });

      // Advance timer and flush microtasks
      await act(async () => {
        jest.advanceTimersByTime(100);
        await Promise.resolve();
        await Promise.resolve(); // extra flush
      });

      // Debug: print call count
      console.log('mockUploadAsset call count (error test):', mockUploadAsset.mock.calls.length);
      expect(mockUploadAsset).toHaveBeenCalled();
      expect(result.current.uploadResourceLoading).toBe(false);
    });
  });

  describe('resource management', () => {
    it('should remove resources correctly', async () => {
      const { result } = renderHookWithWrapper();

      // First add some resources
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-1',
        ref: mockCurrentConversationId,
        name: 'test.jpg',
        statusCreated: 'status-1',
        url: 'https://example.com/uploaded.jpg',
      });

      act(() => {
        result.current.addAttachedFiles([file]);
      });

      // Wait for the upload to complete
      await act(async () => {
        jest.advanceTimersByTime(100);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Now remove a resource
      await act(async () => {
        result.current.removeResources(['test-uuid']);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Debug: print call count
      console.log(
        'mockDraftResources.addDraftResources call count (remove test):',
        mockDraftResources.addDraftResources.mock.calls.length
      );
      expect(mockDraftResources.addDraftResources).toHaveBeenCalled();
      expect(sendCustomEvent).toHaveBeenCalledWith('deca-livechat-sync-chat-message-height', {});
    });

    it('should reset resources correctly', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.resetResourcesHandler();
      });

      expect(mockDraftResources.clearDraftResources).toHaveBeenCalledWith(
        mockCurrentConversationId
      );
      expect(sendCustomEvent).toHaveBeenCalledWith('deca-livechat-sync-chat-message-height', {});
    });

    it('should handle server deletion for valid resources', async () => {
      const { result } = renderHookWithWrapper();

      // First add some resources
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-1',
        ref: mockCurrentConversationId,
        name: 'test.jpg',
        statusCreated: 'status-1',
        url: 'https://example.com/uploaded.jpg',
      });

      act(() => {
        result.current.addAttachedFiles([file]);
      });

      // Wait for the upload to complete
      await act(async () => {
        jest.advanceTimersByTime(100);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Now remove the resource
      await act(async () => {
        result.current.removeResources(['test-uuid']);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Debug: print call count
      console.log(
        'mockDeleteAsset call count (server deletion test):',
        mockDeleteAsset.mock.calls.length
      );
      expect(mockDeleteAsset).toHaveBeenCalledWith(mockCurrentConversationId, 'status-1');
    });
  });

  describe('message type detection', () => {
    it('should detect image types correctly', async () => {
      const { result } = renderHookWithWrapper();
      // Use a valid image MIME type
      const file = new File(['test'], 'test.png', { type: 'image/png' });
      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-image',
        ref: mockCurrentConversationId,
        name: 'test.png',
        statusCreated: 'status-image',
        url: 'https://example.com/uploaded-image.png',
      });
      await act(async () => {
        result.current.addAttachedFiles([file]);
        jest.advanceTimersByTime(100);
        await Promise.resolve();
      });
      console.log(
        'mockDraftResources.addDraftResources call count (image type test):',
        mockDraftResources.addDraftResources.mock.calls.length
      );
      expect(mockDraftResources.addDraftResources).toHaveBeenCalled();
    });

    it('should detect video types correctly', async () => {
      const { result } = renderHookWithWrapper();

      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-2',
        ref: mockCurrentConversationId,
        name: 'test.mp4',
        statusCreated: 'status-2',
        url: 'https://example.com/uploaded.mp4',
      });

      const videoFile = new File(['test'], 'test.mp4', { type: 'video/mp4' });

      act(() => {
        result.current.addAttachedFiles([videoFile]);
      });

      await act(async () => {
        jest.advanceTimersByTime(100);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Debug: print call count
      console.log(
        'mockDraftResources.addDraftResources call count (video type test):',
        mockDraftResources.addDraftResources.mock.calls.length
      );
      expect(mockDraftResources.addDraftResources).toHaveBeenCalled();
    });

    it('should detect document types correctly', async () => {
      const { result } = renderHookWithWrapper();
      // Use a valid document MIME type
      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-doc',
        ref: mockCurrentConversationId,
        name: 'test.pdf',
        statusCreated: 'status-doc',
        url: 'https://example.com/uploaded-doc.pdf',
      });
      await act(async () => {
        result.current.addAttachedFiles([file]);
        jest.advanceTimersByTime(100);
        await Promise.resolve();
      });
      console.log(
        'mockDraftResources.addDraftResources call count (doc type test):',
        mockDraftResources.addDraftResources.mock.calls.length
      );
      expect(mockDraftResources.addDraftResources).toHaveBeenCalled();
    });
  });

  describe('conversation switching', () => {
    it('should handle conversation ID changes', () => {
      const { result, rerender } = renderHookWithWrapper();

      const newConversationId = 'new-conversation-id';

      // Rerender with new conversation ID
      rerender();
      renderHook(() => useAttachmentUpload(newConversationId), {
        wrapper: TestWrapper,
      });

      expect(mockDraftResources.getDraftResources).toHaveBeenCalledWith(newConversationId);
    });
  });

  describe('error handling', () => {
    it('should handle empty file array', () => {
      const { result } = renderHookWithWrapper();

      act(() => {
        result.current.addAttachedFiles([]);
      });

      expect(mockUploadAsset).not.toHaveBeenCalled();
    });

    it('should handle null conversation ID', async () => {
      const { result } = renderHook(() => useAttachmentUpload(''), { wrapper: TestWrapper });

      mockUploadAsset.mockResolvedValue({
        id: 'uploaded-4',
        ref: '',
        name: 'test.jpg',
        statusCreated: 'status-4',
        url: 'https://example.com/uploaded.jpg',
      });

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      act(() => {
        result.current.addAttachedFiles([file]);
      });

      await act(async () => {
        jest.advanceTimersByTime(100);
        await Promise.resolve();
        await Promise.resolve();
      });

      // Debug: print call count
      console.log(
        'mockUploadAsset call count (null conversation ID):',
        mockUploadAsset.mock.calls.length
      );
      expect(mockUploadAsset).toHaveBeenCalledWith('', 'conversation', expect.any(File));
    });
  });
});
