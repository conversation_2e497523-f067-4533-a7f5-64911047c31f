import { useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import AppConfig from '../configs';
import axiosService from '../services/axios';
import { isAllowedDomains } from '../utils/location';
import { getOrganizationName } from '../utils/organization';

const useOrgRedirect = () => {
  const { user } = useAuth0();
  useEffect(() => {
    if (user && typeof window !== 'undefined') {
      const currentOrganizationName = getOrganizationName();
      const wantedOrganizationName = user.org_name;
      if (user.org_id) {
        axiosService.setOrgId(user.org_id);
      }
      const isAllowed = isAllowedDomains();
      if (
        wantedOrganizationName !== undefined &&
        currentOrganizationName !== wantedOrganizationName &&
        isAllowed
      ) {
        const allowedDomain = AppConfig.INCLUDED_BASE_PATH_DOMAINS.find((domain) =>
          window.location.origin.includes(domain)
        );
        if (allowedDomain) {
          console.log('USE ORG REDIRECT');
          window.location.href = `https://${wantedOrganizationName}.${allowedDomain}${window.location.pathname}`;
        }
      }
    }
  }, [user]);
};

export default useOrgRedirect;
