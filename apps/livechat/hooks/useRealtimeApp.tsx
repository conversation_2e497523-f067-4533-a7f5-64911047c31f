import { OperatorStatus } from '@resola-ai/models';
import { sendCustomEvent } from '@resola-ai/utils';
import { Centrifuge, SubscribedContext } from 'centrifuge';
import { useCallback, useEffect, useState } from 'react';
import AppConfig from '../configs';
import {
  ALIAS_USER_FOCUS_EVENT,
  ALIAS_USER_TYPE_EVENT,
  GLOBAL_REALTIME_EVENT_NAME,
  USER_FOCUS_IN_CONVERSATION,
  USER_READ_MESSAGE_IN_CONVERSATION,
  USER_TYPING_STATUS_HAPPENING,
  USER_TYPING_STATUS_STOP,
  USER_UNFOCUS_IN_CONVERSATION,
} from '../constants';
import { IWebsocketResponse, RealtimeEventType } from '../models/websocket';
import { useUserContext } from '../modules/userContext';
import ApiService from '../services/api';
import { reloadWholePage } from '../utils/widgets';

// This hook can be placed anywhere but must be under child component
// of /modules/RootLayout/RootLayout.tsx
export function useRealtimeApp() {
  const [wsToken, setWsToken] = useState<string>('');
  const { organizationId, userId, handleUpdateOperatorStatusFromSocket } = useUserContext();

  const getWsToken = useCallback(async () => {
    const token = await ApiService.getWsToken();
    return token;
  }, []);

  const updateWsToken = useCallback(async () => {
    const token = await getWsToken();
    setWsToken(token);
  }, [getWsToken]);

  const getChannelName = useCallback((organizationId: string, userId: string) => {
    return `livechat:org_${organizationId}|user#${userId}`;
  }, []);

  const eventHandler = useCallback(
    async (data: IWebsocketResponse<any>) => {
      const eventType = data.type;

      const eventTypesToBroadcast: RealtimeEventType[] = [
        'message.new', //
        'team.member.added', //
        'openai.message.adjustment', //
        'openai.message.suggestion', //
        'openai.conversation.summary', //
        'conversation.created', //
        'conversation.team.assigned', //
        'conversation.status.updated', //
        'conversation.operator.assigned', //
        'user.workspace.updated', //
        'user.workspace.teams.updated', //
        'user.conversation.state.updated', //
        'user.unread.conversation.updated', //
        'user.workspace.unassigned_team.updated', //
      ];

      const eventTypeToConvertBeforeBroadcast: RealtimeEventType[] = [
        USER_TYPING_STATUS_STOP,
        USER_TYPING_STATUS_HAPPENING,
        USER_READ_MESSAGE_IN_CONVERSATION,
        USER_FOCUS_IN_CONVERSATION,
        USER_UNFOCUS_IN_CONVERSATION,
      ];

      if (eventTypesToBroadcast.includes(eventType)) {
        sendCustomEvent(GLOBAL_REALTIME_EVENT_NAME, { data });
      }

      if (eventTypeToConvertBeforeBroadcast.includes(eventType)) {
        const AliasTypingTypes = [USER_TYPING_STATUS_HAPPENING, USER_TYPING_STATUS_STOP];
        const AliasFocusingTypes = [USER_FOCUS_IN_CONVERSATION, USER_UNFOCUS_IN_CONVERSATION];

        let eventTypeBroadcast: string = eventType;
        if (AliasTypingTypes.includes(eventType)) eventTypeBroadcast = ALIAS_USER_TYPE_EVENT;
        if (AliasFocusingTypes.includes(eventType)) eventTypeBroadcast = ALIAS_USER_FOCUS_EVENT;

        const newData = {
          ...((data as any)?.payload || data?.data || {}),
          type: eventTypeBroadcast,
          status: eventType,
        };
        sendCustomEvent(GLOBAL_REALTIME_EVENT_NAME, { data: newData });
      }

      if (eventType === 'user.role.changed') {
        reloadWholePage();
      }

      if (eventType === 'user.status.changed') {
        if (!data?.data) return;
        const newOperatorStatus = data as IWebsocketResponse<OperatorStatus>;
        handleUpdateOperatorStatusFromSocket(
          newOperatorStatus.data.id,
          newOperatorStatus.data.status
        );
      }
    },
    [handleUpdateOperatorStatusFromSocket]
  );

  useEffect(() => {
    updateWsToken();
  }, [updateWsToken]);

  useEffect(() => {
    if (!wsToken || !organizationId || !userId) {
      return;
    }
    const centrifuge = new Centrifuge(AppConfig.WEBSOCKET_URL, {
      debug: !AppConfig.IS_PRODUCTION,
      token: wsToken,
      getToken: getWsToken,
    });
    const channel = getChannelName(organizationId, userId);
    const subscription = centrifuge.newSubscription(channel);

    subscription.on('subscribed', (ctx: SubscribedContext) => {
      if (ctx.wasRecovering && !ctx.recovered) {
        reloadWholePage();
      }
    });

    subscription.on('publication', (ctx) => {
      eventHandler(ctx.data);
    });
    subscription.subscribe();
    centrifuge.connect();

    // eslint-disable-next-line no-unused-vars
    centrifuge.on('connected', function (ctx) {
      // now client connected to Centrifugo and authenticated.
      console.log('centrifugo connected');
    });
    centrifuge.on('error', function (err) {
      console.log('centrifugo error', err);
      const errorCode = err?.error?.code;
      // Reference here : https://github.com/centrifugal/centrifuge/blob/master/errors.go
      if ([101, 109, 110].includes(errorCode)) {
        try {
          updateWsToken();
        } catch (e) {
          reloadWholePage();
        }
      } else {
        reloadWholePage();
      }
    });
    return () => {
      subscription.unsubscribe();
      centrifuge.disconnect();
      console.log('unsubscribe & disconnect from centrifugo server');
    };
  }, [userId, wsToken, organizationId, eventHandler, getWsToken, getChannelName, updateWsToken]);

  return {};
}
