import { useEffect, useState } from 'react';

type InputType = File | string | (File | string)[];

type FileInfo = {
  url: string;
  size: number;
  extension: string;
  origin: 'FILE' | 'URL' | 'NO_DATA';
};

const bytesToMB = (bytes: number): number => {
  return bytes / (1024 * 1024);
};

const useFileInfo = (input: InputType): FileInfo[] => {
  const [fileInfoList, setFileInfoList] = useState<FileInfo[]>([]);

  useEffect(() => {
    let infoList: FileInfo[] = [];

    const revokeURLs = () => {
      infoList.forEach((info) => URL.revokeObjectURL(info.url));
    };

    if (input instanceof File) {
      const objectURL = URL.createObjectURL(input);
      const fileInfo: FileInfo = {
        url: objectURL,
        size: bytesToMB(input.size),
        extension: input.name.split('.').pop().toLowerCase(),
        origin: 'FILE',
      };
      infoList = [fileInfo];
    } else if (typeof input === 'string') {
      infoList = [{ url: input, size: 0, origin: 'URL', extension: 'png' }];
    } else if (Array.isArray(input)) {
      infoList = input.map((item) => {
        if (item instanceof File) {
          const objectURL = URL.createObjectURL(item);
          return {
            url: objectURL,
            size: bytesToMB(item.size),
            origin: 'FILE',
            extension: item.name.split('.').pop().toLowerCase(),
          };
        } else if (typeof item === 'string') {
          return { url: item, size: 0, origin: 'URL', extension: 'png' };
        }
        return { url: '', size: 0, origin: 'NO_DATA', extension: '' };
      });
    }

    setFileInfoList(infoList);

    return () => {
      revokeURLs();
    };
  }, [input]);

  return fileInfoList;
};

export default useFileInfo;
