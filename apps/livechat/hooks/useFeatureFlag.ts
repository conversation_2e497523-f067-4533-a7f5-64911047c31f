import { FeatureFlagName } from '@resola-ai/models';
import React from 'react';
import useSWR from 'swr';
import { FeatureFlagAPI } from '../services/api/featureFlag';

const useFeatureFlag = (name?: FeatureFlagName, defaultValue = undefined) => {
  const { data = {} } = useSWR('/settings/features', () => FeatureFlagAPI.getList());

  return React.useMemo(() => {
    return {
      flags: data,
      enabled: data[name] ? data[name] : defaultValue,
    };
  }, [data, defaultValue, name]);
};

export default useFeatureFlag;
