import {
  IGetConversationMessageParams,
  IMessage,
  ISuccessMessageResponse,
} from '@resola-ai/models';
import { createCustomEventListener, sendCustomEvent } from '@resola-ai/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE } from '../constants';
import ApiService from '../services/api';
import { createMessageId } from '../utils/message';
import { scrollToMessageListBottom } from '../utils/scroll';
import useVisibilityControl from './useVisibilityControl';

const updateAllMessages = (
  currentAllMessages: Record<string, IMessage[]>,
  currentConversationId: string,
  response: ISuccessMessageResponse
) => {
  const oldMessages = currentAllMessages[currentConversationId] ?? [];
  const newMessages = response.data;
  // id should not be duplicated
  const all = oldMessages.concat(
    newMessages.filter((item) => !oldMessages.find((i) => i.id === item.id))
  );
  // order by createdAt
  const sorted = all.sort((a, b) => {
    return new Date(a?.created).getTime() - new Date(b?.created).getTime();
  });
  return {
    ...currentAllMessages,
    [currentConversationId]: sorted,
  };
};

const addNewMessageToAllMessages = (
  currentAllMessages: Record<string, IMessage[]>,
  currentConversationId: string,
  message: any
) => {
  const oldMessages = currentAllMessages[currentConversationId] ?? [];
  const newMessages: IMessage[] = [
    {
      id: message.id,
      conversationId: message.conversationId,
      created: message.created,
      sender: message.sender,
      data: message.data,
    },
  ];
  // id should not be duplicated
  const all = oldMessages.concat(
    newMessages.filter((item) => !oldMessages.find((i) => i.id === item.id))
  );
  // order by createdAt
  const sorted = all.sort((a, b) => {
    return new Date(a.created).getTime() - new Date(b.created).getTime();
  });
  return {
    ...currentAllMessages,
    [currentConversationId]: sorted,
  };
};

const getKeyName = (conversationId: string) => {
  return `id-${conversationId}`;
};

const useMessagePaging = (conversationId: string) => {
  const [loading, setLoading] = useState(false);
  const [showLoadingMore, setShowLoadingMore] = useState(true);
  const [allMessages, setAllMessages] = useState<Record<string, IMessage[]>>({});
  const {
    visible: initialRefetchMessages,
    open: turnOnInitialRefetchMessages,
    close: turnOffInitialRefetchMessages,
  } = useVisibilityControl();
  const [paramsMessage, setParamsMessage] = useState<IGetConversationMessageParams>({});
  const intervalId = useRef(undefined);
  const scrollToImageAfterImageLoadedRef = useRef(false);
  const latestMessageRef = useRef<IMessage | undefined>(undefined);

  const messages = useMemo(
    () => (allMessages[getKeyName(conversationId)] ?? [])?.filter(Boolean),
    [allMessages, conversationId]
  );

  const isEmpty = useMemo(() => messages.length === 0, [messages]);
  const isReachingEnd = useMemo(
    () =>
      isEmpty || (messages.length < CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE && !paramsMessage?.ts),
    [isEmpty, messages.length, paramsMessage?.ts]
  );

  const addNewMessageToAllMessagesHandler = useCallback((newMessage: IMessage) => {
    setAllMessages((prev) => {
      return addNewMessageToAllMessages(prev, getKeyName(newMessage.conversationId), newMessage);
    });
  }, []);

  const fetchMessages = useCallback(async (id?: string, params?: IGetConversationMessageParams) => {
    return await ApiService.getConversationMessages(id ?? '', params);
  }, []);

  const getMessages = useCallback(
    async (id?: string, params?: IGetConversationMessageParams) => {
      try {
        setLoading(true);
        const response = await fetchMessages(id, params);
        setParamsMessage({
          per_page: CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE,
          ts: response.pagination.ts,
          conversationId: id,
        });

        setShowLoadingMore(
          response.data.length > 0 &&
            (response.data.length >= CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE ||
              response?.pagination?.ts)
        );
        return response;
      } finally {
        setLoading(false);
      }
    },
    [fetchMessages, setShowLoadingMore]
  );

  const getMoreMessages = useCallback(async () => {
    scrollToImageAfterImageLoadedRef.current = false;

    if (initialRefetchMessages) return;

    try {
      const id = paramsMessage.conversationId;
      const params = {
        per_page: CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE,
        ts: paramsMessage.ts,
      };
      const response = await fetchMessages(id, params);

      if (!response) {
        return;
      }
      if (response && response.data.length === 0) {
        showLoadingMore && setShowLoadingMore(false);
        return;
      } else if (!response.pagination.ts) {
        setShowLoadingMore(false);
      } else {
        !showLoadingMore && setShowLoadingMore(true);
      }

      setParamsMessage({
        per_page: CONVERSATION_MESSAGES_DEFAULT_PAGE_SIZE,
        ts: response.pagination.ts,
        conversationId: id,
      });
      setAllMessages((prev) => {
        const currentConversation = prev[getKeyName(id)];
        if (currentConversation) {
          // scroll to the first message of the new ones
          const firstMessageId = createMessageId(currentConversation[0]);
          document.getElementById(firstMessageId)?.scrollIntoView({ behavior: 'smooth' });
        }
        return updateAllMessages(prev, getKeyName(id), response);
      });
      const responseFirstMessage = response.data[response.data.length - 1];
      if (responseFirstMessage) {
        const firstMessageTimestamp = new Date(responseFirstMessage.createdAt).getTime();
        const latestTimestamp = new Date(latestMessageRef.current?.created).getTime();
        if (firstMessageTimestamp < latestTimestamp) {
          setTimeout(() => {
            document.getElementById(createMessageId(responseFirstMessage))?.scrollIntoView();
            latestMessageRef.current = responseFirstMessage;
          });
        }
      }
    } finally {
    }
  }, [
    fetchMessages,
    showLoadingMore,
    paramsMessage.ts,
    initialRefetchMessages,
    paramsMessage.conversationId,
  ]);

  const refetchMessages = useCallback(async () => {
    if (conversationId) {
      scrollToImageAfterImageLoadedRef.current = true;
      const currentConversationId = `id-${conversationId}`;
      const currentParamsMessage = { ...paramsMessage };
      if (currentParamsMessage.conversationId !== conversationId) {
        currentParamsMessage.ts = undefined;
        currentParamsMessage.conversationId = conversationId;
      }
      turnOnInitialRefetchMessages();

      const response = await getMessages(conversationId, currentParamsMessage);
      if (response) {
        setAllMessages((prev) => {
          return updateAllMessages(prev, currentConversationId, response);
        });

        sendCustomEvent('deca-livechat-widgets-event-message-new', {
          conversationId: conversationId,
        });

        setTimeout(() => {
          scrollToMessageListBottom();
          turnOffInitialRefetchMessages();
        }, 300);
        return;
      }
      turnOffInitialRefetchMessages();
    }
  }, [
    getMessages,
    paramsMessage,
    conversationId,
    turnOnInitialRefetchMessages,
    turnOffInitialRefetchMessages,
  ]);

  useEffect(() => {
    refetchMessages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conversationId]);

  useEffect(() => {
    if (messages.length > 0 && !isReachingEnd) {
      setShowLoadingMore(true);
    } else {
      setShowLoadingMore(false);
    }
  }, [messages.length, isReachingEnd, setShowLoadingMore]);

  useEffect(() => {
    if (intervalId.current) {
      clearInterval(intervalId.current);
    }
    intervalId.current = setInterval(() => {
      if (isReachingEnd) {
        setShowLoadingMore(false);
        clearInterval(intervalId.current);
      }
    }, 1000);
    return () => {
      if (intervalId.current) {
        clearInterval(intervalId.current);
      }
    };
  }, [isReachingEnd]);

  useEffect(() => {
    // eslint-disable-next-line no-unused-vars
    const imageLoadedHandler = (e: any) => {
      // if it is scroll up then do not scroll
      if (scrollToImageAfterImageLoadedRef.current !== true) {
        return;
      }
      setTimeout(() => {
        scrollToMessageListBottom();
      });
    };
    const unregisterEvent = createCustomEventListener(
      'deca-livechat-image-loaded',
      imageLoadedHandler
    );
    return () => unregisterEvent();
  }, []);

  /**
   * Use this effect to send a custom event , to notify the other users about change of the messages
   * Just to reduce the rendering cycles , improve performance.
   */
  useEffect(() => {
    const handleGetLastMessage = () => {
      const messages = allMessages[getKeyName(conversationId)] ?? [];
      if (messages.length) {
        const messageEndedThisConversation = messages.find((item) => {
          return (
            item?.data?.text === 'conversation.completed' ||
            item?.data?.text === 'conversation.completed.enduser' ||
            item?.data?.text === 'conversation.inwrapup'
          );
        });
        const messageSendToListener = messageEndedThisConversation
          ? messageEndedThisConversation
          : messages.slice(-1)?.[0];

        sendCustomEvent('deca-livechat-update-message-latest', {
          message: messageSendToListener || {},
        });
      }
    };
    // eslint-disable-next-line no-unused-vars
    const sendLastMessage = (event?: CustomEvent) => {
      handleGetLastMessage();
      setTimeout(() => handleGetLastMessage(), 500);
      setTimeout(() => handleGetLastMessage(), 1000);
    };
    const cleanUp = createCustomEventListener('deca-livechat-messages-get-last-messages', () => {
      sendLastMessage;
    });

    // Auto send last message for audiences.
    sendLastMessage();

    return () => {
      cleanUp();
    };
  }, [allMessages, conversationId]);

  return useMemo(
    () => ({
      loading,
      messages,
      showLoadingMore,
      getMoreMessages,
      noMessage: isEmpty,
      initialRefetchMessages,
      isReachingEnd: isReachingEnd,
      addNewMessageToAllMessagesHandler,
      fetchConversationMessages: fetchMessages,
      updateMessageParamsHandler: setParamsMessage,
    }),
    [
      isEmpty,
      loading,
      messages,
      isReachingEnd,
      showLoadingMore,
      initialRefetchMessages,
      fetchMessages,
      getMoreMessages,
      addNewMessageToAllMessagesHandler,
    ]
  );
};

export default useMessagePaging;
