import { createStyles } from '@mantine/emotion';
import { useMemo } from 'react';

const useStyle = createStyles((theme) => ({
  button: {
    '&:disabled': {
      backgroundColor: '#9ea1f7',
      color: '#fff',
    },
  },
  actionIcon: {
    '&:disabled': {
      color: theme.colors.gray[4],
      backgroundColor: 'unset',
    },
  },
  text: {
    color: '#',
  },
}));

export default function useCustomStyle() {
  const { classes } = useStyle();

  return useMemo(() => ({ classes }), [classes]);
}
