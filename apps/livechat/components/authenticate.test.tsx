import { useAuth0 } from '@auth0/auth0-react';
import { ReactOptions } from 'i18next';
import Error from 'next/error';
import { useRouter } from 'next/router';
import { ReactNode } from 'react';
/* eslint-disable react/display-name */
import { useAppContext } from '../modules/appContext';
import { renderWithProvider } from '../utils/testing';
import AuthenticateLayer from './authenticate'; // replace with your actual path

jest.mock('@auth0/auth0-react', () => ({
  useAuth0: jest.fn(),
}));
jest.mock('../modules/appContext');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
}));

jest.mock('next/error', () =>
  jest.fn((props) => {
    if (!props) {
      return <div>Error</div>;
    }
    if (!props.title) {
      return <div>Error - {props.statusCode}</div>;
    }
    return (
      <div>
        {props.title} - {props.statusCode}
      </div>
    );
  })
);

jest.mock('./notAuthenticated', () => () => <div>NotAuthenticate</div>); // replace with your actual path
jest.mock('@mantine/core', () => ({
  ...jest.requireActual('@mantine/core'),
  Loader: jest.fn(() => <div>Loading...</div>),
}));
jest.mock('react-i18next', () => ({
  initReactI18next: { type: '3rdParty', init: jest.fn() },
  useTranslation: (file: string) => ({ t: (key: string, data: any) => key, i18n: {} }),
}));
jest.mock('@blocknote/core/fonts/inter.css', () => null);
jest.mock('@blocknote/shadcn/style.css', () => null);
jest.mock('react-querybuilder/dist/query-builder.css', () => null);
jest.mock('react-contexify/dist/ReactContexify.css', () => null);
jest.mock('react-shadow', () => null);
jest.mock('react-phone-input-2/lib/style.css', () => null);
jest.mock('../tolgee', () => null);
jest.mock('@resola-ai/ui', () => null);
jest.mock('@resola-ai/services-shared', () => null);
const Layout = ({ children }: { children?: ReactNode }) => {
  return <AuthenticateLayer>{children}</AuthenticateLayer>;
};
describe('AuthenticateLayer', () => {
  it('renders loading state', () => {
    (useAuth0 as jest.Mock).mockReturnValue({ isLoading: true });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: '' });

    const { getByText } = renderWithProvider(<Layout />);
    expect(getByText('Loading...')).toBeInTheDocument(); // replace 'Loading...' with the actual text in your loader
  });

  it('renders error state when query is not exist', () => {
    (useRouter as jest.Mock).mockReturnValue({ query: undefined });
    (useAuth0 as jest.Mock).mockReturnValue({ error: new Error('Test error' as any) });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: '' });
    const { getByText } = renderWithProvider(<Layout />);
    expect(getByText('Error - 500')).toBeInTheDocument();
  });

  it('renders not authenticated state', () => {
    (useRouter as jest.Mock).mockReturnValue({ query: {} });
    (useAuth0 as jest.Mock).mockReturnValue({ isAuthenticated: false });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: '' });
    const { getByText } = renderWithProvider(<Layout />);
    expect(getByText('NotAuthenticate')).toBeInTheDocument();
  });

  it('renders children when authenticated', () => {
    (useAuth0 as jest.Mock).mockReturnValue({ isAuthenticated: true });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: 'test-token' });
    const { getByText } = renderWithProvider(<Layout>Test children</Layout>);
    expect(getByText('Test children')).toBeInTheDocument();
  });

  it('render error state when query is valid', () => {
    (useRouter as jest.Mock).mockReturnValue({
      query: { error: 'invalid_request', error_description: 'test error' },
    });
    (useAuth0 as jest.Mock).mockReturnValue({ error: new Error('Test error' as any) });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: '' });
    const { getByText } = renderWithProvider(<Layout />);
    expect(getByText('Test error - 403')).toBeInTheDocument();
  });

  it('render error state when query is invalid', () => {
    (useRouter as jest.Mock).mockReturnValue({
      query: { error: '', error_description: 'test error' },
    });
    (useAuth0 as jest.Mock).mockReturnValue({ error: new Error('Test error' as any) });
    (useAppContext as jest.Mock).mockReturnValue({ accessToken: '' });
    const { getByText } = renderWithProvider(<Layout />);
    expect(getByText('error400')).toBeInTheDocument();
  });
});
