import { render, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../utils/testing';
import ViewOnly from './index';

// Mock the useTranslate hook
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock the IconEye component
jest.mock('@tabler/icons-react', () => ({
  IconEye: () => <span data-testid='icon-eye' />,
}));

// Test suite for ViewOnly component
describe('ViewOnly Component', () => {
  test('renders without crashing', () => {
    renderWithProvider(<ViewOnly />);
    expect(screen.getByTestId('icon-eye')).toBeInTheDocument();
    expect(screen.getByText('viewonly')).toBeInTheDocument();
  });
});
