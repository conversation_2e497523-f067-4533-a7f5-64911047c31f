import { fireEvent, render } from '@testing-library/react';
import { ReactOptions } from 'i18next';
import React from 'react';
import { renderWithProvider } from '../../utils/testing';
import Preview from './index';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
}));

describe('Preview Component', () => {
  // without the imageUrl and with the children
  it('renders the component without the imageUrl and with the children', () => {
    const { getByText } = renderWithProvider(
      <Preview imageUrl=''>
        <div>Preview Component</div>
      </Preview>
    );

    // Check if the children is rendered
    expect(getByText('Preview Component')).toBeInTheDocument();
  });

  // with the imageUrl and with the children and click on the preview component, check for imageName
  it('renders the component with the imageUrl and click on the preview component, check for imageName', async () => {
    const imageUrl =
      'https://images.unsplash.com/photo-1699366089285-1170e63b78a5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1fHx8ZW58MHx8fHx8';
    const imageName = 'image.png';
    const component = renderWithProvider(
      <Preview imageUrl={imageUrl} imageName={imageName} showFileName={true}>
        <div>Preview Component</div>
      </Preview>
    );

    const container = component.getByTestId('preview-container');

    // Simulate a click event on the Preview component
    fireEvent.click(container);

    // Check if the image is rendered
    expect(await component.findByTestId('preview-image')).toBeInTheDocument();
    expect(await component.findByTestId('preview-file-name')).toBeInTheDocument();
  });

  it('renders the download button when showDownloadButton is true', async () => {
    const imageUrl =
      'https://images.unsplash.com/photo-1699366089285-1170e63b78a5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1fHx8ZW58MHx8fHx8';
    const imageName = 'image.png';
    const component = renderWithProvider(
      <Preview imageUrl={imageUrl} imageName={imageName} showDownloadButton={true}>
        <div>Preview Component</div>
      </Preview>
    );

    // Simulate a click event on the Preview component
    fireEvent.click(component.getByTestId('preview-container'));

    // Check if the download button is rendered
    expect(await component.findByTestId('preview-download-button')).toBeInTheDocument();
  });

  it('does not render the download button when showDownloadButton is false', () => {
    const imageUrl =
      'https://images.unsplash.com/photo-1699366089285-1170e63b78a5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1fHx8ZW58MHx8fHx8';
    const imageName = 'image.png';
    const component = renderWithProvider(
      <Preview imageUrl={imageUrl} imageName={imageName} showDownloadButton={false}>
        <div>Preview Component</div>
      </Preview>
    );

    // Simulate a click event on the Preview component
    fireEvent.click(component.getByTestId('preview-container'));

    // Check if the download button is not rendered
    expect(component.queryByTestId('preview-download-button')).toBeNull();
  });

  it('renders the file name when showFileName is true', async () => {
    const imageUrl =
      'https://images.unsplash.com/photo-1699366089285-1170e63b78a5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1fHx8ZW58MHx8fHx8';
    const imageName = 'image.png';

    const component = renderWithProvider(
      <Preview imageUrl={imageUrl} imageName={imageName} showFileName={true}>
        <div>Preview Component</div>
      </Preview>
    );
    // Simulate a click event on the Preview component
    fireEvent.click(component.getByTestId('preview-container'));

    // Check if the file name is rendered
    expect(await component.findByTestId('preview-file-name')).toBeInTheDocument();
  });

  it('does not render the file name when showFileName is false', async () => {
    const imageUrl =
      'https://images.unsplash.com/photo-1699366089285-1170e63b78a5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHw1fHx8ZW58MHx8fHx8';
    const imageName = 'image.png';
    const component = renderWithProvider(
      <Preview imageUrl={imageUrl} imageName={imageName} showFileName={false}>
        <div>Preview Component</div>
      </Preview>
    );

    // Simulate a click event on the Preview component
    fireEvent.click(component.getByTestId('preview-container'));

    // Check if the file name is not rendered
    expect(component.queryByTestId('preview-file-name')).toBeNull();
  });
});
