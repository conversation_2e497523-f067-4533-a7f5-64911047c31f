import styled from '@emotion/styled';
import { CloseButton, Flex, Image, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import {
  calculateImageRatio,
  getCurrentTimeInFullDateTimeFormat,
  getImageMetaData,
} from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import React, { useEffect, useLayoutEffect, useRef, useState, useMemo } from 'react';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';
import { VIDEO_EXTENSIONS } from '../../modules/Preview/constants';
import DownloadPictureButton from './DownloadPictureButton';

const getFileExtension = () => {
  // TODO: later we need to get the file extension from the backend
  return '.png';
};
const Container = styled.div`
    position: relative;
    cursor: pointer;
    // on hover
    &:hover {
        opacity: 0.9;
    }
    background-color: #ced4da;
    border-radius: 50%;
`;

const FileViewer = styled.div`
    display: flex;
    flex-direction: column;
    font-size: 15px;
    font-weight: 400;
    height: 70vh;
    line-height: 1.46668;
    overflow: hidden;
    position: relative;
`;

const FileViewerContent = styled.div`
    flex: 1 1 0;
    overflow: hidden;
    position: relative;
`;

const ImageViewerContainer = styled.div`
    height: 100%;
    width: 100%;
`;

const ImageViewer = styled.div`
    align-items: center;
    display: flex;
    justify-content: center;
    position: relative;
    height: 100%;
    width: 100%;
`;

type Props = {
  imageUrl: string;
  imageName?: string;
  children: React.ReactNode;
  showDownloadButton?: boolean;
  showFileName?: boolean;
};

interface IMetaData {
  width: number;
  height: number;
}

const useStyles = createStyles(() => ({
  mediaContainer: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    background: 'none',
    border: 'none',
  },
}));

const Preview = ({
  imageUrl,
  children,
  imageName,
  showDownloadButton = true,
  showFileName = true,
}: Props) => {
  const { t } = useTranslate('workspace');
  const fileViewerContentRef = useRef();
  const [opened, { open, close }] = useDisclosure(false);
  const [, setVideoDimensions] = useState<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });

  const { classes } = useStyles();

  const isVideo = useMemo(
    () => VIDEO_EXTENSIONS.some((ext) => imageUrl?.endsWith(ext)),
    [imageUrl]
  );

  const isPdf = useMemo(() => imageUrl?.toLowerCase().endsWith('.pdf'), [imageUrl]);

  const fileExtension = isVideo ? imageUrl?.split('.')?.pop() : getFileExtension();
  const currentTime = getCurrentTimeInFullDateTimeFormat();
  const defaultMediaNameKey = isVideo ? 'video' : 'image';
  const defaultMediaName = `${t(defaultMediaNameKey)}-${currentTime}.${fileExtension}`;
  const [fileViewerContent, setFileViewerContent] = React.useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });

  const [meta, setMeta] = React.useState<IMetaData>({
    width: 0,
    height: 0,
  });

  const displayMediaName = imageName || defaultMediaName;

  // calculate the image size based on the screen size, i want to show the image as big as possible in side of the screen
  const imageWidth =
    meta.width *
    calculateImageRatio(meta.width, meta.height, fileViewerContent.width, fileViewerContent.height);
  const imageHeight =
    meta.height *
    calculateImageRatio(meta.width, meta.height, fileViewerContent.width, fileViewerContent.height);

  useEffect(() => {
    if (!imageUrl) return;
    setTimeout(() => {
      getImageMetaData(imageUrl).then((metaData) => {
        setMeta(metaData);
      });
    }, 100);
  }, [imageUrl]);

  useLayoutEffect(() => {
    if (!opened) return;
    setTimeout(() => {
      if (fileViewerContentRef.current) {
        const { clientWidth, clientHeight } = fileViewerContentRef.current as any;
        setFileViewerContent({ width: clientWidth, height: clientHeight });
      }
    }, 100);
  }, [opened]);

  const handleVideoLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement>) => {
    const { videoWidth, videoHeight } = event.currentTarget;
    setVideoDimensions({ width: videoWidth, height: videoHeight });
  };

  if (!imageUrl) return children;
  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        centered
        size={'100%'}
        withCloseButton={false}
        transitionProps={{ transition: 'fade', duration: 200 }}
        sx={{
          '.mantine-Modal-body': {
            position: 'relative',
          },
          '.mantine-Paper-root': {
            overflow: 'hidden',
          },
          overflow: 'hidden',
        }}
      >
        <Flex
          sx={{
            position: 'absolute',
            right: '20px',
            top: '20px',
            zIndex: 9,
            overflow: 'hidden',
          }}
        >
          <CloseButton data-testid={'close-button'} size={rem(24)} onClick={close} />
        </Flex>
        <Flex
          direction={'column'}
          px={74}
          pb={40}
          gap={30}
          sx={{
            position: 'relative',
          }}
        >
          <Flex align={'center'} justify={'space-between'}>
            <Text
              color={'#000000'}
              fw={700}
              size={rem(18)}
              sx={{ display: showFileName ? undefined : 'none' }}
              data-testid={showFileName ? 'preview-file-name' : undefined}
            >
              {displayMediaName}
            </Text>
            <Flex
              sx={{ display: showDownloadButton ? undefined : 'none' }}
              data-testid={showDownloadButton ? 'preview-download-button' : undefined}
            >
              <DownloadPictureButton name={displayMediaName} url={imageUrl} />
            </Flex>
          </Flex>
          <FileViewer>
            <FileViewerContent ref={fileViewerContentRef}>
              <ImageViewerContainer>
                <ImageViewer>
                  {isVideo ? (
                    <video
                      src={imageUrl}
                      controls
                      autoPlay
                      onLoadedMetadata={handleVideoLoadedMetadata}
                      className={classes.mediaContainer}
                    />
                  ) : (
                    <TransformWrapper maxScale={2}>
                      {() => (
                        <React.Fragment>
                          <TransformComponent>
                            <Image
                              src={imageUrl}
                              alt={displayMediaName}
                              width={imageWidth}
                              height={imageHeight}
                              data-testid={'preview-image'}
                              style={{
                                width: imageWidth || undefined,
                                height: imageHeight || undefined,
                              }}
                            />
                          </TransformComponent>
                        </React.Fragment>
                      )}
                    </TransformWrapper>
                  )}
                </ImageViewer>
              </ImageViewerContainer>
            </FileViewerContent>
          </FileViewer>
        </Flex>
      </Modal>
      <Container
        data-testid={'preview-container'}
        onClick={open}
        style={{ backgroundColor: isVideo || isPdf ? 'transparent' : '#ced4da' }}
      >
        {children}
      </Container>
    </>
  );
};

export default Preview;
