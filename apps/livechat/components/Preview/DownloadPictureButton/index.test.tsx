import { fireEvent } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../../utils/testing';
import DownloadPictureButton from './index';

// Mock the useTranslate hook
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key, params) => key,
  }),
}));

test('DownloadPictureButton renders correctly with react-test-renderer', () => {
  const url = 'https://example.com/image.jpg';
  const name = 'example-image.jpg';

  // use the `render` function from `@testing-library/react` here
  const { getByTestId } = renderWithProvider(<DownloadPictureButton url={url} name={name} />);
  const downloadButton = getByTestId('download-picture-button');

  // expect the button to be in the document
  expect(downloadButton).toBeInTheDocument();
});

test('DownloadPictureButton triggers download with fireEvent', () => {
  const url = 'https://example.com/image.jpg';
  const name = 'example-image.jpg';
  // Mock the `click` method of the anchor element
  const clickSpy = jest.fn();
  const anchorMock = { href: url, download: name, target: '_blank', click: clickSpy };

  const { getByTestId } = renderWithProvider(<DownloadPictureButton url={url} name={name} />);
  const downloadButton = getByTestId('download-picture-button');

  const createAElementSpy = jest.spyOn(document, 'createElement');

  createAElementSpy.mockReturnValue(anchorMock as any);

  fireEvent.click(downloadButton);

  expect(createAElementSpy).toHaveBeenCalled();
  expect(createAElementSpy.mock.calls[0][0]).toBe('a');
  expect(createAElementSpy.mock.results[0].value.href).toBe(url);
  expect(createAElementSpy.mock.results[0].value.download).toBe(name);
  expect(createAElementSpy.mock.results[0].value.target).toBe('_blank');
  // expect the click to have been called
  expect(clickSpy).toHaveBeenCalled();

  createAElementSpy.mockRestore();
});
