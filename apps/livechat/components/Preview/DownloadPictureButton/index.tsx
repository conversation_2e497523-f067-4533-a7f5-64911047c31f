import { Button } from '@mantine/core';
import { IconDownload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React from 'react';
interface Props {
  url: string;
  name: string;
}
const DownloadPictureButton: React.FC<Props> = ({ url, name }) => {
  const { t } = useTranslate('common');
  const downloadLabel = t('download');
  const downloadPictureHandler = () => {
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    // open in new tab
    a.target = '_blank';
    a.click();
  };
  return (
    <Button
      data-testid={'download-picture-button'}
      leftSection={<IconDownload />}
      color='navy.0'
      variant='outline'
      radius={8}
      onClick={downloadPictureHandler}
    >
      {downloadLabel}
    </Button>
  );
};

export default DownloadPictureButton;
