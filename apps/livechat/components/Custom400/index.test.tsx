import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../utils/testing';
import Custom400 from './index';

// Mock useAppContext
const mockLogout = jest.fn();
jest.mock('../../modules/appContext', () => ({
  useAppContext: () => ({ logout: mockLogout }),
}));

// Mock useTranslate
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock useRouter
const mockUseRouter = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => mockUseRouter(),
}));

// Mock ShieldIcon
jest.mock('@resola-ai/ui', () => ({
  ShieldIcon: () => <span data-testid='shield-icon' />,
}));

// Mock IconBrowserX
jest.mock('@tabler/icons-react', () => ({
  IconBrowserX: (props: any) => <span data-testid='icon-browser-x' {...props} />,
}));

describe('Custom400', () => {
  beforeEach(() => {
    mockLogout.mockClear();
  });

  it('renders error400 and logout button by default', () => {
    mockUseRouter.mockReturnValue({ query: {} });
    renderWithProvider(<Custom400 />);
    expect(screen.getByTestId('icon-browser-x')).toBeInTheDocument();
    expect(screen.getByText('error400')).toBeInTheDocument();
    expect(screen.getByText('logout')).toBeInTheDocument();
  });

  it('calls logout when logout button is clicked', () => {
    mockUseRouter.mockReturnValue({ query: {} });
    renderWithProvider(<Custom400 />);
    fireEvent.click(screen.getByText('logout'));
    expect(mockLogout).toHaveBeenCalled();
  });

  it('renders IP block error when query matches', () => {
    mockUseRouter.mockReturnValue({
      query: { error: 'access_denied', error_description: 'ip_blocked' },
    });
    renderWithProvider(<Custom400 />);
    expect(screen.getByTestId('shield-icon')).toBeInTheDocument();
    expect(screen.getByText('title_ip_block')).toBeInTheDocument();
    expect(screen.getByText('description_ip_block')).toBeInTheDocument();
    expect(screen.getByText('button_label_back_to_login')).toBeInTheDocument();
  });

  it('calls logout when IP block button is clicked', () => {
    mockUseRouter.mockReturnValue({
      query: { error: 'access_denied', error_description: 'ip_blocked' },
    });
    renderWithProvider(<Custom400 />);
    fireEvent.click(screen.getByText('button_label_back_to_login'));
    expect(mockLogout).toHaveBeenCalled();
  });
});
