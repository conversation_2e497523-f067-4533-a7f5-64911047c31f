import { Button, Flex, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { ShieldIcon } from '@resola-ai/ui';
import { IconBrowserX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useRouter } from 'next/router';
import { useAppContext } from '../../modules/appContext';

const useStyle = createStyles((theme) => ({
  box: {
    width: '100vw',
    height: '100vh',
    padding: '20px',
    color: theme.colors.dark[5],
  },
  title: {
    color: '#525252',
    fontSize: '20px',
    fontWeight: 700,
  },
  ip_block_text: {
    textAlign: 'center',
    color: '#8F8F8F',
    fontSize: '14px',
    fontWeight: 400,
    whiteSpace: 'pre',
    wordBreak: 'break-word',
    lineBreak: 'auto',
  },
  text: {
    whiteSpace: 'pre-line',
    textAlign: 'center',
    fontSize: '20px',
    fontWeight: 700,
  },
  icon: {
    color: theme.colors.violet[5],
    width: rem(70),
    height: rem(70),
  },
}));

const Custom400 = () => {
  const { logout } = useAppContext();
  const { t } = useTranslate('common');
  const { query } = useRouter();
  const { classes } = useStyle();

  return (
    <Flex
      direction={'column'}
      justify={'center'}
      align={'center'}
      gap='30px'
      className={classes.box}
    >
      {query?.error === 'access_denied' && query?.error_description === 'ip_blocked' ? (
        <AccessDenyDueToIpBlock />
      ) : (
        <>
          <IconBrowserX className={classes.icon} />
          <Text className={classes.text}>{t('error400')}</Text>
          <Button variant='filled' radius='md' color='violet' onClick={logout}>
            {t('logout')}
          </Button>
        </>
      )}
    </Flex>
  );
};

export default Custom400;

function AccessDenyDueToIpBlock() {
  const { t } = useTranslate('common');
  const { logout } = useAppContext();
  const { classes } = useStyle();
  return (
    <>
      <Flex direction={'column'} align='center' justify='center'>
        <ShieldIcon />
        <Title className={classes.title} mt={10}>
          {t('title_ip_block')}
        </Title>
        <Text className={classes.ip_block_text} mt={'md'}>
          {t('description_ip_block')}
        </Text>
        <Button variant='filled' radius='sm' color='navy.0' px='sm' mt={30} onClick={logout}>
          <Text size={rem(16)}>{t('button_label_back_to_login')}</Text>
        </Button>
      </Flex>
    </>
  );
}
