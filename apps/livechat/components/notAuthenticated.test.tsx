import { useAuth0 } from '@auth0/auth0-react';
import { render } from '@testing-library/react';
import React from 'react';
import NotAuthenticate from './notAuthenticated';

jest.mock('@auth0/auth0-react');

describe('NotAuthenticate', () => {
  beforeEach(() => {
    (useAuth0 as jest.Mock).mockReturnValue({
      loginWithRedirect: jest.fn(),
    });
  });

  it('should call loginWithRedirect on render', () => {
    render(<NotAuthenticate />);
    expect(useAuth0().loginWithRedirect).toHaveBeenCalled();
  });

  it('should render null', () => {
    const { container } = render(<NotAuthenticate />);
    expect(container.firstChild).toBeNull();
  });
});
