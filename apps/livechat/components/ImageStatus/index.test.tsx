import { Status } from '@resola-ai/models';
import { screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../utils/testing';
import { ImageStatus } from './index';

// Mock Mantine notifications to avoid getDefaultZIndex error
jest.mock('@mantine/notifications', () => ({ notifications: {} }));

// Mock createTheme and <PERSON>tineProvider to avoid theme errors
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    createTheme: () => ({}),
    MantineProvider: ({ children }: any) => <div>{children}</div>,
    Avatar: (props: any) => <img data-testid='avatar' {...props} />,
  };
});

describe('ImageStatus', () => {
  const picture = 'https://example.com/avatar.png';

  it('renders Avatar with correct src', () => {
    renderWithProvider(<ImageStatus picture={picture} status={Status.Offline} />);
    const avatar = screen.getByTestId('avatar');
    expect(avatar).toHaveAttribute('src', picture);
  });

  it('shows green status indicator when status is Available', () => {
    renderWithProvider(<ImageStatus picture={picture} status={Status.Available} />);
    const indicator = screen.getByTestId('avatar').nextSibling as HTMLElement;
    expect(indicator).toHaveStyle('background-color: #40C057');
  });

  it('shows gray status indicator when status is Offline', () => {
    renderWithProvider(<ImageStatus picture={picture} status={Status.Offline} />);
    const indicator = screen.getByTestId('avatar').nextSibling as HTMLElement;
    expect(indicator).toHaveStyle('background-color: #868E96');
  });
});
