import { render, screen } from '@testing-library/react';
import { ReactOptions } from '@tolgee/react';
import { renderWithProvider } from '../utils/testing';
import CustomMedia from './customMedia';

jest.mock('@tolgee/react', () => ({
  useTranslate: (ns?: string[] | string, options?: ReactOptions) => ({
    t: (key) => key,
  }),
  useTolgee: () => ({
    getLanguage: jest.fn().mockReturnValue('en'),
  }),
}));

describe('CustomMedia', () => {
  it('renders an image with the provided URL and alt text', () => {
    const url = 'https://example.com/image.jpg';
    const alt = 'Example Image';
    renderWithProvider(<CustomMedia url={url} alt={alt} />);

    const imageElement = screen.getByAltText(alt);
    expect(imageElement).toBeInTheDocument();
    expect(imageElement).toHaveAttribute('src', url);
  });

  it('renders an image with an empty alt text if alt prop is not provided', () => {
    const url = 'https://example.com/image.jpg';
    renderWithProvider(<CustomMedia url={url} />);

    const imageElement = screen.getByAltText('');
    expect(imageElement).toBeInTheDocument();
    expect(imageElement).toHaveAttribute('src', url);
  });

  it('renders a video with the provided URL', () => {
    const url = 'https://example.com/video.mp4';
    renderWithProvider(<CustomMedia url={url} />);

    const videoElement = screen.getByTestId('video-element');
    expect(videoElement).toBeInTheDocument();
    expect(videoElement).toHaveAttribute('src', url);
  });
});
