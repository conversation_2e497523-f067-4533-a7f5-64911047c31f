import { useAuth0 } from '@auth0/auth0-react';
import { Loader } from '@mantine/core';
import Error from 'next/error';
import { useRouter } from 'next/router';
import React from 'react';
import { useAppContext } from '../modules/appContext';
import Custom400 from './Custom400';
import NotAuthenticate from './notAuthenticated';

const AuthenticateLayer: React.FC<any> = ({ children }) => {
  const router = useRouter();
  const { error, isAuthenticated, isLoading } = useAuth0();
  const { accessToken } = useAppContext();

  if (isLoading) {
    return (
      <div
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          // this is to prevent the change of position when the loader is shown, as mantine at first is not loaded fully yet on init.
        }}
      >
        <Loader color={'navy.0'} />
      </div>
    );
  }

  if (error && accessToken === '') {
    if (router.query) {
      const { error: errorName, error_description: errorDescription } = router.query;
      if (typeof errorDescription === 'string' && typeof errorName === 'string') {
        if (errorName === 'invalid_request') {
          return (
            <Error
              statusCode={403}
              title={errorDescription.charAt(0).toUpperCase() + errorDescription.slice(1)}
            />
          );
        }
      }
      return <Custom400 />; // <Error statusCode={400} title={errorDescription} />;
    }
    return <Error statusCode={500} title={error.message} />;
  }

  if (!isAuthenticated && accessToken === '') {
    return <NotAuthenticate />;
  }

  if (accessToken) {
    return <>{children}</>;
  }
  return null;
};

export default AuthenticateLayer;
