import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../../utils/testing';
import { EmojiPicker } from './EmojiPicker';

// Mock emoji-mart Picker
const mockOnSelect = jest.fn();
const mockPicker = jest.fn();
jest.mock('emoji-mart', () => ({
  Picker: (props: any) => {
    mockPicker(props);
    return (
      <button data-testid='picker' onClick={() => props.onSelect({ native: '😀' })}>
        Emoji
      </button>
    );
  },
}));

// Mock useTranslate
jest.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock <PERSON> createStyles and MantineProvider
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    createStyles: () => () => ({ classes: { customStyle: 'custom-style' } }),
    MantineProvider: ({ children }: any) => <div>{children}</div>,
  };
});

// Mock MantineEmotionProvider
jest.mock('@mantine/emotion', () => {
  const actual = jest.requireActual('@mantine/emotion');
  return {
    ...actual,
    MantineEmotionProvider: ({ children }: any) => <div>{children}</div>,
    createStyles: () => () => ({ classes: { customStyle: 'custom-style' } }),
  };
});

describe('EmojiPicker', () => {
  it('renders Picker', () => {
    renderWithProvider(<EmojiPicker onSelect={mockOnSelect} />);
    expect(screen.getByTestId('picker')).toBeInTheDocument();
  });

  it('calls onSelect with emoji when clicked', () => {
    renderWithProvider(<EmojiPicker onSelect={mockOnSelect} />);
    fireEvent.click(screen.getByTestId('picker'));
    expect(mockOnSelect).toHaveBeenCalledWith('😀');
  });
});
