import {
  rem,
  Container,
  Group,
  Textarea,
  TextareaProps,
  Text,
  ActionIcon,
  Popover,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { BaseSyntheticEvent, useCallback, useMemo, useRef, useState } from 'react';
import { useFocusWithin } from '@mantine/hooks';
import { IconMoodSmile } from '@tabler/icons-react';
import { EmojiPicker } from './EmojiPicker';
import { useTranslate } from '@tolgee/react';

type Props = Omit<TextareaProps & React.RefAttributes<HTMLTextAreaElement>, 'onChange'> & {
  // eslint-disable-next-line no-unused-vars
  onChange?: (val: string) => void;
  colorIcon?: string;
  disableCountChar?: boolean;
};

const removeLineFeedWindowStyle = (text: string | undefined) => {
  return (text || '')?.replaceAll('\r\n', '\n');
};

const useStyle = createStyles(
  (
    theme,
    {
      responsiveScreen = false,
      focusedEditor = false,
      error = false,
    }: { responsiveScreen: boolean; focusedEditor: boolean; error: boolean }
  ) => ({
    container: {
      backgroundColor: 'white',
      border: `1px solid ${
        error ? 'red' : focusedEditor ? theme.colors.navy[0] : theme.colors.gray[4]
      } `,
      boxShadow: focusedEditor
        ? `0px 0px 1px 1px ${error ? '#ffabab' : '#1F84F4CC'}, 0 0 4px 0px ${
            error ? '#ff000024' : '#81dcffad'
          }`
        : 'none',
      borderRadius: '5px',
      padding: `0 0px 10px 10px`,
    },
    textArea: {
      '& textarea': { paddingRight: '10px', paddingLeft: '5px' },
      lineHeight: '18px',
      fontWeight: 400,
      fontSize: responsiveScreen ? '10px' : '12px',
      '::placeholder': {
        fontSize: responsiveScreen ? '10px' : '12px',
      },
      textarea: {
        padding: responsiveScreen ? '10px' : undefined,
        '&:disabled': {
          marginLeft: '-5px',
          paddingLeft: '10px',
          transform: 'scaleX(1.01)',
        },
        '&.mantine-Input-input:focus-within': {
          outline: 'none',
          borderColor: 'transparent',
        },
      },
    },
    textLength: {
      fontSize: 13,
      fontWeight: 400,
      color: theme.colors.gray[5],
    },
    popoverDropdown: {
      padding: '0',
    },
  })
);

const MAX_CHAR_INPUT = 500;

export function TextAreaEditor({
  placeholder,
  onChange,
  className,
  value,
  error,
  maxLength,
  disabled = false,
  colorIcon = 'navy.1',
  disableCountChar = false,
  ...rest
}: Props) {
  const { t } = useTranslate('workspace');
  const savedRangeRef = useRef<{ selectionStart: number | null; selectionEnd: number | null }>({
    selectionStart: null,
    selectionEnd: null,
  });
  const { ref: textRef, focused: focusedTextArea } = useFocusWithin<HTMLTextAreaElement>();
  const [internalVal, setInternalVal] = useState(value?.toString() || '');
  const { classes, cx } = useStyle({
    responsiveScreen: false,
    focusedEditor: focusedTextArea,
    error: !!error,
  });

  const currentInputLength = useMemo(() => {
    if (value) return value.toString().length;
    if (internalVal) return internalVal.length;
    return 0;
  }, [internalVal, value]);

  const handleChange = useCallback(
    (e: BaseSyntheticEvent) => {
      const value = e.currentTarget.value;
      setInternalVal(value);
      onChange?.(value);
    },
    [onChange]
  );

  const handleKeyUp = useCallback(() => {
    if (!textRef.current) return;
    savedRangeRef.current = {
      selectionStart: textRef.current.selectionStart,
      selectionEnd: textRef.current.selectionEnd,
    };
  }, [textRef]);

  const handleInsertEmoji = useCallback(
    (val: string) => {
      if (!textRef.current) return;
      const maxLengthChar = maxLength || MAX_CHAR_INPUT;
      const element = textRef.current;
      const currentContent = element.value;
      const inputLength = val.length;
      if (currentContent?.length >= maxLengthChar) return;
      if (savedRangeRef.current.selectionStart === null) {
        setInternalVal(val);
        onChange?.(val);
        element.selectionStart = inputLength;
        element.selectionEnd = inputLength;
        element.focus();
        savedRangeRef.current = {
          selectionEnd: inputLength,
          selectionStart: inputLength,
        };
      }
      if (savedRangeRef.current.selectionStart !== null) {
        const { selectionStart, selectionEnd } = savedRangeRef.current;
        const before = currentContent.substring(0, selectionStart);
        const after = currentContent.substring(selectionEnd, currentContent.length);
        const newContent = `${before}${val}${after}`;
        setInternalVal(newContent);
        onChange?.(newContent);
        const newRange = selectionStart + inputLength;
        element.selectionStart = element.selectionEnd = newRange;

        savedRangeRef.current = {
          selectionStart: newRange,
          selectionEnd: newRange,
        };
        element.setSelectionRange(newRange, newRange);
      }
    },
    [textRef, onChange, maxLength]
  );

  const handlePasteAsPlainText = useCallback((e: ClipboardEvent) => {
    e.preventDefault();
    let text = e?.clipboardData?.getData('text/plain');
    text = removeLineFeedWindowStyle(text);
    document.execCommand('insertText', false, text);
  }, []);

  const handleMouseUpToSetSelectedText = useCallback(() => {
    savedRangeRef.current = {
      selectionStart: null,
      selectionEnd: null,
    };
    if (!textRef.current) return;
    const selectionStart = textRef.current.selectionStart;
    const selectionEnd = textRef.current.selectionEnd;
    savedRangeRef.current = {
      selectionStart: selectionStart,
      selectionEnd: selectionEnd,
    };
  }, [textRef]);

  return (
    <>
      <Container fluid className={classes.container}>
        <Textarea
          ref={textRef}
          variant='unstyled'
          aria-label='Text Area Editor'
          onKeyUp={handleKeyUp}
          onChange={handleChange}
          value={value || internalVal}
          disabled={disabled}
          maxLength={disableCountChar ? undefined : maxLength || MAX_CHAR_INPUT}
          className={cx([classes.textArea, className])}
          onMouseUp={handleMouseUpToSetSelectedText}
          placeholder={placeholder || t('placeholder_text_area')}
          onPaste={(e) => handlePasteAsPlainText(e as unknown as ClipboardEvent)}
          {...rest}
        />
        <Group justify='space-between' mr={10} mt={3}>
          <Group>
            <EmojiPickerButton
              onSelect={handleInsertEmoji}
              disabled={disabled}
              colorIcon={colorIcon}
            />
          </Group>
          {!disableCountChar && (
            <Group>
              <Text className={classes.textLength}>{`${currentInputLength}/${
                maxLength || MAX_CHAR_INPUT
              } ${t('character')}`}</Text>
            </Group>
          )}
        </Group>
      </Container>
      <Group mt={5} style={{ display: error ? 'flex' : 'none' }}>
        <Text c='red' size={rem(12)}>
          {error}
        </Text>
      </Group>
    </>
  );
}

// eslint-disable-next-line no-unused-vars
function EmojiPickerButton({
  onSelect,
  disabled,
  colorIcon,
}: {
  // eslint-disable-next-line no-unused-vars
  onSelect: (val: string) => void;
  disabled?: boolean;
  colorIcon?: string;
}) {
  const { classes } = useStyle({ responsiveScreen: false, focusedEditor: false, error: false });
  const [opened, setOpened] = useState(false);
  const handleOnSelect = useCallback(
    (val: string) => {
      setOpened(false);
      onSelect(val);
    },
    [onSelect]
  );

  return (
    <Popover position='top-start' withArrow shadow='md' opened={opened} onChange={setOpened}>
      <Popover.Target>
        <ActionIcon
          variant='transparent'
          color={colorIcon || 'navy.1'}
          size='1.2rem'
          disabled={!!disabled}
          onClick={() => setOpened((o) => !o)}
        >
          <IconMoodSmile />
        </ActionIcon>
      </Popover.Target>
      <Popover.Dropdown className={classes.popoverDropdown}>
        <EmojiPicker onSelect={handleOnSelect} />
      </Popover.Dropdown>
    </Popover>
  );
}
