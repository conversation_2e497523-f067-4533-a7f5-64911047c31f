import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { BaseEmoji, Picker } from 'emoji-mart';
import 'emoji-mart/css/emoji-mart.css';
import { useCallback } from 'react';

const useStyle = createStyles(() => ({
  customStyle: {
    display: 'inherit',
    '& .emoji-mart-anchor-icon': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
  },
}));

// eslint-disable-next-line no-unused-vars
export function EmojiPicker({ onSelect }: { onSelect: (val: string) => void }) {
  const { classes } = useStyle();
  const { t } = useTranslate('common');
  const handleSelectEmoji = useCallback(
    (e: BaseEmoji) => {
      onSelect(e.native);
    },
    [onSelect]
  );
  return (
    <div className={classes.customStyle}>
      <Picker
        title=':grinning:'
        emoji='grinning'
        i18n={{
          // use japanese
          search: t('emoji_picker.search_placeholder'),
          categories: {
            search: t('emoji_picker.search'),
            recent: t('emoji_picker.recent'),
            people: t('emoji_picker.people'),
            nature: t('emoji_picker.nature'),
            foods: t('emoji_picker.foods'),
            activity: t('emoji_picker.activity'),
            places: t('emoji_picker.places'),
            objects: t('emoji_picker.objects'),
            symbols: t('emoji_picker.symbols'),
            flags: t('emoji_picker.flags'),
            custom: t('emoji_picker.custom'),
          },
          notfound: t('emoji_picker.search_notfound'),
        }}
        onSelect={handleSelectEmoji}
        autoFocus={true}
        color='#1D2088'
      />
    </div>
  );
}
