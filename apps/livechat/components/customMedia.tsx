import { Image } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconPlayerPlay } from '@tabler/icons-react';
import React, { useState, useMemo } from 'react';
import ErrorImage from '../modules/Chatbox/MessageHolders/ErrorIcons/ErrorImage';
import ErrorVideo from '../modules/Chatbox/MessageHolders/ErrorIcons/ErrorVideo';
import { VIDEO_EXTENSIONS } from '../modules/Preview/constants';
import Preview from './Preview';

interface CustomMediaProps {
  url: string;
  alt?: string;
  onLoaded?: () => void;
}

const useStyles = createStyles(() => ({
  videoContainer: {
    position: 'relative',
    cursor: 'pointer',
  },
  videoStyle: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    background: 'transparent',
    border: 'none',
    borderRadius: '10px',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    color: 'white',
  },
  imageStyle: {
    borderRadius: '10px',
  },
}));

const CustomMedia: React.FC<CustomMediaProps> = ({ url, alt, onLoaded }) => {
  const imageAlt = alt || '';
  const [, { open }] = useDisclosure(false);
  const { classes } = useStyles();
  const [hasError, setHasError] = useState<boolean>(false);

  const isVideo = useMemo(() => VIDEO_EXTENSIONS.some((ext) => url.endsWith(ext)), [url]);

  const renderError = () => {
    return isVideo ? <ErrorVideo /> : <ErrorImage />;
  };

  const renderMedia = () => {
    return isVideo ? (
      <div onClick={open} className={classes.videoContainer}>
        <video
          src={url}
          className={classes.videoStyle}
          muted
          playsInline
          onError={() => setHasError(true)}
          data-testid='video-element'
        />
        <IconPlayerPlay size={48} className={classes.playIcon} />
      </div>
    ) : (
      <Image
        src={url}
        alt={imageAlt}
        fit='fill'
        onLoad={onLoaded}
        onError={() => setHasError(true)}
        className={classes.imageStyle}
      />
    );
  };

  return hasError ? renderError() : <Preview imageUrl={url}>{renderMedia()}</Preview>;
};

export default CustomMedia;
