import { screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../utils/testing';
import DotLoader from './dotLoader';

// Mock Mantine Center and Loader
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Center: ({ children, ...props }: any) => (
      <div data-testid='center' {...props}>
        {children}
      </div>
    ),
    Loader: (props: any) => <div data-testid='loader' {...props} />,
  };
});

describe('DotLoader', () => {
  it('renders Center and Loader with correct props', () => {
    renderWithProvider(<DotLoader />);
    expect(screen.getByTestId('center')).toBeInTheDocument();
    const loader = screen.getByTestId('loader');
    expect(loader).toBeInTheDocument();
    expect(loader).toHaveAttribute('type', 'dots');
    expect(loader).toHaveAttribute('color', 'navy.0');
  });
});
