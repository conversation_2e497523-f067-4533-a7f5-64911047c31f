import { screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../utils/testing';
import BarLoader from './barLoader';

// Mock Mantine Center and Loader
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Center: ({ children, ...props }: any) => (
      <div data-testid='center' {...props}>
        {children}
      </div>
    ),
    Loader: (props: any) => <div data-testid='loader' {...props} />,
  };
});

describe('BarLoader', () => {
  it('renders Center and Loader with correct props', () => {
    renderWithProvider(<BarLoader />);
    expect(screen.getByTestId('center')).toBeInTheDocument();
    const loader = screen.getByTestId('loader');
    expect(loader).toBeInTheDocument();
    expect(loader).toHaveAttribute('type', 'bars');
    expect(loader).toHaveAttribute('color', 'navy.0');
  });
});
