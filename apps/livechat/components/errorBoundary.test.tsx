import React from 'react';
import { render } from '@testing-library/react';
import ErrorBoundary from './errorBoundary';

describe('ErrorBoundary', () => {
  it('should render children when there is no error', () => {
    const RENDERED_TEXT = 'Content';
    const ERROR_TEXT = 'Error occurred';
    const { getByText } = render(
      <ErrorBoundary fallback={<div>{ERROR_TEXT}</div>}>
        <div>{RENDERED_TEXT}</div>
      </ErrorBoundary>
    );

    expect(getByText(RENDERED_TEXT)).toBeInTheDocument();
  });

  it.skip('should render fallback when there is an error', () => {
    const TestComponent = () => {
      const [value, setValue] = React.useState(0);
      React.useEffect(() => {
        if (value >= 0) {
          throw new Error('Error occurred');
        }
      }, [value]);
      return (
        <div>
          <button type='button' onClick={() => setValue(value + 1)}>
            Click
          </button>
          {value}
        </div>
      );
    };
    const ERROR_TEXT = 'Error occurred';
    const { getByText } = render(
      <ErrorBoundary fallback={<div>{ERROR_TEXT}</div>}>
        <TestComponent />
      </ErrorBoundary>
    );

    expect(getByText(ERROR_TEXT)).toBeInTheDocument();
  });
});
