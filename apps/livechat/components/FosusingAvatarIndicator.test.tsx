import { screen } from '@testing-library/react';
import React from 'react';
import { renderWithProvider } from '../utils/testing';
import FocusingAvatarIndicator from './FosusingAvatarIndicator';

// Mock Mantine Indicator and Avatar
jest.mock('@mantine/core', () => {
  const actual = jest.requireActual('@mantine/core');
  return {
    ...actual,
    Indicator: ({ children, label, ...props }: any) => (
      <div data-testid='indicator' {...props}>
        <span data-testid='label'>{label}</span>
        {children}
      </div>
    ),
    Avatar: ({ children, ...props }: any) => (
      <div data-testid='avatar' {...props}>
        {children}
      </div>
    ),
  };
});

// Mock Tabler icons
jest.mock('@tabler/icons-react', () => ({
  IconEye: () => <span data-testid='icon-eye' />,
  IconEyeOff: () => <span data-testid='icon-eyeoff' />,
}));

describe('FocusingAvatarIndicator', () => {
  it('renders children only if enable is false', () => {
    renderWithProvider(
      <FocusingAvatarIndicator enable={false}>
        <span data-testid='child'>Child</span>
      </FocusingAvatarIndicator>
    );
    expect(screen.getByTestId('child')).toBeInTheDocument();
    expect(screen.queryByTestId('indicator')).not.toBeInTheDocument();
  });

  it('renders Indicator and Avatar with IconEye when focusing', () => {
    renderWithProvider(
      <FocusingAvatarIndicator enable focusing>
        <span data-testid='child'>Child</span>
      </FocusingAvatarIndicator>
    );
    expect(screen.getByTestId('indicator')).toBeInTheDocument();
    expect(screen.getByTestId('avatar')).toHaveAttribute('bg', '#62D821');
    expect(screen.getByTestId('icon-eye')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-eyeoff')).not.toBeInTheDocument();
  });

  it('renders Indicator and Avatar with IconEyeOff when not focusing', () => {
    renderWithProvider(
      <FocusingAvatarIndicator enable focusing={false}>
        <span data-testid='child'>Child</span>
      </FocusingAvatarIndicator>
    );
    expect(screen.getByTestId('indicator')).toBeInTheDocument();
    expect(screen.getByTestId('avatar')).toHaveAttribute('bg', '#A3A3A3');
    expect(screen.getByTestId('icon-eyeoff')).toBeInTheDocument();
    expect(screen.queryByTestId('icon-eye')).not.toBeInTheDocument();
  });

  it('passes offset and size props to Indicator and Avatar', () => {
    renderWithProvider(
      <FocusingAvatarIndicator enable focusing offset={10} size={24}>
        <span data-testid='child'>Child</span>
      </FocusingAvatarIndicator>
    );
    expect(screen.getByTestId('indicator')).toHaveAttribute('offset', '10');
    expect(screen.getByTestId('avatar')).toHaveAttribute('size', '24');
  });
});
