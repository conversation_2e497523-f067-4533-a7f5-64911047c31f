import { render, screen } from '@testing-library/react';
import React from 'react';
import Animate3DotsSvg from './Animate3DotsSvg';

describe('Animate3DotsSvg', () => {
  it('renders without crashing', () => {
    render(<Animate3DotsSvg />);
    const svgElement = screen.getByRole('img');
    expect(svgElement).toBeInTheDocument();
  });

  it('renders with default color', () => {
    render(<Animate3DotsSvg />);
    const circle1 = screen.getByTestId('dot1');
    const circle2 = screen.getByTestId('dot2');
    const circle3 = screen.getByTestId('dot3');
    expect(circle1).toBeInTheDocument();
    expect(circle2).toBeInTheDocument();
    expect(circle3).toBeInTheDocument();
    expect(circle1).toHaveAttribute('fill', 'white');
    expect(circle2).toHaveAttribute('fill', 'white');
    expect(circle3).toHaveAttribute('fill', 'white');
  });

  it('renders with custom color', () => {
    render(<Animate3DotsSvg color='red' />);
    const circle1 = screen.getByTestId('dot1');
    const circle2 = screen.getByTestId('dot2');
    const circle3 = screen.getByTestId('dot3');
    expect(circle1).toHaveAttribute('fill', 'red');
    expect(circle2).toHaveAttribute('fill', 'red');
    expect(circle3).toHaveAttribute('fill', 'red');
  });

  it('renders with custom style', () => {
    const customStyle = { width: '50px', height: '20px' };
    render(<Animate3DotsSvg style={customStyle} />);
    const svgElement = screen.getByRole('img');
    expect(svgElement).toHaveStyle('width: 50px');
    expect(svgElement).toHaveStyle('height: 20px');
  });

  it('circle with correct id', () => {
    render(<Animate3DotsSvg />);
    const circle1 = screen.getByTestId('dot1');
    const circle2 = screen.getByTestId('dot2');
    const circle3 = screen.getByTestId('dot3');
    expect(circle1).toHaveAttribute('id', 'dot1');
    expect(circle2).toHaveAttribute('id', 'dot2');
    expect(circle3).toHaveAttribute('id', 'dot3');
  });

  it('circle with animate', () => {
    render(<Animate3DotsSvg />);
    const animate1 = screen.getByTestId('animate1');
    const animate2 = screen.getByTestId('animate2');
    const animate3 = screen.getByTestId('animate3');
    expect(animate1).toHaveAttribute('attributeName', 'cy');
    expect(animate2).toHaveAttribute('attributeName', 'cy');
    expect(animate3).toHaveAttribute('attributeName', 'cy');
  });
});
