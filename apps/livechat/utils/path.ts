import { NextRouter } from 'next/router';
import { MAIN_ROUTE } from '../constants';

export const getIsNotMainPathValueAsPath = (asPath: string) => {
  if (asPath.includes('widget')) return true;
  if (asPath.includes('settings/')) return true;
  if (asPath.includes('automation')) return true;
  return false;
};

export const getIsNotMainPathValue = (router: NextRouter) => {
  // if (router.asPath.includes('widget')) return true;
  // if (router.asPath.includes('settings/')) return true;
  // if (router.asPath.includes('automation')) return true;
  // return router.pathname !== MAIN_ROUTE;

  const asPathCheck = getIsNotMainPathValueAsPath(router.asPath);
  if (asPathCheck) return true;
  return router.pathname !== MAIN_ROUTE;

  // initContext router.pathname !== MAIN_ROUTE;

  // addParamsToCurrentUrl router.route !== MAIN_ROUTE
};
