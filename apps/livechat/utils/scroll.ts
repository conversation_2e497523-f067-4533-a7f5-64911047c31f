import {
  MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID,
  MESSAGE_AI_ADJUSTMENT_LIST_SCROLL_POINT_ID,
} from '../constants';

export const scrollToMessageListBottom = (smoothScroll = false) => {
  document
    .getElementById(MESSAGE_LIST_BOTTOM_SCROLL_POINT_ID)
    ?.scrollIntoView({ behavior: smoothScroll ? 'smooth' : 'instant' });
};

export const scrollToAIAdjustmentMessageListBottom = () => {
  document.getElementById(MESSAGE_AI_ADJUSTMENT_LIST_SCROLL_POINT_ID)?.scrollIntoView();
};
