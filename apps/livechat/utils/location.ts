import AppConfig from '../configs';

export const isAllowedDomains = () => {
  if (typeof window === 'undefined') return false;
  const includedDomains = AppConfig.INCLUDED_BASE_PATH_DOMAINS;
  return (
    includedDomains.filter((domain) => window.location.origin === 'https://' + domain).length > 0
  );
};

export const isInCludedBasePathDomains = () => {
  return (
    AppConfig.INCLUDED_BASE_PATH_DOMAINS.filter((domain) => window.location.origin.includes(domain))
      .length > 0
  );
};

export const isExcludedBasePathDomains = () => {
  return (
    AppConfig.EXCLUDED_BASE_PATH_DOMAINS.filter((domain) => window.location.origin.includes(domain))
      .length > 0
  );
};
