import dayjs, { Dayjs } from 'dayjs';

export const deepCopy = (obj: any) => {
  return JSON.parse(JSON.stringify(obj));
};

export function delay(milliseconds = 500) {
  return new Promise((resolve) => setTimeout(resolve, milliseconds));
}

export const isMinutesPast = (currTime: Dayjs, storedTime: string, pastMinutes: number) => {
  return currTime.isAfter(dayjs(storedTime).add(pastMinutes, 'minutes'));
};

export const isSecondsPast = (currTime: Dayjs, storedTime: string, pastSeconds: number) => {
  return currTime.isAfter(dayjs(storedTime).add(pastSeconds, 'seconds'));
};
