import { NextRouter } from 'next/router';
import AppConfig from '../configs';
import { getIsNotMainPathValue } from './path';

const getUrl = () => {
  // console.log("getEnvPreviewVariables", AppConfig.IS_PR, AppConfig.PREVIEW_ENVIRONMENT_VARIABLES);
  if (AppConfig.IS_PR) {
    if (!AppConfig.PREVIEW_ENVIRONMENT_VARIABLES) {
      throw new Error('PREVIEW_ENVIRONMENT_VARIABLES is not defined');
    }
    if (!AppConfig.PREVIEW_ENVIRONMENT_VARIABLES.NEXT_PUBLIC_BASE_URL) {
      throw new Error('PREVIEW_ENVIRONMENT_VARIABLES.NEXT_PUBLIC_BASE_URL is not defined');
    }
    return new URL(
      window.location.origin + AppConfig.PREVIEW_ENVIRONMENT_VARIABLES.NEXT_PUBLIC_BASE_URL
    );
  }
  return new URL(window.location.origin + AppConfig.BASE_URL);
};

export const addParamsToCurrentUrl = (inputParams: any, router: NextRouter) => {
  if (getIsNotMainPathValue(router)) return;

  const url = getUrl();

  const params = new URLSearchParams(url.search);
  Object.keys(inputParams).forEach((key) => {
    const value = inputParams[key];
    if (value === undefined || value === null || value === '') {
      return;
    }
    params.set(key, value);
  });
  url.search = params.toString();
  history.pushState({}, '', url.toString());
};

const getPathName = () => {
  let pathName = window.location.pathname || '';
  while (pathName.endsWith('/')) {
    pathName = pathName?.slice(0, -1);
  }
  return pathName;
};

export const historyPushState = (queryStr: string) => {
  if (window && history?.pushState) {
    let newUrl = `${window.location.protocol}//${window.location.host}${
      window.location.pathname
    }?${queryStr}`;

    if (AppConfig.IS_PRODUCTION) {
      const pathName = getPathName();
      newUrl = `${window.location.protocol}//${window.location.host}${pathName}/?${queryStr}`;
    }
    history.pushState({}, '', newUrl);
  }
};

export const addActiveTabAsParamToCurrentUrl = (activeTab: string, additionalUrl?: string) => {
  if (activeTab === undefined || activeTab === null || activeTab === '') return;
  historyPushState(`tab=${activeTab}${additionalUrl ?? ''}`);
};
