import dayjs from 'dayjs';
import { LocalStorageUtils } from '@resola-ai/utils';
import { RELOAD_TIMES_WHEN_CHANGE_ROLE } from '../constants';
import AppConfig from '../configs';

export const groupMessageHistory = (prevValues, values) => {
  const grouped = values?.reduce((acc, curr) => {
    const { created } = curr;
    const date = dayjs(created).format('YYYYMMDD');
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(curr);
    return acc;
  }, prevValues);

  return grouped;
};

export function reloadWholePage() {
  const reloadTimes = parseInt(LocalStorageUtils.get(RELOAD_TIMES_WHEN_CHANGE_ROLE) || '0', 10);
  if (!window) return;
  if (reloadTimes < 3) {
    LocalStorageUtils.set(RELOAD_TIMES_WHEN_CHANGE_ROLE, `${reloadTimes + 1}`);
    window?.location.reload();
  } else {
    // redirect to login page
    LocalStorageUtils.set(RELOAD_TIMES_WHEN_CHANGE_ROLE, '0');
    window.location.href = AppConfig.NO_PERMISSION_REDIRECT_URL;
  }
}
