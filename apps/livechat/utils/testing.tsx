import { render } from '@testing-library/react';
import { AppMantineEmotionProvider } from '../emotion';

export const initTestingEnvironment = () => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      // Add these methods to the mock
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Add ResizeObserver mock
  global.ResizeObserver = class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
};

export const renderWithProvider = (content: React.ReactNode) => {
  initTestingEnvironment();

  return render(<AppMantineEmotionProvider>{content}</AppMantineEmotionProvider>);
};
