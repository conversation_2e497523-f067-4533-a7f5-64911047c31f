import { datadogService } from '@resola-ai/services-shared';
import AppConfig from '../configs';
import { isAllowedDomains, isExcludedBasePathDomains } from './location';

export const getOrganizationName = () => {
  if (typeof window === 'undefined') {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
  // todo: move these values into AppConfig
  if (
    window.location.origin.includes('localhost') ||
    window.location.origin.includes('ngrok') ||
    window.location.origin.includes('cloudworkstations')
  ) {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
  try {
    if (isExcludedBasePathDomains()) {
      return AppConfig.DEFAULT_ORGANIZATION_NAME;
    }
    const orgName = window.location.origin.split('.')[0].split('//')[1];
    if (isAllowedDomains()) {
      return undefined;
    }
    datadogService.setOrganizationName(orgName);
    return orgName;
  } catch (error) {
    return AppConfig.DEFAULT_ORGANIZATION_NAME;
  }
};
