import AppConfig from '../configs';
import { ConversationListParams } from '../models/conversationParams';

export const createConversationRoute = (params: ConversationListParams, conversationId: string) => {
  const baseUrl = AppConfig.BASE_URL;
  const paramQuery = Object.keys(params)
    .map((key) => `${key}=${params[key]}`)
    .join('&');
  if (!conversationId) {
    const path = `${baseUrl}?${paramQuery}`;
    return path;
  }
  const conversationQuery = `cId=${conversationId}`;
  const query = [paramQuery, conversationQuery].filter(Boolean).join('&');
  const path = `${baseUrl}?${query}`;
  return path;
};
