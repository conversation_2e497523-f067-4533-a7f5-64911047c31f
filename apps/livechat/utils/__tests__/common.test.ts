import dayjs from 'dayjs';
import { isMinutesPast, isSecondsPast } from '../common';

describe('common utils', () => {
  describe('isMinutesPast', () => {
    it('should return true if the stored time is more than the specified minutes in the past', () => {
      const currTime = dayjs('2023-10-27T10:15:00.000Z');
      const storedTime = '2023-10-27T10:00:00.000Z';
      const pastMinutes = 10;
      expect(isMinutesPast(currTime, storedTime, pastMinutes)).toBe(true);
    });

    it('should return false if the stored time is within the specified minutes in the past', () => {
      const currTime = dayjs('2023-10-27T10:10:00.000Z');
      const storedTime = '2023-10-27T10:05:00.000Z';
      const pastMinutes = 10;
      expect(isMinutesPast(currTime, storedTime, pastMinutes)).toBe(false);
    });

    it('should return false if the stored time is the same as the current time', () => {
      const currTime = dayjs('2023-10-27T10:00:00.000Z');
      const storedTime = '2023-10-27T10:00:00.000Z';
      const pastMinutes = 10;
      expect(isMinutesPast(currTime, storedTime, pastMinutes)).toBe(false);
    });
    it('should return false if the stored time is in the future', () => {
      const currTime = dayjs('2023-10-27T10:00:00.000Z');
      const storedTime = '2023-10-27T10:10:00.000Z';
      const pastMinutes = 10;
      expect(isMinutesPast(currTime, storedTime, pastMinutes)).toBe(false);
    });
  });

  describe('isSecondsPast', () => {
    it('should return true if the stored time is more than the specified seconds in the past', () => {
      const currTime = dayjs('2023-10-27T10:00:15.000Z');
      const storedTime = '2023-10-27T10:00:00.000Z';
      const pastSeconds = 10;
      expect(isSecondsPast(currTime, storedTime, pastSeconds)).toBe(true);
    });

    it('should return false if the stored time is within the specified seconds in the past', () => {
      const currTime = dayjs('2023-10-27T10:00:05.000Z');
      const storedTime = '2023-10-27T10:00:00.000Z';
      const pastSeconds = 10;
      expect(isSecondsPast(currTime, storedTime, pastSeconds)).toBe(false);
    });

    it('should return false if the stored time is the same as the current time', () => {
      const currTime = dayjs('2023-10-27T10:00:00.000Z');
      const storedTime = '2023-10-27T10:00:00.000Z';
      const pastSeconds = 10;
      expect(isSecondsPast(currTime, storedTime, pastSeconds)).toBe(false);
    });

    it('should return false if the stored time is in the future', () => {
      const currTime = dayjs('2023-10-27T10:00:00.000Z');
      const storedTime = '2023-10-27T10:00:10.000Z';
      const pastSeconds = 10;
      expect(isSecondsPast(currTime, storedTime, pastSeconds)).toBe(false);
    });
  });
});
