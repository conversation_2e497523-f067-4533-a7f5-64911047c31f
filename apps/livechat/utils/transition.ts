// related the new View Transition API

import { NextRouter } from 'next/router';
import { ExtendedDocument } from '../types/extendedDocument';

export const animatedNavigate = (url: string, router: NextRouter) => {
  const extendedDocument = document as ExtendedDocument;
  if (!extendedDocument.startViewTransition) {
    return router.push(url);
  }
  extendedDocument.startViewTransition(() => {
    router.push(url);
  });
};

export const viewTransitionsStatus = () => {
  const extendedDocument = document as ExtendedDocument;
  let status = "Opss, Your browser doesn't support View Transitions API";
  if (extendedDocument?.startViewTransition) {
    status = 'Yess, Your browser support View Transitions API';
  }
  return status;
};
