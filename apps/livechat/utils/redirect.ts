import AppConfig from '../configs';
import { isExcludedBasePathDomains, isInCludedBasePathDomains } from './location';

export const getRedirectUri = () => {
  if (typeof window === 'undefined') {
    return '';
  }
  if (isExcludedBasePathDomains()) {
    return `${window.location.origin}`;
  }
  if (isInCludedBasePathDomains()) {
    return `${window.location.origin}${AppConfig.BASE_URL}`;
  }
  return `${window.location.origin}`;
};
