import { EmojiLine } from '@resola-ai/models';

const urlRegex = /(((https?:\/\/)|(www\.))[^\s]+)/g;

export const hasUrl = (str: string) => urlRegex.test(str);

export const transformTextHyperLinks = (str = '') => {
  return str.replace(urlRegex, (_, contents) => `<a href="${contents.trim()}" role="link" target='_blank' referrerpolicy='no-referrer' rel='nofollow noreferrer noopener'>${contents.trim()}</a>`);
};
const EMOJI_SIZE_BIG = 100;
const EMOJI_SIZE_NORMAL = 40;

export const hasOneAndOnlyOneLineEmojiNoText = (text: string, emojis: EmojiLine[] = []) => {
  if (!emojis || !Array.isArray(emojis)) true;
  return emojis?.length === 1 && text.length === emojis[0].length;
};

export const shouldTransformLink = (text = '', shouldDo = true) => {
  return shouldDo ? transformTextHyperLinks(text) : text;
};

type ConvertMessageFuncType = {
  emojis: EmojiLine[];
  originalText: string;
  forceImgSize?: number;
  shouldDoTransform?: boolean;
};

export const convertTextLineEmojisSupport = ({
  emojis = [],
  originalText = '',
  shouldDoTransform = true,
  forceImgSize = undefined,
}: ConvertMessageFuncType) => {
  if (!originalText) return '';
  if (!emojis || !emojis.length) return shouldTransformLink(originalText, shouldDoTransform);

  let convertedText = originalText;
  // Special case: check if text has only one emoji
  if (emojis.length === 1 && originalText.length === emojis[0]?.length) {
    const sizeImageBigAdjust = forceImgSize ?? EMOJI_SIZE_BIG;
    return `<img loading="lazy" src="${emojis[0]?.url}" alt='${originalText}' style="width: ${sizeImageBigAdjust}px; height: ${sizeImageBigAdjust}px;"/>`;
  }

  const size = forceImgSize ?? EMOJI_SIZE_NORMAL;
  const imageTemplate = (url: string, alt = 'line-emoji') =>
    `<img src="${url}" alt="${alt}" loading="lazy" style="width: ${size}px; height: ${size}px;"/>`;
  let arrStrSplitedByEmojis: string[] = [];
  // Reverse the order to process the text from back to front
  emojis
    .slice()
    .reverse()
    .forEach((emoji) => {
      const { length = 0, index = 0 } = emoji || {};
      const strFromIndexToEndOfCurrString = convertedText.substring(index);
      convertedText = convertedText.slice(0, index);
      arrStrSplitedByEmojis.push(
        strFromIndexToEndOfCurrString.substring(length),
        imageTemplate(emoji.url, strFromIndexToEndOfCurrString.substring(0, length))
      );
    });

  // Support displaying link as anchor tag
  arrStrSplitedByEmojis = arrStrSplitedByEmojis.map((item) => {
    if (item.includes('<img src=')) return item; // emoji item
    if (hasUrl(item)) return shouldTransformLink(item, shouldDoTransform);
    return item;
  });

  return (
    shouldTransformLink(convertedText, shouldDoTransform) + arrStrSplitedByEmojis.reverse().join('')
  );
};
