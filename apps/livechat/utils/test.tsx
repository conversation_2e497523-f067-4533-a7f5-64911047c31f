import { MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { render } from '@testing-library/react';
import React from 'react';

export const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

describe('MantineWrapper', () => {
  it('renders children without crashing', () => {
    const { container } = render(
      <MantineWrapper>
        <div>Test</div>
      </MantineWrapper>
    );
    expect(container).toBeTruthy();
  });
});
