import { ILastMessage, IMessage, IMessageUser, MessageUserType } from '@resola-ai/models';
import { displayChatDate } from '@resola-ai/utils';
import { deepCopy } from './common';
import dayjs from 'dayjs';

const createUser = (id: string, type: MessageUserType) => {
  return {
    id,
    type,
  };
};

export const createLastMessage = (
  id: string,
  created: string,
  text: string,
  stickerId: string,
  user: IMessageUser
): ILastMessage => {
  return {
    id,
    created,
    data: {
      text,
      type: 'text',
      id: `last-message-${
        text ? text.split(' ').join('') : stickerId?.split(' ').join('')
      }-${new Date(created).getTime().toString()}`,
    },
    sender: createUser(user.id, user.type),
  };
};

export const createLastMessageFromMessage = (message: IMessage): ILastMessage => {
  const text = message.data.text;
  return {
    id: message.id,
    created: message.created,
    sender: createUser(message.sender.id, message.sender.type),
    data: {
      ...deepCopy(message.data),
      type: 'text',
      id: `last-message-${
        text ? text.split(' ').join('') : message.data.stickerId?.split(' ').join('')
      }-${new Date(message.created).getTime().toString()}`,
    },
  };
};

export const createMessageId = (message: IMessage) => {
  return `conver:${message?.conversationId}-message:${message?.id}`;
};

type ConvertMessagesType = {
  lang: string; // 'ja' | 'en',
  messages: IMessage[];
  lastUserReadAt?: string | undefined;
};

export const createMessagesWithCustomClientDate = ({
  messages = [],
  lang = 'ja',
  lastUserReadAt = undefined,
}: ConvertMessagesType): IMessage[] => {
  // we will have a new type as 'custom-client-date',
  // check for each date inside of each message (createdAt),
  // if the date is different with the previous one,
  // then add a new message with type 'custom-client-date'
  const newMessages: IMessage[] = [];
  const previousDates: Set<string> = new Set();
  for (let i = 0; i < messages.length; i++) {
    let message = messages[i];

    const newHasReadAtThisMoment = lastUserReadAt
      ? dayjs(message.created).isBefore(dayjs(lastUserReadAt))
      : false;
    if (!message?.hasRead && newHasReadAtThisMoment) {
      message = { ...message, hasRead: newHasReadAtThisMoment };
    }

    const currentDate = displayChatDate(message.created, lang);
    if (!previousDates.has(currentDate)) {
      const newMessage = {
        ...message,
        data: {
          type: 'custom-client-date',
          text: message.created,
          id: `custom-client-date-${currentDate}`,
        },
        // type: 'custom-client-date',
        // text: message.createdAt,
        id: `custom-client-date-${currentDate}`,
      };
      newMessages.push(newMessage);
      previousDates.add(currentDate);
    }
    newMessages.push(message);
  }
  previousDates.clear();
  return newMessages;
};
