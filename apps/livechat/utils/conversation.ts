import { IConversation } from '@resola-ai/models';
import { CONVERSATION_LIST_CONTAINER_ID } from '../constants';
import { ConversationListParams } from '../models/conversationParams';

const NO_SPECIFIED = 'NO_SPECIFIED';
export enum PAGE_TYPE {
  // eslint-disable-next-line no-unused-vars
  ALL_CONVERSATION = 0,
  // eslint-disable-next-line no-unused-vars
  BOOKMAKRED_CONVERSATION = 1,
  // eslint-disable-next-line no-unused-vars
  UNASSIGNED_CONVERSATION = 2,
  // eslint-disable-next-line no-unused-vars
  SPECIFIED_TEAM_CONVERSATION = 3,
  // eslint-disable-next-line no-unused-vars
  SPECIFIED_TEAM_UNASSIGNED_CONVERSATION = 4,
}
export const getConversationKey = (inputParams: ConversationListParams) => {
  const params: ConversationListParams = { ...inputParams };

  params.next = undefined;
  params.per_page = undefined;
  params.cId = undefined;
  if (![true, 'true'].includes(params?.bookmark)) params.bookmark = undefined;

  if (!('assigned' in params)) {
    params.assigned = undefined;
  }

  if (!('team' in params)) {
    params.team = undefined;
  }

  // convert status if status is inWrapUp
  if (params?.status === 'inWrapUp') {
    params.status = 'inProgress';
  }

  // get all the object keys and values out of the params
  // then put them into one unique string to make it as a key id
  return Object.entries(params)
    .sort((en1, en2) => en1[0].localeCompare(en2[0]))
    .map((item) => `${item[0]}=${item[1] ?? NO_SPECIFIED}`)
    .join('__');
};

// paramsString : result of getConversationKey()
const isAllConversationPage = (paramsString: string) => {
  return paramsString.includes('assigned=true') && paramsString.includes(`team=${NO_SPECIFIED}`);
};

const isBookmarkedPage = (paramsString: string) => {
  return (
    paramsString.includes(`assigned=${NO_SPECIFIED}`) && paramsString.includes('bookmark=true')
  );
};

const isUnassingedPage = (paramsString: string) => {
  return paramsString.includes('assigned=false') && paramsString.includes(`team=${NO_SPECIFIED}`);
};

const isSpecifiedTeamAllConverPage = (paramsString: string) => {
  const simpliedParams = paramsString.split('__');
  const team = simpliedParams.find((item) => item.includes('team'));
  return simpliedParams.includes(`assigned=${NO_SPECIFIED}`) && team !== `team=${NO_SPECIFIED}`;
};

const isSpecifiedTeamUnassignedConverPage = (paramsString: string) => {
  const simpliedParams = paramsString.split('__');
  const team = simpliedParams.find((item) => item.includes('team'));
  return simpliedParams.includes('assigned=false') && team !== `team=${NO_SPECIFIED}`;
};

export const getPageType = (key: string): PAGE_TYPE => {
  if (isAllConversationPage(key)) {
    return PAGE_TYPE.ALL_CONVERSATION;
  }

  if (isBookmarkedPage(key)) {
    return PAGE_TYPE.BOOKMAKRED_CONVERSATION;
  }

  if (isUnassingedPage(key)) {
    return PAGE_TYPE.UNASSIGNED_CONVERSATION;
  }

  if (isSpecifiedTeamAllConverPage(key)) {
    return PAGE_TYPE.SPECIFIED_TEAM_CONVERSATION;
  }

  if (isSpecifiedTeamUnassignedConverPage(key)) {
    return PAGE_TYPE.SPECIFIED_TEAM_UNASSIGNED_CONVERSATION;
  }
};

export const generateConversationItemKey = (data: IConversation, idx?: number) => {
  // use all the data in conversation params and index to generate the key
  return `${data.id}_${data.status}_${data.teamId}_${data.assigneeId}_${idx}`;
};

export const generateConversationDomElementId = (conversationId: string) => {
  return conversationId;
};

export const getTheFirstConversationIdInView = () => {
  // console.log('run getTheFirstConversationIdInView')
  const containerElement = document.getElementById(CONVERSATION_LIST_CONTAINER_ID);
  // loop through all the children of the container element
  // and find the first element that is in view
  // check from the top to the bottom, get the first one that is  in view
  // if not found, return null
  if (!containerElement) {
    return undefined;
  }

  const children = containerElement.children;

  for (let i = 0; i < children.length; i++) {
    const child = children[i];
    const bounding = child.getBoundingClientRect();

    // get full size of the element
    // if (bounding.top >= 0 && bounding.bottom <= window.innerHeight) {
    //     return child.id;
    // }

    // get only part of the element, do not need to full
    if (bounding.top >= 0 && bounding.top <= window.innerHeight) {
      // console.log('getTheFirstConversationIdInView', child.id)
      return child.id;
    }
  }
  return undefined;
};

export const getTheLastConversationIdInView = () => {
  const containerElement = document.getElementById(CONVERSATION_LIST_CONTAINER_ID);
  // loop through all the children of the container element
  // and find the first element that is in view
  // check from the top to the bottom, get the first one that is  in view
  // if not found, return null
  if (!containerElement) {
    return undefined;
  }

  const children = containerElement.children;

  for (let i = children.length - 1; i >= 0; i--) {
    const child = children[i];
    const bounding = child.getBoundingClientRect();
    if (bounding.top >= 0 && bounding.bottom <= window.innerHeight) {
      return child.id;
    }
  }
  return undefined;
};

export const getTheSecondConversationIdInView = () => {
  const containerElement = document.getElementById(CONVERSATION_LIST_CONTAINER_ID);
  // loop through all the children of the container element
  // and find the first element that is in view
  // check from the top to the bottom, get the first one that is  in view
  // if not found, return null
  if (!containerElement) {
    return undefined;
  }

  const children = containerElement.children;

  for (let i = 0; i < children.length; i++) {
    const child = children[i];
    const bounding = child.getBoundingClientRect();
    if (bounding.top >= 0 && bounding.bottom <= window.innerHeight) {
      return children[i + 1]?.id;
    }
  }
  return undefined;
};

export const scrollToConversationId = (conversationId: string) => {
  document.getElementById(conversationId)?.scrollIntoView();
};
