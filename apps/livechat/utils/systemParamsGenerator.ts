import {
  MENU_BOOKMARK,
  MENU_MY_ASSIGNED,
  MENU_SUBTYPE_TEAM_ALL,
  MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR,
  MENU_UNASSIGNED,
} from '../constants/menu';
import { ConversationListParams } from '../models/conversationParams';

type DataParamsType = {
  params: Partial<ConversationListParams>;
  next?: string;
};

const baseParamsObject: DataParamsType = {
  params: {},
  next: undefined,
};

export class ParamsTypeGenerator {
  static paramsStatic = JSON.parse(JSON.stringify(baseParamsObject));

  static getParamsByType(type: string, teamId?: string, cId?: string): DataParamsType {
    const params = JSON.parse(JSON.stringify(ParamsTypeGenerator.paramsStatic)) as DataParamsType;
    switch (type) {
      case MENU_MY_ASSIGNED:
        params.params = {
          team: undefined,
          assigned: true,
          bookmark: undefined,
          next: undefined,
        };
        break;
      case MENU_UNASSIGNED:
        params.params = {
          team: undefined,
          assigned: false,
          bookmark: undefined,
          next: undefined,
          status: 'new',
        };
        break;
      case MENU_SUBTYPE_TEAM_ALL:
        params.params = {
          team: teamId,
          assigned: undefined,
          bookmark: undefined,
          next: undefined,
        };
        break;
      case MENU_SUBTYPE_TEAM_UNASSIGNED_OPERATOR:
        params.params = {
          team: teamId,
          assigned: false,
          bookmark: undefined,
          next: undefined,
        };
        break;
      case MENU_BOOKMARK: {
        params.params = {
          team: undefined,
          assigned: undefined,
          bookmark: true,
          next: undefined,
          status: 'new',
        };
      }
      default:
        break;
    }
    if (cId) {
      params.params.cId = cId;
    }
    return params;
  }
}
