{"type": "object", "name": "appSelector", "displayName": "App Selector Configuration", "description": "Configuration for app selection in workflows", "required": ["app"], "properties": {"app": {"type": "object", "name": "app", "displayName": "App", "required": ["id", "name"], "properties": {"id": {"type": "string", "name": "id", "displayName": "App ID", "description": "Unique identifier for the app"}, "name": {"type": "string", "name": "name", "displayName": "App Name", "description": "Display name of the app"}, "icon": {"type": "string", "name": "icon", "displayName": "App Icon", "description": "URL to the app icon image"}}}, "validation": {"type": "object", "name": "validation", "displayName": "Validation Settings", "properties": {"required": {"type": "boolean", "name": "required", "displayName": "Required", "description": "Whether app selection is required", "default": true}}}, "catalog": {"type": "object", "name": "appCatalogTools", "displayName": "App Catalog & Tools", "description": "Configure the catalog components and tools for your workflow", "required": ["catalog", "search", "categories", "popularApps", "buildInTools", "decaCloudTools", "aiTools"], "properties": {"catalog": {"type": "object", "name": "catalog", "displayName": "Catalog Configuration", "required": ["name", "enableSearch"], "properties": {"name": {"type": "string", "name": "name", "displayName": "Name", "default": "App Catalog & Tools"}, "enableSearch": {"type": "boolean", "name": "enableSearch", "displayName": "Enable Search", "default": true}, "showCategories": {"type": "boolean", "name": "showCategories", "displayName": "Show Categories", "default": true}, "showSelectedItem": {"type": "boolean", "name": "showSelectedItem", "displayName": "Show Selected Item", "default": false}}}, "search": {"type": "object", "name": "search", "displayName": "Search Configuration", "properties": {"placeholder": {"type": "string", "name": "placeholder", "displayName": "Search Placeholder", "default": "Search apps and tools..."}, "searchFields": {"type": "array", "name": "searchFields", "displayName": "Fields to Search", "items": {"type": "string", "enum": ["name", "description", "category", "tags"]}, "default": ["name", "description"]}}}, "categories": {"type": "object", "name": "categories", "displayName": "Categories", "properties": {"all": {"type": "boolean", "name": "all", "displayName": "All", "icon": "<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.52853 0.52827C6.78888 0.26792 7.21099 0.26792 7.47134 0.52827L13.4713 6.52827C13.662 6.71894 13.719 7.00568 13.6159 7.2548C13.5127 7.50391 13.2696 7.66634 12.9999 7.66634H12.3333V11.6663C12.3333 12.1968 12.1226 12.7055 11.7475 13.0806C11.3724 13.4556 10.8637 13.6663 10.3333 13.6663H3.6666C3.13617 13.6663 2.62746 13.4556 2.25239 13.0806C1.87732 12.7055 1.6666 12.1968 1.6666 11.6663V7.66634H0.999938C0.730297 7.66634 0.487206 7.50391 0.384018 7.2548C0.280831 7.00568 0.337868 6.71894 0.528534 6.52827L6.52853 0.52827ZM2.56721 6.37521C2.82 6.46995 2.99994 6.7138 2.99994 6.99967V11.6663C2.99994 11.8432 3.07018 12.0127 3.1952 12.1377C3.32022 12.2628 3.48979 12.333 3.6666 12.333H4.33327V8.99967C4.33327 8.46924 4.54399 7.96053 4.91906 7.58546C5.29413 7.21039 5.80284 6.99967 6.33327 6.99967H7.6666C8.19704 6.99967 8.70575 7.21039 9.08082 7.58546C9.45589 7.96053 9.6666 8.46924 9.6666 8.99967V12.333H10.3333C10.5101 12.333 10.6797 12.2628 10.8047 12.1377C10.9297 12.0127 10.9999 11.8432 10.9999 11.6663V6.99967C10.9999 6.7138 11.1799 6.46995 11.4327 6.37521L6.99994 1.94248L2.56721 6.37521ZM8.33327 12.333V8.99967C8.33327 8.82286 8.26303 8.6533 8.13801 8.52827C8.01298 8.40325 7.84342 8.33301 7.6666 8.33301H6.33327C6.15646 8.33301 5.98689 8.40325 5.86187 8.52827C5.73684 8.6533 5.6666 8.82286 5.6666 8.99967V12.333H8.33327Z\" fill=\"currentColor\"/>\n            </svg>\n            ", "default": true}, "popularApps": {"type": "boolean", "name": "popularApps", "displayName": "Apps", "icon": "<svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M0 1.33333C0 0.596954 0.596954 0 1.33333 0H4C4.73638 0 5.33333 0.596954 5.33333 1.33333V4C5.33333 4.73638 4.73638 5.33333 4 5.33333H1.33333C0.596954 5.33333 0 4.73638 0 4V1.33333ZM4 1.33333H1.33333V4H4V1.33333ZM9.33333 0C9.70152 0 10 0.298477 10 0.666667V2H11.3333C11.7015 2 12 2.29848 12 2.66667C12 3.03486 11.7015 3.33333 11.3333 3.33333H10V4.66667C10 5.03486 9.70152 5.33333 9.33333 5.33333C8.96514 5.33333 8.66667 5.03486 8.66667 4.66667V3.33333H7.33333C6.96514 3.33333 6.66667 3.03486 6.66667 2.66667C6.66667 2.29848 6.96514 2 7.33333 2H8.66667V0.666667C8.66667 0.298477 8.96514 0 9.33333 0ZM0 8C0 7.26362 0.596954 6.66667 1.33333 6.66667H4C4.73638 6.66667 5.33333 7.26362 5.33333 8V10.6667C5.33333 11.403 4.73638 12 4 12H1.33333C0.596954 12 0 11.403 0 10.6667V8ZM4 8H1.33333V10.6667H4V8ZM6.66667 8C6.66667 7.26362 7.26362 6.66667 8 6.66667H10.6667C11.403 6.66667 12 7.26362 12 8V10.6667C12 11.403 11.403 12 10.6667 12H8C7.26362 12 6.66667 11.403 6.66667 10.6667V8ZM8 8V10.6667H10.6667V8H8Z\" fill=\"currentColor\"/>\n            </svg>\n            ", "default": true}, "aiTools": {"type": "boolean", "name": "aiTools", "displayName": "AI", "icon": "<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect width=\"16\" height=\"16\" fill=\"currentColor\" fill-opacity=\"0.01\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M5.99992 3.33301C6.36811 3.33301 6.66659 3.63148 6.66659 3.99967C6.66659 4.88373 7.01778 5.73158 7.6429 6.3567C8.26802 6.98182 9.11586 7.33301 9.99992 7.33301C10.3681 7.33301 10.6666 7.63148 10.6666 7.99967C10.6666 8.36786 10.3681 8.66634 9.99992 8.66634C9.11586 8.66634 8.26802 9.01753 7.6429 9.64265C7.01778 10.2678 6.66659 11.1156 6.66659 11.9997C6.66659 12.3679 6.36811 12.6663 5.99992 12.6663C5.63173 12.6663 5.33325 12.3679 5.33325 11.9997C5.33325 11.1156 4.98206 10.2678 4.35694 9.64265C3.73182 9.01753 2.88397 8.66634 1.99992 8.66634C1.63173 8.66634 1.33325 8.36786 1.33325 7.99967C1.33325 7.63148 1.63173 7.33301 1.99992 7.33301C2.88397 7.33301 3.73182 6.98182 4.35694 6.3567C4.98206 5.73158 5.33325 4.88373 5.33325 3.99967C5.33325 3.63148 5.63173 3.33301 5.99992 3.33301ZM5.99992 6.4034C5.80554 6.72689 5.57107 7.02818 5.29975 7.29951C5.02843 7.57083 4.72713 7.80529 4.40365 7.99968C4.72713 8.19406 5.02843 8.42852 5.29975 8.69984C5.57107 8.97117 5.80554 9.27246 5.99992 9.59595C6.1943 9.27246 6.42876 8.97117 6.70009 8.69984C6.97141 8.42852 7.27271 8.19406 7.59619 7.99968C7.27271 7.80529 6.97141 7.57083 6.70009 7.29951C6.42876 7.02818 6.1943 6.72689 5.99992 6.4034Z\" fill=\"currentColor\"/>\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.9999 2.33301C12.184 2.33301 12.3333 2.48225 12.3333 2.66634C12.3333 2.93156 12.4386 3.18591 12.6261 3.37345C12.8137 3.56098 13.068 3.66634 13.3333 3.66634C13.5173 3.66634 13.6666 3.81558 13.6666 3.99967C13.6666 4.18377 13.5173 4.33301 13.3333 4.33301C13.068 4.33301 12.8137 4.43836 12.6261 4.6259C12.4386 4.81344 12.3333 5.06779 12.3333 5.33301C12.3333 5.5171 12.184 5.66634 11.9999 5.66634C11.8158 5.66634 11.6666 5.5171 11.6666 5.33301C11.6666 5.06779 11.5612 4.81344 11.3737 4.6259C11.1862 4.43836 10.9318 4.33301 10.6666 4.33301C10.4825 4.33301 10.3333 4.18377 10.3333 3.99967C10.3333 3.81558 10.4825 3.66634 10.6666 3.66634C10.9318 3.66634 11.1862 3.56098 11.3737 3.37345C11.5612 3.18591 11.6666 2.93156 11.6666 2.66634C11.6666 2.48225 11.8158 2.33301 11.9999 2.33301ZM11.9999 3.66635C11.9529 3.72908 11.9012 3.78876 11.8451 3.84485C11.789 3.90095 11.7293 3.95263 11.6666 3.99967C11.7293 4.04672 11.789 4.0984 11.8451 4.1545C11.9012 4.21059 11.9529 4.27027 11.9999 4.333C12.047 4.27027 12.0986 4.21059 12.1547 4.1545C12.2108 4.0984 12.2705 4.04672 12.3332 3.99967C12.2705 3.95263 12.2108 3.90095 12.1547 3.84485C12.0986 3.78876 12.047 3.72908 11.9999 3.66635ZM11.9999 10.333C12.184 10.333 12.3333 10.4822 12.3333 10.6663C12.3333 10.9316 12.4386 11.1859 12.6261 11.3734C12.8137 11.561 13.068 11.6663 13.3333 11.6663C13.5173 11.6663 13.6666 11.8156 13.6666 11.9997C13.6666 12.1838 13.5173 12.333 13.3333 12.333C13.068 12.333 12.8137 12.4384 12.6261 12.6259C12.4386 12.8134 12.3333 13.0678 12.3333 13.333C12.3333 13.5171 12.184 13.6663 11.9999 13.6663C11.8158 13.6663 11.6666 13.5171 11.6666 13.333C11.6666 13.0678 11.5612 12.8134 11.3737 12.6259C11.1862 12.4384 10.9318 12.333 10.6666 12.333C10.4825 12.333 10.3333 12.1838 10.3333 11.9997C10.3333 11.8156 10.4825 11.6663 10.6666 11.6663C10.9318 11.6663 11.1862 11.561 11.3737 11.3734C11.5612 11.1859 11.6666 10.9316 11.6666 10.6663C11.6666 10.4822 11.8158 10.333 11.9999 10.333ZM11.9999 11.6664C11.9529 11.7291 11.9012 11.7888 11.8451 11.8449C11.789 11.9009 11.7293 11.9526 11.6666 11.9997C11.7293 12.0467 11.789 12.0984 11.8451 12.1545C11.9012 12.2106 11.9529 12.2703 11.9999 12.333C12.047 12.2703 12.0986 12.2106 12.1547 12.1545C12.2108 12.0984 12.2705 12.0467 12.3332 11.9997C12.2705 11.9526 12.2108 11.9009 12.1547 11.8449C12.0986 11.7888 12.047 11.7291 11.9999 11.6664Z\" fill=\"currentColor\"/>\n            </svg>\n            ", "default": true}, "buildInTools": {"type": "boolean", "name": "buildInTools", "displayName": "Build-in Tools", "icon": "<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.99992 0.333008C5.36811 0.333008 5.66659 0.631485 5.66659 0.999674C5.66659 1.17649 5.73682 1.34605 5.86185 1.47108C5.98687 1.5961 6.15644 1.66634 6.33325 1.66634C6.70144 1.66634 6.99992 1.96482 6.99992 2.33301C6.99992 2.7012 6.70144 2.99967 6.33325 2.99967C6.15644 2.99967 5.98687 3.06991 5.86185 3.19494C5.73682 3.31996 5.66659 3.48953 5.66659 3.66634C5.66659 4.03453 5.36811 4.33301 4.99992 4.33301C4.63173 4.33301 4.33325 4.03453 4.33325 3.66634C4.33325 3.48953 4.26301 3.31996 4.13799 3.19494C4.01297 3.06991 3.8434 2.99967 3.66659 2.99967C3.2984 2.99967 2.99992 2.7012 2.99992 2.33301C2.99992 1.96482 3.2984 1.66634 3.66659 1.66634C3.8434 1.66634 4.01297 1.5961 4.13799 1.47108C4.26301 1.34605 4.33325 1.17649 4.33325 0.999674C4.33325 0.631485 4.63173 0.333008 4.99992 0.333008ZM10.5285 0.52827C10.7889 0.26792 11.211 0.26792 11.4713 0.52827L13.4713 2.52827C13.7317 2.78862 13.7317 3.21073 13.4713 3.47108L3.47132 13.4711C3.21097 13.7314 2.78886 13.7314 2.52851 13.4711L0.528514 11.4711C0.268165 11.2107 0.268165 10.7886 0.528514 10.5283L8.52838 2.52841L10.5285 0.52827ZM8.99992 3.94248L1.94273 10.9997L2.99992 12.0569L10.0571 4.99967L8.99992 3.94248ZM10.9999 4.05687L9.94273 2.99967L10.9999 1.94248L12.0571 2.99967L10.9999 4.05687ZM11.6666 6.99967C12.0348 6.99967 12.3333 7.29815 12.3333 7.66634C12.3333 7.84315 12.4035 8.01272 12.5285 8.13775C12.6535 8.26277 12.8231 8.33301 12.9999 8.33301C13.3681 8.33301 13.6666 8.63148 13.6666 8.99967C13.6666 9.36786 13.3681 9.66634 12.9999 9.66634C12.8231 9.66634 12.6535 9.73658 12.5285 9.8616C12.4035 9.98663 12.3333 10.1562 12.3333 10.333C12.3333 10.7012 12.0348 10.9997 11.6666 10.9997C11.2984 10.9997 10.9999 10.7012 10.9999 10.333C10.9999 10.1562 10.9297 9.98663 10.8047 9.8616C10.6796 9.73658 10.5101 9.66634 10.3333 9.66634C9.96506 9.66634 9.66659 9.36786 9.66659 8.99967C9.66659 8.63148 9.96506 8.33301 10.3333 8.33301C10.5101 8.33301 10.6796 8.26277 10.8047 8.13775C10.9297 8.01272 10.9999 7.84315 10.9999 7.66634C10.9999 7.29815 11.2984 6.99967 11.6666 6.99967Z\" fill=\"currentColor\"/>\n            </svg>\n            ", "default": true}, "decaCloudTools": {"type": "boolean", "name": "decaCloudTools", "displayName": "DECA Cloud", "icon": "<svg width=\"12\" height=\"14\" viewBox=\"0 0 12 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.87291 0.365731C7.14752 0.455028 7.33342 0.710961 7.33342 0.99972V4.99972H10.6668C10.9175 4.99972 11.147 5.14042 11.2608 5.36387C11.3746 5.58732 11.3534 5.85571 11.2059 6.0585L5.87257 13.3918C5.70273 13.6254 5.40186 13.723 5.12725 13.6337C4.85265 13.5444 4.66675 13.2885 4.66675 12.9997V8.99972H1.33342C1.08267 8.99972 0.853126 8.85902 0.739341 8.63557C0.625556 8.41212 0.646776 8.14373 0.79426 7.94094L6.12759 0.607605C6.29743 0.374076 6.59831 0.276434 6.87291 0.365731ZM2.6426 7.66639H5.33342C5.70161 7.66639 6.00008 7.96486 6.00008 8.33305V10.9496L9.35757 6.33305H6.66675C6.29856 6.33305 6.00008 6.03458 6.00008 5.66639V3.04984L2.6426 7.66639Z\" fill=\"currentColor\"/>\n            </svg>\n            ", "default": true}}}, "popularApps": {"type": "array", "name": "popularApps", "displayName": "Popular Apps", "items": {"type": "object", "properties": {"name": {"type": "string", "name": "name", "displayName": "App Name"}, "icon": {"type": "string", "name": "icon", "displayName": "Icon URL", "description": "URL to the app icon"}, "description": {"type": "string", "name": "description", "displayName": "Description"}, "enabled": {"type": "boolean", "name": "enabled", "displayName": "Enabled", "default": true}}, "required": ["name", "icon"]}, "default": [{"displayName": "Gmail", "name": "gmail", "icon": "📧", "description": "Send and receive emails using Gmail", "enabled": true}, {"displayName": "Google Drive", "name": "google-drive", "icon": "📁", "description": "Access and manage files in Google Drive", "enabled": true}, {"displayName": "<PERSON><PERSON>ck", "name": "slack", "icon": "💬", "description": "Send messages and interact with <PERSON><PERSON><PERSON>", "enabled": true}, {"displayName": "Hubspot", "name": "hubspot", "icon": "📊", "description": "Connect and manage your Hubspot CRM", "enabled": true}, {"displayName": "Google Calendar", "name": "google-calendar", "icon": "📅", "description": "Connect and manage your Google Calendar events", "enabled": true}, {"displayName": "Google Docs", "name": "google-docs", "icon": "📝", "description": "Connect and manage your Google Docs documents", "enabled": true}, {"displayName": "Google Sheets", "name": "google-sheets", "icon": "📊", "description": "Connect and manage your Google Sheets spreadsheets", "enabled": true}, {"displayName": "Google Slides", "name": "google-slides", "icon": "📑", "description": "Connect and manage your Google Slides presentations", "enabled": true}]}, "buildInTools": {"type": "array", "name": "buildInTools", "displayName": "Build-in Tools", "items": {"type": "object", "properties": {"name": {"type": "string", "name": "name", "displayName": "Tool Name"}, "icon": {"type": "string", "name": "icon", "displayName": "Icon", "description": "SVG content for the icon"}, "description": {"type": "string", "name": "description", "displayName": "Description"}, "enabled": {"type": "boolean", "name": "enabled", "displayName": "Enabled", "default": true}}, "required": ["name", "icon"]}, "default": [{"displayName": "Code", "name": "code", "icon": "🧩", "description": "Execute custom JavaScript code", "enabled": true}, {"displayName": "Function", "name": "function", "icon": "▶️", "description": "Execute a function from the function server", "enabled": true}, {"displayName": "Path", "name": "path", "icon": "🛣️", "description": "Create conditional paths in your workflow", "enabled": true}, {"displayName": "Zoom Meetings", "name": "zoom-meetings", "icon": "📹", "description": "Interact with Zoom meetings, webinars, and users", "enabled": true}, {"displayName": "<PERSON> Trigger", "name": "new-trigger", "icon": "🎯", "description": "Create custom triggers for your workflows", "enabled": true}, {"displayName": "Filter", "name": "filter", "icon": "🔍", "description": "Filter data based on conditions", "enabled": true}, {"displayName": "Loop", "name": "loop", "icon": "🔄", "description": "Iterate over data or repeat actions multiple times", "enabled": true}, {"displayName": "Wait", "name": "wait", "icon": "⏱️", "description": "Pause workflow execution for a specified duration, until a specific time, or until an event occurs", "enabled": true}, {"displayName": "HTTP", "name": "http", "icon": "🌐", "description": "Make HTTP requests to external APIs and services", "enabled": true}, {"displayName": "Schedule", "name": "schedule", "icon": "📅", "description": "Trigger a workflow on a scheduled basis such as hourly, daily, weekly, or monthly", "enabled": true}, {"displayName": "Webhook", "name": "webhook", "icon": "🔧", "description": "Trigger a workflow on a scheduled basis such as hourly, daily, weekly, or monthly", "enabled": true}]}, "decaCloudTools": {"type": "array", "name": "decaCloudTools", "displayName": "DECA Cloud Tools", "items": {"type": "object", "properties": {"name": {"type": "string", "name": "name", "displayName": "Tool Name"}, "icon": {"type": "string", "name": "icon", "displayName": "Icon", "description": "Icon content or identifier"}, "description": {"type": "string", "name": "description", "displayName": "Description"}, "enabled": {"type": "boolean", "name": "enabled", "displayName": "Enabled", "default": true}}, "required": ["name", "icon"]}, "default": [{"displayName": "DECA AI Widgets", "name": "deca-ai-widgets", "icon": "🤖", "description": "Interact with DECA AI Widgets to manage widgets, widget templates, and more.", "enabled": true}, {"displayName": "DECA Chatbot", "name": "chatbot", "icon": "🤖", "description": "Create and manage conversational interfaces", "enabled": true}, {"displayName": "DECA CRM", "name": "deca-crm", "icon": "👥", "description": "Interact with DECA CRM to manage contacts, companies, deals, activities and more.", "enabled": true}, {"displayName": "DECA KB", "name": "deca-kb", "icon": "📚", "description": "Interact with DECA Knowledge Base to manage folders, knowledge bases, articles, article templates, documents, and perform searches.", "enabled": true}, {"displayName": "DECA Livechat", "name": "deca-livechat", "icon": "💬", "description": "Interact with DECA Livechat to manage conversations, messages, and more.", "enabled": true}, {"displayName": "DECA Pages", "name": "pages", "icon": "📄", "description": "Create and manage pages", "enabled": true}, {"displayName": "DECA Tables", "name": "deca-tables", "icon": "📊", "description": "Data storage and management", "enabled": true}]}, "aiTools": {"type": "array", "name": "aiTools", "displayName": "AI Tools", "items": {"type": "object", "properties": {"name": {"type": "string", "name": "name", "displayName": "Tool Name"}, "icon": {"type": "string", "name": "icon", "displayName": "Icon", "description": "Icon content or identifier"}, "description": {"type": "string", "name": "description", "displayName": "Description"}, "enabled": {"type": "boolean", "name": "enabled", "displayName": "Enabled", "default": true}}, "required": ["name", "icon"]}, "default": [{"displayName": "OpenAI", "name": "openai", "icon": "🦾", "description": "Interact with OpenAI models to generate text, chat completions, embeddings, and more.", "enabled": true}]}}}}}