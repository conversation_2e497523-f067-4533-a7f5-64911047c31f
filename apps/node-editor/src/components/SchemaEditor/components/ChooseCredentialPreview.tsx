import { useMemo, useState, useCallback } from 'react';
import { with<PERSON>ontainer } from '@resola-ai/ui/components/hoc/withContainer';
import ChooseCredential from './ChooseCredential';
import useSWR from 'swr';
import { mockCredentials } from '../../../constants/credential';
import get from 'lodash/get';

interface ChooseCredentialPreviewProps {
  schema: Record<string, any>;
}

export const ChooseCredentialPreview: React.FC<ChooseCredentialPreviewProps> = ({ schema }) => {
  // Mock API fetcher function
  const fetcher = async () => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return mockCredentials;
  };

  // Fetch credentials with SWR
  const { data: credentials, isLoading } = useSWR('credentials', fetcher);

  const credentialType = get(schema, 'credentialTypes', []);

  // Convert credentials to ComboboxOption format
  const credentialOptions = useMemo(() => {
    if (!credentials) return [];
    if (credentialType.length > 0) {
      return credentials
        .filter((cred) => credentialType.includes(cred.authenticationScheme))
        .map((cred) => ({
          value: cred.id,
          label: cred.name,
          description: cred.description,
          type: cred.authenticationScheme,
          provider: cred.provider,
        }));
    }
    return credentials.map((cred) => ({
      value: cred.id,
      label: cred.name,
      description: cred.description,
      type: cred.authenticationScheme,
      provider: cred.provider,
    }));
  }, [credentials, credentialType]);

  const [activeValue, setActiveValue] = useState<string>('');

  const handleChange = useCallback((value: string) => {
    setActiveValue(value);
  }, []);

  return (
    <ChooseCredential
      id='choose-credential-combobox'
      value={activeValue}
      onChange={handleChange}
      options={credentialOptions}
      isLoading={isLoading}
    />
  );
};

export const ChooseCredentialPreviewWithContainer = withContainer(ChooseCredentialPreview);
