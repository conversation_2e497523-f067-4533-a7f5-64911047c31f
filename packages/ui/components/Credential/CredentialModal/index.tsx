import {
  Modal,
  Flex,
  Text,
  Box,
  rem,
  TextInput,
  ActionIcon,
  Group,
  Textarea,
  Loader,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconPencil } from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import { CredentialForm } from '../CredentialForm';
import { SchemaField } from '../../FormChema/type';
import { CardStatus, ICredentialPayload, ICredential } from '../type';
import { withTolgee } from '../../hoc/withTolgee';
import ErrorBoundary from '../../ErrorBoundary';
interface CredentialModalProps {
  opened: boolean;
  schema: SchemaField;
  credential?: ICredential;
  loading?: boolean;
  zIndex?: number;
  onSubmit: (values: ICredentialPayload, setStatus?: (status: CardStatus) => void) => Promise<void>;
  onClose: () => void;
  onTest?: (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onDelete?: (credential: ICredentialPayload) => Promise<void>;
}

const useStyles = createStyles(() => ({
  modal: {
    section: {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflow: 'hidden',
    },
    header: {
      h2: {
        flex: 1,
      },
    },
    '.mantine-Modal-body': {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflow: 'hidden',
    },
  },
  icon: {
    marginRight: rem(8),
  },
  editButton: {
    marginLeft: rem(8),
  },
  nameInput: {
    width: '30%',
  },
  descriptionInput: {
    width: '70%',
    textarea: {
      border: 'none',
      padding: 0,
    },
  },
  credentialForm: {
    padding: 0,
    maxWidth: '100%',
  },
}));

export const CredentialModal = ({
  opened,
  schema,
  credential,
  loading,
  zIndex,
  onClose,
  onSubmit,
  onTest,
  onDelete,
}: CredentialModalProps) => {
  const { classes } = useStyles();
  const [isEditing, setIsEditing] = useState(false);
  const [editedCredential, setEditedCredential] = useState<ICredentialPayload | undefined>(
    undefined
  );

  useEffect(() => {
    if (!credential) {
      setEditedCredential({
        provider: schema?.provider ?? schema?.name ?? '',
        name: schema?.displayName ?? '',
        description: schema?.description ?? '',
        authenticationScheme:
          schema?.settings?.credential?.default ?? Object.keys(schema?.credentials ?? {})[0] ?? '',
        settings: undefined,
      });
      return;
    }

    setEditedCredential(credential);
  }, [credential, schema]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEditedCredential({ ...editedCredential, name: value });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedCredential({ ...editedCredential, description: e.target.value });
  };

  const handleBlur = () => {
    if (editedCredential?.name === '') {
      setEditedCredential({
        ...editedCredential,
        name: credential?.name ?? schema?.displayName ?? '',
      });
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      if (editedCredential?.name === '') {
        setEditedCredential({
          ...editedCredential,
          name: credential?.name ?? schema?.displayName ?? '',
        });
      }
      setIsEditing(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      className={classes.modal}
      data-testid='credential-modal'
      closeButtonProps={{
        'aria-label': 'Close',
      }}
      zIndex={zIndex}
      title={
        <Flex align='center'>
          <Box flex={1}>
            {isEditing ? (
              <Group gap='xs'>
                <TextInput
                  value={editedCredential?.name}
                  className={classes.nameInput}
                  onChange={handleNameChange}
                  onBlur={handleBlur}
                  onKeyDown={handleKeyDown}
                  data-testid='title-input'
                  size='xs'
                />
                <ActionIcon
                  aria-label='edit-prompt-name'
                  c={'decaGrey.9'}
                  variant='transparent'
                  onClick={handleBlur}>
                  <IconCheck size={14} />
                </ActionIcon>
              </Group>
            ) : (
              <Group align='center' gap='xs'>
                <Text fw={600}>{editedCredential?.name}</Text>
                <ActionIcon
                  aria-label='edit-prompt-name'
                  c={'decaGrey.9'}
                  variant='transparent'
                  onClick={() => setIsEditing(!isEditing)}>
                  <IconPencil size={14} />
                </ActionIcon>
              </Group>
            )}
            <Textarea
              size='xs'
              c='dimmed'
              value={editedCredential?.description}
              className={classes.descriptionInput}
              autosize
              onChange={handleDescriptionChange}
              data-testid='description-input'
            />
          </Box>
        </Flex>
      }
      size='1120'>
      {/* add fallback here */}
      <ErrorBoundary fallback={<div>...</div>}>
        {loading ? (
          <Loader />
        ) : (
          <CredentialForm
            schema={schema}
            credential={editedCredential}
            onSubmit={onSubmit}
            onCancel={onClose}
            onTest={onTest}
            onDelete={onDelete}
          />
        )}
      </ErrorBoundary>
    </Modal>
  );
};

export const CredentialModalTolgee = withTolgee(CredentialModal);
