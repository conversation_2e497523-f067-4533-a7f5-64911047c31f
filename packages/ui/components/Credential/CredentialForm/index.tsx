import { Box, Group, Text, Stack } from '@mantine/core';
import { useForm } from 'react-hook-form';
import { useTranslate } from '@tolgee/react';
import { ICredentialPayload } from '../type';
import { CredentialTypeSelector } from '../CredentialTypeSelector';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CardStatus } from '../type';
import { createStyles } from '@mantine/emotion';
import { SchemaField } from '../../FormChema/type';
import AlertStatus from '../../AlertStatus';
import { DynamicSettingsForm } from '../DynamicSettingsForm';
import { getDefaultValue } from '../../../utils/schema';
import { DecaButton } from '../../DecaButton';

export interface CredentialFormProps {
  schema: SchemaField;
  credential?: ICredentialPayload;
  className?: string;
  onSubmit?: (
    values: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onCancel?: () => void;
  onTest?: (
    credential: ICredentialPayload,
    setStatus?: (status: CardStatus) => void
  ) => Promise<void>;
  onDelete?: (credential: ICredentialPayload) => Promise<void>;
}

const useStyles = createStyles(() => ({
  boxForm: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  form: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflow: 'hidden',
  },
  stack: {
    overflow: 'auto',
  },
}));

export const CredentialForm = ({
  schema,
  credential,
  onSubmit,
  onCancel,
  onTest,
  onDelete,
  className,
}: CredentialFormProps) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate(['credential', 'common']);
  const [status, setStatus] = useState<CardStatus | undefined>();
  const dynamicSettingsFormRef = useRef<any>(null);
  const { credentials, defaultCredential } = useMemo(() => {
    const settingsCredential = schema?.settings?.credential;
    const schemaCredentials = schema?.credentials ?? {};
    const defaultCredential =
      settingsCredential?.default ?? Object.keys(schemaCredentials)[0];
    return {
      defaultCredential,
      credentials: schemaCredentials,
    };
  }, [schema]);
  const { control, handleSubmit, watch, setValue } = useForm<ICredentialPayload>({
    defaultValues: {
      provider: schema?.provider ?? '',
      name: schema?.displayName ?? '',
      description: schema?.description ?? '',
      authenticationScheme: defaultCredential ?? '',
      settings: undefined,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (credential) {
      setValue('provider', credential.provider);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [credential?.provider]);

  useEffect(() => {
    if (credential) {
      setValue('description', credential.description);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [credential?.description]);

  useEffect(() => {
    if (credential) {
      setValue('id', credential.id);
      setValue('authenticationScheme', credential.authenticationScheme);
      setValue('name', credential.name);
      setValue('provider', credential.provider);
      if (credential.settings) {
        setValue('settings', credential.settings);
      } else {
        handleTypeChange(credential.authenticationScheme ?? '');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [credential]);

  const type = watch('authenticationScheme') ?? '';

  const clearSettings = useCallback(() => {
    setValue('settings', undefined);
  }, [setValue]);

  const handleTypeChange = useCallback(
    (value: string) => {
      const currentCredentialConfig = credentials[value];
      if (!value || !currentCredentialConfig) {
        clearSettings();
        return;
      }

      let newSettings: Record<string, any> = {};
      if (credential?.authenticationScheme === value && credential?.settings) {
        newSettings = credential.settings;
      } else {
        Object.values(currentCredentialConfig.properties).forEach((prop: any) => {
          newSettings[prop.name] = getDefaultValue(prop);
        });
      }
      setValue('settings', newSettings);
      dynamicSettingsFormRef.current?.focus();
    },
    [
      clearSettings,
      credential?.settings,
      credential?.authenticationScheme,
      credentials,
      setValue,
      dynamicSettingsFormRef,
    ]
  );

  const handleSubmitForm = handleSubmit((values: ICredentialPayload) => {
    onSubmit?.(values, setStatus);
  });

  const handleTest = handleSubmit((values: ICredentialPayload) => {
    onTest?.(values, setStatus);
  });

  return (
    <Box className={cx(classes.boxForm, className)}>
      <form
        onSubmit={handleSubmit(async values => {
          if (onSubmit) {
            await onSubmit(values, setStatus);
          }
        })}
        className={classes.form}
        data-testid='credential-form'>
        <Stack className={classes.stack}>
          {Object.keys(credentials).length > 0 && (
            <Box>
              <Text fw={600}>{t('form.type')}</Text>
              <CredentialTypeSelector
                control={control}
                credentialTypes={credentials}
                onChange={handleTypeChange}
              />
              <DynamicSettingsForm
                control={control}
                selectedCredential={credentials[type]}
                watch={watch}
                ref={dynamicSettingsFormRef}
              />
            </Box>
          )}
        </Stack>
        <Box mt={16}>
          {status && (
            <AlertStatus
              status={status.status}
              message={status.message}
              variant='box'
              onClose={() => setStatus(undefined)}
            />
          )}
          <Group justify='space-between' gap='md'>
            <Group>
              {credential?.id && (
                <DecaButton
                  variant='negative'
                  onClick={() => onDelete?.(credential)}
                  data-testid='delete-button'>
                  {t('button.remove', { ns: 'common' })}
                </DecaButton>
              )}
              <DecaButton
                onClick={handleTest}
                data-testid='test-button'
                variant='secondary'
                size='md'>
                {t('button.test', { ns: 'common' })}
              </DecaButton>
            </Group>
            <Group>
              {onCancel && (
                <DecaButton variant='neutral' onClick={onCancel} data-testid='cancel-button'>
                  {t('button.cancel', { ns: 'common' })}
                </DecaButton>
              )}
              <DecaButton
                variant='primary'
                type='submit'
                onClick={handleSubmitForm}
                data-testid='submit-button'>
                {t('button.save', { ns: 'common' })}
              </DecaButton>
            </Group>
          </Group>
        </Box>
      </form>
    </Box>
  );
};
