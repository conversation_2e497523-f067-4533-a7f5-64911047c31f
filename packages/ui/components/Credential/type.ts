export interface CardStatus {
  status: 'success' | 'error';
  message: string;
}
export interface ICredential {
  id: string;
  name: string;
  provider: string;
  description?: string;
  authenticationScheme: string;
  settings: Record<string, any>;
  metadata?: Record<string, any>;
  workspaceId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICredentialPayload extends Partial<ICredential> {}