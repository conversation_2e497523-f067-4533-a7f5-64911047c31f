import { Radio } from 'react-hook-form-mantine';
import { Control } from 'react-hook-form';
import { Flex } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { SchemaField } from '../../FormChema/type';

interface CredentialTypeSelectorProps {
  control: Control<any>;
  credentialTypes: Record<string, SchemaField>;
  onChange?: (value: string) => void;
}

const useStyles = createStyles(() => ({
  radioGroup: {
    marginBottom: '16px',
    marginTop: '16px',
    display: 'flex',
    gap: '8px',
  },
}));

export const CredentialTypeSelector = ({
  control,
  credentialTypes,
  onChange,
}: CredentialTypeSelectorProps) => {
  const { classes } = useStyles();

  return (
    <Radio.Group control={control} name='authenticationScheme' data-testid='type-radio-group' required>
      <Flex className={classes.radioGroup}>
        {Object.keys(credentialTypes).map(type => (
          <Radio.Item
            key={type}
            label={credentialTypes[type].displayName}
            value={type}
            data-testid={`type-radio-${type}`}
            onClick={() => {
              onChange?.(type);
            }}
          />
        ))}
      </Flex>
    </Radio.Group>
  );
};
