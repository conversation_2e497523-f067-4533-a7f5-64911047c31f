import { ComponentConfig } from './type';
import { FormTextInput } from './components/TextInput';
import { FormPasswordInput } from './components/PasswordInput';
import { FormCheckbox } from './components/Checkbox';
import { FormRadio } from './components/Radio';
import { FormSelectOption, FormSelect } from './components/SelectOption';
import { SchemaDisplayName } from './components/DisplayName';
import { FormKeyValueInput } from './components/KeyValueInput';
import { FormCode } from './components/Code';
import { FormTextareaInput } from './components/TextareaInput';
import { FormArrayObject } from './components/ArrayObject';
import { FormComboboxSelectDataPoint } from './components/ComboboxSelectDataPoint';
import { FormGroup } from './components/Group';

export const FORM_SCHEMA_CONFIGS: Record<string, ComponentConfig<any>> = {
  textInput: {
    id: 'textInput',
    types: ['text', 'number', 'object', 'datetime', 'string'],
    Component: FormTextInput,
  },
  passwordInput: {
    id: 'passwordInput',
    types: ['password', 'text-masked'],
    Component: FormPasswordInput,
  },
  checkbox: {
    id: 'checkbox',
    types: ['boolean', 'checkbox'],
    Component: FormCheckbox,
  },
  displayName: {
    id: 'displayName',
    types: ['displayName'],
    Component: SchemaDisplayName,
  },
  selectOption: {
    id: 'selectOption',
    types: ['options'],
    Component: FormSelectOption,
  },
  select: {
    id: 'select',
    types: ['select'],
    Component: FormSelect,
  },
  radio: {
    id: 'radio',
    types: ['radio'],
    Component: FormRadio,
  },
  arrayInput: {
    id: 'arrayObject',
    types: ['arrayObject'],
    Component: FormArrayObject,
  },
  keyValueInput: {
    id: 'keyValueInput',
    types: ['keyvalue'],
    Component: FormKeyValueInput,
  },
  textarea: {
    id: 'textarea',
    types: ['textarea'],
    Component: FormTextareaInput,
  },
  code: {
    id: 'code',
    types: ['code'],
    Component: FormCode,
  },
  comboboxSelectDataPoint: {
    id: 'comboboxSelectDataPoint',
    types: ['comboboxDataPoint'],
    Component: FormComboboxSelectDataPoint,
  },
  group: {
    id: 'group',
    types: ['group'],
    Component: FormGroup,
  },
};
