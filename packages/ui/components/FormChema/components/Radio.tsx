import React, { useState, useEffect } from 'react';
import { Radio } from '@mantine/core';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';

interface SchemaRadioProps {
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  value?: string;
}

export const SchemaRadio: React.FC<SchemaRadioProps> = ({ schema, onChange, value: valueProp }) => {
  const [value, setValue] = useState(valueProp || schema.default || '');

  useEffect(() => {
    setValue(valueProp || schema.default || '');
  }, [valueProp, schema.default]);

  return (
    <Radio.Group
      name={schema.name}
      value={value}
      onChange={value => {
        setValue(value);
        onChange?.(value);
      }}
      label={schema.displayName}
      description={schema.description}
      readOnly={schema?.readOnly}>
      {schema.options?.map((option: any) => (
        <Radio
          key={option.value}
          value={option.value}
          label={option.label}
          disabled={schema?.disabled}
        />
      ))}
    </Radio.Group>
  );
};

export const SchemaRadioWithContainer = withContainer(SchemaRadio);
export const FormRadio = withReactHookForm(SchemaRadio);
