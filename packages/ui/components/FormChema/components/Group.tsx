import { useMemo } from 'react';
import { SCHEMA_CONFIGS } from '../config';
import { Schema<PERSON>ield } from '../type';
import { Control, Controller } from 'react-hook-form';

interface SchemaRadioProps {
  schema: Record<string, any>;
  control: Control<any>;
}

export const FormGroup = ({ schema, control }: SchemaRadioProps) => {
  const fields = useMemo(() => {
    return Object.entries(schema.properties).map(([name, fieldSchema]) => ({
      name,
      schema: fieldSchema as Schema<PERSON>ield,
    }));
  }, [schema.properties]);

  if (fields.length === 0) {
    return null;
  }

  return (
    <>
      {fields.map(({ name, schema: fieldSchema }) => {
        const config = Object.values(SCHEMA_CONFIGS).find(
          config => Array.isArray(config.types) && config.types.includes(fieldSchema.type)
        );
        const FieldComponent = config?.Component;
        const isRequired = !!fieldSchema?.required;
        const defaultValue = fieldSchema?.default;

        if (!FieldComponent) {
          return null;
        }

        return (
          <Controller
            key={name}
            control={control}
            name={name}
            rules={{
              required: isRequired,
            }}
            defaultValue={defaultValue}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FieldComponent
                schema={fieldSchema}
                onChange={onChange}
                value={value}
                error={error}
              />
            )}
          />
        );
      })}
    </>
  );
};
