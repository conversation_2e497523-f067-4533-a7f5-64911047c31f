import { NumberInput, PasswordInput, Textarea, TextInput } from '@mantine/core';
import React, { useEffect, useMemo, useState } from 'react';
import { withContainer } from '../../hoc/withContainer';

export interface SchemaBaseInputProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

const NUMBER_TYPES = ['number', 'integer'];
const NUMBER_TYPES_WITH_DECIMAL = ['number'];

export const SchemaBaseInput: React.FC<SchemaBaseInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaBaseInputProps) => {
  const [value, setValue] = useState(valueProp || (schema.default ?? ''));
  useEffect(() => {
    if (
      schema.default &&
      typeof schema.default === 'object' &&
      Object.keys(schema.default).length === 0
    ) {
      setValue('');
      onChange?.('');
    } else {
      setValue(valueProp || (schema.default ?? ''));
      onChange?.(valueProp || (schema.default ?? ''));
    }
  }, [valueProp, schema.default]);

  const isNotNumber = isNaN(Number(value));
  const haveLinebreak = isNotNumber && typeof value === 'string' && value.includes('\n');

  const InputComponent = useMemo(() => {
    if (haveLinebreak) {
      return Textarea;
    }
    if (schema.type === 'text-masked') {
      return PasswordInput;
    }
    if (NUMBER_TYPES.includes(schema.type)) {
      return NumberInput;
    }
    return TextInput;
  }, [haveLinebreak, schema.type]);

  return (
    <InputComponent
      name={schema.name}
      value={value}
      onChange={e => {
        let newValue = e?.target?.value || '';
        if (NUMBER_TYPES.includes(schema.type)) {
          newValue = String(e);
        }

        setValue(newValue);
        onChange?.(newValue);
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      labelProps={{
        required: schema.required,
      }}
      min={0}
      max={100000000}
      description={schema.description}
      error={error}
      allowDecimal={NUMBER_TYPES_WITH_DECIMAL.includes(schema.type)}
      allowNegative={false}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
      hideControls
    />
  );
};

export const SchemaBaseInputWithContainer = withContainer(SchemaBaseInput);
