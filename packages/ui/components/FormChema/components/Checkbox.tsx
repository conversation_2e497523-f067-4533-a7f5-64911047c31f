import React, { useState, useEffect } from 'react';
import { Checkbox } from '@mantine/core';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';

interface SchemaCheckboxProps {
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  value?: boolean;
}

export const SchemaCheckbox: React.FC<SchemaCheckboxProps> = ({ schema, onChange, value }) => {
  const [checked, setChecked] = useState(value || schema.default || false);

  useEffect(() => {
    setChecked(value || schema.default || false);
  }, [value, schema.default]);

  return (
    <Checkbox
      name={schema.name}
      checked={checked}
      onChange={event => {
        setChecked(event.currentTarget.checked);
        onChange?.(event.currentTarget.checked);
      }}
      label={schema.displayName}
      description={schema.description}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
    />
  );
};

export const SchemaCheckboxWithContainer = withContainer(SchemaCheckbox);
export const FormCheckbox = withReactHookForm(SchemaCheckbox);
export const FormCheckboxWithContainer = withReactHookForm(SchemaCheckboxWithContainer);
