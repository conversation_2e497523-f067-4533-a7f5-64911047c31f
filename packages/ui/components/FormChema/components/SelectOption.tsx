import { Select } from '@mantine/core';
import React, { useEffect, useState } from 'react';
import { with<PERSON>ontainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';

interface SchemaSelectOptionProps {
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  value: string;
  error?: string;
}

export const SchemaSelectOption: React.FC<SchemaSelectOptionProps> = ({
  schema,
  onChange,
  value: initialValue,
  error,
}) => {
  const [value, setValue] = useState(initialValue ?? schema.default ?? '');

  useEffect(() => {
    setValue(initialValue ?? schema.default ?? '');
  }, [initialValue, schema.default]);

  return (
    <Select
      name={schema.name}
      value={`${value}`}
      onChange={value => {
        value && setValue(value);
        onChange?.(value);
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      data={schema.options?.map((option: any) => ({
        value: `${option.value}`,
        label: option.label || option.name,
      }))}
      description={schema.description}
      required={schema.required}
      error={error}
      disabled={schema?.disabled}
    />
  );
};

export const SchemaSelect: React.FC<SchemaSelectOptionProps> = ({
  schema,
  onChange,
  value: valueProp,
}: SchemaSelectOptionProps) => {
  const [value, setValue] = useState(valueProp ?? schema.default ?? '');

  useEffect(() => {
    setValue(valueProp ?? schema.default ?? '');
  }, [valueProp, schema.default]);

  return (
    <Select
      name={schema.name}
      value={`${value}`}
      onChange={e => {
        setValue(e || '');
        onChange?.(e || '');
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      data={schema.options.map(option => ({
        value: option.name,
        label: option.displayName,
      }))}
      description={schema.description}
      required={schema.required}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
    />
  );
};

export const SchemaSelectWithContainer = withContainer(SchemaSelect);
export const SchemaSelectOptionWithContainer = withContainer(SchemaSelectOption);

export const FormSelectOption = withReactHookForm(SchemaSelectOption);
export const FormSelectOptionWithContainer = withReactHookForm(SchemaSelectOptionWithContainer);

export const FormSelect = withReactHookForm(SchemaSelect);
export const FormSelectWithContainer = withReactHookForm(SchemaSelectWithContainer);
