import { Textarea } from '@mantine/core';
import React, { useEffect, useState } from 'react';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';

export interface SchemaTextareaInputProps {
  value?: string;
  schema: Record<string, any>;
  onChange?: (value: any) => void;
  withContainer?: boolean;
  error?: string;
}

export const SchemaTextareaInput: React.FC<SchemaTextareaInputProps> = ({
  schema,
  onChange,
  error,
  value: valueProp,
}: SchemaTextareaInputProps) => {
  const [value, setValue] = useState(valueProp || schema.default || '');

  useEffect(() => {
    setValue(valueProp || schema.default || '');
  }, [valueProp, schema.default]);

  return (
    <Textarea
      name={schema.name}
      value={value}
      onChange={e => {
        setValue(e.target.value);
        onChange?.(e.target.value);
      }}
      label={schema.displayName}
      placeholder={schema.placeholder}
      labelProps={{
        required: schema.required,
      }}
      description={schema.description}
      error={error}
      minRows={schema.minRows || 3}
      maxRows={schema.maxRows || 10}
      autosize={schema.autosize !== false}
      disabled={schema?.disabled}
      readOnly={schema?.readOnly}
    />
  );
};

export const SchemaTextareaInputWithContainer = withContainer(SchemaTextareaInput);
export const FormTextareaInput = withReactHookForm(SchemaTextareaInput);
export const FormTextareaInputWithContainer = withReactHookForm(SchemaTextareaInputWithContainer);
