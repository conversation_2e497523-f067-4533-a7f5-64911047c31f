{"name": "webhook", "displayName": "Webhook", "icon": "🔧", "group": "core", "category": ["builtin-popular"], "description": "Trigger from a request POST to a DECA webhook url", "version": "1.0.0", "settings": {}, "schemas": {"catchHook.hookUrl.request": {"properties": {"organizationId": {"type": "string", "required": true, "description": "Organization ID in ULID format for webhook URL generation"}}}, "catchHook.hookUrl.data": {"properties": {"url": {"type": "string", "required": true, "description": "Generated webhook URL"}, "apiKey": {"type": "string", "required": true, "description": "API key for webhook authentication"}}}}, "credentials": {}, "triggers": {"catchHook": {"name": "catchHook", "displayName": "Catch Hook", "description": "Trigger from a request POST to a DECA webhook url", "properties": {"urlGroup": {"name": "urlGroup", "attribute": "urlGroup", "displayName": "URL Group", "description": "The URL and API key to the DECA webhook", "type": "group", "required": true, "order": 1, "apiCall": {"payload": {}, "data": {"$ref": "#/schemas/catchHook.hookUrl.data"}}, "properties": {"hookUrl": {"name": "hookUrl", "displayName": "Hook URL", "description": "The URL to the DECA webhook", "type": "text", "apiCall": {"dataField": "data.url"}, "required": true, "readOnly": true, "order": 1}, "hookApiKey": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "Hook API Key", "description": "The API key to the DECA webhook", "type": "password", "apiCall": {"dataField": "data.apiKey"}, "required": true, "readOnly": true, "order": 2}}}, "pickupAttributeKey": {"name": "pickupAttributeKey", "displayName": "Pickup Attribute Key", "description": "By default, the node will pickup all data from the request body. You can change the key to pickup a different attribute. This can be dot notation.", "type": "string", "required": false, "order": 2}}}}, "actions": {}}