export type Schema = Record<string, any>;

export type SchemaType = {
  type?: string;
  name?: string;
  displayName?: string;
  description?: string;
  section?: string;
  properties?: Record<string, SchemaType>;
  options?: ComboboxOption[];
  data?: any;
  order?: number;
  [key: string]: any;
};

export type ComboboxOption = {
  name: string;
  value: string;
  description: string;
};

export type SetupFormInput = {
  credential?: string;
  action?: string;
  trigger?: string;
};

export type ConfigureFormInput = Record<string, any>;

export type SchemaFormInput = SetupFormInput & ConfigureFormInput;

export type SchemaFormCallback = (formValues: SchemaFormInput, fieldName: string) => void;

export type SectionType = 'triggers' | 'actions' | 'credential';

export type ObjectType = 'action' | 'trigger';

export type NodeApiCallCallback = (
  node: string,
  attribute: string,
  payload: any
) => Promise<{ data: Record<string, any> }>;
