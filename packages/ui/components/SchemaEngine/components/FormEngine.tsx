import { Stack, Text } from '@mantine/core';
import { SchemaType } from '../types';
import { useMemo } from 'react';
import { getUniqueOptions, objectToSortedArray } from '../../../utils/schema';
import { SCHEMA_CONFIGS_ENGINE } from '../../FormChema/configEngine';
import { useFormContext, useWatch } from 'react-hook-form';
import { ComboboxNode } from '../../FormChema/components/ComboboxSelectDataPoint';

// --- Utility ---
const isEmptyArray = (value: any): boolean =>
  Array.isArray(value) &&
  (value.length === 0 || value.every(item =>
    item == null || item === '' || (typeof item === 'object' && item !== null && isEmptyObject(item))
  ));

const isEmptyObject = (value: any): boolean =>
  typeof value === 'object' &&
  !Array.isArray(value) &&
  value !== null &&
  (Object.keys(value).length === 0 ||
    Object.values(value).every(val =>
      val == null || val === '' || (typeof val === 'object' && val !== null && !Array.isArray(val) && isEmptyObject(val)) || (Array.isArray(val) && isEmptyArray(val))
    ));

const validateRequiredArrayOrObject = (value: any, schemaType: string | string[]): boolean => {
  if (!value) return false;

  const types = Array.isArray(schemaType) ? schemaType : [schemaType];
  let parsedValue = value;

  if (typeof value === 'string') {
    try {
      parsedValue = JSON.parse(value);
    } catch {
      parsedValue = value;
    }
  }

  if (types.includes('array') && isEmptyArray(parsedValue)) return false;
  if ((types.includes('object') || types.includes('keyvalue')) && isEmptyObject(parsedValue)) return false;

  return true;
};

interface FormEngineItemProps {
  schema: SchemaType;
  previousNodes?: ComboboxNode[];
}

const FormEngineItem = (props: FormEngineItemProps) => {
  const { schema, previousNodes } = props;
  const { control } = useFormContext();

  const schemaType = schema.type;
  const fieldName = schema.name;
  const isRequired = !!schema?.required;
  const defaultValue = isRequired ? schema?.default : undefined;

  // --- visibleIf logic ---
  const visibleIf = schema.visibleIf;
  const watchedFields = useMemo(() => {
    if (!visibleIf) return [];
    return Object.keys(visibleIf);
  }, [visibleIf]);
  const watchedValues = useWatch({ control, name: watchedFields });

  const isVisible = useMemo(() => {
    if (!visibleIf) return true;
    return watchedFields.every((field, idx) => {
      const allowed = visibleIf[field];
      const value = watchedValues[idx];
      return Array.isArray(allowed) ? allowed.includes(value) : value === allowed;
    });
  }, [visibleIf, watchedFields, watchedValues]);
  // --- end visibleIf logic ---

  const tabConfig = useMemo(() => {
    if (
      Array.isArray(schemaType) &&
      (schemaType?.includes('array') || schemaType?.includes('object'))
    ) {
      return SCHEMA_CONFIGS_ENGINE.textArray;
    }
    return Object.values(SCHEMA_CONFIGS_ENGINE).find(
      config => Array.isArray(config.types) && config.types.includes(schemaType || '')
    );
  }, [schemaType]);

  // Enhanced validation rules
  const validationRules = useMemo(() => {
    const rules: any = {};
    
    if (isRequired) {
      const types = Array.isArray(schemaType) ? schemaType : [schemaType || ''];
      
      // Check if this is an array, object, or keyvalue field
      if (types.includes('array') || types.includes('object') || types.includes('keyvalue')) {
        rules.validate = (value: any) => validateRequiredArrayOrObject(value, schemaType || '');
      } else {
        rules.required = 'This field is required';
      }
    }
    
    return rules;
  }, [isRequired, schemaType]);

  if (!isVisible) return null;

  if (tabConfig && fieldName) {
    const DynamicField = tabConfig.Component;
    return (
      <DynamicField
        schema={schema}
        control={control}
        name={fieldName}
        previousNodes={previousNodes}
        rules={validationRules}
        defaultValue={defaultValue}
      />
    );
  }

  if ((Array.isArray(schemaType) && schemaType.includes('hidden')) || schemaType === 'hidden') {
    return null;
  }

  // Default fallback
  return (
    <Text c='blue' fw={500} size='lg' ta='center'>
      Unsupported schema type: {JSON.stringify(schemaType)}
    </Text>
  );
};

interface FormEngineProps {
  commonProperties?: Record<string, any>;
  selectedProperties?: Record<string, any>;
  previousNodes?: ComboboxNode[];
}

const FormEngine: React.FC<FormEngineProps> = props => {
  const { commonProperties = {}, selectedProperties = {}, previousNodes } = props;

  const items = useMemo(() => {
    const merged = [...objectToSortedArray(commonProperties, 'order'), ...objectToSortedArray(selectedProperties, 'order')];
    return getUniqueOptions({ options: merged, key: 'name' });
  }, [commonProperties, selectedProperties]);

  if (!items.length) return null;

  return (
    <Stack gap='lg'>
      {items.map(i => (
        <FormEngineItem key={i.name} schema={i} previousNodes={previousNodes} />
      ))}
    </Stack>
  );
};

export default FormEngine;
