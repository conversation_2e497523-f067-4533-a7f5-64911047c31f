import { IconChevronRight, IconCircleCheckFilled } from '@tabler/icons-react';
import { Group, Flex, rem, useMantineTheme, Box } from '@mantine/core';
import { IconCircle } from '@tabler/icons-react';
import { Fragment, useCallback } from 'react';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '../../DecaButton';

const useStyles = createStyles(theme => ({
  step: {
    fontWeight: 500,
    alignItems: 'center',
    gap: rem(4),
    cursor: 'pointer',
    userSelect: 'none',
    color: theme.colors.decaGrey[4],

    '&[data-completed="false"]': {
      cursor: 'not-allowed',
    },
    '&[data-active="true"], &[data-completed="true"]': {
      color: theme.colors.decaGrey[9],
      cursor: 'pointer',
    },
    '&:not([data-completed="false"]):hover': {
      color: theme.colors.decaGrey[9],
    },
    '&[data-completed="true"]:hover, &[data-active="true"]:hover': {
      color: theme.colors.decaNavy[4],
    },
  },
}));

export type Step = {
  id: string;
  label: string;
  isCompleted?: boolean;
};

export interface StepperProps {
  steps: Step[];
  activeStep: number;
  completedStep: number;
  currentStepValid: boolean;
  onStepChange?: (step: number) => void;
}

export const Stepper = (props: StepperProps) => {
  const { steps = [], activeStep = 0, completedStep = -1, currentStepValid, onStepChange } = props;
  const theme = useMantineTheme();
  const { classes } = useStyles();

  const handleStepChange = useCallback(
    (step: number) => {
      if (step > completedStep) return;
      onStepChange?.(step);
    },
    [completedStep, onStepChange]
  );

  if (steps.length === 0) return null;

  return (
    <Group c='decaGrey.6' gap='xs'>
      {steps.map((step, index) => {
        const isLastStep = index === steps.length - 1;
        const isActive = index === activeStep;
        const isCompleted = completedStep >= index || (isActive && currentStepValid);

        return (
          <Fragment key={step.id}>
            <Flex
              data-active={isActive}
              data-completed={isCompleted}
              className={classes.step}
              onClick={() => handleStepChange(index)}>
              {isCompleted ? (
                <IconCircleCheckFilled size={16} style={{ color: theme.colors.green[6] }} />
              ) : (
                <IconCircle size={16} />
              )}
              {step.label}
            </Flex>
            {!isLastStep && <IconChevronRight size={20} />}
          </Fragment>
        );
      })}
    </Group>
  );
};

export interface StepperActionsProps {
  activeStep: number;
  showBackBtn?: boolean;
  showNextBtn?: boolean;
  disabledNextBtn?: boolean;
  disabledBackBtn?: boolean;
  nextBtnLabel?: string;
  backBtnLabel?: string;
  onNext?: (step: number) => void;
  onBack?: (step: number) => void;
  isLoading?: boolean;
}

export const StepperActions = (props: StepperActionsProps) => {
  const {
    onNext,
    onBack,
    activeStep = 0,
    disabledNextBtn = false,
    disabledBackBtn = false,
    showNextBtn = true,
    showBackBtn = true,
    nextBtnLabel = 'Continue',
    backBtnLabel = 'Back',
    isLoading = false,
  } = props;

  const isFirstStep = activeStep === 0;

  const handleNext = useCallback(() => {
    if (!disabledNextBtn) {
      onNext?.(activeStep + 1);
    }
  }, [onNext, activeStep, disabledNextBtn]);

  const handleBack = useCallback(() => {
    if (!isFirstStep && !disabledBackBtn) {
      onBack?.(activeStep - 1);
    }
  }, [isFirstStep, onBack, activeStep, disabledBackBtn]);

  return (
    <>
      <Box h={rem(64)} />
      <Group grow pos='absolute' bottom={0} left={0} right={0}>
        {showBackBtn && (
          <DecaButton
            variant='neutral'
            disabled={disabledBackBtn}
            onClick={handleBack}
            size='md'
            loading={isLoading}>
            {backBtnLabel}
          </DecaButton>
        )}
        {showNextBtn && (
          <DecaButton disabled={disabledNextBtn} onClick={handleNext} size='md' loading={isLoading}>
            {nextBtnLabel}
          </DecaButton>
        )}
      </Group>
    </>
  );
};
