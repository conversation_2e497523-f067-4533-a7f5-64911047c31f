import { useMemo, useState } from 'react';
import { Flex } from '@mantine/core';
import { withTolgee } from '../hoc/withTolgee';
import { ICredential, ICredentialPayload } from '../Credential/type';
import { NodeApiCallCallback, Schema, SchemaFormCallback, SchemaFormInput } from './types';
import SetupForm from './components/SetupForm';
import ConfigureForm from './components/ConfigureForm';
import { Step } from './components/Stepper';
import { ComboboxNode } from '../FormChema/components/ComboboxSelectDataPoint';
import { useSchema } from './hooks/useSchema';
import { NODE_NAME } from '../../constants';

const STEPS = {
  SETUP: {
    id: 'setup',
    label: 'Select',
  },
  CONFIGURE: {
    id: 'configure',
    label: 'Configure',
  },
  TEST: {
    id: 'test',
    label: 'Test',
  },
};

export const ACTION_FIELD = 'action';
export const TRIGGER_FIELD = 'trigger';

const isSchemaActionsOrTriggers = (schema: Record<string, any>) => {
  return schema?.actions && Object.keys(schema?.actions).length > 0 ? ACTION_FIELD : TRIGGER_FIELD;
};

interface SchemaEngineProps {
  schema?: Schema;
  activeStep?: number;
  completedStep?: number;
  onStepChange?: (step: number) => void;
  onClose?: () => void;
  credentials: ICredential[];
  isCredentialsLoading: boolean;
  createCredential?: (credential: ICredentialPayload) => Promise<ICredential | null>;
  onOpenAppCatalog?: () => void;
  // Form
  form: SchemaFormInput;
  onFormChange?: SchemaFormCallback;
  previousNodes?: ComboboxNode[];
  nodeContext?: typeof ACTION_FIELD | typeof TRIGGER_FIELD;
  // API Call
  onNodeApiCall: NodeApiCallCallback;
}

const SchemaEngine: React.FC<SchemaEngineProps> = props => {
  const {
    form: formRaw,
    schema: schemaRoot = {},
    credentials,
    previousNodes,
    activeStep = 0,
    completedStep = -1,
    isCredentialsLoading,
    onStepChange,
    onFormChange,
    onOpenAppCatalog,
    createCredential,
    onClose,
    nodeContext,
    onNodeApiCall,
  } = props;

  const { schema, form } = useSchema(schemaRoot, formRaw);
  const schemaName = schema?.name;
  const [nodeApiCallResults, setNodeApiCallResults] = useState<Record<string, any>>({});
  const [isNodeApiCallLoaded, setIsNodeApiCallLoaded] = useState(false);

  const objectType = useMemo(() => {
    if (nodeContext === ACTION_FIELD) return ACTION_FIELD;
    if (nodeContext === TRIGGER_FIELD) return TRIGGER_FIELD;
    return isSchemaActionsOrTriggers(schema);
  }, [nodeContext, schema]);

  const showSetupStep = schemaName !== NODE_NAME.PATH;

  const schemaEngineSteps = useMemo(() => {
    const steps: Step[] = [];

    if (showSetupStep) {
      steps.push(STEPS.SETUP);
    }

    steps.push(STEPS.CONFIGURE);

    return steps;
  }, [showSetupStep]);

  const currentStep = schemaEngineSteps[activeStep];

  return (
    <Flex direction='column' h='100%' gap='0' pos='relative'>
      {currentStep.id === STEPS.SETUP.id && (
        <SetupForm
          schema={schema}
          steps={schemaEngineSteps}
          activeStep={activeStep}
          completedStep={completedStep}
          objectType={objectType}
          credentials={credentials}
          isCredentialsLoading={isCredentialsLoading}
          createCredential={createCredential}
          onOpenAppCatalog={onOpenAppCatalog}
          onStepChange={onStepChange}
          form={form}
          onFormChange={onFormChange}
          onNodeApiCall={onNodeApiCall}
          setNodeApiCallResults={setNodeApiCallResults}
          isNodeApiCallLoaded={isNodeApiCallLoaded}
          setIsNodeApiCallLoaded={setIsNodeApiCallLoaded}
        />
      )}

      {currentStep.id === STEPS.CONFIGURE.id && (
        <ConfigureForm
          schema={schema}
          steps={schemaEngineSteps}
          activeStep={activeStep}
          completedStep={completedStep}
          objectType={objectType}
          onStepChange={onStepChange}
          form={form}
          onFormChange={onFormChange}
          defaultToFirstObject={!showSetupStep}
          previousNodes={previousNodes}
          onClose={onClose}
          nodeApiCallResults={nodeApiCallResults}
        />
      )}
    </Flex>
  );
};

export default SchemaEngine;
export const SchemaEngineWithTolgee = withTolgee(SchemaEngine);
