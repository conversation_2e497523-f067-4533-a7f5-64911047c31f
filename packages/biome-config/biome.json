{"$schema": "https://biomejs.dev/schemas/1.5.3/schema.json", "organizeImports": {"enabled": true}, "vcs": {"enabled": true, "clientKind": "git"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "warn"}, "correctness": {"useExhaustiveDependencies": "off"}, "suspicious": {"noArrayIndexKey": "warn", "noExplicitAny": "off", "noEmptyInterface": "warn"}, "style": {"noNonNullAssertion": "off"}, "complexity": {"noForEach": "off", "noUselessSwitchCase": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "indentWidth": 2, "indentStyle": "space", "jsxQuoteStyle": "single", "semicolons": "always", "trailingComma": "es5"}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "files": {"ignore": ["dist/**", "build/**", "coverage/**", "node_modules/**", "*.d.ts", ".next/**", ".turbo/**", "amplify/**", "public/**", "tsconfig.json", "tsconfig.node.json", "tsconfig.app.json"]}}