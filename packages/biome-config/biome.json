{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": true, "clientKind": "git"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "warn"}, "correctness": {"useExhaustiveDependencies": "off"}, "suspicious": {"noArrayIndexKey": "warn", "noExplicitAny": "off", "noEmptyInterface": "warn"}, "style": {"noNonNullAssertion": "off"}, "complexity": {"noForEach": "off", "noUselessSwitchCase": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single", "indentWidth": 2, "indentStyle": "space", "jsxQuoteStyle": "single", "semicolons": "always", "trailingCommas": "es5"}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "files": {"ignoreUnknown": false, "includes": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.json"], "maxSize": 10485760}}